const { sequelize } = require('../src/models');
const fs = require('fs');
const path = require('path');

async function backupAndPrepareReimport() {
  try {
    console.log('🔄 开始备份当前数据并准备重新导入...');

    // 1. 创建备份目录
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);

    console.log(`📋 备份文件: ${backupFile}`);

    // 2. 导出当前数据统计
    console.log('\n📊 当前数据统计:');
    
    const [courseStats] = await sequelize.query(`
      SELECT COUNT(*) as total FROM courses
    `);
    
    const [categoryStats] = await sequelize.query(`
      SELECT COUNT(*) as total FROM categories
    `);
    
    const [unitStats] = await sequelize.query(`
      SELECT COUNT(*) as total FROM course_units
    `);

    console.log(`  - 课程数量: ${courseStats[0].total}`);
    console.log(`  - 分类数量: ${categoryStats[0].total}`);
    console.log(`  - 课程单元数量: ${unitStats[0].total}`);

    // 3. 检查是否有原始导入数据
    console.log('\n📋 检查原始导入数据...');

    try {
      const [importTasks] = await sequelize.query(`
        SELECT id, filename, type, status, total_records, success_records, error_records, created_at
        FROM import_tasks
        ORDER BY created_at DESC
        LIMIT 10
      `);

      if (importTasks.length > 0) {
        console.log('最近的导入任务:');
        importTasks.forEach(task => {
          console.log(`  - ${task.filename} (${task.type}): ${task.success_records}/${task.total_records} 成功, 状态: ${task.status}`);
        });
      } else {
        console.log('没有找到导入任务记录');
      }
    } catch (error) {
      console.log('import_tasks表不存在，跳过导入任务检查');
    }

    // 4. 检查数据完整性
    console.log('\n📋 检查数据完整性...');
    
    // 检查课程分类关联
    const [coursesWithoutCategory] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses WHERE category_id IS NULL
    `);
    
    // 检查孤立的课程单元
    const [orphanUnits] = await sequelize.query(`
      SELECT COUNT(*) as count FROM course_units cu
      LEFT JOIN courses c ON cu.course_id = c.id
      WHERE c.id IS NULL
    `);

    console.log(`  - 无分类的课程: ${coursesWithoutCategory[0].count}`);
    console.log(`  - 孤立的课程单元: ${orphanUnits[0].count}`);

    // 5. 显示分类分布
    const [categoryDistribution] = await sequelize.query(`
      SELECT c.name, COUNT(co.id) as course_count
      FROM categories c
      LEFT JOIN courses co ON c.id = co.category_id
      GROUP BY c.id, c.name
      HAVING course_count > 0
      ORDER BY course_count DESC
      LIMIT 15
    `);

    console.log('\n📋 当前分类分布:');
    categoryDistribution.forEach(cat => {
      console.log(`  - ${cat.name}: ${cat.course_count} 个课程`);
    });

    // 6. 清理准备重新导入
    console.log('\n🧹 准备清理数据以重新导入...');
    console.log('⚠️  注意：重新导入将清空以下表的数据：');
    console.log('  - courses (课程表)');
    console.log('  - course_units (课程单元表)');
    console.log('  - categories (分类表)');
    console.log('  - import_tasks (导入任务表)');

    // 7. 提供重新导入指南
    console.log('\n📋 重新导入步骤指南:');
    console.log('1. 确认要重新导入后，运行清理脚本:');
    console.log('   node scripts/clean-for-reimport.js');
    console.log('');
    console.log('2. 按顺序导入数据:');
    console.log('   a) 首先导入分类数据 (courses_classify)');
    console.log('   b) 然后导入课程数据 (courses)');
    console.log('   c) 最后导入课程单元数据 (courses_item)');
    console.log('');
    console.log('3. 导入完成后运行验证脚本:');
    console.log('   node scripts/verify-import.js');

    // 8. 检查导入文件是否存在
    console.log('\n📋 检查导入文件...');
    const uploadsDir = path.join(__dirname, '../uploads');
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir).filter(file => 
        file.endsWith('.xlsx') || file.endsWith('.csv')
      );
      
      if (files.length > 0) {
        console.log('找到以下导入文件:');
        files.forEach(file => {
          const filePath = path.join(uploadsDir, file);
          const stats = fs.statSync(filePath);
          console.log(`  - ${file} (${(stats.size / 1024 / 1024).toFixed(2)} MB, ${stats.mtime.toISOString()})`);
        });
      } else {
        console.log('uploads目录中没有找到Excel或CSV文件');
      }
    } else {
      console.log('uploads目录不存在');
    }

    console.log('\n✅ 备份和检查完成！');
    console.log('\n💡 建议的重新导入流程:');
    console.log('1. 确保有完整的原始数据文件');
    console.log('2. 运行清理脚本清空现有数据');
    console.log('3. 按正确顺序重新导入所有数据');
    console.log('4. 验证导入结果的完整性');

  } catch (error) {
    console.error('❌ 备份检查失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  backupAndPrepareReimport().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = backupAndPrepareReimport;
