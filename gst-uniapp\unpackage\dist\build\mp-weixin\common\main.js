(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/main"],{"100a":function(t,e,n){"use strict";var o=n("656d"),r=n.n(o);r.a},2694:function(t,e,n){"use strict";n.r(e);var o=n("b724");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("100a");var a=n("828b"),c=Object(a["a"])(o["default"],void 0,void 0,!1,null,null,null,!1,void 0,void 0);e["default"]=c.exports},"656d":function(t,e,n){},b724:function(t,e,n){"use strict";n.r(e);var o=n("f9c7"),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=r.a},d6ca:function(t,e,n){"use strict";(function(t,e,o){var r=n("47a9"),a=r(n("7ca3"));n("5788");var c=r(n("2694")),i=r(n("3240")),u=n("daaa"),f=r(n("1e9e")),l=r(n("ef16"));function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}t.__webpack_require_UNI_MP_PLUGIN__=n,i.default.prototype.$http=u.http,i.default.prototype.$store=f.default,i.default.config.productionTip=!1,i.default.use(l.default);var d=e.getUpdateManager();d.onCheckForUpdate((function(t){console.log(t.hasUpdate)})),d.onUpdateReady((function(t){e.showModal({title:"更新提示",content:"新版本已经准备好，是否重启应用？",success:function(t){t.confirm&&d.applyUpdate()}})})),d.onUpdateFailed((function(t){})),c.default.mpType="app";var s=new i.default(function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach((function(e){(0,a.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},c.default));o(s).$mount()}).call(this,n("3223")["default"],n("df3c")["default"],n("df3c")["createApp"])},f9c7:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={globalData:{r_type:12,app_title:"日语云课"},onLaunch:function(){console.log("App Launch"),this.initUserInfo()},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")},methods:{initUserInfo:function(){console.log("App启动 - 初始化用户信息"),this.$store.commit("initUserData")}}};e.default=o}},[["d6ca","common/runtime","common/vendor"]]]);