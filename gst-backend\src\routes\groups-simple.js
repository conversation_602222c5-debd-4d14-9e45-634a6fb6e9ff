const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { StudyGroup, User, GroupMember, Course, GroupCourse } = require('../models');
const { auth, requireTeacher, requireAdmin } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 获取小组列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    status,
    level,
    teacherId,
    page = 1,
    limit = 10,
    search
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = {};

  if (status) whereClause.status = status;
  if (level) whereClause.level = level;
  if (teacherId) whereClause.teacherId = teacherId;
  if (search) {
    whereClause[StudyGroup.sequelize.Op.or] = [
      { name: { [StudyGroup.sequelize.Op.iLike]: `%${search}%` } },
      { description: { [StudyGroup.sequelize.Op.iLike]: `%${search}%` } }
    ];
  }

  const { rows: groups, count } = await StudyGroup.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'teacher',
        attributes: ['id', 'realName', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName']
      }
    ],
    order: [['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.json({
    success: true,
    data: {
      groups,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    }
  });
}));

// 获取小组详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const group = await StudyGroup.findByPk(id, {
    include: [
      {
        model: User,
        as: 'teacher',
        attributes: ['id', 'realName', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName']
      },
      {
        model: GroupMember,
        as: 'members',
        include: [
          {
            model: User,
            attributes: ['id', 'username', 'realName', 'email']
          }
        ]
      }
    ]
  });

  if (!group) {
    return res.status(404).json({
      success: false,
      message: '小组不存在'
    });
  }

  res.json({
    success: true,
    data: { group }
  });
}));

// 创建小组
router.post('/', auth, requireTeacher, [
  body('name').isLength({ min: 2, max: 100 }).withMessage('小组名称长度必须在2-100个字符之间'),
  body('description').optional().isLength({ max: 1000 }).withMessage('描述不能超过1000个字符'),
  body('level').isIn(['N5', 'N4', 'N3', 'N2', 'N1']).withMessage('等级必须是N5-N1之一'),
  body('maxMembers').optional().isInt({ min: 1, max: 100 }).withMessage('最大成员数必须在1-100之间')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  const { name, description, level, maxMembers = 30, teacherId, startDate, endDate } = req.body;

  // 检查小组名称是否已存在
  const existingGroup = await StudyGroup.findOne({ where: { name } });
  if (existingGroup) {
    return res.status(409).json({
      success: false,
      message: '小组名称已存在'
    });
  }

  // 创建小组
  const group = await StudyGroup.create({
    name,
    description,
    level,
    maxMembers,
    teacherId: teacherId || req.user.id,
    startDate,
    endDate,
    createdBy: req.user.id,
    status: 'active'
  });

  logger.info(`小组创建成功: ${group.name} (${group.id}) by ${req.user.username}`);

  res.status(201).json({
    success: true,
    message: '小组创建成功',
    data: { group }
  });
}));

// 加入小组
router.post('/:id/join', auth, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const group = await StudyGroup.findByPk(id);
  if (!group) {
    return res.status(404).json({
      success: false,
      message: '小组不存在'
    });
  }

  if (group.status !== 'active') {
    return res.status(400).json({
      success: false,
      message: '小组当前不可加入'
    });
  }

  if (group.currentMembers >= group.maxMembers) {
    return res.status(400).json({
      success: false,
      message: '小组人数已满'
    });
  }

  // 检查是否已经是成员
  const existingMember = await GroupMember.findOne({
    where: {
      groupId: id,
      userId: req.user.id,
      status: 'active'
    }
  });

  if (existingMember) {
    return res.status(409).json({
      success: false,
      message: '您已经是该小组的成员'
    });
  }

  // 加入小组
  await GroupMember.create({
    groupId: id,
    userId: req.user.id,
    joinDate: new Date(),
    status: 'active'
  });

  // 更新小组成员数
  await group.increment('currentMembers');

  logger.info(`用户加入小组: ${req.user.username} 加入 ${group.name}`);

  res.json({
    success: true,
    message: '成功加入小组',
    data: { group }
  });
}));

// 退出小组
router.post('/:id/leave', auth, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const member = await GroupMember.findOne({
    where: {
      groupId: id,
      userId: req.user.id,
      status: 'active'
    }
  });

  if (!member) {
    return res.status(404).json({
      success: false,
      message: '您不是该小组的成员'
    });
  }

  // 退出小组
  await member.update({ status: 'left' });

  // 更新小组成员数
  const group = await StudyGroup.findByPk(id);
  if (group && group.currentMembers > 0) {
    await group.decrement('currentMembers');
  }

  logger.info(`用户退出小组: ${req.user.username} 退出 ${group.name}`);

  res.json({
    success: true,
    message: '成功退出小组'
  });
}));

// 获取小组成员
router.get('/:id/members', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const members = await GroupMember.findAll({
    where: {
      groupId: id,
      status: 'active'
    },
    include: [
      {
        model: User,
        as: 'User',
        attributes: ['id', 'username', 'realName', 'email', 'role']
      }
    ],
    order: [['joinDate', 'ASC']]
  });

  res.json({
    success: true,
    data: { members }
  });
}));

// 更新小组信息
router.put('/:id', auth, requireTeacher, [
  body('name').optional().isLength({ min: 2, max: 100 }).withMessage('小组名称长度必须在2-100个字符之间'),
  body('description').optional().isLength({ max: 1000 }).withMessage('描述不能超过1000个字符'),
  body('level').optional().isIn(['N5', 'N4', 'N3', 'N2', 'N1']).withMessage('等级必须是N5-N1之一'),
  body('maxMembers').optional().isInt({ min: 1, max: 100 }).withMessage('最大成员数必须在1-100之间'),
  body('status').optional().isIn(['active', 'inactive', 'archived']).withMessage('状态必须是active、inactive或archived')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  const { id } = req.params;
  const updateData = req.body;

  // 检查小组是否存在
  const group = await StudyGroup.findByPk(id);
  if (!group) {
    return res.status(404).json({
      success: false,
      message: '小组不存在'
    });
  }

  // 检查权限：只有小组创建者或管理员可以更新
  if (group.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '没有权限更新此小组'
    });
  }

  // 更新小组信息
  await group.update(updateData);

  // 获取更新后的小组信息
  const updatedGroup = await StudyGroup.findByPk(id, {
    include: [
      {
        model: User,
        as: 'teacher',
        attributes: ['id', 'realName', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName']
      }
    ]
  });

  res.json({
    success: true,
    message: '小组信息更新成功',
    data: { group: updatedGroup }
  });
}));

module.exports = router;
