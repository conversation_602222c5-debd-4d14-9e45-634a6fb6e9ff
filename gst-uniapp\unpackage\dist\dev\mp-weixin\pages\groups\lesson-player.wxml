<view class="lesson-player data-v-1d657ad8"><view class="player-container data-v-1d657ad8"><block wx:if="{{lessonInfo.type==='video'}}"><view class="video-player-wrapper data-v-1d657ad8"><video class="video-player data-v-1d657ad8" id="lessonVideo" src="{{lessonInfo.url}}" poster="{{lessonInfo.poster}}" controls="{{true}}" autoplay="{{false}}" show-center-play-btn="{{true}}" enable-play-gesture="{{true}}" object-fit="contain" data-event-opts="{{[['play',[['onPlay',['$event']]]],['pause',[['onPause',['$event']]]],['ended',[['onEnded',['$event']]]],['timeupdate',[['onTimeUpdate',['$event']]]],['error',[['onError',['$event']]]],['fullscreenchange',[['onFullscreenChange',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" binderror="__e" bindfullscreenchange="__e"></video><block wx:if="{{showOverlay}}"><view class="player-overlay data-v-1d657ad8"><view data-event-opts="{{[['tap',[['togglePlay',['$event']]]]]}}" class="play-button data-v-1d657ad8" bindtap="__e"><text class="play-icon data-v-1d657ad8">{{isPlaying?'⏸':'▶'}}</text></view></view></block></view></block><block wx:else><block wx:if="{{lessonInfo.type==='audio'}}"><view class="audio-player-wrapper data-v-1d657ad8"><view class="audio-cover data-v-1d657ad8"><image src="{{lessonInfo.poster||'/static/imgs/audio-default.png'}}" mode="aspectFit" class="data-v-1d657ad8"></image><view data-event-opts="{{[['tap',[['toggleAudioPlay',['$event']]]]]}}" class="audio-play-btn data-v-1d657ad8" bindtap="__e"><text class="audio-play-icon data-v-1d657ad8">{{isPlaying?'⏸':'▶'}}</text></view></view><view class="audio-controls data-v-1d657ad8"><view class="audio-progress data-v-1d657ad8"><view class="progress-bar data-v-1d657ad8"><view class="progress-fill data-v-1d657ad8" style="{{'width:'+(progressPercent+'%')+';'}}"></view><view class="progress-thumb data-v-1d657ad8" style="{{'left:'+(progressPercent+'%')+';'}}"></view></view><view class="time-display data-v-1d657ad8"><text class="current-time data-v-1d657ad8">{{$root.m0}}</text><text class="total-time data-v-1d657ad8">{{$root.m1}}</text></view></view><view class="audio-actions data-v-1d657ad8"><view data-event-opts="{{[['tap',[['changeSpeed',['$event']]]]]}}" class="speed-control data-v-1d657ad8" bindtap="__e"><text class="speed-text data-v-1d657ad8">{{playbackRate+"x"}}</text></view></view></view></view></block><block wx:else><view class="loading-wrapper data-v-1d657ad8"><view class="loading-spinner data-v-1d657ad8"></view><text class="loading-text data-v-1d657ad8">加载中...</text></view></block></block></view><view class="lesson-info data-v-1d657ad8"><view class="lesson-header data-v-1d657ad8"><view class="lesson-meta data-v-1d657ad8"><text class="lesson-number data-v-1d657ad8">{{lessonInfo.title}}</text></view></view><text class="lesson-title data-v-1d657ad8">{{lessonInfo.course.title}}</text></view><block wx:if="{{showMenuPopup}}"><view data-event-opts="{{[['tap',[['hideMenu',['$event']]]]]}}" class="menu-overlay data-v-1d657ad8" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="menu-popup data-v-1d657ad8" catchtap="__e"><view data-event-opts="{{[['tap',[['adjustSpeed',['$event']]]]]}}" class="menu-item data-v-1d657ad8" bindtap="__e"><text class="menu-icon data-v-1d657ad8">⚡</text><text class="menu-text data-v-1d657ad8">播放速度</text></view><view data-event-opts="{{[['tap',[['adjustQuality',['$event']]]]]}}" class="menu-item data-v-1d657ad8" bindtap="__e"><text class="menu-icon data-v-1d657ad8">🎬</text><text class="menu-text data-v-1d657ad8">画质设置</text></view><view data-event-opts="{{[['tap',[['reportIssue',['$event']]]]]}}" class="menu-item data-v-1d657ad8" bindtap="__e"><text class="menu-icon data-v-1d657ad8">⚠</text><text class="menu-text data-v-1d657ad8">反馈问题</text></view></view></view></block><block wx:if="{{lessonInfo.type==='audio'}}"><audio style="display:none;" id="lessonAudio" src="{{lessonInfo.mediaUrl}}" data-event-opts="{{[['play',[['onAudioPlay',['$event']]]],['pause',[['onAudioPause',['$event']]]],['ended',[['onAudioEnded',['$event']]]],['timeupdate',[['onAudioTimeUpdate',['$event']]]],['error',[['onAudioError',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" binderror="__e" class="data-v-1d657ad8"></audio></block></view>