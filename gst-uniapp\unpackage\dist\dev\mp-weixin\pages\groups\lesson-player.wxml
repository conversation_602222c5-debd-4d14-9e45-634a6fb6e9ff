<view class="lesson-player data-v-1d657ad8"><view class="player-container data-v-1d657ad8"><view class="video-player-wrapper data-v-1d657ad8"><video class="video-player data-v-1d657ad8" id="lessonVideo" src="{{lessonInfo.url}}" poster="{{lessonInfo.poster}}" controls="{{true}}" autoplay="{{false}}" show-center-play-btn="{{true}}" enable-play-gesture="{{true}}" object-fit="contain" data-event-opts="{{[['play',[['onPlay',['$event']]]],['pause',[['onPause',['$event']]]],['ended',[['onEnded',['$event']]]],['timeupdate',[['onTimeUpdate',['$event']]]],['error',[['onError',['$event']]]],['fullscreenchange',[['onFullscreenChange',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" binderror="__e" bindfullscreenchange="__e"></video><block wx:if="{{showOverlay}}"><view class="player-overlay data-v-1d657ad8"><view data-event-opts="{{[['tap',[['togglePlay',['$event']]]]]}}" class="play-button data-v-1d657ad8" bindtap="__e"><text class="play-icon data-v-1d657ad8">{{isPlaying?'⏸':'▶'}}</text></view></view></block></view></view><view class="lesson-info data-v-1d657ad8"><view class="lesson-header data-v-1d657ad8"><view class="lesson-meta data-v-1d657ad8"><text class="lesson-number data-v-1d657ad8">{{lessonInfo.title}}</text></view></view><text class="lesson-title data-v-1d657ad8">{{lessonInfo.course.title}}</text></view><block wx:if="{{showMenuPopup}}"><view data-event-opts="{{[['tap',[['hideMenu',['$event']]]]]}}" class="menu-overlay data-v-1d657ad8" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="menu-popup data-v-1d657ad8" catchtap="__e"><view data-event-opts="{{[['tap',[['adjustSpeed',['$event']]]]]}}" class="menu-item data-v-1d657ad8" bindtap="__e"><text class="menu-icon data-v-1d657ad8">⚡</text><text class="menu-text data-v-1d657ad8">播放速度</text></view><view data-event-opts="{{[['tap',[['adjustQuality',['$event']]]]]}}" class="menu-item data-v-1d657ad8" bindtap="__e"><text class="menu-icon data-v-1d657ad8">🎬</text><text class="menu-text data-v-1d657ad8">画质设置</text></view><view data-event-opts="{{[['tap',[['reportIssue',['$event']]]]]}}" class="menu-item data-v-1d657ad8" bindtap="__e"><text class="menu-icon data-v-1d657ad8">⚠</text><text class="menu-text data-v-1d657ad8">反馈问题</text></view></view></view></block><block wx:if="{{lessonInfo.type==='audio'}}"><audio style="display:none;" id="lessonAudio" src="{{lessonInfo.mediaUrl}}" data-event-opts="{{[['play',[['onAudioPlay',['$event']]]],['pause',[['onAudioPause',['$event']]]],['ended',[['onAudioEnded',['$event']]]],['timeupdate',[['onAudioTimeUpdate',['$event']]]],['error',[['onAudioError',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" binderror="__e" class="data-v-1d657ad8"></audio></block></view>