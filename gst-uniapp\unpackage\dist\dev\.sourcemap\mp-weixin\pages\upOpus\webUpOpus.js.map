{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/upOpus/webUpOpus.vue?81cb", "webpack:///D:/gst/gst-uniapp/pages/upOpus/webUpOpus.vue?dd7f", "webpack:///D:/gst/gst-uniapp/pages/upOpus/webUpOpus.vue?9071", "webpack:///D:/gst/gst-uniapp/pages/upOpus/webUpOpus.vue?f3c7", "uni-app:///pages/upOpus/webUpOpus.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "onLoad", "console", "obj", "data", "methods", "message"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eCU1nB;EACAC;IACAC;IACA;IACAC;IACAD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAE;IACA;MACAD;IACA;EACA;EACAE;IACAC;MACAJ;IACA;EACA;AACA;AAAA,2B", "file": "pages/upOpus/webUpOpus.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/upOpus/webUpOpus.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./webUpOpus.vue?vue&type=template&id=4e5c20dc&\"\nvar renderjs\nimport script from \"./webUpOpus.vue?vue&type=script&lang=js&\"\nexport * from \"./webUpOpus.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/upOpus/webUpOpus.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webUpOpus.vue?vue&type=template&id=4e5c20dc&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webUpOpus.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webUpOpus.vue?vue&type=script&lang=js&\"", "<template>\n\t <!-- headerBG=\"linear-gradient(to right, #0585F3, #04C7ED)\" statusBarBG=\"linear-gradient(to right, #0585F3, #04C7ED)\" -->\n\t<gracePage>\n\t\t<view slot=\"gBody\">\n\t\t\t<web-view :src=\"obj.link\" @message=\"message\"></web-view>\n\t\t</view>\n\t</gracePage>\n</template>\n\n<script>\n\texport default{\n\t\tonLoad(option) {\r\n\t\t\tconsole.log(option)\n\t\t\tlet obj = JSON.parse(decodeURIComponent(option.data));\n\t\t\tobj.link = obj.link + \"?data=\" + encodeURIComponent(obj.datas);\n\t\t\tconsole.log(obj)\n\t\t\t// let obj = {\n\t\t\t// \ttoken:{\"access_token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvdGVzdC5taW5pLmpwd29ybGQuY25cL2FwaVwvYXV0aFwvd3hsb2dpbiIsImlhdCI6MTU5ODQ5MDQwMiwiZXhwIjoxOTU4NDkwNDAyLCJuYmYiOjE1OTg0OTA0MDIsImp0aSI6Iko4YTFQSkNiNE1DU2lhZU0iLCJzdWIiOjY4NTEsInBydiI6Ijg3ZTBhZjFlZjlmZDE1ODEyZmRlYzk3MTUzYTE0ZTBiMDQ3NTQ2YWEifQ.i_x2cuR7kH9Hma2-0VlB6YS8sFi3W8AKpvd1pwrxtbc\",\"token_type\":\"bearer\",\"expires_in\":6000000},\n\t\t\t// \ttypeId: 3,\n\t\t\t// \ttypeTitle: \"唱歌\"\n\t\t\t// }\n\t\t\t// obj.link = \"/hybrid/html/upOpus.html\" + \"?data=\" + encodeURIComponent(JSON.stringify(obj));\n\t\t\tthis.obj = JSON.parse(JSON.stringify(obj));\n\t\t},\n\t\tdata(){\n\t\t\treturn {\n\t\t\t\tobj:{}\n\t\t\t}\n\t\t},\n\t\tmethods:{\n\t\t\tmessage(event){\n\t\t\t        console.log('接收到消息',event.detail.data)\n\t\t\t    }\n\t\t}\n\t}\n</script>\n"], "sourceRoot": ""}