// 全局样式文件

// 变量定义
:root {
  // 主色调
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  // 成功色
  --success-color: #67c23a;
  --success-light: #95d475;
  --success-dark: #529b2e;
  
  // 警告色
  --warning-color: #e6a23c;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  // 危险色
  --danger-color: #f56c6c;
  --danger-light: #f78989;
  --danger-dark: #c45656;
  
  // 信息色
  --info-color: #909399;
  --info-light: #a6a9ad;
  --info-dark: #73767a;
  
  // 文字颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景色
  --bg-color: #ffffff;
  --bg-color-page: #f2f3f5;
  --bg-color-overlay: #ffffff;
  
  // 阴影
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  // 圆角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-round: 20px;
  --border-radius-circle: 100%;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 字体大小
  --font-size-extra-small: 12px;
  --font-size-small: 13px;
  --font-size-base: 14px;
  --font-size-medium: 16px;
  --font-size-large: 18px;
  --font-size-extra-large: 20px;
  
  // 布局
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
}

// 全局重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-color-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: 3px;
  
  &:hover {
    background: var(--border-light);
  }
}

// 页面容器样式
.page-container {
  width: 100%;
  height: 100%;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);

    h1 {
      margin: 0;
      font-size: var(--font-size-large);
      font-weight: 600;
      color: var(--text-primary);
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
}

// 通用工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

// 卡片样式
.card {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

// 页面容器
.page-container {
  padding: var(--spacing-lg);
  min-height: calc(100vh - var(--header-height));
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  
  h1 {
    margin: 0;
    font-size: var(--font-size-large);
    font-weight: 600;
    color: var(--text-primary);
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: var(--bg-color-page);
      color: var(--text-regular);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: var(--bg-color-page);
    }
  }
}

// 表单样式增强
.el-form {
  .el-form-item__label {
    font-weight: 500;
    color: var(--text-regular);
  }
}

// 按钮组样式
.button-group {
  display: flex;
  gap: var(--spacing-sm);
  
  .el-button {
    margin-left: 0;
  }
}

// 状态标签样式
.status-tag {
  &.active { color: var(--success-color); }
  &.inactive { color: var(--info-color); }
  &.pending { color: var(--warning-color); }
  &.error { color: var(--danger-color); }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
