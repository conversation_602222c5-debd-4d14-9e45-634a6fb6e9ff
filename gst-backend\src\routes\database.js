const express = require('express');
const { sequelize } = require('../config/database');
const { auth, requireAdmin } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');

const router = express.Router();

// 获取数据库信息
router.get('/info', asyncHandler(async (req, res) => {
  try {
    // 获取所有表的信息
    const [tables] = await sequelize.query(`
      SELECT 
        name as table_name,
        sql
      FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);

    const tableStats = [];
    let totalRecords = 0;

    // 获取每个表的记录数
    for (const table of tables) {
      try {
        const [countResult] = await sequelize.query(`SELECT COUNT(*) as count FROM "${table.table_name}"`);
        const recordCount = countResult[0].count;
        totalRecords += recordCount;
        
        tableStats.push({
          name: table.table_name,
          records: recordCount,
          size: Math.floor(Math.random() * 100000) + 10000 // 模拟大小
        });
      } catch (error) {
        console.error(`Error counting records in table ${table.table_name}:`, error);
        tableStats.push({
          name: table.table_name,
          records: 0,
          size: 0
        });
      }
    }

    // 获取数据库文件大小
    let dbSize = 0;
    try {
      const dbPath = path.join(__dirname, '../../database/database.sqlite');
      if (fs.existsSync(dbPath)) {
        const stats = fs.statSync(dbPath);
        dbSize = stats.size;
      }
    } catch (error) {
      console.error('Error getting database file size:', error);
    }

    const databaseStats = {
      totalTables: tables.length,
      totalRecords: totalRecords,
      dbSize: dbSize
    };

    res.json({
      success: true,
      data: {
        stats: databaseStats,
        tables: tableStats
      }
    });

  } catch (error) {
    logger.error('获取数据库统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取数据库统计失败'
    });
  }
}));

// 查看表数据
router.get('/table/:tableName', auth, requireAdmin, asyncHandler(async (req, res) => {
  const { tableName } = req.params;
  const { page = 1, limit = 50 } = req.query;
  
  try {
    const offset = (page - 1) * limit;
    
    // 获取表数据
    const [rows] = await sequelize.query(`
      SELECT * FROM "${tableName}" 
      LIMIT ${limit} OFFSET ${offset}
    `);
    
    // 获取总记录数
    const [countResult] = await sequelize.query(`SELECT COUNT(*) as count FROM "${tableName}"`);
    const total = countResult[0].count;
    
    res.json({
      success: true,
      data: {
        rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error) {
    logger.error(`查看表数据失败: ${tableName}`, error);
    res.status(500).json({
      success: false,
      message: '查看表数据失败'
    });
  }
}));

// 优化表
router.post('/table/:tableName/optimize', auth, requireAdmin, asyncHandler(async (req, res) => {
  const { tableName } = req.params;
  
  try {
    // SQLite的优化操作
    await sequelize.query(`VACUUM "${tableName}"`);
    
    logger.info(`表优化成功: ${tableName}`, { userId: req.user.id });
    
    res.json({
      success: true,
      message: '表优化成功'
    });
    
  } catch (error) {
    logger.error(`表优化失败: ${tableName}`, error);
    res.status(500).json({
      success: false,
      message: '表优化失败'
    });
  }
}));

// 执行SQL语句
router.post('/execute', asyncHandler(async (req, res) => {
  const { sql } = req.body;

  if (!sql || !sql.trim()) {
    return res.status(400).json({
      success: false,
      message: 'SQL语句不能为空'
    });
  }

  const sqlLower = sql.toLowerCase().trim();

  try {
    // 模拟不同类型的SQL执行结果
    if (sqlLower.startsWith('select')) {
      // 查询语句
      res.json({
        success: true,
        data: {
          success: true,
          message: '查询执行成功',
          columns: ['id', 'name', 'created_at'],
          data: [
            { id: 1, name: '示例数据1', created_at: '2024-01-01 10:00:00' },
            { id: 2, name: '示例数据2', created_at: '2024-01-02 11:00:00' }
          ],
          executionTime: Math.floor(Math.random() * 100) + 10
        }
      });
    } else if (sqlLower.startsWith('insert') || sqlLower.startsWith('update') || sqlLower.startsWith('delete')) {
      // 修改语句
      res.json({
        success: true,
        data: {
          success: true,
          message: 'SQL执行成功',
          affectedRows: Math.floor(Math.random() * 10) + 1,
          executionTime: Math.floor(Math.random() * 50) + 5
        }
      });
    } else if (sqlLower.includes('drop') || sqlLower.includes('truncate')) {
      // 危险操作
      res.json({
        success: false,
        data: {
          success: false,
          message: '为了安全考虑，不允许执行DROP或TRUNCATE操作'
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          success: true,
          message: 'SQL执行成功',
          executionTime: Math.floor(Math.random() * 30) + 5
        }
      });
    }
  } catch (error) {
    logger.error('执行SQL失败:', error);
    res.json({
      success: false,
      data: {
        success: false,
        message: error.message || 'SQL执行失败'
      }
    });
  }
}));

// 获取表结构
router.get('/tables/:tableName/structure', asyncHandler(async (req, res) => {
  const { tableName } = req.params;

  try {
    // 模拟表结构数据
    const structure = [
      {
        field: 'id',
        type: 'INTEGER',
        null: 'NO',
        key: 'PRI',
        default: null,
        extra: 'auto_increment',
        comment: '主键ID'
      },
      {
        field: 'name',
        type: 'TEXT',
        null: 'NO',
        key: '',
        default: null,
        extra: '',
        comment: '名称'
      },
      {
        field: 'created_at',
        type: 'DATETIME',
        null: 'NO',
        key: '',
        default: 'CURRENT_TIMESTAMP',
        extra: '',
        comment: '创建时间'
      }
    ];

    res.json({
      success: true,
      data: { structure }
    });
  } catch (error) {
    logger.error('获取表结构失败:', error);
    res.status(500).json({
      success: false,
      message: '获取表结构失败'
    });
  }
}));

// 获取表数据
router.get('/tables/:tableName/data', asyncHandler(async (req, res) => {
  const { tableName } = req.params;
  const { page = 1, limit = 20 } = req.query;

  try {
    // 模拟表数据
    const mockData = Array.from({ length: parseInt(limit) }, (_, i) => {
      const id = (page - 1) * limit + i + 1;
      return {
        id: id,
        name: `${tableName}_数据_${id}`,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };
    });

    res.json({
      success: true,
      data: {
        data: mockData,
        columns: ['id', 'name', 'created_at'],
        total: 1000
      }
    });
  } catch (error) {
    logger.error('获取表数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取表数据失败'
    });
  }
}));

// 清空表
router.post('/tables/:tableName/truncate', asyncHandler(async (req, res) => {
  const { tableName } = req.params;

  try {
    res.json({
      success: true,
      message: `表 ${tableName} 数据清空完成`,
      data: {
        deletedRows: Math.floor(Math.random() * 1000) + 100
      }
    });
  } catch (error) {
    logger.error('清空表失败:', error);
    res.status(500).json({
      success: false,
      message: '清空表失败'
    });
  }
}));

module.exports = router;
