﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>备份管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="createBackup" :loading="creating">
          <el-icon><FolderAdd /></el-icon>
          创建备份
        </el-button>
      </div>
    </div>

    <!-- 备份统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总备份数" :value="stats.totalBackups" />
          <div class="stats-extra">
            <el-icon><Files /></el-icon>
            <span>个备份文件</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总大小" :value="stats.totalSize" suffix="GB" />
          <div class="stats-extra">
            <el-icon><Coin /></el-icon>
            <span>存储空间</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="最近备份" :value="stats.lastBackupDays" suffix="天前" />
          <div class="stats-extra">
            <el-icon><Clock /></el-icon>
            <span>上次备份</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="自动备份" :value="stats.autoBackupEnabled ? '已启用' : '已禁用'" />
          <div class="stats-extra">
            <el-icon><Setting /></el-icon>
            <span>自动备份状态</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 备份配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>备份配置</span>
        </div>
      </template>
      <el-form :model="backupConfig" inline>
        <el-form-item label="自动备份">
          <el-switch v-model="backupConfig.autoBackup" @change="updateConfig" />
        </el-form-item>
        <el-form-item label="备份频率">
          <el-select v-model="backupConfig.frequency" @change="updateConfig">
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>
        <el-form-item label="保留天数">
          <el-input-number v-model="backupConfig.retentionDays" :min="1" :max="365" @change="updateConfig" />
        </el-form-item>
        <el-form-item label="备份类型">
          <el-checkbox-group v-model="backupConfig.types" @change="updateConfig">
            <el-checkbox label="database">数据库</el-checkbox>
            <el-checkbox label="files">文件</el-checkbox>
            <el-checkbox label="config">配置</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 备份列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>备份列表</span>
        </div>
      </template>

      <el-table :data="backups" v-loading="loading" stripe>
        <el-table-column prop="name" label="备份名称" min-width="200" />
        <el-table-column prop="type" label="备份类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="文件大小" width="120">
          <template #default="{ row }">
            {{ formatSize(row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="downloadBackup(row)" :disabled="row.status !== 'completed'">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button link size="small" @click="restoreBackup(row)" :disabled="row.status !== 'completed'">
              <el-icon><RefreshLeft /></el-icon>
              恢复
            </el-button>
            <el-button link size="small" @click="deleteBackup(row)" class="danger-button">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadBackups"
          @current-change="loadBackups"
        />
      </div>
    </el-card>

    <!-- 创建备份对话框 -->
    <el-dialog
      title="创建备份"
      v-model="showCreateDialog"
      width="500px"
    >
      <el-form ref="backupFormRef" :model="backupForm" :rules="backupFormRules" label-width="100px">
        <el-form-item label="备份名称" prop="name">
          <el-input v-model="backupForm.name" placeholder="请输入备份名称" />
        </el-form-item>
        <el-form-item label="备份类型" prop="types">
          <el-checkbox-group v-model="backupForm.types">
            <el-checkbox label="database">数据库</el-checkbox>
            <el-checkbox label="files">文件</el-checkbox>
            <el-checkbox label="config">配置</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备份描述" prop="description">
          <el-input
            v-model="backupForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入备份描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmCreateBackup" :loading="creating">
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get, post, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  FolderAdd,
  Files,
  Coin,
  Clock,
  Setting,
  Download,
  RefreshLeft,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)
const backups = ref([])
const total = ref(0)

// 统计数据
const stats = reactive({
  totalBackups: 0,
  totalSize: 0,
  lastBackupDays: 0,
  autoBackupEnabled: false
})

// 备份配置
const backupConfig = reactive({
  autoBackup: false,
  frequency: 'daily',
  retentionDays: 30,
  types: ['database', 'files']
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 备份表单
const backupForm = reactive({
  name: '',
  types: ['database'],
  description: ''
})

const backupFormRules = {
  name: [
    { required: true, message: '请输入备份名称', trigger: 'blur' }
  ],
  types: [
    { required: true, message: '请选择备份类型', trigger: 'change' }
  ]
}

// 加载备份列表
const loadBackups = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size
    }

    const response = await get('/api/backups', params)
    if (response.success) {
      backups.value = response.data.backups || []
      total.value = response.data.total || 0
      Object.assign(stats, response.data.stats || {})
    }
  } catch (error) {
    console.error('加载备份列表失败:', error)
    ElMessage.error('加载备份列表失败')

    // 使用模拟数据
    generateMockData()
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  backups.value = [
    {
      id: 1,
      name: '自动备份-20241201',
      type: 'full',
      size: 1024 * 1024 * 500, // 500MB
      status: 'completed',
      createdAt: new Date(),
      description: '每日自动备份'
    },
    {
      id: 2,
      name: '手动备份-数据库',
      type: 'database',
      size: 1024 * 1024 * 200, // 200MB
      status: 'completed',
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      description: '数据库手动备份'
    }
  ]

  Object.assign(stats, {
    totalBackups: 15,
    totalSize: 2.5,
    lastBackupDays: 1,
    autoBackupEnabled: true
  })
}

// 创建备份
const createBackup = () => {
  showCreateDialog.value = true
}

// 确认创建备份
const confirmCreateBackup = async () => {
  const backupFormRef = ref()
  try {
    await backupFormRef.value.validate()
  } catch (error) {
    return
  }

  creating.value = true
  try {
    await post('/api/backups', backupForm)
    ElMessage.success('备份创建成功')
    showCreateDialog.value = false
    resetForm()
    await loadBackups()
  } catch (error) {
    console.error('创建备份失败:', error)
    ElMessage.error('创建备份失败')
  } finally {
    creating.value = false
  }
}

// 下载备份
const downloadBackup = (backup) => {
  const link = document.createElement('a')
  link.href = `/api/backups/${backup.id}/download`
  link.download = backup.name
  link.click()
  ElMessage.success('备份下载开始')
}

// 恢复备份
const restoreBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(`确定要恢复备份"${backup.name}"吗？此操作将覆盖当前数据！`, '确认恢复', {
      type: 'warning'
    })

    await post(`/api/backups/${backup.id}/restore`)
    ElMessage.success('备份恢复成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复备份失败:', error)
      ElMessage.error('恢复备份失败')
    }
  }
}

// 删除备份
const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(`确定要删除备份"${backup.name}"吗？`, '确认删除', {
      type: 'warning'
    })

    await del(`/api/backups/${backup.id}`)
    ElMessage.success('备份删除成功')
    await loadBackups()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除备份失败')
    }
  }
}

// 更新配置
const updateConfig = async () => {
  try {
    await put('/api/backups/config', backupConfig)
    ElMessage.success('配置更新成功')
  } catch (error) {
    console.error('更新配置失败:', error)
    ElMessage.error('更新配置失败')
  }
}

// 工具函数
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getTypeColor = (type) => {
  const colors = {
    full: 'primary',
    database: 'success',
    files: 'warning',
    config: 'info'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    full: '完整备份',
    database: '数据库',
    files: '文件',
    config: '配置'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '等待中',
    running: '进行中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

const resetForm = () => {
  Object.assign(backupForm, {
    name: '',
    types: ['database'],
    description: ''
  })
}

const refreshData = () => {
  loadBackups()
}

// 组件挂载时加载数据
onMounted(() => {
  loadBackups()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  .stats-extra {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
  }
}

.config-card {
  margin-bottom: 20px;

  .card-header {
    font-size: 16px;
    font-weight: 600;
  }
}

.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}
</style>
