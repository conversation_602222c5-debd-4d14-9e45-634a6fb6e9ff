(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/groups/index"],{

/***/ 476:
/*!*******************************************************************!*\
  !*** D:/gst/gst-uniapp/main.js?{"page":"pages%2Fgroups%2Findex"} ***!
  \*******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/groups/index.vue */ 477));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 477:
/*!************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/index.vue ***!
  \************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=6e8c2b60&scoped=true& */ 478);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 480);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css& */ 482);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "6e8c2b60",
  null,
  false,
  _index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/groups/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 478:
/*!*******************************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/index.vue?vue&type=template&id=6e8c2b60&scoped=true& ***!
  \*******************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6e8c2b60&scoped=true& */ 479);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_6e8c2b60_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 479:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/groups/index.vue?vue&type=template&id=6e8c2b60&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var render = function () {}
var staticRenderFns = []
var recyclableRender
var components



/***/ }),

/***/ 480:
/*!*************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/index.vue?vue&type=script&lang=js& ***!
  \*************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 481);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 481:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/groups/index.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 34));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 36));
var CustomTabbar = function CustomTabbar() {
  __webpack_require__.e(/*! require.ensure | components/custom-tabbar */ "components/custom-tabbar").then((function () {
    return resolve(__webpack_require__(/*! @/components/custom-tabbar.vue */ 499));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    CustomTabbar: CustomTabbar
  },
  data: function data() {
    return {
      pageLoading: false,
      hasGroupPermission: false,
      selectedGroupId: 'concept',
      selectedGroup: null,
      selectedGroupIndex: 0,
      groupsLoading: false,
      // 吸顶列表相关数据
      activeNavIndex: 0,
      stickyIndex: 0,
      scrollTop: 0,
      // 新概念教程数据
      conceptTutorial: {
        id: 'concept',
        title: '新标日本语教程',
        description: '适合所有学员的基础教程',
        totalLessons: 30,
        completedLessons: 8
      },
      // 新概念教程的分类列表数据
      conceptCategoryList: [{
        id: 'speaking',
        name: '口语',
        icon: '🗣️',
        expanded: true,
        // 默认展开第一个
        lessons: [{
          id: 1,
          title: '第一课',
          subtitle: '基础问候语',
          completed: true
        }, {
          id: 2,
          title: '第二课',
          subtitle: '自我介绍',
          completed: true
        }, {
          id: 3,
          title: '第三课',
          subtitle: '日常对话',
          completed: false
        }, {
          id: 4,
          title: '第四课',
          subtitle: '购物用语',
          completed: false
        }, {
          id: 5,
          title: '第五课',
          subtitle: '餐厅用语',
          completed: false
        }]
      }, {
        id: 'grammar',
        name: '语法',
        icon: '📝',
        expanded: false,
        lessons: [{
          id: 6,
          title: '第一课',
          subtitle: '基本句型',
          completed: true
        }, {
          id: 7,
          title: '第二课',
          subtitle: '动词变位',
          completed: false
        }, {
          id: 8,
          title: '第三课',
          subtitle: '形容词活用',
          completed: false
        }, {
          id: 9,
          title: '第四课',
          subtitle: '助词用法',
          completed: false
        }, {
          id: 10,
          title: '第五课',
          subtitle: '敬语表达',
          completed: false
        }]
      }, {
        id: 'vocabulary',
        name: '词汇',
        icon: '📚',
        expanded: false,
        lessons: [{
          id: 11,
          title: '第一课',
          subtitle: '基础词汇',
          completed: true
        }, {
          id: 12,
          title: '第二课',
          subtitle: '生活词汇',
          completed: false
        }, {
          id: 13,
          title: '第三课',
          subtitle: '工作词汇',
          completed: false
        }, {
          id: 14,
          title: '第四课',
          subtitle: '学习词汇',
          completed: false
        }, {
          id: 15,
          title: '第五课',
          subtitle: '旅游词汇',
          completed: false
        }]
      }, {
        id: 'listening',
        name: '听力',
        icon: '🎧',
        expanded: false,
        lessons: [{
          id: 16,
          title: '第一课',
          subtitle: '基础听力',
          completed: false
        }, {
          id: 17,
          title: '第二课',
          subtitle: '对话听力',
          completed: false
        }, {
          id: 18,
          title: '第三课',
          subtitle: '新闻听力',
          completed: false
        }, {
          id: 19,
          title: '第四课',
          subtitle: '故事听力',
          completed: false
        }, {
          id: 20,
          title: '第五课',
          subtitle: '综合听力',
          completed: false
        }]
      }, {
        id: 'reading',
        name: '阅读',
        icon: '📖',
        expanded: false,
        lessons: [{
          id: 21,
          title: '第一课',
          subtitle: '短文阅读',
          completed: false
        }, {
          id: 22,
          title: '第二课',
          subtitle: '新闻阅读',
          completed: false
        }, {
          id: 23,
          title: '第三课',
          subtitle: '小说阅读',
          completed: false
        }, {
          id: 24,
          title: '第四课',
          subtitle: '说明文阅读',
          completed: false
        }, {
          id: 25,
          title: '第五课',
          subtitle: '综合阅读',
          completed: false
        }]
      }, {
        id: 'writing',
        name: '写作',
        icon: '✍️',
        expanded: false,
        lessons: [{
          id: 26,
          title: '第一课',
          subtitle: '基础写作',
          completed: false
        }, {
          id: 27,
          title: '第二课',
          subtitle: '日记写作',
          completed: false
        }, {
          id: 28,
          title: '第三课',
          subtitle: '书信写作',
          completed: false
        }, {
          id: 29,
          title: '第四课',
          subtitle: '作文写作',
          completed: false
        }, {
          id: 30,
          title: '第五课',
          subtitle: '应用写作',
          completed: false
        }]
      }],
      // 小组专用的分类列表数据
      groupCategoryList: [{
        id: 'n5-basic',
        name: 'N5基础',
        icon: '🌱',
        expanded: true,
        lessons: [{
          id: 101,
          title: '第一课',
          subtitle: '五十音图（あ行）',
          completed: true
        }, {
          id: 102,
          title: '第二课',
          subtitle: '五十音图（か行）',
          completed: true
        }, {
          id: 103,
          title: '第三课',
          subtitle: '五十音图（さ行）',
          completed: false
        }, {
          id: 104,
          title: '第四课',
          subtitle: '五十音图（た行）',
          completed: false
        }, {
          id: 105,
          title: '第五课',
          subtitle: '五十音图（な行）',
          completed: false
        }]
      }, {
        id: 'n5-grammar',
        name: 'N5语法',
        icon: '📖',
        expanded: false,
        lessons: [{
          id: 106,
          title: '第一课',
          subtitle: 'です/である',
          completed: true
        }, {
          id: 107,
          title: '第二课',
          subtitle: '名词+は+名词',
          completed: false
        }, {
          id: 108,
          title: '第三课',
          subtitle: '疑问词',
          completed: false
        }, {
          id: 109,
          title: '第四课',
          subtitle: '数字和时间',
          completed: false
        }, {
          id: 110,
          title: '第五课',
          subtitle: '动词现在时',
          completed: false
        }]
      }, {
        id: 'n5-vocabulary',
        name: 'N5词汇',
        icon: '📚',
        expanded: false,
        lessons: [{
          id: 111,
          title: '第一课',
          subtitle: '家族称呼',
          completed: true
        }, {
          id: 112,
          title: '第二课',
          subtitle: '职业名称',
          completed: false
        }, {
          id: 113,
          title: '第三课',
          subtitle: '日常用品',
          completed: false
        }, {
          id: 114,
          title: '第四课',
          subtitle: '食物饮料',
          completed: false
        }, {
          id: 115,
          title: '第五课',
          subtitle: '颜色形状',
          completed: false
        }]
      }, {
        id: 'n5-conversation',
        name: 'N5会话',
        icon: '💬',
        expanded: false,
        lessons: [{
          id: 116,
          title: '第一课',
          subtitle: '初次见面',
          completed: false
        }, {
          id: 117,
          title: '第二课',
          subtitle: '日常问候',
          completed: false
        }, {
          id: 118,
          title: '第三课',
          subtitle: '购物对话',
          completed: false
        }, {
          id: 119,
          title: '第四课',
          subtitle: '餐厅点餐',
          completed: false
        }, {
          id: 120,
          title: '第五课',
          subtitle: '问路指路',
          completed: false
        }]
      }],
      // 当前显示的分类列表（动态切换）
      currentCategoryList: [],
      currentView: 'review',
      // 'review' 或 'practice'
      selectedDate: '',
      currentPracticeType: 'all',
      groupList: [],
      // 练习类型
      practiceTypes: [{
        id: 'listening',
        name: '听力练习',
        icon: '🎧',
        count: 25
      }, {
        id: 'grammar',
        name: '语法练习',
        icon: '📝',
        count: 30
      }, {
        id: 'vocabulary',
        name: '词汇练习',
        icon: '📚',
        count: 40
      }, {
        id: 'speaking',
        name: '口语练习',
        icon: '🗣️',
        count: 15
      }],
      // 课程回顾数据
      reviewCourses: [{
        id: 1,
        title: '第一课：基础发音练习',
        date: '2024-01-15',
        teacher: '田中老师',
        duration: '45:30',
        thumbnail: '/static/imgs/course-thumb1.png',
        groupId: 1
      }, {
        id: 2,
        title: '第二课：日常问候语',
        date: '2024-01-16',
        teacher: '佐藤老师',
        duration: '38:20',
        thumbnail: '/static/imgs/course-thumb2.png',
        groupId: 1
      }, {
        id: 3,
        title: '第三课：数字与时间',
        date: '2024-01-17',
        teacher: '山田老师',
        duration: '42:15',
        thumbnail: '/static/imgs/course-thumb3.png',
        groupId: 2
      }, {
        id: 4,
        title: '第四课：家族称呼',
        date: '2024-01-18',
        teacher: '田中老师',
        duration: '39:45',
        thumbnail: '/static/imgs/course-thumb4.png',
        groupId: 2
      }],
      // 练习数据
      practices: [{
        id: 1,
        title: '五十音图听力练习',
        date: '2024-01-15',
        type: '听力练习',
        questionCount: 20,
        duration: 15,
        status: 'completed',
        groupId: 1
      }, {
        id: 2,
        title: '基础语法选择题',
        date: '2024-01-16',
        type: '语法练习',
        questionCount: 25,
        duration: 20,
        status: 'in-progress',
        groupId: 1
      }],
      ver: this.$store.state.ver,
      second: 0
    };
  },
  computed: {
    // 总成员数
    totalMembers: function totalMembers() {
      return this.groupList.reduce(function (total, group) {
        return total + group.num;
      }, 0);
    },
    // 过滤后的课程回顾
    filteredReviewCourses: function filteredReviewCourses() {
      var _this = this;
      var courses = this.reviewCourses.filter(function (course) {
        return !_this.selectedGroup || course.groupId === _this.selectedGroup.id;
      });
      if (this.selectedDate) {
        courses = courses.filter(function (course) {
          return course.date === _this.selectedDate;
        });
      }
      return courses.sort(function (a, b) {
        return new Date(b.date) - new Date(a.date);
      });
    },
    // 过滤后的练习
    filteredPractices: function filteredPractices() {
      var _this2 = this;
      var practices = this.practices.filter(function (practice) {
        return !_this2.selectedGroup || practice.groupId === _this2.selectedGroup.id;
      });
      if (this.currentPracticeType !== 'all') {
        practices = practices.filter(function (practice) {
          var _this2$practiceTypes$;
          return practice.type === ((_this2$practiceTypes$ = _this2.practiceTypes.find(function (t) {
            return t.id === _this2.currentPracticeType;
          })) === null || _this2$practiceTypes$ === void 0 ? void 0 : _this2$practiceTypes$.name);
        });
      }
      return practices.sort(function (a, b) {
        return new Date(b.date) - new Date(a.date);
      });
    }
  },
  onLoad: function onLoad() {
    var _this3 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _this3.checkGroupAccess();
              _context.next = 3;
              return _this3.initializeData();
            case 3:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  onShow: function onShow() {
    var _this4 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
      return _regenerator.default.wrap(function _callee2$(_context2) {
        while (1) {
          switch (_context2.prev = _context2.next) {
            case 0:
              if (!_this4.$store.state.user.token) {} else {
                _this4.$store.dispatch('refreshUserMember');
              }
              // 每次显示页面时都检查权限，确保权限状态是最新的
              // 但首先确保用户数据已经从本地存储恢复
              _this4.$store.commit('initUserData');
              _this4.checkGroupAccess();
              // 刷新静态数据
              if (_this4.hasGroupPermission) {
                if (_this4.second == 0) {
                  _this4.initializeData();
                  _this4.second++;
                } else {
                  _this4.loadStaticGroupData();
                }
              }
            case 4:
            case "end":
              return _context2.stop();
          }
        }
      }, _callee2);
    }))();
  },
  methods: {
    // 滚动到指定分类
    scrollToCategory: function scrollToCategory(categoryIndex) {
      var _this5 = this;
      this.activeNavIndex = categoryIndex;

      // 计算目标位置
      var query = uni.createSelectorQuery().in(this);
      query.select("#category-".concat(categoryIndex)).boundingClientRect(function (data) {
        if (data) {
          _this5.scrollTop = data.top - 100; // 减去导航栏高度
        }
      }).exec();
    },
    // 监听内容滚动
    onContentScroll: function onContentScroll(e) {
      var scrollTop = e.detail.scrollTop;

      // 根据滚动位置更新活跃的导航项和吸顶状态
      this.updateActiveNav(scrollTop);
    },
    // 更新活跃的导航项
    updateActiveNav: function updateActiveNav(scrollTop) {
      // 根据滚动位置计算当前应该高亮的导航项
      // 简化实现：每200px切换一个分类
      var newIndex = Math.floor(scrollTop / 200);
      if (newIndex !== this.activeNavIndex && newIndex < this.currentCategoryList.length) {
        this.activeNavIndex = newIndex;
        this.stickyIndex = newIndex;
      }
    },
    // 进入课程学习
    enterLesson: function enterLesson(category, lesson) {
      console.log('进入课程:', category.title, lesson.title);

      // 跳转到美化的播放页面
      uni.navigateTo({
        url: "/pages/groups/lesson-player?lessonId=".concat(lesson.id, "&categoryId=").concat(category.id, "&categoryName=").concat(encodeURIComponent(category.title))
      });
    },
    // 检查用户权限
    checkPermission: function checkPermission() {
      console.log('=== 开始检查小组权限 ===');

      // 检查用户是否登录
      var userToken = this.$store.state.user.token;
      var userInfo = this.$store.state.user.userInfo;
      var hasLogin = this.$store.getters.hasLogin;
      console.log('权限检查数据:', {
        userToken: userToken ? '存在' : '不存在',
        userInfo: userInfo,
        hasLogin: hasLogin
      });
      if (!userToken && !hasLogin) {
        console.log('用户未登录，拒绝访问');
        this.hasGroupPermission = false;
        return;
      }

      // 检查用户是否有小组权限
      this.checkGroupAccess();
    },
    // 检查小组访问权限 - 检查会员状态
    checkGroupAccess: function checkGroupAccess() {
      var userInfo = this.$store.state.user.userInfo;
      var userMember = this.$store.state.user.member;
      var userToken = this.$store.state.user.token;
      var hasLogin = this.$store.getters.hasLogin;
      var isLoggedIn = this.$store.getters.isLoggedIn;
      console.log('=== 检查小组访问权限 ===');
      console.log('用户Token:', userToken ? '存在' : '不存在');
      console.log('用户信息:', userInfo);
      console.log('会员信息:', userMember);
      console.log('登录状态:', {
        hasLogin: hasLogin,
        isLoggedIn: isLoggedIn
      });

      // 从本地存储再次确认数据
      var localToken = uni.getStorageSync('token');
      var localUserInfo = uni.getStorageSync('userInfo');
      var localMember = uni.getStorageSync('userMember');
      console.log('本地存储数据:', {
        hasLocalToken: !!localToken,
        hasLocalUserInfo: !!localUserInfo,
        hasLocalMember: !!localMember
      });

      // 检查用户是否登录
      var userLoggedIn = hasLogin || isLoggedIn || userInfo && userInfo.id || userToken;
      if (!userLoggedIn) {
        console.log('❌ 用户未登录，无权限访问小组');
        this.hasGroupPermission = false;
        this.showLoginTip();
        return;
      }

      // 检查用户是否有会员权限（优先使用内存中的数据，如果没有则使用本地存储）
      var memberToCheck = userMember || localMember;
      var hasMemberPermission = this.checkMemberPermission(memberToCheck);
      console.log('会员权限检查:', {
        memberToCheck: memberToCheck,
        hasMemberPermission: hasMemberPermission
      });
      if (hasMemberPermission) {
        console.log('✅ 用户有会员权限，可以访问小组');
        this.hasGroupPermission = true;

        // 如果内存中没有会员信息但本地有，则同步到内存
        if (!userMember && localMember) {
          console.log('同步本地会员信息到内存');
          this.$store.commit('setUserMember', localMember);
        }
      } else {
        console.log('❌ 用户没有会员权限，无法访问小组');
        this.hasGroupPermission = false;
        this.showMemberTip();
      }
      console.log('=== 权限检查完成，结果:', this.hasGroupPermission, '===');
    },
    // 检查会员权限
    checkMemberPermission: function checkMemberPermission(memberInfo) {
      // 特殊用户权限（用于测试）
      var userInfo = this.$store.state.user.userInfo;
      if (userInfo && (userInfo.id === 576 || userInfo.name === '彭伟')) {
        console.log('✅ 特殊用户权限，允许访问');
        return true;
      }
      if (!memberInfo) {
        console.log('没有会员信息');
        return false;
      }

      // 检查会员是否有效
      var now = new Date();
      var endDate = new Date(memberInfo.end_date);
      console.log('会员有效期检查:', {
        now: now.toISOString(),
        endDate: endDate.toISOString(),
        isValid: endDate > now
      });

      // 如果会员还在有效期内，则有权限
      return endDate > now;
    },
    // 显示登录提示
    showLoginTip: function showLoginTip() {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
    },
    // 显示会员提示
    showMemberTip: function showMemberTip() {
      var userMember = this.$store.state.user.member;
      var message = '需要会员权限才能访问学习小组';
      if (userMember) {
        var endDate = new Date(userMember.end_date);
        var now = new Date();
        if (endDate < now) {
          message = '您的会员已过期，请续费后访问学习小组';
        }
      }
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
    },
    // 初始化数据 - 使用静态数据
    initializeData: function initializeData() {
      if (this.hasGroupPermission) {
        // 使用静态数据，不需要API调用
        this.loadStaticGroupData();
        // 默认选择新概念教程（会自动加载对应的分类数据）
        this.selectConceptTutorial();
      }
    },
    // 加载静态小组数据
    loadStaticGroupData: function loadStaticGroupData() {
      var _this6 = this;
      console.log('📋 加载静态小组数据...');

      // 静态小组数据
      // this.groupList = [
      // 	{
      // 		id: 1,
      // 		name: 'N5基础班',
      // 		description: '适合零基础学员',
      // 		level: 'N5',
      // 		icon: '/static/imgs/group-n5.png',
      // 		memberCount: 28,
      // 		courseCount: 12,
      // 		progress: 65,
      // 		status: 'active'
      // 	},
      // 	{
      // 		id: 2,
      // 		name: 'N4进阶班',
      // 		description: '有一定基础的学员',
      // 		level: 'N4',
      // 		icon: '/static/imgs/group-n4.png',
      // 		memberCount: 22,
      // 		courseCount: 15,
      // 		progress: 45,
      // 		status: 'active'
      // 	},
      // 	{
      // 		id: 3,
      // 		name: 'N3提高班',
      // 		description: '中级水平学员',
      // 		level: 'N3',
      // 		icon: '/static/imgs/group-n3.png',
      // 		memberCount: 18,
      // 		courseCount: 18,
      // 		progress: 30,
      // 		status: 'active'
      // 	},
      // 	{
      // 		id: 4,
      // 		name: '商务日语班',
      // 		description: '职场日语专项训练',
      // 		level: '商务',
      // 		icon: '/static/imgs/group-business.png',
      // 		memberCount: 15,
      // 		courseCount: 10,
      // 		progress: 20,
      // 		status: 'active'
      // 	}
      // ];

      // console.log('✅ 静态小组数据加载完成，共', this.groupList.length, '个小组');
      this.$http.get("v1/course/getGroups").then(function (res) {
        if (res.data.code == 0) {
          _this6.groupList = res.data.data;
        }
      });
    },
    // 模拟新概念教程API接口
    loadConceptTutorialData: function loadConceptTutorialData() {
      var _this7 = this;
      // console.log('📡 模拟调用新概念教程API...');

      // // 模拟API延迟
      // await new Promise(resolve => setTimeout(resolve, 500));

      // // 返回新概念教程的分类数据
      // return {
      // 	success: true,
      // 	data: {
      // 		categories: this.conceptCategoryList,
      // 		totalLessons: this.conceptCategoryList.reduce((total, cat) => total + cat.lessons.length, 0),
      // 		completedLessons: this.conceptCategoryList.reduce((total, cat) =>
      // 			total + cat.lessons.filter(lesson => lesson.completed).length, 0
      // 		)
      // 	}
      // };
      this.$http.get("v1/course/getGroupsCourse").then(function (res) {
        if (res.data.code == 0) {
          _this7.conceptCategoryList = res.data.data.class;
          _this7.currentCategoryList = res.data.data.class;
          _this7.currentCategoryList.forEach(function (item) {
            item.expanded = false; // 添加新字段及其值
          });
        }
      });
    },
    // 模拟小组课程API接口
    loadGroupCourseData: function loadGroupCourseData(groupId) {
      var _this8 = this;
      console.log('📡 模拟调用小组课程API，小组ID:', groupId);

      // // 模拟API延迟
      // await new Promise(resolve => setTimeout(resolve, 500));

      // // 根据不同小组返回不同的课程数据
      // let categoryData = JSON.parse(JSON.stringify(this.groupCategoryList)); // 深拷贝

      // // 根据小组级别调整课程内容
      // if (groupId === 2) { // N4进阶班
      // 	categoryData = categoryData.map(cat => ({
      // 		...cat,
      // 		name: cat.name.replace('N5', 'N4'),
      // 		lessons: cat.lessons.map(lesson => ({
      // 			...lesson,
      // 			subtitle: lesson.subtitle + '（进阶）'
      // 		}))
      // 	}));
      // } else if (groupId === 3) { // N3提高班
      // 	categoryData = categoryData.map(cat => ({
      // 		...cat,
      // 		name: cat.name.replace('N5', 'N3'),
      // 		lessons: cat.lessons.map(lesson => ({
      // 			...lesson,
      // 			subtitle: lesson.subtitle + '（提高）'
      // 		}))
      // 	}));
      // }

      // return {
      // 	success: true,
      // 	data: {
      // 		categories: categoryData,
      // 		groupInfo: this.groupList.find(g => g.id === groupId),
      // 		totalLessons: categoryData.reduce((total, cat) => total + cat.lessons.length, 0),
      // 		completedLessons: categoryData.reduce((total, cat) =>
      // 			total + cat.lessons.filter(lesson => lesson.completed).length, 0
      // 		)
      // 	}
      // };
      this.$http.get("v1/course/getGroupsCourse", {
        params: {
          id: groupId
        }
      }).then(function (res) {
        if (res.data.code == 0) {
          _this8.groupCategoryList = res.data.data.class;
          _this8.currentCategoryList = res.data.data.class;
          _this8.currentCategoryList.forEach(function (item) {
            item.expanded = false; // 添加新字段及其值
          });
        }
      });
    },
    // 进入新概念分类学习 - 静态版本
    enterConceptCategory: function enterConceptCategory(category) {
      console.log('进入新概念分类:', category);

      // 显示提示信息
      uni.showToast({
        title: "\u5373\u5C06\u5F00\u653E".concat(category.name, "\u8BFE\u7A0B"),
        icon: 'none',
        duration: 2000
      });

      // 可以跳转到课程列表页面（如果有的话）
      // uni.navigateTo({
      //     url: `/pages/concept/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`
      // });
    },
    // 开始新概念学习 - 静态版本
    startConceptLearning: function startConceptLearning() {
      console.log('开始新概念学习');
      uni.showToast({
        title: '开始学习：口语 - 第一课',
        icon: 'none',
        duration: 2000
      });

      // 可以跳转到具体的课程页面
      // uni.navigateTo({
      //     url: '/pages/concept/lesson?lessonId=1&isFirst=true'
      // });
    },
    // 继续新概念学习 - 静态版本
    continueConceptLearning: function continueConceptLearning() {
      console.log('继续新概念学习');
      uni.showToast({
        title: '继续学习：语法 - 第二课',
        icon: 'none',
        duration: 2000
      });

      // 可以跳转到上次学习的课程
      // uni.navigateTo({
      //     url: '/pages/concept/lesson?lessonId=7&continue=true'
      // });
    },
    // 查看新概念学习进度 - 静态版本
    viewConceptProgress: function viewConceptProgress() {
      console.log('查看学习进度');
      uni.showToast({
        title: '学习进度：已完成8/30课时',
        icon: 'none',
        duration: 2000
      });

      // 可以跳转到进度页面
      // uni.navigateTo({
      //     url: '/pages/concept/progress'
      // });
    },
    // 选择新概念教程
    selectConceptTutorial: function selectConceptTutorial() {
      this.selectedGroupId = 'concept';
      this.selectedGroup = null;
      this.selectedGroupIndex = -1;
      this.loadConceptTutorialData();
      // 模拟调用新概念教程API
      // try {
      // 	const response = await this.loadConceptTutorialData();
      // 	if (response.success) {
      // 		this.currentCategoryList = response.data.categories;
      // 		console.log('✅ 新概念教程数据加载成功:', response.data);
      // 	}
      // } catch (error) {
      // 	console.error('❌ 加载新概念教程数据失败:', error);
      // 	// 降级使用静态数据
      // 	this.currentCategoryList = this.conceptCategoryList;
      // }
    },
    // 选择小组用于详情显示（左右联动）
    selectGroupForDetail: function selectGroupForDetail(group, index) {
      this.selectedGroupId = group.id;
      this.selectedGroup = group;
      this.selectedGroupIndex = index;
      this.loadGroupCourseData(group.id);
      // 模拟调用小组课程API
      // try {
      // 	const response = await this.loadGroupCourseData(group.id);
      // 	if (response.success) {
      // 		this.currentCategoryList = response.data.categories;
      // 		console.log('✅ 小组课程数据加载成功:', group.name, response.data);
      // 	}
      // } catch (error) {
      // 	console.error('❌ 加载小组课程数据失败:', error);
      // 	// 降级使用静态数据
      // 	this.currentCategoryList = this.groupCategoryList;
      // }
    },
    // 选择小组 - 显示操作选择弹窗（保留原有功能）
    selectGroup: function selectGroup(group) {
      var _this9 = this;
      this.selectedGroupId = group.id;
      this.selectedGroup = group;

      // 显示操作选择弹窗
      uni.showActionSheet({
        title: "".concat(group.name, " - \u8BF7\u9009\u62E9\u64CD\u4F5C"),
        itemList: ['🎥 查看课程回顾', '✍️ 进入练习题库', '📊 查看小组详情', '👥 查看小组成员'],
        success: function success(res) {
          switch (res.tapIndex) {
            case 0:
              _this9.quickViewReview(group);
              break;
            case 1:
              _this9.quickViewPractice(group);
              break;
            case 2:
              _this9.enterGroup(group);
              break;
            case 3:
              _this9.viewGroupMembers(group);
              break;
          }
        },
        fail: function fail() {
          // 用户取消选择，重置选中状态
          _this9.selectedGroupId = null;
          _this9.selectedGroup = null;
        }
      });
    },
    // 切换视图
    switchView: function switchView(view) {
      this.currentView = view;
    },
    // 日期选择
    onDateChange: function onDateChange(e) {
      this.selectedDate = e.detail.value;
    },
    // 选择练习类型
    selectPracticeType: function selectPracticeType(type) {
      this.currentPracticeType = type.id;
    },
    // 快速查看课程回顾 - 直接跳转
    quickViewReview: function quickViewReview(group) {
      uni.showToast({
        title: '正在进入课程回顾',
        icon: 'loading',
        duration: 1000
      });
      setTimeout(function () {
        uni.navigateTo({
          url: "/pages/groups/course-review?groupId=".concat(group.id, "&groupName=").concat(encodeURIComponent(group.name))
        });
      }, 500);
    },
    // 快速查看练习 - 直接跳转
    quickViewPractice: function quickViewPractice(group) {
      uni.showToast({
        title: '正在进入练习题库',
        icon: 'loading',
        duration: 1000
      });
      setTimeout(function () {
        uni.navigateTo({
          url: "/pages/groups/practice?groupId=".concat(group.id, "&groupName=").concat(encodeURIComponent(group.name))
        });
      }, 500);
    },
    // 播放课程回顾视频
    playCourseReview: function playCourseReview(course) {
      uni.navigateTo({
        url: "/pages/groups/course-review?courseId=".concat(course.id, "&groupId=").concat(course.groupId)
      });
    },
    // 开始练习
    startPractice: function startPractice(practice) {
      uni.navigateTo({
        url: "/pages/groups/practice?practiceId=".concat(practice.id, "&groupId=").concat(practice.groupId)
      });
    },
    // 查看小组成员
    viewGroupMembers: function viewGroupMembers(group) {
      uni.showToast({
        title: '正在加载成员列表',
        icon: 'loading',
        duration: 1000
      });
      setTimeout(function () {
        uni.navigateTo({
          url: "/pages/groups/members?groupId=".concat(group.id, "&groupName=").concat(encodeURIComponent(group.name))
        });
      }, 500);
    },
    // 获取小组颜色
    getGroupColor: function getGroupColor(index) {
      var colors = ['linear-gradient(135deg, #FF6B6B, #EE4437)',
      // 红色
      'linear-gradient(135deg, #4ECDC4, #44A08D)',
      // 青色
      'linear-gradient(135deg, #45B7D1, #96C93D)',
      // 蓝绿色
      'linear-gradient(135deg, #FFA726, #FB8C00)',
      // 橙色
      'linear-gradient(135deg, #AB47BC, #8E24AA)' // 紫色
      ];

      return colors[index % colors.length];
    },
    // 跳转到登录页面
    goToLogin: function goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      });
    },
    // 联系管理员
    contactAdmin: function contactAdmin() {
      uni.showModal({
        title: '联系管理员',
        content: '请通过以下方式联系管理员开通权限：\n\n微信：admin123\n电话：400-123-4567\n邮箱：<EMAIL>',
        showCancel: false,
        confirmText: '我知道了'
      });
    },
    // 进入小组详情
    enterGroup: function enterGroup(group) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var userToken;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                // 检查用户是否已登录
                userToken = uni.getStorageSync('token');
                if (userToken) {
                  _context3.next = 5;
                  break;
                }
                uni.showModal({
                  title: '需要登录',
                  content: '请先登录后再查看小组详情',
                  confirmText: '去登录',
                  success: function success(res) {
                    if (res.confirm) {
                      uni.navigateTo({
                        url: '/pages/login/login'
                      });
                    }
                  }
                });
                return _context3.abrupt("return");
              case 5:
                // 跳转到小组详情页面
                uni.navigateTo({
                  url: "/pages/groups/group-detail?groupId=".concat(group.id, "&groupName=").concat(group.name)
                });
                _context3.next = 12;
                break;
              case 8:
                _context3.prev = 8;
                _context3.t0 = _context3["catch"](0);
                console.error('进入小组详情失败:', _context3.t0);
                uni.showToast({
                  title: '进入失败，请重试',
                  icon: 'none'
                });
              case 12:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 8]]);
      }))();
    },
    // 加入小组
    joinGroup: function joinGroup(group) {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var userToken;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                // 检查用户是否已登录
                userToken = uni.getStorageSync('token');
                if (userToken) {
                  _context4.next = 5;
                  break;
                }
                uni.showModal({
                  title: '需要登录',
                  content: '请先登录后再加入小组',
                  confirmText: '去登录',
                  success: function success(res) {
                    if (res.confirm) {
                      uni.navigateTo({
                        url: '/pages/login/login'
                      });
                    }
                  }
                });
                return _context4.abrupt("return");
              case 5:
                // 静态版本 - 直接显示成功提示
                uni.showToast({
                  title: "\u6210\u529F\u52A0\u5165".concat(group.name),
                  icon: 'success'
                });

                // 刷新静态数据
                _this10.loadStaticGroupData();
                _context4.next = 12;
                break;
              case 9:
                _context4.prev = 9;
                _context4.t0 = _context4["catch"](0);
                console.error('加入小组失败:', _context4.t0);
                // API服务已经显示了错误提示，这里不需要重复显示
              case 12:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 9]]);
      }))();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 482:
/*!*********************************************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css& ***!
  \*********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css& */ 483);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_6e8c2b60_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 483:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/groups/index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[476,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/groups/index.js.map