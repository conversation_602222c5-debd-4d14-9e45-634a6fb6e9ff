<template>
	<view class="custom-tabbar">
		<!-- 测试：先显示一个简单的红色条 -->
		<view class="test-bar">
			<text style="color: white;">自定义TabBar测试</text>
		</view>

		<view class="tabbar-content">
			<!-- 固定显示4个基础菜单，不依赖权限 -->
			<view class="tab-item" @click="goToPage('pages/index/index')">
				<text class="tab-text">🏠</text>
				<text class="tab-text">首页</text>
			</view>
			<view class="tab-item" @click="goToPage('pages/category/list-page')">
				<text class="tab-text">🔍</text>
				<text class="tab-text">找课</text>
			</view>
			<view class="tab-item" @click="goToPage('pages/study/study')">
				<text class="tab-text">📚</text>
				<text class="tab-text">学习</text>
			</view>
			<view class="tab-item" @click="goToPage('pages/user/user')">
				<text class="tab-text">👤</text>
				<text class="tab-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTabbar',
	data() {
		return {
			currentTab: ''
		};
	},
	mounted() {
		console.log('CustomTabbar mounted - 测试版本');

		// 隐藏原生tabBar
		uni.hideTabBar({
			animation: false
		});
	},
	methods: {
		// 简单的页面跳转
		goToPage(pagePath) {
			console.log('跳转到页面:', pagePath);
			uni.reLaunch({
				url: `/${pagePath}`,
				success: () => {
					console.log('跳转成功');
				},
				fail: (err) => {
					console.error('跳转失败:', err);
				}
			});
		}
	}
};
</script>

<style scoped>
.custom-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	border-top: 2rpx solid #e5e5e5;
	z-index: 99999;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.test-bar {
	background: red;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.tabbar-content {
	display: flex;
	height: 120rpx;
	align-items: center;
	background: #ffffff;
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx 0;
	cursor: pointer;
}

.tab-item:active {
	background: rgba(0, 0, 0, 0.1);
}

.tab-text {
	font-size: 24rpx;
	color: #333;
	margin: 4rpx 0;
}
</style>
