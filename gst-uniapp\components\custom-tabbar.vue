<template>
	<view class="custom-tabbar" :style="{ paddingBottom: safeAreaBottom + 'px' }">
		<view class="tabbar-content">
			<view
				class="tab-item"
				v-for="(item, index) in visibleTabs"
				:key="index"
				:class="{ 'active': currentTab === item.pagePath }"
				@click="switchTab(item)"
			>
				<image
					class="tab-icon"
					:src="currentTab === item.pagePath ? item.selectedIconPath : item.iconPath"
					mode="aspectFit"
				/>
				<text class="tab-text" :class="{ 'active': currentTab === item.pagePath }">
					{{ item.text }}
				</text>
				<!-- 会员专享标识 -->
				<view v-if="item.memberOnly && hasGroupPermission" class="member-badge">
					<text class="member-text">VIP</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTabbar',
	data() {
		return {
			currentTab: '',
			safeAreaBottom: 0,
			// 所有可能的tab配置
			allTabs: [
				{
					pagePath: "pages/index/index",
					iconPath: "/static/tab-home.png",
					selectedIconPath: "/static/tab-home-current.png",
					text: "首页",
					memberOnly: false
				},
				{
					pagePath: "pages/category/list-page",
					iconPath: "/static/tab-search.png",
					selectedIconPath: "/static/tab-search-current.png",
					text: "找课",
					memberOnly: false
				},
				{
					pagePath: "pages/study/study",
					iconPath: "/static/tab-cate.png",
					selectedIconPath: "/static/tab-cate-current.png",
					text: "学习",
					memberOnly: false
				},
				{
					pagePath: "pages/groups/index",
					iconPath: "/static/tab-my.png",
					selectedIconPath: "/static/tab-my-current.png",
					text: "小组",
					memberOnly: true // 标记为会员专享
				},
				{
					pagePath: "pages/user/user",
					iconPath: "/static/tab-my.png",
					selectedIconPath: "/static/tab-my-current.png",
					text: "我的",
					memberOnly: false
				}
			]
		};
	},
	computed: {
		// 获取用户权限状态
		hasGroupPermission() {
			const userInfo = this.$store.state.user.userInfo;
			const userMember = this.$store.state.user.member;
			const hasLogin = this.$store.getters.hasLogin;
			const isLoggedIn = this.$store.getters.isLoggedIn;
			
			// 检查用户是否登录
			const userLoggedIn = hasLogin || isLoggedIn || (userInfo && userInfo.id);
			if (!userLoggedIn) return false;
			
			// 检查会员权限
			return this.checkMemberPermission(userMember);
		},
		
		// 根据权限过滤可见的tabs
		visibleTabs() {
			return this.allTabs.filter(tab => {
				// 如果不是会员专享功能，直接显示
				if (!tab.memberOnly) return true;
				// 如果是会员专享功能，检查权限
				return this.hasGroupPermission;
			});
		}
	},
	mounted() {
		// 获取安全区域
		const systemInfo = uni.getSystemInfoSync();
		this.safeAreaBottom = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0;
		
		// 获取当前页面路径
		this.getCurrentTab();
	},
	methods: {
		// 获取当前tab
		getCurrentTab() {
			const pages = getCurrentPages();
			if (pages.length > 0) {
				const currentPage = pages[pages.length - 1];
				this.currentTab = currentPage.route;
			}
		},
		
		// 切换tab
		switchTab(item) {
			if (this.currentTab === item.pagePath) return;
			
			// 如果是会员专享功能但没有权限，显示提示
			if (item.memberOnly && !this.hasGroupPermission) {
				this.showMemberTip();
				return;
			}
			
			// 切换页面
			uni.reLaunch({
				url: `/${item.pagePath}`,
				success: () => {
					this.currentTab = item.pagePath;
				},
				fail: (err) => {
					console.error('切换tab失败:', err);
				}
			});
		},
		
		// 检查会员权限
		checkMemberPermission(member) {
			if (!member) return false;
			
			// 检查会员等级
			if (member.level && ['premium', 'vip', 'svip'].includes(member.level.toLowerCase())) {
				return true;
			}
			
			// 检查会员到期时间
			if (member.expireDate) {
				const expireDate = new Date(member.expireDate);
				const now = new Date();
				return expireDate > now;
			}
			
			// 检查会员状态
			if (member.status === 'active' || member.status === 1) {
				return true;
			}
			
			return false;
		},
		
		// 显示会员提示
		showMemberTip() {
			uni.showModal({
				title: '会员专享',
				content: '小组功能为会员专享，请先开通会员',
				confirmText: '开通会员',
				cancelText: '暂不开通',
				success: (res) => {
					if (res.confirm) {
						// 跳转到会员开通页面
						uni.navigateTo({
							url: '/pages/user/member'
						});
					}
				}
			});
		}
	}
};
</script>

<style scoped>
.custom-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	border-top: 1rpx solid #e5e5e5;
	z-index: 1000;
}

.tabbar-content {
	display: flex;
	height: 100rpx;
	align-items: center;
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	padding: 8rpx 0;
	transition: all 0.3s ease;
}

.tab-item:active {
	background: rgba(0, 0, 0, 0.05);
}

.tab-icon {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 4rpx;
}

.tab-text {
	font-size: 20rpx;
	color: #C0C4CC;
	transition: color 0.3s ease;
}

.tab-text.active {
	color: #2094CE;
	font-weight: 600;
}

.member-badge {
	position: absolute;
	top: 2rpx;
	right: 20rpx;
	background: linear-gradient(135deg, #ff6b6b, #ffa500);
	border-radius: 20rpx;
	padding: 2rpx 8rpx;
	transform: scale(0.8);
}

.member-text {
	font-size: 16rpx;
	color: #fff;
	font-weight: 600;
}

/* 动画效果 */
.tab-item.active .tab-icon {
	transform: scale(1.1);
}
</style>
