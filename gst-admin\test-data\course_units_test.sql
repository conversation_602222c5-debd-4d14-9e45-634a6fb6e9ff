/*
 课程单元测试数据
 用于测试课程单元导入功能
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for course_units
-- ----------------------------
DROP TABLE IF EXISTS `course_units`;
CREATE TABLE `course_units`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `sort_order` int(11) NOT NULL DEFAULT 1,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'text',
  `duration` int(11) DEFAULT 30,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` timestamp(0) DEFAULT NULL,
  `updated_at` timestamp(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of course_units
-- ----------------------------
INSERT INTO `course_units` VALUES (1, 1, '五十音图-平假名', '学习平假名的发音和写法，包括あいうえお行', 1, 'text', 45, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (2, 1, '五十音图-片假名', '学习片假名的发音和写法，包括アイウエオ行', 2, 'text', 45, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (3, 1, '五十音图练习', '通过练习巩固五十音图的记忆', 3, 'exercise', 30, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (4, 2, '基础问候语-早上', '学习早上的问候用语：おはよう、おはようございます', 1, 'text', 20, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (5, 2, '基础问候语-白天', '学习白天的问候用语：こんにちは、こんばんは', 2, 'text', 20, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (6, 2, '问候语练习', '通过对话练习掌握问候语的使用', 3, 'dialogue', 25, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (7, 3, '数字1-10', '学习日语中1到10的数字表达', 1, 'text', 15, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `course_units` VALUES (8, 3, '数字11-100', '学习日语中11到100的数字表达规律', 2, 'text', 25, 'active', '2024-07-28 10:00:00', '2024-07-28 10:00:00');

SET FOREIGN_KEY_CHECKS = 1;
