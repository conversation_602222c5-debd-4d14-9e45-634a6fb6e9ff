<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1 clean-groups-page">
			<!-- 有权限时显示内容 -->
			<view v-if="hasGroupPermission" class="page-content">
				<!-- 简洁的页面头部 -->
				<view class="page-header">
					<view class="header-content">
						<view class="title-section">
							<text class="page-title">🎓 GST派遣日语培训班</text>
							<text class="page-subtitle">与同伴一起进步，共同成长</text>
						</view>
						<view class="stats-section">
							<view class="stat-item">
								<text class="stat-number">{{groupList.length}}</text>
								<text class="stat-label">个小组</text>
							</view>
							<view class="stat-item">
								<text class="stat-number">{{totalMembers}}</text>
								<text class="stat-label">名成员</text>
							</view>
							<view class="stat-item">
								<text class="stat-number">48</text>
								<text class="stat-label">门课程</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 左右联动布局 -->
				<view class="split-layout">
					<!-- 左侧小组列表 -->
					<view class="left-panel">
						<view class="panel-header">
							<text class="panel-title">学习内容</text>
							<text class="panel-subtitle">{{groupList.length + 1}}项</text>
						</view>
						<scroll-view class="group-list" scroll-y="true">
							<!-- 公共新概念教程 -->
							<view
								class="concept-tutorial-item"
								:class="{ 'active': selectedGroupId === 'concept' }"
								@click="selectConceptTutorial()"
							>
								<view class="concept-content">
									<view class="concept-icon">📖</view>
									<text class="concept-title">新概念教程</text>
									<view class="concept-badge">公共</view>
								</view>
							</view>
							<view
								class="group-item"
								v-for="(group, index) in groupList"
								:key="index"
								:class="{ 'active': selectedGroupId === group.id }"
								@click="selectGroupForDetail(group, index)"
							>
								<view class="simple-group-content">
									<view class="group-level-badge" :style="{ background: getGroupColor(index) }">
										{{group.level}}
									</view>
									<text class="simple-group-name">{{group.name}}</text>
									<view class="simple-status-dot" :class="group.status"></view>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 右侧详情面板 -->
					<view class="right-panel">
						<!-- 新概念教程详情 -->
						<view v-if="selectedGroupId === 'concept'" class="concept-detail-content">
							<!-- 教程头部 -->
							<view class="concept-detail-header">
								<view class="concept-bg">
									<view class="concept-overlay">
										<view class="concept-detail-icon">📖</view>
										<view class="concept-detail-info">
											<text class="concept-detail-title">{{conceptTutorial.title}}</text>
											<text class="concept-detail-subtitle">{{conceptTutorial.description}}</text>
											<view class="concept-progress-info">
												<text class="progress-info-text">
													{{conceptTutorial.completedLessons}} / {{conceptTutorial.totalLessons}} 课时
												</text>
											</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 学习分类 - 简单二级列表 -->
							<view class="concept-categories">
								<view class="categories-title">学习内容</view>
								<view class="simple-category-list">
									<view
										class="simple-category-item"
										v-for="(category, categoryIndex) in simpleCategoryList"
										:key="categoryIndex"
									>
										<view class="category-header" @click="toggleCategory(categoryIndex)">
											<view class="category-left">
												<view class="category-icon">{{category.icon}}</view>
												<text class="category-name">{{category.name}}</text>
											</view>
											<view class="category-toggle" :class="{ 'expanded': category.expanded }">
												<text class="toggle-icon">{{category.expanded ? '▼' : '▶'}}</text>
											</view>
										</view>
										<view v-if="category.expanded" class="lessons-list">
											<view
												class="lesson-item"
												v-for="(lesson, lessonIndex) in category.lessons"
												:key="lessonIndex"
												@click="enterLesson(category, lesson)"
											>
												<view class="lesson-number">{{lessonIndex + 1}}</view>
												<view class="lesson-content">
													<text class="lesson-title">{{lesson.title}}</text>
													<text class="lesson-subtitle">{{lesson.subtitle}}</text>
												</view>
												<view class="lesson-status" :class="lesson.completed ? 'completed' : 'pending'">
													<text class="status-icon">{{lesson.completed ? '✓' : '○'}}</text>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 快速操作 -->
							<view class="concept-actions">
								<view class="concept-action-title">快速开始</view>
								<view class="concept-action-buttons">
									<view class="concept-btn primary" @click="startConceptLearning()">
										<view class="concept-btn-icon">🚀</view>
										<view class="concept-btn-content">
											<text class="concept-btn-title">开始学习</text>
											<text class="concept-btn-desc">从第一课开始</text>
										</view>
									</view>
									<view class="concept-btn secondary" @click="continueConceptLearning()">
										<view class="concept-btn-icon">📖</view>
										<view class="concept-btn-content">
											<text class="concept-btn-title">继续学习</text>
											<text class="concept-btn-desc">从上次位置继续</text>
										</view>
									</view>
									<view class="concept-btn tertiary" @click="viewConceptProgress()">
										<view class="concept-btn-icon">📊</view>
										<view class="concept-btn-content">
											<text class="concept-btn-title">学习进度</text>
											<text class="concept-btn-desc">查看详细进度</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 小组详情 -->
						<view v-else-if="selectedGroup" class="detail-content">
							<!-- 详情头部 -->
							<view class="detail-header">
								<view class="detail-bg" :style="{ background: getGroupColor(selectedGroupIndex) }">
									<view class="detail-overlay">
										<view class="detail-avatar">
											<image :src="selectedGroup.icon" mode="aspectFit" />
										</view>
										<view class="detail-info">
											<text class="detail-title">{{selectedGroup.name}}</text>
											<text class="detail-subtitle">{{selectedGroup.description}}</text>
											<view class="detail-level">{{selectedGroup.level}}等级</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 统计卡片 -->
							<view class="detail-stats">
								<view class="stat-card-detail">
									<view class="stat-icon-detail">📚</view>
									<view class="stat-content">
										<text class="stat-number-detail">{{selectedGroup.courseCount}}</text>
										<text class="stat-label-detail">课程数量</text>
									</view>
								</view>
								<view class="stat-card-detail">
									<view class="stat-icon-detail">👥</view>
									<view class="stat-content">
										<text class="stat-number-detail">{{selectedGroup.memberCount}}</text>
										<text class="stat-label-detail">小组成员</text>
									</view>
								</view>
								<view class="stat-card-detail">
									<view class="stat-icon-detail">📈</view>
									<view class="stat-content">
										<text class="stat-number-detail">{{selectedGroup.progress}}%</text>
										<text class="stat-label-detail">学习进度</text>
									</view>
								</view>
							</view>

							<!-- 进度详情 -->
							<view class="progress-detail">
								<view class="progress-header">
									<text class="progress-title">学习进度</text>
									<text class="progress-value">{{selectedGroup.progress}}%</text>
								</view>
								<view class="progress-bar-detail">
									<view
										class="progress-fill-detail"
										:style="{
											width: selectedGroup.progress + '%',
											background: getGroupColor(selectedGroupIndex)
										}"
									></view>
								</view>
								<text class="progress-desc">已完成 {{Math.floor(selectedGroup.courseCount * selectedGroup.progress / 100)}} / {{selectedGroup.courseCount}} 门课程</text>
							</view>

							<!-- 功能按钮 -->
							<view class="detail-actions">
								<view class="action-row">
									<view class="action-btn-large primary" @click="quickViewReview(selectedGroup)">
										<view class="btn-icon-large">🎥</view>
										<view class="btn-content">
											<text class="btn-title">课程回顾</text>
											<text class="btn-desc">观看录制的课程视频</text>
										</view>
										<view class="btn-arrow-large">→</view>
									</view>
								</view>
								<view class="action-row">
									<view class="action-btn-large secondary" @click="quickViewPractice(selectedGroup)">
										<view class="btn-icon-large">✍️</view>
										<view class="btn-content">
											<text class="btn-title">在线练习</text>
											<text class="btn-desc">完成课后练习题目</text>
										</view>
										<view class="btn-arrow-large">→</view>
									</view>
								</view>
								<view class="action-row">
									<view class="action-btn-large tertiary" @click="enterGroup(selectedGroup)">
										<view class="btn-icon-large">📊</view>
										<view class="btn-content">
											<text class="btn-title">小组详情</text>
											<text class="btn-desc">查看详细信息和设置</text>
										</view>
										<view class="btn-arrow-large">→</view>
									</view>
								</view>
								<view class="action-row">
									<view class="action-btn-large quaternary" @click="viewGroupMembers(selectedGroup)">
										<view class="btn-icon-large">👥</view>
										<view class="btn-content">
											<text class="btn-title">小组成员</text>
											<text class="btn-desc">查看所有小组成员</text>
										</view>
										<view class="btn-arrow-large">→</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 未选择状态 -->
						<view v-else class="empty-detail">
							<view class="empty-icon">👈</view>
							<text class="empty-title">选择一个小组</text>
							<text class="empty-desc">点击左侧小组查看详细信息</text>
						</view>
					</view>
				</view>
		</view>

		<!-- 无权限提示 -->
		<view v-else class="no-permission-page">
			<view class="permission-container">
				<view class="permission-icon">🔒</view>
				<text class="permission-title">访问受限</text>
				<text class="permission-desc">您暂时没有访问学习小组的权限</text>
				<text class="permission-hint">请联系管理员开通权限或使用授权账号登录</text>
				<view class="permission-actions">
					<button class="btn-login" @click="goToLogin">重新登录</button>
					<button class="btn-contact" @click="contactAdmin">联系管理员</button>
				</view>
			</view>
		</view>
	</view>
	</gui-page>
</template>

<script>
export default {
	data() {
		return {
			pageLoading: false,
			hasGroupPermission: false,
			selectedGroupId: null,
			selectedGroup: null,
			selectedGroupIndex: 0,
			groupsLoading: false,
			// 新概念教程数据
			conceptTutorial: {
				id: 'concept',
				title: '新概念日语教程',
				description: '适合所有学员的基础教程',
				totalLessons: 30,
				completedLessons: 8,
			},
			// 简单分类列表数据
			simpleCategoryList: [
				{
					id: 'speaking',
					name: '口语',
					icon: '🗣️',
					expanded: true, // 默认展开第一个
					lessons: [
						{ id: 1, title: '第一课', subtitle: '基础问候语', completed: true },
						{ id: 2, title: '第二课', subtitle: '自我介绍', completed: true },
						{ id: 3, title: '第三课', subtitle: '日常对话', completed: false },
						{ id: 4, title: '第四课', subtitle: '购物用语', completed: false },
						{ id: 5, title: '第五课', subtitle: '餐厅用语', completed: false }
					]
				},
				{
					id: 'grammar',
					name: '语法',
					icon: '📝',
					expanded: false,
					lessons: [
						{ id: 6, title: '第一课', subtitle: '基本句型', completed: true },
						{ id: 7, title: '第二课', subtitle: '动词变位', completed: false },
						{ id: 8, title: '第三课', subtitle: '形容词活用', completed: false },
						{ id: 9, title: '第四课', subtitle: '助词用法', completed: false },
						{ id: 10, title: '第五课', subtitle: '敬语表达', completed: false }
					]
				},
				{
					id: 'vocabulary',
					name: '词汇',
					icon: '📚',
					expanded: false,
					lessons: [
						{ id: 11, title: '第一课', subtitle: '基础词汇', completed: true },
						{ id: 12, title: '第二课', subtitle: '生活词汇', completed: false },
						{ id: 13, title: '第三课', subtitle: '工作词汇', completed: false },
						{ id: 14, title: '第四课', subtitle: '学习词汇', completed: false },
						{ id: 15, title: '第五课', subtitle: '旅游词汇', completed: false }
					]
				},
				{
					id: 'listening',
					name: '听力',
					icon: '🎧',
					expanded: false,
					lessons: [
						{ id: 16, title: '第一课', subtitle: '基础听力', completed: false },
						{ id: 17, title: '第二课', subtitle: '对话听力', completed: false },
						{ id: 18, title: '第三课', subtitle: '新闻听力', completed: false },
						{ id: 19, title: '第四课', subtitle: '故事听力', completed: false },
						{ id: 20, title: '第五课', subtitle: '综合听力', completed: false }
					]
				},
				{
					id: 'reading',
					name: '阅读',
					icon: '📖',
					expanded: false,
					lessons: [
						{ id: 21, title: '第一课', subtitle: '短文阅读', completed: false },
						{ id: 22, title: '第二课', subtitle: '新闻阅读', completed: false },
						{ id: 23, title: '第三课', subtitle: '小说阅读', completed: false },
						{ id: 24, title: '第四课', subtitle: '说明文阅读', completed: false },
						{ id: 25, title: '第五课', subtitle: '综合阅读', completed: false }
					]
				},
				{
					id: 'writing',
					name: '写作',
					icon: '✍️',
					expanded: false,
					lessons: [
						{ id: 26, title: '第一课', subtitle: '基础写作', completed: false },
						{ id: 27, title: '第二课', subtitle: '日记写作', completed: false },
						{ id: 28, title: '第三课', subtitle: '书信写作', completed: false },
						{ id: 29, title: '第四课', subtitle: '作文写作', completed: false },
						{ id: 30, title: '第五课', subtitle: '应用写作', completed: false }
					]
				}
			],
			currentView: 'review', // 'review' 或 'practice'
			selectedDate: '',
			currentPracticeType: 'all',
			groupList: [],
			// 练习类型
			practiceTypes: [
				{ id: 'listening', name: '听力练习', icon: '🎧', count: 25 },
				{ id: 'grammar', name: '语法练习', icon: '📝', count: 30 },
				{ id: 'vocabulary', name: '词汇练习', icon: '📚', count: 40 },
				{ id: 'speaking', name: '口语练习', icon: '🗣️', count: 15 }
			],
			// 课程回顾数据
			reviewCourses: [
				{
					id: 1,
					title: '第一课：基础发音练习',
					date: '2024-01-15',
					teacher: '田中老师',
					duration: '45:30',
					thumbnail: '/static/imgs/course-thumb1.png',
					groupId: 1
				},
				{
					id: 2,
					title: '第二课：日常问候语',
					date: '2024-01-16',
					teacher: '佐藤老师',
					duration: '38:20',
					thumbnail: '/static/imgs/course-thumb2.png',
					groupId: 1
				},
				{
					id: 3,
					title: '第三课：数字与时间',
					date: '2024-01-17',
					teacher: '山田老师',
					duration: '42:15',
					thumbnail: '/static/imgs/course-thumb3.png',
					groupId: 2
				},
				{
					id: 4,
					title: '第四课：家族称呼',
					date: '2024-01-18',
					teacher: '田中老师',
					duration: '39:45',
					thumbnail: '/static/imgs/course-thumb4.png',
					groupId: 2
				}
			],
			// 练习数据
			practices: [
				{
					id: 1,
					title: '五十音图听力练习',
					date: '2024-01-15',
					type: '听力练习',
					questionCount: 20,
					duration: 15,
					status: 'completed',
					groupId: 1
				},
				{
					id: 2,
					title: '基础语法选择题',
					date: '2024-01-16',
					type: '语法练习',
					questionCount: 25,
					duration: 20,
					status: 'in-progress',
					groupId: 1
				}
			]
		}
	},
	computed: {
		// 总成员数
		totalMembers() {
			return this.groupList.reduce((total, group) => total + group.memberCount, 0);
		},

		// 过滤后的课程回顾
		filteredReviewCourses() {
			let courses = this.reviewCourses.filter(course =>
				!this.selectedGroup || course.groupId === this.selectedGroup.id
			);

			if (this.selectedDate) {
				courses = courses.filter(course => course.date === this.selectedDate);
			}

			return courses.sort((a, b) => new Date(b.date) - new Date(a.date));
		},

		// 过滤后的练习
		filteredPractices() {
			let practices = this.practices.filter(practice =>
				!this.selectedGroup || practice.groupId === this.selectedGroup.id
			);

			if (this.currentPracticeType !== 'all') {
				practices = practices.filter(practice =>
					practice.type === this.practiceTypes.find(t => t.id === this.currentPracticeType)?.name
				);
			}

			return practices.sort((a, b) => new Date(b.date) - new Date(a.date));
		}
	},
	async onLoad() {
		this.checkGroupAccess();
		await this.initializeData();
	},
	async onShow() {
		// 每次显示页面时都检查权限，确保权限状态是最新的
		this.checkGroupAccess();
		// 刷新静态数据
		if (this.hasGroupPermission) {
			this.loadStaticGroupData();
		}
	},
	methods: {
		// 切换分类展开/收起
		toggleCategory(categoryIndex) {
			this.simpleCategoryList[categoryIndex].expanded = !this.simpleCategoryList[categoryIndex].expanded;
		},

		// 进入课程学习
		enterLesson(category, lesson) {
			console.log('进入课程:', category.name, lesson.title);
			// 这里可以跳转到具体的课程页面
			uni.showToast({
				title: `开始学习：${category.name} - ${lesson.title}`,
				icon: 'none',
				duration: 2000
			});

			// 可以跳转到课程详情页
			// uni.navigateTo({
			//     url: `/pages/course/detail?id=${lesson.id}&category=${category.id}`
			// });
		},

		// 检查用户权限
		checkPermission() {
			console.log('=== 开始检查小组权限 ===');

			// 检查用户是否登录
			const userToken = this.$store.state.user.token;
			const userInfo = this.$store.state.user.userInfo;
			const hasLogin = this.$store.getters.hasLogin;

			console.log('权限检查数据:', {
				userToken: userToken ? '存在' : '不存在',
				userInfo: userInfo,
				hasLogin: hasLogin
			});

			if (!userToken && !hasLogin) {
				console.log('用户未登录，拒绝访问');
				this.hasGroupPermission = false;
				return;
			}

			// 检查用户是否有小组权限
			this.checkGroupAccess();
		},
		
		// 检查小组访问权限 - 简化版，只检查member权限
		checkGroupAccess() {
			const userInfo = this.$store.state.user.userInfo;
			const hasLogin = this.$store.getters.hasLogin;

			console.log('=== 检查小组访问权限 ===');
			console.log('用户信息:', userInfo);
			console.log('登录状态:', hasLogin);

			// 检查用户是否登录
			if (!hasLogin || !userInfo) {
				console.log('❌ 用户未登录，无权限访问小组');
				this.hasGroupPermission = false;
				this.showLoginTip();
				return;
			}

			// 检查用户是否有member权限
			const userRole = userInfo.role || '';
			const isMember = userRole.includes('member');

			console.log('用户角色信息:', {
				role: userRole,
				isMember
			});

			if (isMember) {
				console.log('✅ 用户有member权限，可以访问小组');
				this.hasGroupPermission = true;
			} else {
				console.log('❌ 用户没有member权限，无法访问小组');
				this.hasGroupPermission = false;
				this.showMemberTip();
			}

			console.log('=== 权限检查完成，结果:', this.hasGroupPermission, '===');
		},

		// 显示登录提示
		showLoginTip() {
			uni.showToast({
				title: '请先登录',
				icon: 'none',
				duration: 2000
			});
		},

		// 显示会员提示
		showMemberTip() {
			uni.showToast({
				title: '需要会员权限才能访问学习小组',
				icon: 'none',
				duration: 3000
			});
		},
		
		// 初始化数据 - 使用静态数据
		async initializeData() {
			if (this.hasGroupPermission) {
				// 使用静态数据，不需要API调用
				this.loadStaticGroupData();
				// 默认选择新概念教程
				this.selectConceptTutorial();
			}
		},

		// 加载静态小组数据
		loadStaticGroupData() {
			console.log('📋 加载静态小组数据...');

			// 静态小组数据
			this.groupList = [
				{
					id: 1,
					name: 'N5基础班',
					description: '适合零基础学员',
					level: 'N5',
					icon: '/static/imgs/group-n5.png',
					memberCount: 28,
					courseCount: 12,
					progress: 65,
					status: 'active'
				},
				{
					id: 2,
					name: 'N4进阶班',
					description: '有一定基础的学员',
					level: 'N4',
					icon: '/static/imgs/group-n4.png',
					memberCount: 22,
					courseCount: 15,
					progress: 45,
					status: 'active'
				},
				{
					id: 3,
					name: 'N3提高班',
					description: '中级水平学员',
					level: 'N3',
					icon: '/static/imgs/group-n3.png',
					memberCount: 18,
					courseCount: 18,
					progress: 30,
					status: 'active'
				},
				{
					id: 4,
					name: '商务日语班',
					description: '职场日语专项训练',
					level: '商务',
					icon: '/static/imgs/group-business.png',
					memberCount: 15,
					courseCount: 10,
					progress: 20,
					status: 'active'
				}
			];

			console.log('✅ 静态小组数据加载完成，共', this.groupList.length, '个小组');
		},



		// 进入新概念分类学习 - 静态版本
		enterConceptCategory(category) {
			console.log('进入新概念分类:', category);

			// 显示提示信息
			uni.showToast({
				title: `即将开放${category.name}课程`,
				icon: 'none',
				duration: 2000
			});

			// 可以跳转到课程列表页面（如果有的话）
			// uni.navigateTo({
			//     url: `/pages/concept/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`
			// });
		},

		// 开始新概念学习 - 静态版本
		startConceptLearning() {
			console.log('开始新概念学习');

			uni.showToast({
				title: '开始学习：口语 - 第一课',
				icon: 'none',
				duration: 2000
			});

			// 可以跳转到具体的课程页面
			// uni.navigateTo({
			//     url: '/pages/concept/lesson?lessonId=1&isFirst=true'
			// });
		},

		// 继续新概念学习 - 静态版本
		continueConceptLearning() {
			console.log('继续新概念学习');

			uni.showToast({
				title: '继续学习：语法 - 第二课',
				icon: 'none',
				duration: 2000
			});

			// 可以跳转到上次学习的课程
			// uni.navigateTo({
			//     url: '/pages/concept/lesson?lessonId=7&continue=true'
			// });
		},

		// 查看新概念学习进度 - 静态版本
		viewConceptProgress() {
			console.log('查看学习进度');

			uni.showToast({
				title: '学习进度：已完成8/30课时',
				icon: 'none',
				duration: 2000
			});

			// 可以跳转到进度页面
			// uni.navigateTo({
			//     url: '/pages/concept/progress'
			// });
		},

		// 选择新概念教程
		selectConceptTutorial() {
			this.selectedGroupId = 'concept';
			this.selectedGroup = null;
			this.selectedGroupIndex = -1;
		},

		// 选择小组用于详情显示（左右联动）
		selectGroupForDetail(group, index) {
			this.selectedGroupId = group.id;
			this.selectedGroup = group;
			this.selectedGroupIndex = index;
		},

		// 选择小组 - 显示操作选择弹窗（保留原有功能）
		selectGroup(group) {
			this.selectedGroupId = group.id;
			this.selectedGroup = group;

			// 显示操作选择弹窗
			uni.showActionSheet({
				title: `${group.name} - 请选择操作`,
				itemList: [
					'🎥 查看课程回顾',
					'✍️ 进入练习题库',
					'📊 查看小组详情',
					'👥 查看小组成员'
				],
				success: (res) => {
					switch(res.tapIndex) {
						case 0:
							this.quickViewReview(group);
							break;
						case 1:
							this.quickViewPractice(group);
							break;
						case 2:
							this.enterGroup(group);
							break;
						case 3:
							this.viewGroupMembers(group);
							break;
					}
				},
				fail: () => {
					// 用户取消选择，重置选中状态
					this.selectedGroupId = null;
					this.selectedGroup = null;
				}
			});
		},

		// 切换视图
		switchView(view) {
			this.currentView = view;
		},

		// 日期选择
		onDateChange(e) {
			this.selectedDate = e.detail.value;
		},

		// 选择练习类型
		selectPracticeType(type) {
			this.currentPracticeType = type.id;
		},

		// 快速查看课程回顾 - 直接跳转
		quickViewReview(group) {
			uni.showToast({
				title: '正在进入课程回顾',
				icon: 'loading',
				duration: 1000
			});

			setTimeout(() => {
				uni.navigateTo({
					url: `/pages/groups/course-review?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`
				});
			}, 500);
		},

		// 快速查看练习 - 直接跳转
		quickViewPractice(group) {
			uni.showToast({
				title: '正在进入练习题库',
				icon: 'loading',
				duration: 1000
			});

			setTimeout(() => {
				uni.navigateTo({
					url: `/pages/groups/practice?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`
				});
			}, 500);
		},

		// 播放课程回顾视频
		playCourseReview(course) {
			uni.navigateTo({
				url: `/pages/groups/course-review?courseId=${course.id}&groupId=${course.groupId}`
			});
		},

		// 开始练习
		startPractice(practice) {
			uni.navigateTo({
				url: `/pages/groups/practice?practiceId=${practice.id}&groupId=${practice.groupId}`
			});
		},

		// 查看小组成员
		viewGroupMembers(group) {
			uni.showToast({
				title: '正在加载成员列表',
				icon: 'loading',
				duration: 1000
			});

			setTimeout(() => {
				uni.navigateTo({
					url: `/pages/groups/members?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`
				});
			}, 500);
		},

		// 获取小组颜色
		getGroupColor(index) {
			const colors = [
				'linear-gradient(135deg, #FF6B6B, #EE4437)', // 红色
				'linear-gradient(135deg, #4ECDC4, #44A08D)', // 青色
				'linear-gradient(135deg, #45B7D1, #96C93D)', // 蓝绿色
				'linear-gradient(135deg, #FFA726, #FB8C00)', // 橙色
				'linear-gradient(135deg, #AB47BC, #8E24AA)'  // 紫色
			];
			return colors[index % colors.length];
		},



		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},

		// 联系管理员
		contactAdmin() {
			uni.showModal({
				title: '联系管理员',
				content: '请通过以下方式联系管理员开通权限：\n\n微信：admin123\n电话：400-123-4567\n邮箱：<EMAIL>',
				showCancel: false,
				confirmText: '我知道了'
			});
		},

		// 进入小组详情
		async enterGroup(group) {
			try {
				// 检查用户是否已登录
				const userToken = uni.getStorageSync('token');
				if (!userToken) {
					uni.showModal({
						title: '需要登录',
						content: '请先登录后再查看小组详情',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
					return;
				}

				// 跳转到小组详情页面
				uni.navigateTo({
					url: `/pages/groups/group-detail?groupId=${group.id}&groupName=${group.name}`
				});

			} catch (error) {
				console.error('进入小组详情失败:', error);
				uni.showToast({
					title: '进入失败，请重试',
					icon: 'none'
				});
			}
		},

		// 加入小组
		async joinGroup(group) {
			try {
				// 检查用户是否已登录
				const userToken = uni.getStorageSync('token');
				if (!userToken) {
					uni.showModal({
						title: '需要登录',
						content: '请先登录后再加入小组',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
					return;
				}

				// 静态版本 - 直接显示成功提示
				uni.showToast({
					title: `成功加入${group.name}`,
					icon: 'success'
				});

				// 刷新静态数据
				this.loadStaticGroupData();

			} catch (error) {
				console.error('加入小组失败:', error);
				// API服务已经显示了错误提示，这里不需要重复显示
			}
		}
	}
}
</script>

<style scoped>
/* ===== 基础页面样式 ===== */
.clean-groups-page {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}

.page-content {
	position: relative;
	z-index: 1;
}

/* ===== 页面头部样式 ===== */
.page-header {
	background: white;
	border-radius: 0 0 30rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.header-content {
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.title-section {
	flex: 1;
}

.page-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.page-subtitle {
	font-size: 22rpx;
	color: #666;
}

.stats-section {
	display: flex;
	gap: 25rpx;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 28rpx;
	font-weight: 700;
	color: #667eea;
	display: block;
	line-height: 1;
}

.stat-label {
	font-size: 18rpx;
	color: #999;
	margin-top: 5rpx;
}

/* ===== 左右联动布局 ===== */
.split-layout {
	display: flex;
	height: calc(100vh - 200rpx);
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
	margin: 0 20rpx;
	box-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);
}

/* ===== 左侧面板 ===== */
.left-panel {
	width: 200rpx;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}

.panel-header {
	padding: 25rpx 15rpx 20rpx;
	background: white;
	border-bottom: 1rpx solid #e9ecef;
	text-align: center;
}

.panel-title {
	font-size: 22rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.panel-subtitle {
	font-size: 18rpx;
	color: #999;
}

.group-list {
	flex: 1;
	padding: 10rpx 0;
}

/* ===== 小组列表项 ===== */
.group-item {
	margin: 0 10rpx 15rpx;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	text-align: center;
	position: relative;
}

.group-item.active {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	transform: scale(1.05);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.group-item:active {
	transform: scale(0.95);
}

.simple-group-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.group-level-badge {
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	font-size: 18rpx;
	font-weight: 700;
	min-width: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.simple-group-name {
	font-size: 20rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	line-height: 1.3;
	word-break: break-all;
}

.simple-status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}

.simple-status-dot.active {
	background: #4CAF50;
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}

.simple-status-dot.completed {
	background: #2196F3;
	box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
}

.simple-status-dot.pending {
	background: #FF9800;
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
}
.groups-container {
	padding: 30rpx;
}

.page-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 40rpx;
}

.groups-grid {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.group-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	display: flex;
	align-items: center;
	position: relative;
}

.group-icon {
	width: 120rpx;
	height: 120rpx;
	margin-right: 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.group-icon image {
	width: 80rpx;
	height: 80rpx;
}

.group-info {
	flex: 1;
}

.group-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.group-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.group-stats {
	display: flex;
	gap: 20rpx;
}

.stat-item {
	font-size: 24rpx;
	color: #999;
	background: #f8f8f8;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.group-status {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.group-status.active {
	background: #e8f5e8;
	color: #52c41a;
}

.group-status.completed {
	background: #f0f0f0;
	color: #999;
}

.group-status.pending {
	background: #fff7e6;
	color: #fa8c16;
}

/* 简洁清爽的样式 */
.clean-groups-page {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}

.page-content {
	position: relative;
	z-index: 1;
}

/* 简洁的头部样式 */
.page-header {
	background: white;
	border-radius: 0 0 30rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.header-content {
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.title-section {
	flex: 1;
}

.page-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.page-subtitle {
	font-size: 24rpx;
	color: #666;
}

.stats-section {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 32rpx;
	font-weight: 700;
	color: #667eea;
	display: block;
	line-height: 1;
}

.stat-label {
	font-size: 20rpx;
	color: #999;
	margin-top: 5rpx;
}

.header-content-wrapper {
	position: relative;
	z-index: 2;
	padding: 60rpx 30rpx 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.title-section-enhanced {
	display: flex;
	align-items: center;
	flex: 1;
}

.title-icon-container {
	position: relative;
	margin-right: 25rpx;
}

.icon-bg-circle {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 50%;
	opacity: 0.2;
}

.title-icon-enhanced {
	position: relative;
	z-index: 2;
	font-size: 70rpx;
	display: block;
	text-align: center;
	line-height: 100rpx;
}

.icon-pulse {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100rpx;
	height: 100rpx;
	border: 3rpx solid rgba(102,126,234,0.3);
	border-radius: 50%;
	animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
	0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
	50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }
}

.title-text-enhanced {
	position: relative;
}

.main-title-enhanced {
	font-size: 52rpx;
	font-weight: 800;
	color: #333;
	line-height: 1.2;
	margin-bottom: 8rpx;
	background: linear-gradient(135deg, #333, #667eea);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.sub-title-enhanced {
	font-size: 28rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 15rpx;
}

.title-decoration {
	width: 80rpx;
	height: 6rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 3rpx;
	animation: decorationGlow 2s ease-in-out infinite;
}

@keyframes decorationGlow {
	0%, 100% { box-shadow: 0 0 10rpx rgba(102,126,234,0.3); }
	50% { box-shadow: 0 0 20rpx rgba(102,126,234,0.6); }
}

.stats-section-enhanced {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.stat-card-enhanced {
	position: relative;
	background: rgba(255,255,255,0.9);
	border-radius: 20rpx;
	padding: 25rpx 20rpx;
	min-width: 120rpx;
	text-align: center;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255,255,255,0.3);
	box-shadow:
		0 8rpx 25rpx rgba(0,0,0,0.1),
		inset 0 1rpx 0 rgba(255,255,255,0.6);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
}

.stat-card-enhanced:hover {
	transform: translateY(-5rpx);
	box-shadow:
		0 15rpx 35rpx rgba(0,0,0,0.15),
		inset 0 1rpx 0 rgba(255,255,255,0.8);
}

.stat-icon-bg {
	font-size: 32rpx;
	margin-bottom: 10rpx;
	display: block;
}

.stat-number-enhanced {
	font-size: 36rpx;
	font-weight: 800;
	color: #333;
	line-height: 1;
	margin-bottom: 5rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.stat-label-enhanced {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}

.stat-sparkle {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 8rpx;
	height: 8rpx;
	background: #667eea;
	border-radius: 50%;
	animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.5); }
}

/* 装饰元素 */
.header-decorations {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	z-index: 1;
}

.deco-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255,255,255,0.1);
}

.deco-1 {
	width: 60rpx;
	height: 60rpx;
	top: 20rpx;
	right: 80rpx;
	animation: float1 6s ease-in-out infinite;
}

.deco-2 {
	width: 40rpx;
	height: 40rpx;
	bottom: 30rpx;
	left: 60rpx;
	animation: float2 8s ease-in-out infinite;
}

.deco-triangle {
	position: absolute;
	width: 0;
	height: 0;
	border-left: 15rpx solid transparent;
	border-right: 15rpx solid transparent;
	border-bottom: 25rpx solid rgba(255,255,255,0.1);
}

.deco-3 {
	top: 60rpx;
	left: 40rpx;
	animation: float3 7s ease-in-out infinite;
}

.deco-square {
	position: absolute;
	background: rgba(255,255,255,0.1);
	transform: rotate(45deg);
}

.deco-4 {
	width: 20rpx;
	height: 20rpx;
	bottom: 60rpx;
	right: 40rpx;
	animation: float4 5s ease-in-out infinite;
}

@keyframes float1 {
	0%, 100% { transform: translateY(0) rotate(0deg); }
	50% { transform: translateY(-20rpx) rotate(180deg); }
}

@keyframes float2 {
	0%, 100% { transform: translateX(0) rotate(0deg); }
	50% { transform: translateX(15rpx) rotate(-180deg); }
}

@keyframes float3 {
	0%, 100% { transform: translateY(0) rotate(0deg); }
	50% { transform: translateY(10rpx) rotate(120deg); }
}

@keyframes float4 {
	0%, 100% { transform: translateY(0) rotate(45deg); }
	50% { transform: translateY(-15rpx) rotate(225deg); }
}

.title-icon {
	font-size: 60rpx;
	margin-right: 20rpx;
}

.title-text {
	display: flex;
	flex-direction: column;
}

.main-title {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	line-height: 1.2;
}

.sub-title {
	font-size: 26rpx;
	color: #666;
	margin-top: 5rpx;
}

.stats-section {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #667eea;
	line-height: 1;
}

.stat-label {
	font-size: 22rpx;
	color: #999;
	margin-top: 5rpx;
}

/* 左右联动布局 */
.split-layout {
	display: flex;
	height: calc(100vh - 200rpx);
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
	margin: 0 20rpx;
	box-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);
}

/* 左侧面板 */
.left-panel {
	width: 200rpx;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}

.panel-header {
	padding: 25rpx 15rpx 20rpx;
	background: white;
	border-bottom: 1rpx solid #e9ecef;
	text-align: center;
}

.panel-title {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.panel-subtitle {
	font-size: 20rpx;
	color: #999;
}

.group-list {
	flex: 1;
	padding: 10rpx 0;
}

/* 简化的小组列表项 */
.group-item {
	margin: 0 10rpx 15rpx;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	text-align: center;
	position: relative;
}

.group-item.active {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	transform: scale(1.05);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.group-item:active {
	transform: scale(0.95);
}

.simple-group-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.group-level-badge {
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 700;
	min-width: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.simple-group-name {
	font-size: 22rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	line-height: 1.3;
	word-break: break-all;
}

.simple-status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}

.simple-status-dot.active {
	background: #4CAF50;
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}

.simple-status-dot.completed {
	background: #2196F3;
	box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
}

.simple-status-dot.pending {
	background: #FF9800;
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
}

/* 新概念教程样式 */
.concept-tutorial-item {
	margin: 0 10rpx 20rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 15rpx;
	padding: 25rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	position: relative;
	overflow: hidden;
}

.concept-tutorial-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
	pointer-events: none;
}

.concept-tutorial-item.active {
	transform: scale(1.05);
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
	border-color: rgba(255,255,255,0.3);
}

.concept-tutorial-item:active {
	transform: scale(0.95);
}

.concept-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	position: relative;
	z-index: 1;
}

.concept-icon {
	font-size: 32rpx;
	margin-bottom: 5rpx;
}

.concept-title {
	font-size: 22rpx;
	font-weight: 600;
	color: white;
	text-align: center;
	line-height: 1.3;
}

.concept-badge {
	background: rgba(255,255,255,0.2);
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 18rpx;
	font-weight: 500;
}

/* 新概念教程详情页面 */
.concept-detail-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.concept-detail-header {
	position: relative;
	height: 200rpx;
	overflow: hidden;
}

.concept-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
}

.concept-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.2);
	display: flex;
	align-items: center;
	padding: 30rpx;
}

.concept-detail-icon {
	font-size: 60rpx;
	margin-right: 20rpx;
}

.concept-detail-info {
	flex: 1;
	color: white;
}

.concept-detail-title {
	font-size: 36rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}

.concept-detail-subtitle {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}

.concept-progress-info {
	background: rgba(255,255,255,0.2);
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	display: inline-block;
}

.progress-info-text {
	font-size: 20rpx;
	font-weight: 600;
}

/* 学习分类 */
.concept-categories {
	padding: 30rpx;
	flex: 1;
}

.categories-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

/* 简单分类列表样式 */
.simple-category-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.simple-category-item {
	background: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.category-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: #f8f9fa;
	transition: all 0.3s ease;
}

.category-header:active {
	background: #e9ecef;
}

.category-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.category-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
}

.category-name {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
}

.category-toggle {
	padding: 8rpx;
	transition: transform 0.3s ease;
}

.category-toggle.expanded {
	transform: rotate(0deg);
}

.toggle-icon {
	font-size: 20rpx;
	color: #666;
}

/* 课程列表样式 */
.lessons-list {
	background: #fff;
}

.lesson-item {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;
}

.lesson-item:last-child {
	border-bottom: none;
}

.lesson-item:active {
	background: #f8f9fa;
}

.lesson-number {
	width: 40rpx;
	height: 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 20rpx;
	font-weight: 600;
	margin-right: 16rpx;
}

.lesson-content {
	flex: 1;
}

.lesson-title {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.lesson-subtitle {
	font-size: 20rpx;
	color: #666;
}

.lesson-status {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lesson-status.completed {
	background: #52c41a;
}

.lesson-status.pending {
	background: #f0f0f0;
	border: 2rpx solid #d9d9d9;
}

.status-icon {
	font-size: 16rpx;
	color: #fff;
}

.lesson-status.pending .status-icon {
	color: #999;
}

/* 快速操作 */
.concept-actions {
	padding: 0 30rpx 30rpx;
}

.concept-action-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.concept-action-buttons {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.concept-btn {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}

.concept-btn.primary {
	background: linear-gradient(135deg, #4CAF50, #45A049);
	color: white;
}

.concept-btn.secondary {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.concept-btn.tertiary {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}

.concept-btn:active {
	transform: scale(0.98);
}

.concept-btn-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}

.concept-btn-content {
	flex: 1;
}

.concept-btn-title {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}

.concept-btn-desc {
	font-size: 22rpx;
	opacity: 0.9;
}

/* 无权限页面样式 */
.no-permission-page {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.permission-container {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 80rpx 60rpx;
	text-align: center;
	max-width: 600rpx;
	margin: 0 40rpx;
	backdrop-filter: blur(20rpx);
	box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}

.permission-icon {
	font-size: 120rpx;
	margin-bottom: 40rpx;
	opacity: 0.8;
}

.permission-title {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.permission-desc {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 15rpx;
	display: block;
	line-height: 1.5;
}

.permission-hint {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 60rpx;
	display: block;
	line-height: 1.5;
}

.permission-actions {
	display: flex;
	gap: 30rpx;
	justify-content: center;
}

.btn-login, .btn-contact {
	padding: 24rpx 48rpx;
	border-radius: 50rpx;
	font-size: 32rpx;
	border: none;
	min-width: 200rpx;
	transition: all 0.3s ease;
}

.btn-login {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.btn-contact {
	background: #f0f0f0;
	color: #666;
}

.btn-login:active {
	transform: scale(0.95);
}

.btn-contact:active {
	transform: scale(0.95);
}

/* 右侧面板 */
.right-panel {
	flex: 1;
	background: white;
	overflow-y: auto;
}

.detail-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}

/* 详情头部 */
.detail-header {
	position: relative;
	height: 200rpx;
	overflow: hidden;
}

.detail-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.detail-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.3);
	display: flex;
	align-items: center;
	padding: 30rpx;
}

.detail-avatar {
	width: 80rpx;
	height: 80rpx;
	margin-right: 20rpx;
}

.detail-avatar image {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
}

.detail-info {
	flex: 1;
	color: white;
}

.detail-title {
	font-size: 36rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}

.detail-subtitle {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}

.detail-level {
	background: rgba(255,255,255,0.2);
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
	font-weight: 600;
	display: inline-block;
}

/* 统计卡片 */
.detail-stats {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}

.stat-card-detail {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.stat-card-detail:active {
	background: #e9ecef;
	transform: scale(0.95);
}

.stat-icon-detail {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}

.stat-number-detail {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.stat-label-detail {
	font-size: 22rpx;
	color: #666;
}

/* 进度详情 */
.progress-detail {
	padding: 0 30rpx 30rpx;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.progress-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.progress-value {
	font-size: 28rpx;
	font-weight: 700;
	color: #667eea;
}

.progress-bar-detail {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 15rpx;
}

.progress-fill-detail {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-desc {
	font-size: 22rpx;
	color: #666;
	text-align: center;
}

/* 功能按钮 */
.detail-actions {
	flex: 1;
	padding: 0 30rpx 30rpx;
}

.action-row {
	margin-bottom: 15rpx;
}

.action-btn-large {
	display: flex;
	align-items: center;
	padding: 25rpx;
	border-radius: 20rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.action-btn-large.primary {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.action-btn-large.secondary {
	background: linear-gradient(135deg, #4ECDC4, #44A08D);
	color: white;
}

.action-btn-large.tertiary {
	background: linear-gradient(135deg, #45B7D1, #96C93D);
	color: white;
}

.action-btn-large.quaternary {
	background: linear-gradient(135deg, #FFA726, #FB8C00);
	color: white;
}

.action-btn-large:active {
	transform: scale(0.98);
}

.btn-icon-large {
	font-size: 40rpx;
	margin-right: 20rpx;
}

.btn-content {
	flex: 1;
}

.btn-title {
	font-size: 28rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}

.btn-desc {
	font-size: 22rpx;
	opacity: 0.9;
}

.btn-arrow-large {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 空状态 */
.empty-detail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 15rpx;
}

.empty-desc {
	font-size: 24rpx;
	color: #999;
	line-height: 1.5;
}

/* 美化的小组卡片 */
.beautiful-group-card {
	position: relative;
	background: white;
	border-radius: 25rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.08);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
}

.beautiful-group-card:active {
	transform: scale(0.98);
}

.beautiful-group-card.selected {
	transform: translateY(-8rpx);
	box-shadow: 0 20rpx 40rpx rgba(102, 126, 234, 0.2);
}

.card-decoration {
	position: absolute;
	top: 0;
	right: 0;
	width: 100rpx;
	height: 100rpx;
	border-radius: 0 25rpx 0 100rpx;
	opacity: 0.1;
}

/* 美化的小组头部 */
.beautiful-group-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 25rpx;
}

.group-avatar-section {
	display: flex;
	align-items: center;
	flex: 1;
}

.avatar-container {
	position: relative;
	margin-right: 20rpx;
}

.group-avatar-img {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
}

.level-badge {
	position: absolute;
	bottom: -8rpx;
	right: -8rpx;
	color: white;
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
}

.group-basic-info {
	flex: 1;
}

.group-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.3;
	margin-bottom: 8rpx;
}

.group-subtitle {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}

.status-indicator {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: #f8f9fa;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.status-indicator.active .status-dot {
	background: #4CAF50;
}

.status-indicator.completed .status-dot {
	background: #2196F3;
}

.status-indicator.pending .status-dot {
	background: #FF9800;
}

.status-text {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}

/* 美化的统计数据 */
.beautiful-stats {
	display: flex;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.stat-card {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx 15rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}

.stat-card:active {
	background: #e9ecef;
	transform: scale(0.95);
}

.stat-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.stat-info {
	flex: 1;
}

.stat-value {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	line-height: 1;
}

.stat-name {
	font-size: 22rpx;
	color: #999;
	margin-top: 4rpx;
}

/* 美化的进度条 */
.beautiful-progress {
	margin-bottom: 25rpx;
}

.progress-label {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.progress-text {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.progress-percent {
	font-size: 24rpx;
	color: #333;
	font-weight: 600;
}

.progress-track {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-bar-fill {
	height: 100%;
	border-radius: 4rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 美化的操作按钮 */
.beautiful-actions {
	display: flex;
	gap: 15rpx;
}

.action-button {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 18rpx 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.action-button.primary {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.action-button.secondary {
	background: #f8f9fa;
	color: #333;
	border: 2rpx solid #e9ecef;
}

.action-button:active {
	transform: scale(0.95);
}

.action-button.primary:active {
	background: linear-gradient(135deg, #5a6fd8, #6a42a0);
}

.action-button.secondary:active {
	background: #e9ecef;
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	flex: 1;
	text-align: center;
	font-size: 26rpx;
	font-weight: 500;
}

.btn-arrow {
	font-size: 20rpx;
	opacity: 0.8;
}

/* 操作提示样式 */
.operation-tips {
	padding: 20rpx;
	margin-top: 20rpx;
}

.tips-card {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.05);
	backdrop-filter: blur(10rpx);
}

.tips-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
}

.tips-content {
	flex: 1;
}

.tips-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.tips-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.group-avatar {
	position: relative;
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.group-level {
	position: absolute;
	bottom: -10rpx;
	right: -10rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 5rpx 10rpx;
	border-radius: 10rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.group-status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.stat-number {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.stat-label {
	font-size: 22rpx;
	color: #999;
}

.progress-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin: 15rpx 0;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.group-actions {
	display: flex;
	gap: 15rpx;
	margin-top: 20rpx;
}

.action-btn {
	flex: 1;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 15rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.action-icon {
	display: block;
	font-size: 24rpx;
	margin-bottom: 5rpx;
}

.action-text {
	font-size: 22rpx;
	color: #666;
}

.selected-group-content {
	background: white;
	margin: 30rpx 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.content-header {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.content-title {
	font-size: 28rpx;
	font-weight: 600;
}

.view-toggle {
	display: flex;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	overflow: hidden;
}

.toggle-btn {
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	transition: all 0.3s ease;
}

.toggle-btn.active {
	background: white;
	color: #667eea;
}

.date-filter {
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.date-picker {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 15rpx 20rpx;
}

.date-icon {
	font-size: 24rpx;
	margin-right: 10rpx;
}

.date-text {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}

.date-arrow {
	font-size: 20rpx;
	color: #999;
}

.course-list {
	padding: 20rpx 30rpx;
}

.course-item {
	display: flex;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	transition: all 0.3s ease;
}

.course-thumbnail {
	position: relative;
	width: 160rpx;
	height: 120rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-right: 20rpx;
}

.play-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-icon {
	color: white;
	font-size: 24rpx;
}

.course-duration {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	font-size: 20rpx;
}

.course-details {
	flex: 1;
}

.course-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.course-date {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.course-teacher {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.course-tags {
	display: flex;
	gap: 8rpx;
}

.tag {
	background: #667eea;
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 20rpx;
}
</style>
