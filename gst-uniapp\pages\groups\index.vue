<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1 clean-groups-page">
			<!-- 有权限时显示内容 -->
			<view v-if="hasGroupPermission" class="page-content">
				<!-- 简洁的页面头部 -->
				<view class="page-header">
					<view class="header-content">
						<view class="title-section">
							<text class="page-title">🎓 GST派遣日语培训班</text>
							<text class="page-subtitle">与同伴一起进步，共同成长</text>
						</view>
						<view class="stats-section">
							<view class="stat-item">
								<text class="stat-number">{{groupList.length}}</text>
								<text class="stat-label">个班</text>
							</view>
							<!-- <view class="stat-item">
								<text class="stat-number">{{totalMembers}}</text>
								<text class="stat-label">名成员</text>
							</view>
							<view class="stat-item">
								<text class="stat-number">48</text>
								<text class="stat-label">门课程</text>
							</view> -->
						</view>
					</view>
				</view>

				<!-- 左右联动布局 -->
				<view class="split-layout">
					<!-- 左侧小组列表 -->
					<view class="left-panel">
						<view class="panel-header">
							<text class="panel-title">学习内容</text>
							<text class="panel-subtitle">{{groupList.length + 1}}项</text>
						</view>
						<scroll-view
							class="group-list"
							scroll-y="true"
							:enable-back-to-top="true"
							:scroll-with-animation="true"
							:enhanced="true"
							:bounces="true"
							:show-scrollbar="false"
						>
							<!-- 公共新概念教程 -->
							<view class="concept-tutorial-item" :class="{ 'active': selectedGroupId === 'concept' }"
								@click="selectConceptTutorial()">
								<view class="concept-content">
									<view class="concept-icon">📖</view>
									<text class="concept-title">新标日本语教程</text>
									<view class="concept-badge">公共</view>
								</view>
							</view>
							<view class="group-item" v-for="(group, index) in groupList" :key="index"
								:class="{ 'active': selectedGroupId === group.id }"
								@click="selectGroupForDetail(group, index)">
								<view class="simple-group-content">
									<!-- <view class="group-level-badge" :style="{ background: getGroupColor(index) }">
										{{group.name}}
									</view> -->
									<text class="simple-group-name">{{group.name}}</text>
									<view class="simple-status-dot" :class="group.status"></view>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 右侧详情面板 -->
					<view class="right-panel">
						<!-- 新概念教程详情 -->
						<view v-if="selectedGroupId === 'concept'" class="concept-detail-content">
							<!-- 教程头部 -->
							<view class="concept-detail-header">
								<view class="concept-bg">
									<view class="concept-overlay">
										<view class="concept-detail-icon">📖</view>
										<view class="concept-detail-info">
											<text class="concept-detail-title">{{conceptTutorial.title}}</text>
											<text class="concept-detail-subtitle">{{conceptTutorial.description}}</text>
											<!-- <view class="concept-progress-info">
												<text class="progress-info-text">
													{{conceptTutorial.completedLessons}} / {{conceptTutorial.totalLessons}} 课时
												</text>
											</view> -->
										</view>
									</view>
								</view>
							</view>

							<!-- 学习内容 - 吸顶二级列表 -->
							<view class="concept-categories">
								<view class="categories-title">学习内容</view>

								<!-- 吸顶分类导航 -->
								<view class="sticky-nav">
									<scroll-view class="nav-scroll" scroll-x="true" :enhanced="true" :bounces="false" :show-scrollbar="false">
										<view class="nav-items">
											<view
												class="nav-item"
												:class="{ 'active': activeNavIndex === categoryIndex }"
												v-for="(category, categoryIndex) in currentCategoryList"
												:key="categoryIndex"
												@click="scrollToCategory(categoryIndex)"
											>
												<text class="nav-icon">{{category.icon}}</text>
												<text class="nav-text">{{category.title}}</text>
											</view>
										</view>
									</scroll-view>
								</view>

								<!-- 滚动内容区域 -->
								<scroll-view
									class="content-scroll"
									scroll-y="true"
									@scroll="onContentScroll"
									:scroll-top="scrollTop"
									:scroll-with-animation="true"
									:enable-back-to-top="true"
									:enhanced="true"
									:bounces="true"
									:show-scrollbar="true"
								>
									<view class="scroll-content">
										<view
											class="category-section"
											v-for="(category, categoryIndex) in currentCategoryList"
											:key="categoryIndex"
											:id="'category-' + categoryIndex"
										>
											<!-- 分类标题（吸顶） -->
											<view class="section-header" :class="{ 'sticky': stickyIndex === categoryIndex }">
												<view class="header-content">
													<view class="header-left">
														<text class="header-icon">{{category.icon}}</text>
														<text class="header-title">{{category.title}}</text>
													</view>
													<view class="header-right">
														<text class="lesson-count">{{category.li ? category.li.length : 0}}课时</text>
													</view>
												</view>
											</view>

											<!-- 课程列表 -->
											<view class="section-lessons">
												<view
													class="lesson-card"
													v-for="(lesson, lessonIndex) in category.li"
													:key="lessonIndex"
													@click="enterLesson(category, lesson)"
												>
													<view class="lesson-left">
														<view class="lesson-number">{{lessonIndex + 1}}</view>
														<view class="lesson-info">
															<text class="lesson-title">{{lesson.title}}</text>
															<text class="lesson-subtitle">{{lesson.subtitle}}</text>
														</view>
													</view>
													<view class="lesson-right">
														<view class="lesson-status" :class="lesson.completed ? 'completed' : 'pending'">
															<text class="status-icon">{{lesson.completed ? '✓' : '○'}}</text>
														</view>
													</view>
												</view>
											</view>
										</view>
									</view>
								</scroll-view>
							</view>


						</view>

						<!-- 小组详情 - 简单列表结构 -->
						<view v-else-if="selectedGroup" class="detail-content">
							<!-- 小组信息头部 -->
							<view class="group-header">
								<view class="group-info">
									<view class="group-avatar">
										<image :src="selectedGroup.icon" mode="aspectFit" />
									</view>
									<view class="group-details">
										<text class="group-name">{{selectedGroup.name}}</text>
										<text class="group-desc">{{selectedGroup.description}}</text>
										<view class="group-stats">
											<!-- <text class="stat-item">{{selectedGroup.num}}人</text> -->
											<!-- <text class="stat-item">{{selectedGroup.courseCount}}课程</text> -->
											<!-- <text class="stat-item">{{selectedGroup.progress}}%进度</text> -->
										</view>
									</view>
								</view>
							</view>

							<!-- 学习内容 - 吸顶二级列表 -->
							<view class="group-categories">
								<view class="categories-title">学习内容</view>

								<!-- 吸顶分类导航 -->
								<view class="sticky-nav">
									<scroll-view class="nav-scroll" scroll-x="true" :enhanced="true" :bounces="false" :show-scrollbar="false">
										<view class="nav-items">
											<view
												class="nav-item"
												:class="{ 'active': activeNavIndex === categoryIndex }"
												v-for="(category, categoryIndex) in currentCategoryList"
												:key="categoryIndex"
												@click="scrollToCategory(categoryIndex)"
											>
												<text class="nav-icon">{{category.icon}}</text>
												<text class="nav-text">{{category.title}}</text>
											</view>
										</view>
									</scroll-view>
								</view>

								<!-- 滚动内容区域 -->
								<scroll-view
									class="content-scroll"
									scroll-y="true"
									@scroll="onContentScroll"
									:scroll-top="scrollTop"
									:scroll-with-animation="true"
									:enable-back-to-top="true"
									:enhanced="true"
									:bounces="true"
									:show-scrollbar="true"
								>
									<view class="scroll-content">
										<view
											class="category-section"
											v-for="(category, categoryIndex) in currentCategoryList"
											:key="categoryIndex"
											:id="'category-' + categoryIndex"
										>
											<!-- 分类标题（吸顶） -->
											<view class="section-header" :class="{ 'sticky': stickyIndex === categoryIndex }">
												<view class="header-content">
													<view class="header-left">
														<text class="header-icon">{{category.icon}}</text>
														<text class="header-title">{{category.title}}</text>
													</view>
													<view class="header-right">
														<text class="lesson-count">{{category.li ? category.li.length : 0}}课时</text>
													</view>
												</view>
											</view>

											<!-- 课程列表 -->
											<view class="section-lessons">
												<view
													class="lesson-card"
													v-for="(lesson, lessonIndex) in category.li"
													:key="lessonIndex"
													@click="enterLesson(category, lesson)"
												>
													<view class="lesson-left">
														<view class="lesson-number">{{lessonIndex + 1}}</view>
														<view class="lesson-info">
															<text class="lesson-title">{{lesson.title}}</text>
															<text class="lesson-subtitle">{{lesson.subtitle || ''}}</text>
														</view>
													</view>
													<view class="lesson-right">
														<view class="lesson-status" :class="lesson.completed ? 'completed' : 'pending'">
															<text class="status-icon">{{lesson.completed ? '✓' : '○'}}</text>
														</view>
													</view>
												</view>
											</view>
										</view>
									</view>
								</scroll-view>
							</view>
						</view>

						<!-- 未选择状态 -->
						<view v-else class="empty-detail">
							<view class="empty-icon">👈</view>
							<text class="empty-title">选择一个小组</text>
							<text class="empty-desc">点击左侧小组查看详细信息</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 无权限提示 -->
			<view v-else class="no-permission-page">
				<view class="permission-container">
					<view class="permission-icon">🔒</view>
					<text class="permission-title">访问受限</text>
					<text class="permission-desc">您暂时没有访问学习小组的权限</text>
					<text class="permission-hint">请联系管理员开通权限或使用授权账号登录</text>
					<view class="permission-actions">
						<button class="btn-login" @click="goToLogin">重新登录</button>
						<button class="btn-contact" @click="contactAdmin">联系管理员</button>
					</view>
				</view>
			</view>
		</view>
	</gui-page>

	<!-- 自定义底部导航 -->
	<custom-tabbar />
</template>

<script>
	import CustomTabbar from '@/components/custom-tabbar.vue';

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				pageLoading: false,
				hasGroupPermission: false,
				selectedGroupId: 'concept',
				selectedGroup: null,
				selectedGroupIndex: 0,
				groupsLoading: false,

				// 吸顶列表相关数据
				activeNavIndex: 0,
				stickyIndex: 0,
				scrollTop: 0,
				// 新概念教程数据
				conceptTutorial: {
					id: 'concept',
					title: '新标日本语教程',
					description: '适合所有学员的基础教程',
					totalLessons: 30,
					completedLessons: 8,
				},
				// 新概念教程的分类列表数据
				conceptCategoryList: [{
						id: 'speaking',
						name: '口语',
						icon: '🗣️',
						expanded: true, // 默认展开第一个
						lessons: [{
								id: 1,
								title: '第一课',
								subtitle: '基础问候语',
								completed: true
							},
							{
								id: 2,
								title: '第二课',
								subtitle: '自我介绍',
								completed: true
							},
							{
								id: 3,
								title: '第三课',
								subtitle: '日常对话',
								completed: false
							},
							{
								id: 4,
								title: '第四课',
								subtitle: '购物用语',
								completed: false
							},
							{
								id: 5,
								title: '第五课',
								subtitle: '餐厅用语',
								completed: false
							}
						]
					},
					{
						id: 'grammar',
						name: '语法',
						icon: '📝',
						expanded: false,
						lessons: [{
								id: 6,
								title: '第一课',
								subtitle: '基本句型',
								completed: true
							},
							{
								id: 7,
								title: '第二课',
								subtitle: '动词变位',
								completed: false
							},
							{
								id: 8,
								title: '第三课',
								subtitle: '形容词活用',
								completed: false
							},
							{
								id: 9,
								title: '第四课',
								subtitle: '助词用法',
								completed: false
							},
							{
								id: 10,
								title: '第五课',
								subtitle: '敬语表达',
								completed: false
							}
						]
					},
					{
						id: 'vocabulary',
						name: '词汇',
						icon: '📚',
						expanded: false,
						lessons: [{
								id: 11,
								title: '第一课',
								subtitle: '基础词汇',
								completed: true
							},
							{
								id: 12,
								title: '第二课',
								subtitle: '生活词汇',
								completed: false
							},
							{
								id: 13,
								title: '第三课',
								subtitle: '工作词汇',
								completed: false
							},
							{
								id: 14,
								title: '第四课',
								subtitle: '学习词汇',
								completed: false
							},
							{
								id: 15,
								title: '第五课',
								subtitle: '旅游词汇',
								completed: false
							}
						]
					},
					{
						id: 'listening',
						name: '听力',
						icon: '🎧',
						expanded: false,
						lessons: [{
								id: 16,
								title: '第一课',
								subtitle: '基础听力',
								completed: false
							},
							{
								id: 17,
								title: '第二课',
								subtitle: '对话听力',
								completed: false
							},
							{
								id: 18,
								title: '第三课',
								subtitle: '新闻听力',
								completed: false
							},
							{
								id: 19,
								title: '第四课',
								subtitle: '故事听力',
								completed: false
							},
							{
								id: 20,
								title: '第五课',
								subtitle: '综合听力',
								completed: false
							}
						]
					},
					{
						id: 'reading',
						name: '阅读',
						icon: '📖',
						expanded: false,
						lessons: [{
								id: 21,
								title: '第一课',
								subtitle: '短文阅读',
								completed: false
							},
							{
								id: 22,
								title: '第二课',
								subtitle: '新闻阅读',
								completed: false
							},
							{
								id: 23,
								title: '第三课',
								subtitle: '小说阅读',
								completed: false
							},
							{
								id: 24,
								title: '第四课',
								subtitle: '说明文阅读',
								completed: false
							},
							{
								id: 25,
								title: '第五课',
								subtitle: '综合阅读',
								completed: false
							}
						]
					},
					{
						id: 'writing',
						name: '写作',
						icon: '✍️',
						expanded: false,
						lessons: [{
								id: 26,
								title: '第一课',
								subtitle: '基础写作',
								completed: false
							},
							{
								id: 27,
								title: '第二课',
								subtitle: '日记写作',
								completed: false
							},
							{
								id: 28,
								title: '第三课',
								subtitle: '书信写作',
								completed: false
							},
							{
								id: 29,
								title: '第四课',
								subtitle: '作文写作',
								completed: false
							},
							{
								id: 30,
								title: '第五课',
								subtitle: '应用写作',
								completed: false
							}
						]
					}
				],
				// 小组专用的分类列表数据
				groupCategoryList: [{
						id: 'n5-basic',
						name: 'N5基础',
						icon: '🌱',
						expanded: true,
						lessons: [{
								id: 101,
								title: '第一课',
								subtitle: '五十音图（あ行）',
								completed: true
							},
							{
								id: 102,
								title: '第二课',
								subtitle: '五十音图（か行）',
								completed: true
							},
							{
								id: 103,
								title: '第三课',
								subtitle: '五十音图（さ行）',
								completed: false
							},
							{
								id: 104,
								title: '第四课',
								subtitle: '五十音图（た行）',
								completed: false
							},
							{
								id: 105,
								title: '第五课',
								subtitle: '五十音图（な行）',
								completed: false
							}
						]
					},
					{
						id: 'n5-grammar',
						name: 'N5语法',
						icon: '📖',
						expanded: false,
						lessons: [{
								id: 106,
								title: '第一课',
								subtitle: 'です/である',
								completed: true
							},
							{
								id: 107,
								title: '第二课',
								subtitle: '名词+は+名词',
								completed: false
							},
							{
								id: 108,
								title: '第三课',
								subtitle: '疑问词',
								completed: false
							},
							{
								id: 109,
								title: '第四课',
								subtitle: '数字和时间',
								completed: false
							},
							{
								id: 110,
								title: '第五课',
								subtitle: '动词现在时',
								completed: false
							}
						]
					},
					{
						id: 'n5-vocabulary',
						name: 'N5词汇',
						icon: '📚',
						expanded: false,
						lessons: [{
								id: 111,
								title: '第一课',
								subtitle: '家族称呼',
								completed: true
							},
							{
								id: 112,
								title: '第二课',
								subtitle: '职业名称',
								completed: false
							},
							{
								id: 113,
								title: '第三课',
								subtitle: '日常用品',
								completed: false
							},
							{
								id: 114,
								title: '第四课',
								subtitle: '食物饮料',
								completed: false
							},
							{
								id: 115,
								title: '第五课',
								subtitle: '颜色形状',
								completed: false
							}
						]
					},
					{
						id: 'n5-conversation',
						name: 'N5会话',
						icon: '💬',
						expanded: false,
						lessons: [{
								id: 116,
								title: '第一课',
								subtitle: '初次见面',
								completed: false
							},
							{
								id: 117,
								title: '第二课',
								subtitle: '日常问候',
								completed: false
							},
							{
								id: 118,
								title: '第三课',
								subtitle: '购物对话',
								completed: false
							},
							{
								id: 119,
								title: '第四课',
								subtitle: '餐厅点餐',
								completed: false
							},
							{
								id: 120,
								title: '第五课',
								subtitle: '问路指路',
								completed: false
							}
						]
					}
				],
				// 当前显示的分类列表（动态切换）
				currentCategoryList: [],
				currentView: 'review', // 'review' 或 'practice'
				selectedDate: '',
				currentPracticeType: 'all',
				groupList: [],
				// 练习类型
				practiceTypes: [{
						id: 'listening',
						name: '听力练习',
						icon: '🎧',
						count: 25
					},
					{
						id: 'grammar',
						name: '语法练习',
						icon: '📝',
						count: 30
					},
					{
						id: 'vocabulary',
						name: '词汇练习',
						icon: '📚',
						count: 40
					},
					{
						id: 'speaking',
						name: '口语练习',
						icon: '🗣️',
						count: 15
					}
				],
				// 课程回顾数据
				reviewCourses: [{
						id: 1,
						title: '第一课：基础发音练习',
						date: '2024-01-15',
						teacher: '田中老师',
						duration: '45:30',
						thumbnail: '/static/imgs/course-thumb1.png',
						groupId: 1
					},
					{
						id: 2,
						title: '第二课：日常问候语',
						date: '2024-01-16',
						teacher: '佐藤老师',
						duration: '38:20',
						thumbnail: '/static/imgs/course-thumb2.png',
						groupId: 1
					},
					{
						id: 3,
						title: '第三课：数字与时间',
						date: '2024-01-17',
						teacher: '山田老师',
						duration: '42:15',
						thumbnail: '/static/imgs/course-thumb3.png',
						groupId: 2
					},
					{
						id: 4,
						title: '第四课：家族称呼',
						date: '2024-01-18',
						teacher: '田中老师',
						duration: '39:45',
						thumbnail: '/static/imgs/course-thumb4.png',
						groupId: 2
					}
				],
				// 练习数据
				practices: [{
						id: 1,
						title: '五十音图听力练习',
						date: '2024-01-15',
						type: '听力练习',
						questionCount: 20,
						duration: 15,
						status: 'completed',
						groupId: 1
					},
					{
						id: 2,
						title: '基础语法选择题',
						date: '2024-01-16',
						type: '语法练习',
						questionCount: 25,
						duration: 20,
						status: 'in-progress',
						groupId: 1
					}
				],
				ver: this.$store.state.ver,
				second:0
			}
		},
		computed: {
			// 总成员数
			totalMembers() {
				return this.groupList.reduce((total, group) => total + group.num, 0);
			},

			// 过滤后的课程回顾
			filteredReviewCourses() {
				let courses = this.reviewCourses.filter(course =>
					!this.selectedGroup || course.groupId === this.selectedGroup.id
				);

				if (this.selectedDate) {
					courses = courses.filter(course => course.date === this.selectedDate);
				}

				return courses.sort((a, b) => new Date(b.date) - new Date(a.date));
			},

			// 过滤后的练习
			filteredPractices() {
				let practices = this.practices.filter(practice =>
					!this.selectedGroup || practice.groupId === this.selectedGroup.id
				);

				if (this.currentPracticeType !== 'all') {
					practices = practices.filter(practice =>
						practice.type === this.practiceTypes.find(t => t.id === this.currentPracticeType)?.name
					);
				}

				return practices.sort((a, b) => new Date(b.date) - new Date(a.date));
			}
		},
		async onLoad() {
			this.checkGroupAccess();
			await this.initializeData();
		},
		async onShow() {
			if (!this.$store.state.user.token) {			
			} else {
				this.$store.dispatch('refreshUserMember');
			}
			// 每次显示页面时都检查权限，确保权限状态是最新的
			// 但首先确保用户数据已经从本地存储恢复
			this.$store.commit('initUserData');

			this.checkGroupAccess();
			// 刷新静态数据
			if (this.hasGroupPermission) {
				if(this.second==0){
					this.initializeData();
					this.second++;
				}else{
					this.loadStaticGroupData();
				}
				
			}
		},
		methods: {
			// 滚动到指定分类
			scrollToCategory(categoryIndex) {
				this.activeNavIndex = categoryIndex;

				// 计算目标位置
				const query = uni.createSelectorQuery().in(this);
				query.select(`#category-${categoryIndex}`).boundingClientRect((data) => {
					if (data) {
						this.scrollTop = data.top - 100; // 减去导航栏高度
					}
				}).exec();
			},

			// 监听内容滚动
			onContentScroll(e) {
				const scrollTop = e.detail.scrollTop;

				// 根据滚动位置更新活跃的导航项和吸顶状态
				this.updateActiveNav(scrollTop);
			},

			// 更新活跃的导航项
			updateActiveNav(scrollTop) {
				// 根据滚动位置计算当前应该高亮的导航项
				// 简化实现：每200px切换一个分类
				const newIndex = Math.floor(scrollTop / 200);
				if (newIndex !== this.activeNavIndex && newIndex < this.currentCategoryList.length) {
					this.activeNavIndex = newIndex;
					this.stickyIndex = newIndex;
				}
			},

			// 进入课程学习
			enterLesson(category, lesson) {
				console.log('进入课程:', category.title, lesson.title);

				// 跳转到美化的播放页面
				uni.navigateTo({
					url: `/pages/groups/lesson-player?lessonId=${lesson.id}&categoryId=${category.id}&categoryName=${encodeURIComponent(category.title)}`
				});
			},

			// 检查用户权限
			checkPermission() {
				console.log('=== 开始检查小组权限 ===');

				// 检查用户是否登录
				const userToken = this.$store.state.user.token;
				const userInfo = this.$store.state.user.userInfo;
				const hasLogin = this.$store.getters.hasLogin;

				console.log('权限检查数据:', {
					userToken: userToken ? '存在' : '不存在',
					userInfo: userInfo,
					hasLogin: hasLogin
				});

				if (!userToken && !hasLogin) {
					console.log('用户未登录，拒绝访问');
					this.hasGroupPermission = false;
					return;
				}

				// 检查用户是否有小组权限
				this.checkGroupAccess();
			},

			// 检查小组访问权限 - 检查会员状态
			checkGroupAccess() {
				const userInfo = this.$store.state.user.userInfo;
							const userMember = this.$store.state.user.member;
							const userToken = this.$store.state.user.token;
							const hasLogin = this.$store.getters.hasLogin;
							const isLoggedIn = this.$store.getters.isLoggedIn;
				
							console.log('=== 检查小组访问权限 ===');
							console.log('用户Token:', userToken ? '存在' : '不存在');
							console.log('用户信息:', userInfo);
							console.log('会员信息:', userMember);
							console.log('登录状态:', { hasLogin, isLoggedIn });
				
							// 从本地存储再次确认数据
							const localToken = uni.getStorageSync('token');
							const localUserInfo = uni.getStorageSync('userInfo');
							const localMember = uni.getStorageSync('userMember');
							console.log('本地存储数据:', {
								hasLocalToken: !!localToken,
								hasLocalUserInfo: !!localUserInfo,
								hasLocalMember: !!localMember
							});
				
							// 检查用户是否登录
							const userLoggedIn = hasLogin || isLoggedIn || (userInfo && userInfo.id) || userToken;
				
							if (!userLoggedIn) {
								console.log('❌ 用户未登录，无权限访问小组');
								this.hasGroupPermission = false;
								this.showLoginTip();
								return;
							}
				
							// 检查用户是否有会员权限（优先使用内存中的数据，如果没有则使用本地存储）
							const memberToCheck = userMember || localMember;
							const hasMemberPermission = this.checkMemberPermission(memberToCheck);
				
							console.log('会员权限检查:', {
								memberToCheck,
								hasMemberPermission
							});
				
							if (hasMemberPermission) {
								console.log('✅ 用户有会员权限，可以访问小组');
								this.hasGroupPermission = true;
				
								// 如果内存中没有会员信息但本地有，则同步到内存
								if (!userMember && localMember) {
									console.log('同步本地会员信息到内存');
									this.$store.commit('setUserMember', localMember);
								}
							} else {
								console.log('❌ 用户没有会员权限，无法访问小组');
								this.hasGroupPermission = false;
								this.showMemberTip();
							}
				
							console.log('=== 权限检查完成，结果:', this.hasGroupPermission, '===');
			},

			// 检查会员权限
			checkMemberPermission(memberInfo) {
				// 特殊用户权限（用于测试）
				const userInfo = this.$store.state.user.userInfo;
				if (userInfo && (userInfo.id === 576 || userInfo.name === '彭伟')) {
					console.log('✅ 特殊用户权限，允许访问');
					return true;
				}

				if (!memberInfo) {
					console.log('没有会员信息');
					return false;
				}

				// 检查会员是否有效
				const now = new Date();
				const endDate = new Date(memberInfo.end_date);

				console.log('会员有效期检查:', {
					now: now.toISOString(),
					endDate: endDate.toISOString(),
					isValid: endDate > now
				});

				// 如果会员还在有效期内，则有权限
				return endDate > now;
			},

			// 显示登录提示
			showLoginTip() {
				uni.showToast({
					title: '请先登录',
					icon: 'none',
					duration: 2000
				});
			},

			// 显示会员提示
			showMemberTip() {
				const userMember = this.$store.state.user.member;
				let message = '需要会员权限才能访问学习小组';

				if (userMember) {
					const endDate = new Date(userMember.end_date);
					const now = new Date();
					if (endDate < now) {
						message = '您的会员已过期，请续费后访问学习小组';
					}
				}

				uni.showToast({
					title: message,
					icon: 'none',
					duration: 3000
				});
			},

			// 初始化数据 - 使用静态数据
			initializeData() {
				if (this.hasGroupPermission) {
					// 使用静态数据，不需要API调用
					this.loadStaticGroupData();
					// 默认选择新概念教程（会自动加载对应的分类数据）
					this.selectConceptTutorial();
				}
			},

			// 加载静态小组数据
			loadStaticGroupData() {
				console.log('📋 加载静态小组数据...');

				// 静态小组数据
				// this.groupList = [
				// 	{
				// 		id: 1,
				// 		name: 'N5基础班',
				// 		description: '适合零基础学员',
				// 		level: 'N5',
				// 		icon: '/static/imgs/group-n5.png',
				// 		memberCount: 28,
				// 		courseCount: 12,
				// 		progress: 65,
				// 		status: 'active'
				// 	},
				// 	{
				// 		id: 2,
				// 		name: 'N4进阶班',
				// 		description: '有一定基础的学员',
				// 		level: 'N4',
				// 		icon: '/static/imgs/group-n4.png',
				// 		memberCount: 22,
				// 		courseCount: 15,
				// 		progress: 45,
				// 		status: 'active'
				// 	},
				// 	{
				// 		id: 3,
				// 		name: 'N3提高班',
				// 		description: '中级水平学员',
				// 		level: 'N3',
				// 		icon: '/static/imgs/group-n3.png',
				// 		memberCount: 18,
				// 		courseCount: 18,
				// 		progress: 30,
				// 		status: 'active'
				// 	},
				// 	{
				// 		id: 4,
				// 		name: '商务日语班',
				// 		description: '职场日语专项训练',
				// 		level: '商务',
				// 		icon: '/static/imgs/group-business.png',
				// 		memberCount: 15,
				// 		courseCount: 10,
				// 		progress: 20,
				// 		status: 'active'
				// 	}
				// ];

				// console.log('✅ 静态小组数据加载完成，共', this.groupList.length, '个小组');
				this.$http.get("v1/course/getGroups").then(res => {
					if (res.data.code == 0) {
						this.groupList = res.data.data
					}
				});
			},

			// 模拟新概念教程API接口
			loadConceptTutorialData() {
				// console.log('📡 模拟调用新概念教程API...');

				// // 模拟API延迟
				// await new Promise(resolve => setTimeout(resolve, 500));

				// // 返回新概念教程的分类数据
				// return {
				// 	success: true,
				// 	data: {
				// 		categories: this.conceptCategoryList,
				// 		totalLessons: this.conceptCategoryList.reduce((total, cat) => total + cat.lessons.length, 0),
				// 		completedLessons: this.conceptCategoryList.reduce((total, cat) =>
				// 			total + cat.lessons.filter(lesson => lesson.completed).length, 0
				// 		)
				// 	}
				// };
				this.$http.get("v1/course/getGroupsCourse").then(res => {
					if (res.data.code == 0) {
						this.conceptCategoryList = res.data.data.class
						this.currentCategoryList = res.data.data.class
						this.currentCategoryList.forEach(item => {
							item.expanded = false; // 添加新字段及其值
						});
					}
				});
			},

			// 模拟小组课程API接口
			loadGroupCourseData(groupId) {
				console.log('📡 模拟调用小组课程API，小组ID:', groupId);

				// // 模拟API延迟
				// await new Promise(resolve => setTimeout(resolve, 500));

				// // 根据不同小组返回不同的课程数据
				// let categoryData = JSON.parse(JSON.stringify(this.groupCategoryList)); // 深拷贝

				// // 根据小组级别调整课程内容
				// if (groupId === 2) { // N4进阶班
				// 	categoryData = categoryData.map(cat => ({
				// 		...cat,
				// 		name: cat.name.replace('N5', 'N4'),
				// 		lessons: cat.lessons.map(lesson => ({
				// 			...lesson,
				// 			subtitle: lesson.subtitle + '（进阶）'
				// 		}))
				// 	}));
				// } else if (groupId === 3) { // N3提高班
				// 	categoryData = categoryData.map(cat => ({
				// 		...cat,
				// 		name: cat.name.replace('N5', 'N3'),
				// 		lessons: cat.lessons.map(lesson => ({
				// 			...lesson,
				// 			subtitle: lesson.subtitle + '（提高）'
				// 		}))
				// 	}));
				// }

				// return {
				// 	success: true,
				// 	data: {
				// 		categories: categoryData,
				// 		groupInfo: this.groupList.find(g => g.id === groupId),
				// 		totalLessons: categoryData.reduce((total, cat) => total + cat.lessons.length, 0),
				// 		completedLessons: categoryData.reduce((total, cat) =>
				// 			total + cat.lessons.filter(lesson => lesson.completed).length, 0
				// 		)
				// 	}
				// };
				this.$http.get("v1/course/getGroupsCourse", {
					params: {
						id: groupId

					}
				}).then(res => {
					if (res.data.code == 0) {
						this.groupCategoryList = res.data.data.class
						this.currentCategoryList = res.data.data.class
						this.currentCategoryList.forEach(item => {
							item.expanded = false; // 添加新字段及其值
						});
					}
				});
			},



			// 进入新概念分类学习 - 静态版本
			enterConceptCategory(category) {
				console.log('进入新概念分类:', category);

				// 显示提示信息
				uni.showToast({
					title: `即将开放${category.name}课程`,
					icon: 'none',
					duration: 2000
				});

				// 可以跳转到课程列表页面（如果有的话）
				// uni.navigateTo({
				//     url: `/pages/concept/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`
				// });
			},

			// 开始新概念学习 - 静态版本
			startConceptLearning() {
				console.log('开始新概念学习');

				uni.showToast({
					title: '开始学习：口语 - 第一课',
					icon: 'none',
					duration: 2000
				});

				// 可以跳转到具体的课程页面
				// uni.navigateTo({
				//     url: '/pages/concept/lesson?lessonId=1&isFirst=true'
				// });
			},

			// 继续新概念学习 - 静态版本
			continueConceptLearning() {
				console.log('继续新概念学习');

				uni.showToast({
					title: '继续学习：语法 - 第二课',
					icon: 'none',
					duration: 2000
				});

				// 可以跳转到上次学习的课程
				// uni.navigateTo({
				//     url: '/pages/concept/lesson?lessonId=7&continue=true'
				// });
			},

			// 查看新概念学习进度 - 静态版本
			viewConceptProgress() {
				console.log('查看学习进度');

				uni.showToast({
					title: '学习进度：已完成8/30课时',
					icon: 'none',
					duration: 2000
				});

				// 可以跳转到进度页面
				// uni.navigateTo({
				//     url: '/pages/concept/progress'
				// });
			},

			// 选择新概念教程
			selectConceptTutorial() {
				this.selectedGroupId = 'concept';
				this.selectedGroup = null;
				this.selectedGroupIndex = -1;
				this.loadConceptTutorialData();
				// 模拟调用新概念教程API
				// try {
				// 	const response = await this.loadConceptTutorialData();
				// 	if (response.success) {
				// 		this.currentCategoryList = response.data.categories;
				// 		console.log('✅ 新概念教程数据加载成功:', response.data);
				// 	}
				// } catch (error) {
				// 	console.error('❌ 加载新概念教程数据失败:', error);
				// 	// 降级使用静态数据
				// 	this.currentCategoryList = this.conceptCategoryList;
				// }
			},

			// 选择小组用于详情显示（左右联动）
			selectGroupForDetail(group, index) {
				this.selectedGroupId = group.id;
				this.selectedGroup = group;
				this.selectedGroupIndex = index;
				this.loadGroupCourseData(group.id);
				// 模拟调用小组课程API
				// try {
				// 	const response = await this.loadGroupCourseData(group.id);
				// 	if (response.success) {
				// 		this.currentCategoryList = response.data.categories;
				// 		console.log('✅ 小组课程数据加载成功:', group.name, response.data);
				// 	}
				// } catch (error) {
				// 	console.error('❌ 加载小组课程数据失败:', error);
				// 	// 降级使用静态数据
				// 	this.currentCategoryList = this.groupCategoryList;
				// }
			},

			// 选择小组 - 显示操作选择弹窗（保留原有功能）
			selectGroup(group) {
				this.selectedGroupId = group.id;
				this.selectedGroup = group;

				// 显示操作选择弹窗
				uni.showActionSheet({
					title: `${group.name} - 请选择操作`,
					itemList: [
						'🎥 查看课程回顾',
						'✍️ 进入练习题库',
						'📊 查看小组详情',
						'👥 查看小组成员'
					],
					success: (res) => {
						switch (res.tapIndex) {
							case 0:
								this.quickViewReview(group);
								break;
							case 1:
								this.quickViewPractice(group);
								break;
							case 2:
								this.enterGroup(group);
								break;
							case 3:
								this.viewGroupMembers(group);
								break;
						}
					},
					fail: () => {
						// 用户取消选择，重置选中状态
						this.selectedGroupId = null;
						this.selectedGroup = null;
					}
				});
			},

			// 切换视图
			switchView(view) {
				this.currentView = view;
			},

			// 日期选择
			onDateChange(e) {
				this.selectedDate = e.detail.value;
			},

			// 选择练习类型
			selectPracticeType(type) {
				this.currentPracticeType = type.id;
			},

			// 快速查看课程回顾 - 直接跳转
			quickViewReview(group) {
				uni.showToast({
					title: '正在进入课程回顾',
					icon: 'loading',
					duration: 1000
				});

				setTimeout(() => {
					uni.navigateTo({
						url: `/pages/groups/course-review?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`
					});
				}, 500);
			},

			// 快速查看练习 - 直接跳转
			quickViewPractice(group) {
				uni.showToast({
					title: '正在进入练习题库',
					icon: 'loading',
					duration: 1000
				});

				setTimeout(() => {
					uni.navigateTo({
						url: `/pages/groups/practice?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`
					});
				}, 500);
			},

			// 播放课程回顾视频
			playCourseReview(course) {
				uni.navigateTo({
					url: `/pages/groups/course-review?courseId=${course.id}&groupId=${course.groupId}`
				});
			},

			// 开始练习
			startPractice(practice) {
				uni.navigateTo({
					url: `/pages/groups/practice?practiceId=${practice.id}&groupId=${practice.groupId}`
				});
			},

			// 查看小组成员
			viewGroupMembers(group) {
				uni.showToast({
					title: '正在加载成员列表',
					icon: 'loading',
					duration: 1000
				});

				setTimeout(() => {
					uni.navigateTo({
						url: `/pages/groups/members?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`
					});
				}, 500);
			},

			// 获取小组颜色
			getGroupColor(index) {
				const colors = [
					'linear-gradient(135deg, #FF6B6B, #EE4437)', // 红色
					'linear-gradient(135deg, #4ECDC4, #44A08D)', // 青色
					'linear-gradient(135deg, #45B7D1, #96C93D)', // 蓝绿色
					'linear-gradient(135deg, #FFA726, #FB8C00)', // 橙色
					'linear-gradient(135deg, #AB47BC, #8E24AA)' // 紫色
				];
				return colors[index % colors.length];
			},



			// 跳转到登录页面
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},

			// 联系管理员
			contactAdmin() {
				uni.showModal({
					title: '联系管理员',
					content: '请通过以下方式联系管理员开通权限：\n\n微信：admin123\n电话：400-123-4567\n邮箱：<EMAIL>',
					showCancel: false,
					confirmText: '我知道了'
				});
			},

			// 进入小组详情
			async enterGroup(group) {
				try {
					// 检查用户是否已登录
					const userToken = uni.getStorageSync('token');
					if (!userToken) {
						uni.showModal({
							title: '需要登录',
							content: '请先登录后再查看小组详情',
							confirmText: '去登录',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/login/login'
									});
								}
							}
						});
						return;
					}

					// 跳转到小组详情页面
					uni.navigateTo({
						url: `/pages/groups/group-detail?groupId=${group.id}&groupName=${group.name}`
					});

				} catch (error) {
					console.error('进入小组详情失败:', error);
					uni.showToast({
						title: '进入失败，请重试',
						icon: 'none'
					});
				}
			},

			// 加入小组
			async joinGroup(group) {
				try {
					// 检查用户是否已登录
					const userToken = uni.getStorageSync('token');
					if (!userToken) {
						uni.showModal({
							title: '需要登录',
							content: '请先登录后再加入小组',
							confirmText: '去登录',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/login/login'
									});
								}
							}
						});
						return;
					}

					// 静态版本 - 直接显示成功提示
					uni.showToast({
						title: `成功加入${group.name}`,
						icon: 'success'
					});

					// 刷新静态数据
					this.loadStaticGroupData();

				} catch (error) {
					console.error('加入小组失败:', error);
					// API服务已经显示了错误提示，这里不需要重复显示
				}
			}
		}
	}
</script>

<style scoped>
	/* ===== 基础页面样式 ===== */
	.clean-groups-page {
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
		min-height: 100vh;
	}

	.page-content {
		position: relative;
		z-index: 1;
	}

	/* ===== 页面头部样式 ===== */
	.page-header {
		background: white;
		border-radius: 0 0 30rpx 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.header-content {
		padding: 40rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.title-section {
		flex: 1;
	}

	.page-title {
		font-size: 32rpx;
		font-weight: 700;
		color: #333;
		margin-bottom: 8rpx;
		display: block;
		text-align: left;
	}

	.page-subtitle {
		font-size: 22rpx;
		color: #666;
	}

	.stats-section {
		display: flex;
		gap: 25rpx;
	}

	.stat-item {
		text-align: center;
	}

	.stat-number {
		font-size: 28rpx;
		font-weight: 700;
		color: #667eea;
		display: block;
		line-height: 1;
	}

	.stat-label {
		font-size: 18rpx;
		color: #999;
		margin-top: 5rpx;
	}

	/* ===== 左右联动布局 ===== */
	.split-layout {
		display: flex;
		height: calc(100vh - 200rpx);
		background: white;
		border-radius: 20rpx 20rpx 0 0;
		overflow: hidden;
		margin: 0 20rpx;
		box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.1);
		/* iPad滚动优化 */
		-webkit-overflow-scrolling: touch;
		touch-action: pan-y;
	}

	/* ===== 左侧面板 ===== */
	.left-panel {
		width: 200rpx;
		background: #f8f9fa;
		border-right: 1rpx solid #e9ecef;
		display: flex;
		flex-direction: column;
	}

	.panel-header {
		padding: 25rpx 15rpx 20rpx;
		background: white;
		border-bottom: 1rpx solid #e9ecef;
		text-align: center;
	}

	.panel-title {
		font-size: 22rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 5rpx;
	}

	.panel-subtitle {
		font-size: 18rpx;
		color: #999;
	}

	.group-list {
		flex: 1;
		padding: 10rpx 0;
		/* iPad滚动优化 */
		-webkit-overflow-scrolling: touch;
		touch-action: pan-y;
		overscroll-behavior: contain;
	}

	/* ===== 小组列表项 ===== */
	.group-item {
		margin: 0 10rpx 15rpx;
		background: white;
		border-radius: 12rpx;
		padding: 20rpx 15rpx;
		transition: all 0.3s ease;
		border: 2rpx solid transparent;
		text-align: center;
		position: relative;
	}

	.group-item.active {
		border-color: #667eea;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
		transform: scale(1.05);
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
	}

	.group-item:active {
		transform: scale(0.95);
	}

	.simple-group-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 12rpx;
	}

	.group-level-badge {
		color: white;
		padding: 8rpx 12rpx;
		border-radius: 12rpx;
		font-size: 18rpx;
		font-weight: 700;
		min-width: 40rpx;
		text-align: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
	}

	.simple-group-name {
		font-size: 20rpx;
		font-weight: 600;
		color: #333;
		text-align: center;
		line-height: 1.3;
		word-break: break-all;
	}

	.simple-status-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		position: absolute;
		top: 10rpx;
		right: 10rpx;
	}

	.simple-status-dot.active {
		background: #4CAF50;
		box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
	}

	.simple-status-dot.completed {
		background: #2196F3;
		box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
	}

	.simple-status-dot.pending {
		background: #FF9800;
		box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
	}

	.groups-container {
		padding: 30rpx;
	}

	.page-title {
		font-size: 48rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 40rpx;
	}

	.groups-grid {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
	}

	.group-card {
		background: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		display: flex;
		align-items: center;
		position: relative;
	}

	.group-icon {
		width: 120rpx;
		height: 120rpx;
		margin-right: 30rpx;
		border-radius: 20rpx;
		overflow: hidden;
		background: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.group-icon image {
		width: 80rpx;
		height: 80rpx;
	}

	.group-info {
		flex: 1;
	}

	.group-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.group-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		line-height: 1.4;
	}

	.group-stats {
		display: flex;
		gap: 20rpx;
	}

	.stat-item {
		font-size: 24rpx;
		color: #999;
		background: #f8f8f8;
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
	}

	.group-status {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
	}

	.group-status.active {
		background: #e8f5e8;
		color: #52c41a;
	}

	.group-status.completed {
		background: #f0f0f0;
		color: #999;
	}

	.group-status.pending {
		background: #fff7e6;
		color: #fa8c16;
	}

	/* 简洁清爽的样式 */
	.clean-groups-page {
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
		min-height: 100vh;
	}

	.page-content {
		position: relative;
		z-index: 1;
	}

	/* 简洁的头部样式 */
	.page-header {
		background: white;
		border-radius: 0 0 30rpx 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.header-content {
		padding: 40rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.title-section {
		flex: 1;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: 700;
		color: #333;
		margin-bottom: 8rpx;
		display: block;
		text-align: left;
	}

	.page-subtitle {
		font-size: 24rpx;
		color: #666;
	}

	.stats-section {
		display: flex;
		gap: 30rpx;
	}

	.stat-item {
		text-align: center;
	}

	.stat-number {
		font-size: 32rpx;
		font-weight: 700;
		color: #667eea;
		display: block;
		line-height: 1;
	}

	.stat-label {
		font-size: 20rpx;
		color: #999;
		margin-top: 5rpx;
	}

	.header-content-wrapper {
		position: relative;
		z-index: 2;
		padding: 60rpx 30rpx 50rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.title-section-enhanced {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.title-icon-container {
		position: relative;
		margin-right: 25rpx;
	}

	.icon-bg-circle {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 50%;
		opacity: 0.2;
	}

	.title-icon-enhanced {
		position: relative;
		z-index: 2;
		font-size: 70rpx;
		display: block;
		text-align: center;
		line-height: 100rpx;
	}

	.icon-pulse {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 100rpx;
		height: 100rpx;
		border: 3rpx solid rgba(102, 126, 234, 0.3);
		border-radius: 50%;
		animation: iconPulse 3s ease-in-out infinite;
	}

	@keyframes iconPulse {

		0%,
		100% {
			transform: translate(-50%, -50%) scale(1);
			opacity: 0.7;
		}

		50% {
			transform: translate(-50%, -50%) scale(1.2);
			opacity: 0.3;
		}
	}

	.title-text-enhanced {
		position: relative;
	}

	.main-title-enhanced {
		font-size: 52rpx;
		font-weight: 800;
		color: #333;
		line-height: 1.2;
		margin-bottom: 8rpx;
		background: linear-gradient(135deg, #333, #667eea);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.sub-title-enhanced {
		font-size: 28rpx;
		color: #666;
		line-height: 1.4;
		margin-bottom: 15rpx;
	}

	.title-decoration {
		width: 80rpx;
		height: 6rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 3rpx;
		animation: decorationGlow 2s ease-in-out infinite;
	}

	@keyframes decorationGlow {

		0%,
		100% {
			box-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);
		}

		50% {
			box-shadow: 0 0 20rpx rgba(102, 126, 234, 0.6);
		}
	}

	.stats-section-enhanced {
		display: flex;
		gap: 20rpx;
		flex-wrap: wrap;
	}

	.stat-card-enhanced {
		position: relative;
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 25rpx 20rpx;
		min-width: 120rpx;
		text-align: center;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.3);
		box-shadow:
			0 8rpx 25rpx rgba(0, 0, 0, 0.1),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		overflow: hidden;
	}

	.stat-card-enhanced:hover {
		transform: translateY(-5rpx);
		box-shadow:
			0 15rpx 35rpx rgba(0, 0, 0, 0.15),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
	}

	.stat-icon-bg {
		font-size: 32rpx;
		margin-bottom: 10rpx;
		display: block;
	}

	.stat-number-enhanced {
		font-size: 36rpx;
		font-weight: 800;
		color: #333;
		line-height: 1;
		margin-bottom: 5rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.stat-label-enhanced {
		font-size: 22rpx;
		color: #666;
		font-weight: 500;
	}

	.stat-sparkle {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 8rpx;
		height: 8rpx;
		background: #667eea;
		border-radius: 50%;
		animation: sparkle 2s ease-in-out infinite;
	}

	@keyframes sparkle {

		0%,
		100% {
			opacity: 0.3;
			transform: scale(1);
		}

		50% {
			opacity: 1;
			transform: scale(1.5);
		}
	}

	/* 装饰元素 */
	.header-decorations {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		z-index: 1;
	}

	.deco-circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
	}

	.deco-1 {
		width: 60rpx;
		height: 60rpx;
		top: 20rpx;
		right: 80rpx;
		animation: float1 6s ease-in-out infinite;
	}

	.deco-2 {
		width: 40rpx;
		height: 40rpx;
		bottom: 30rpx;
		left: 60rpx;
		animation: float2 8s ease-in-out infinite;
	}

	.deco-triangle {
		position: absolute;
		width: 0;
		height: 0;
		border-left: 15rpx solid transparent;
		border-right: 15rpx solid transparent;
		border-bottom: 25rpx solid rgba(255, 255, 255, 0.1);
	}

	.deco-3 {
		top: 60rpx;
		left: 40rpx;
		animation: float3 7s ease-in-out infinite;
	}

	.deco-square {
		position: absolute;
		background: rgba(255, 255, 255, 0.1);
		transform: rotate(45deg);
	}

	.deco-4 {
		width: 20rpx;
		height: 20rpx;
		bottom: 60rpx;
		right: 40rpx;
		animation: float4 5s ease-in-out infinite;
	}

	@keyframes float1 {

		0%,
		100% {
			transform: translateY(0) rotate(0deg);
		}

		50% {
			transform: translateY(-20rpx) rotate(180deg);
		}
	}

	@keyframes float2 {

		0%,
		100% {
			transform: translateX(0) rotate(0deg);
		}

		50% {
			transform: translateX(15rpx) rotate(-180deg);
		}
	}

	@keyframes float3 {

		0%,
		100% {
			transform: translateY(0) rotate(0deg);
		}

		50% {
			transform: translateY(10rpx) rotate(120deg);
		}
	}

	@keyframes float4 {

		0%,
		100% {
			transform: translateY(0) rotate(45deg);
		}

		50% {
			transform: translateY(-15rpx) rotate(225deg);
		}
	}

	.title-icon {
		font-size: 60rpx;
		margin-right: 20rpx;
	}

	.title-text {
		display: flex;
		flex-direction: column;
	}

	.main-title {
		font-size: 48rpx;
		font-weight: 700;
		color: #333;
		line-height: 1.2;
	}

	.sub-title {
		font-size: 26rpx;
		color: #666;
		margin-top: 5rpx;
	}

	.stats-section {
		display: flex;
		gap: 30rpx;
	}

	.stat-item {
		text-align: center;
	}

	.stat-number {
		display: block;
		font-size: 36rpx;
		font-weight: 700;
		color: #667eea;
		line-height: 1;
	}

	.stat-label {
		font-size: 22rpx;
		color: #999;
		margin-top: 5rpx;
	}

	/* 左右联动布局 */
	.split-layout {
		display: flex;
		height: calc(100vh - 200rpx);
		background: white;
		border-radius: 20rpx 20rpx 0 0;
		overflow: hidden;
		margin: 0 20rpx;
		box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	/* 左侧面板 */
	.left-panel {
		width: 200rpx;
		background: #f8f9fa;
		border-right: 1rpx solid #e9ecef;
		display: flex;
		flex-direction: column;
	}

	.panel-header {
		padding: 25rpx 15rpx 20rpx;
		background: white;
		border-bottom: 1rpx solid #e9ecef;
		text-align: center;
	}

	.panel-title {
		font-size: 24rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 5rpx;
	}

	.panel-subtitle {
		font-size: 20rpx;
		color: #999;
	}

	.group-list {
		flex: 1;
		padding: 10rpx 0;
	}

	/* 简化的小组列表项 */
	.group-item {
		margin: 0 10rpx 15rpx;
		background: white;
		border-radius: 12rpx;
		padding: 20rpx 15rpx;
		transition: all 0.3s ease;
		border: 2rpx solid transparent;
		text-align: center;
		position: relative;
	}

	.group-item.active {
		border-color: #667eea;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
		transform: scale(1.05);
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
	}

	.group-item:active {
		transform: scale(0.95);
	}

	.simple-group-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 12rpx;
	}

	.group-level-badge {
		color: white;
		padding: 8rpx 12rpx;
		border-radius: 12rpx;
		font-size: 20rpx;
		font-weight: 700;
		min-width: 40rpx;
		text-align: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
	}

	.simple-group-name {
		font-size: 22rpx;
		font-weight: 600;
		color: #333;
		text-align: center;
		line-height: 1.3;
		word-break: break-all;
	}

	.simple-status-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		position: absolute;
		top: 10rpx;
		right: 10rpx;
	}

	.simple-status-dot.active {
		background: #4CAF50;
		box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
	}

	.simple-status-dot.completed {
		background: #2196F3;
		box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
	}

	.simple-status-dot.pending {
		background: #FF9800;
		box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
	}

	/* 新概念教程样式 */
	.concept-tutorial-item {
		margin: 0 10rpx 20rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 15rpx;
		padding: 25rpx 15rpx;
		transition: all 0.3s ease;
		border: 2rpx solid transparent;
		position: relative;
		overflow: hidden;
	}

	.concept-tutorial-item::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
		pointer-events: none;
	}

	.concept-tutorial-item.active {
		transform: scale(1.05);
		box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
		border-color: rgba(255, 255, 255, 0.3);
	}

	.concept-tutorial-item:active {
		transform: scale(0.95);
	}

	.concept-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 12rpx;
		position: relative;
		z-index: 1;
	}

	.concept-icon {
		font-size: 32rpx;
		margin-bottom: 5rpx;
	}

	.concept-title {
		font-size: 22rpx;
		font-weight: 600;
		color: white;
		text-align: center;
		line-height: 1.3;
	}

	.concept-badge {
		background: rgba(255, 255, 255, 0.2);
		color: white;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
		font-size: 18rpx;
		font-weight: 500;
	}

	/* 新概念教程详情页面 */
	.concept-detail-content {
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.concept-detail-header {
		position: relative;
		height: 200rpx;
		overflow: hidden;
	}

	.concept-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea, #764ba2);
	}

	.concept-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.2);
		display: flex;
		align-items: center;
		padding: 30rpx;
	}

	.concept-detail-icon {
		font-size: 60rpx;
		margin-right: 20rpx;
	}

	.concept-detail-info {
		flex: 1;
		color: white;
	}

	.concept-detail-title {
		font-size: 36rpx;
		font-weight: 700;
		display: block;
		margin-bottom: 8rpx;
	}

	.concept-detail-subtitle {
		font-size: 24rpx;
		opacity: 0.9;
		display: block;
		margin-bottom: 10rpx;
	}

	.concept-progress-info {
		background: rgba(255, 255, 255, 0.2);
		padding: 8rpx 15rpx;
		border-radius: 15rpx;
		display: inline-block;
	}

	.progress-info-text {
		font-size: 20rpx;
		font-weight: 600;
	}

	/* 学习分类 */
	.concept-categories {
		padding: 30rpx;
		flex: 1;
	}

	.categories-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
	}

	/* 小组头部样式 */
	.group-header {
		padding: 20rpx;
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.group-info {
		display: flex;
		align-items: center;
	}

	.group-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-right: 20rpx;
		background: #f8f9fa;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.group-avatar image {
		width: 100rpx;
		height: 100rpx;
	}

	.group-details {
		flex: 1;
	}

	.group-name {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}

	.group-desc {
		font-size: 22rpx;
		color: #666;
		display: block;
		margin-bottom: 12rpx;
	}

	.group-stats {
		display: flex;
		gap: 20rpx;
	}

	.stat-item {
		font-size: 20rpx;
		color: #999;
		background: #f8f9fa;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		display: flex;
		
	}

	.group-categories {
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.group-categories .categories-title {
		padding: 20rpx;
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		background: #f8f9fa;
		border-bottom: 1rpx solid #f0f0f0;
	}

	/* 吸顶分类导航样式 */
	.sticky-nav {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;
		position: sticky;
		top: 0;
		z-index: 10;
	}

	.nav-scroll {
		height: 88rpx;
	}

	.nav-items {
		display: flex;
		padding: 0 20rpx;
		white-space: nowrap;
	}

	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 16rpx 24rpx;
		margin-right: 20rpx;
		border-radius: 12rpx;
		transition: all 0.3s ease;
		flex-shrink: 0;
	}

	.nav-item.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
	}

	.nav-icon {
		font-size: 24rpx;
		margin-bottom: 4rpx;
	}

	.nav-text {
		font-size: 20rpx;
		font-weight: 500;
	}

	.nav-item.active .nav-icon,
	.nav-item.active .nav-text {
		color: #fff;
	}

	/* 滚动内容区域样式 */
	.content-scroll {
		height: 600rpx; /* 固定高度，可滚动 */
		background: #f8f9fa;
		/* iPad滚动优化 */
		-webkit-overflow-scrolling: touch;
		touch-action: pan-y;
		overscroll-behavior: contain;
	}

	.scroll-content {
		padding-bottom: 40rpx;
	}

	.category-section {
		margin-bottom: 20rpx;
	}

	/* 分类标题（吸顶）样式 */
	.section-header {
		background: #fff;
		border-bottom: 1rpx solid #f0f0f0;
		position: sticky;
		top: 0;
		z-index: 5;
	}

	.section-header.sticky {
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
	}

	.header-left {
		display: flex;
		align-items: center;
	}

	.header-icon {
		font-size: 32rpx;
		margin-right: 16rpx;
	}

	.header-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}

	.header-right {
		display: flex;
		align-items: center;
	}

	.lesson-count {
		font-size: 22rpx;
		color: #999;
		background: #f0f0f0;
		padding: 6rpx 12rpx;
		border-radius: 12rpx;
	}

	/* 课程列表样式 */
	.section-lessons {
		background: #fff;
	}

	.lesson-card {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
		border-bottom: 1rpx solid #f8f9fa;
		transition: background-color 0.3s ease;
	}

	.lesson-card:active {
		background: #f8f9fa;
	}

	.lesson-card:last-child {
		border-bottom: none;
	}

	.lesson-left {
		display: flex;
		align-items: center;
		flex: 1;
		min-width: 0;
	}

	.lesson-number {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
		color: #1890ff;
		font-size: 22rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		flex-shrink: 0;
	}

	.lesson-info {
		flex: 1;
		min-width: 0;
	}

	.lesson-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.lesson-subtitle {
		font-size: 24rpx;
		color: #666;
		display: block;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.lesson-right {
		display: flex;
		align-items: center;
		margin-left: 20rpx;
	}

	.lesson-status {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.lesson-status.completed {
		background: #52c41a;
	}

	.lesson-status.pending {
		background: #f0f0f0;
		border: 2rpx solid #d9d9d9;
	}

	.status-icon {
		font-size: 20rpx;
		font-weight: 600;
	}

	.lesson-status.completed .status-icon {
		color: #fff;
	}

	.lesson-status.pending .status-icon {
		color: #999;
	}

	.simple-category-item {
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.category-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background: #f8f9fa;
		transition: all 0.3s ease;
	}

	.category-header:active {
		background: #e9ecef;
	}

	.category-left {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.category-icon {
		font-size: 28rpx;
		margin-right: 12rpx;
	}

	.category-name {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
	}

	.category-toggle {
		padding: 8rpx;
		transition: transform 0.3s ease;
	}

	.category-toggle.expanded {
		transform: rotate(0deg);
	}

	.toggle-icon {
		font-size: 20rpx;
		color: #666;
	}

	/* 课程列表样式 */
	.lessons-list {
		background: #fff;
	}

	.lesson-item {
		display: flex;
		align-items: center;
		padding: 16rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		transition: background-color 0.3s ease;
	}

	.lesson-item:last-child {
		border-bottom: none;
	}

	.lesson-item:active {
		background: #f8f9fa;
	}

	.lesson-number {
		width: 40rpx;
		height: 40rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 20rpx;
		font-weight: 600;
		margin-right: 16rpx;
	}

	.lesson-content {
		flex: 1;
	}

	.lesson-title {
		font-size: 24rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 4rpx;
	}

	.lesson-subtitle {
		font-size: 20rpx;
		color: #666;
	}

	.lesson-status {
		width: 32rpx;
		height: 32rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.lesson-status.completed {
		background: #52c41a;
	}

	.lesson-status.pending {
		background: #f0f0f0;
		border: 2rpx solid #d9d9d9;
	}

	.status-icon {
		font-size: 16rpx;
		color: #fff;
	}

	.lesson-status.pending .status-icon {
		color: #999;
	}

	/* 快速操作 */
	.concept-actions {
		padding: 0 30rpx 30rpx;
	}

	.concept-action-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
	}

	.concept-action-buttons {
		display: flex;
		flex-direction: column;
		gap: 15rpx;
	}

	.concept-btn {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 15rpx;
		transition: all 0.3s ease;
	}

	.concept-btn.primary {
		background: linear-gradient(135deg, #4CAF50, #45A049);
		color: white;
	}

	.concept-btn.secondary {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: white;
	}

	.concept-btn.tertiary {
		background: linear-gradient(135deg, #FF9800, #F57C00);
		color: white;
	}

	.concept-btn:active {
		transform: scale(0.98);
	}

	.concept-btn-icon {
		font-size: 32rpx;
		margin-right: 15rpx;
	}

	.concept-btn-content {
		flex: 1;
	}

	.concept-btn-title {
		font-size: 26rpx;
		font-weight: 600;
		display: block;
		margin-bottom: 5rpx;
	}

	.concept-btn-desc {
		font-size: 22rpx;
		opacity: 0.9;
	}

	/* 无权限页面样式 */
	.no-permission-page {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.permission-container {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 30rpx;
		padding: 80rpx 60rpx;
		text-align: center;
		max-width: 600rpx;
		margin: 0 40rpx;
		backdrop-filter: blur(20rpx);
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.permission-icon {
		font-size: 120rpx;
		margin-bottom: 40rpx;
		opacity: 0.8;
	}

	.permission-title {
		font-size: 48rpx;
		font-weight: 700;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.permission-desc {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
		line-height: 1.5;
	}

	.permission-hint {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 60rpx;
		display: block;
		line-height: 1.5;
	}

	.permission-actions {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		gap: 30rpx;
		justify-content: center;
		align-items: center;
		width: 100%;
		margin-top: 40rpx;
	}

	/* 小屏幕适配 */
	@media screen and (max-width: 750rpx) {
		.permission-actions {
			flex-direction: column;
			gap: 20rpx;
		}

		.btn-login,
		.btn-contact {
			width: 280rpx;
			max-width: 100%;
		}
	}

	.btn-login,
	.btn-contact {
		padding: 24rpx 48rpx;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: 500;
		border: none;
		min-width: 200rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		box-sizing: border-box;
	}

	.btn-login {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
	}

	.btn-contact {
		background: #f8f9fa;
		color: #495057;
		border: 2rpx solid #e9ecef;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.btn-login:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
	}

	.btn-contact:active {
		transform: scale(0.95);
		background: #e9ecef;
	}

	/* 右侧面板 */
	.right-panel {
		flex: 1;
		background: white;
		overflow-y: auto;
	}

	.detail-content {
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	/* 详情头部 */
	.detail-header {
		position: relative;
		height: 200rpx;
		overflow: hidden;
	}

	.detail-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.detail-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.3);
		display: flex;
		align-items: center;
		padding: 30rpx;
	}

	.detail-avatar {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}

	.detail-avatar image {
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
	}

	.detail-info {
		flex: 1;
		color: white;
	}

	.detail-title {
		font-size: 36rpx;
		font-weight: 700;
		display: block;
		margin-bottom: 8rpx;
	}

	.detail-subtitle {
		font-size: 24rpx;
		opacity: 0.9;
		display: block;
		margin-bottom: 10rpx;
	}

	.detail-level {
		background: rgba(255, 255, 255, 0.2);
		padding: 6rpx 12rpx;
		border-radius: 15rpx;
		font-size: 20rpx;
		font-weight: 600;
		display: inline-block;
	}

	/* 统计卡片 */
	.detail-stats {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;
	}

	.stat-card-detail {
		flex: 1;
		background: #f8f9fa;
		border-radius: 15rpx;
		padding: 25rpx 20rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.stat-card-detail:active {
		background: #e9ecef;
		transform: scale(0.95);
	}

	.stat-icon-detail {
		font-size: 40rpx;
		margin-bottom: 10rpx;
	}

	.stat-number-detail {
		font-size: 32rpx;
		font-weight: 700;
		color: #333;
		display: block;
		margin-bottom: 5rpx;
	}

	.stat-label-detail {
		font-size: 22rpx;
		color: #666;
	}

	/* 进度详情 */
	.progress-detail {
		padding: 0 30rpx 30rpx;
	}

	.progress-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.progress-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}

	.progress-value {
		font-size: 28rpx;
		font-weight: 700;
		color: #667eea;
	}

	.progress-bar-detail {
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
		margin-bottom: 15rpx;
	}

	.progress-fill-detail {
		height: 100%;
		border-radius: 6rpx;
		transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.progress-desc {
		font-size: 22rpx;
		color: #666;
		text-align: center;
	}

	/* 功能按钮 */
	.detail-actions {
		flex: 1;
		padding: 0 30rpx 30rpx;
	}

	.action-row {
		margin-bottom: 15rpx;
	}

	.action-btn-large {
		display: flex;
		align-items: center;
		padding: 25rpx;
		border-radius: 20rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
	}

	.action-btn-large.primary {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
	}

	.action-btn-large.secondary {
		background: linear-gradient(135deg, #4ECDC4, #44A08D);
		color: white;
	}

	.action-btn-large.tertiary {
		background: linear-gradient(135deg, #45B7D1, #96C93D);
		color: white;
	}

	.action-btn-large.quaternary {
		background: linear-gradient(135deg, #FFA726, #FB8C00);
		color: white;
	}

	.action-btn-large:active {
		transform: scale(0.98);
	}

	.btn-icon-large {
		font-size: 40rpx;
		margin-right: 20rpx;
	}

	.btn-content {
		flex: 1;
	}

	.btn-title {
		font-size: 28rpx;
		font-weight: 600;
		display: block;
		margin-bottom: 5rpx;
	}

	.btn-desc {
		font-size: 22rpx;
		opacity: 0.9;
	}

	.btn-arrow-large {
		font-size: 24rpx;
		opacity: 0.8;
	}

	/* 空状态 */
	.empty-detail {
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx;
		text-align: center;
	}

	.empty-icon {
		font-size: 80rpx;
		margin-bottom: 30rpx;
		opacity: 0.5;
	}

	.empty-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 15rpx;
	}

	.empty-desc {
		font-size: 24rpx;
		color: #999;
		line-height: 1.5;
	}

	/* 美化的小组卡片 */
	.beautiful-group-card {
		position: relative;
		background: white;
		border-radius: 25rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		overflow: hidden;
	}

	.beautiful-group-card:active {
		transform: scale(0.98);
	}

	.beautiful-group-card.selected {
		transform: translateY(-8rpx);
		box-shadow: 0 20rpx 40rpx rgba(102, 126, 234, 0.2);
	}

	.card-decoration {
		position: absolute;
		top: 0;
		right: 0;
		width: 100rpx;
		height: 100rpx;
		border-radius: 0 25rpx 0 100rpx;
		opacity: 0.1;
	}

	/* 美化的小组头部 */
	.beautiful-group-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 25rpx;
	}

	.group-avatar-section {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.avatar-container {
		position: relative;
		margin-right: 20rpx;
	}

	.group-avatar-img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 20rpx;
	}

	.level-badge {
		position: absolute;
		bottom: -8rpx;
		right: -8rpx;
		color: white;
		padding: 6rpx 12rpx;
		border-radius: 15rpx;
		font-size: 20rpx;
		font-weight: 600;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	}

	.group-basic-info {
		flex: 1;
	}

	.group-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		line-height: 1.3;
		margin-bottom: 8rpx;
	}

	.group-subtitle {
		font-size: 26rpx;
		color: #666;
		line-height: 1.4;
	}

	.status-indicator {
		display: flex;
		align-items: center;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		background: #f8f9fa;
	}

	.status-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		margin-right: 8rpx;
	}

	.status-indicator.active .status-dot {
		background: #4CAF50;
	}

	.status-indicator.completed .status-dot {
		background: #2196F3;
	}

	.status-indicator.pending .status-dot {
		background: #FF9800;
	}

	.status-text {
		font-size: 22rpx;
		color: #666;
		font-weight: 500;
	}

	/* 美化的统计数据 */
	.beautiful-stats {
		display: flex;
		gap: 15rpx;
		margin-bottom: 25rpx;
	}

	.stat-card {
		flex: 1;
		background: #f8f9fa;
		border-radius: 15rpx;
		padding: 20rpx 15rpx;
		display: flex;
		align-items: center;
		transition: all 0.3s ease;
	}

	.stat-card:active {
		background: #e9ecef;
		transform: scale(0.95);
	}

	.stat-icon {
		font-size: 32rpx;
		margin-right: 12rpx;
	}

	.stat-info {
		flex: 1;
	}

	.stat-value {
		display: block;
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		line-height: 1;
	}

	.stat-name {
		font-size: 22rpx;
		color: #999;
		margin-top: 4rpx;
	}

	/* 美化的进度条 */
	.beautiful-progress {
		margin-bottom: 25rpx;
	}

	.progress-label {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12rpx;
	}

	.progress-text {
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
	}

	.progress-percent {
		font-size: 24rpx;
		color: #333;
		font-weight: 600;
	}

	.progress-track {
		height: 8rpx;
		background: #f0f0f0;
		border-radius: 4rpx;
		overflow: hidden;
	}

	.progress-bar-fill {
		height: 100%;
		border-radius: 4rpx;
		transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
	}

	/* 美化的操作按钮 */
	.beautiful-actions {
		display: flex;
		gap: 15rpx;
	}

	.action-button {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 18rpx 20rpx;
		border-radius: 15rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
	}

	.action-button.primary {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
	}

	.action-button.secondary {
		background: #f8f9fa;
		color: #333;
		border: 2rpx solid #e9ecef;
	}

	.action-button:active {
		transform: scale(0.95);
	}

	.action-button.primary:active {
		background: linear-gradient(135deg, #5a6fd8, #6a42a0);
	}

	.action-button.secondary:active {
		background: #e9ecef;
	}

	.btn-icon {
		font-size: 24rpx;
	}

	.btn-text {
		flex: 1;
		text-align: center;
		font-size: 26rpx;
		font-weight: 500;
	}

	.btn-arrow {
		font-size: 20rpx;
		opacity: 0.8;
	}

	/* 操作提示样式 */
	.operation-tips {
		padding: 20rpx;
		margin-top: 20rpx;
	}

	.tips-card {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 25rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
		backdrop-filter: blur(10rpx);
	}

	.tips-icon {
		font-size: 40rpx;
		margin-right: 20rpx;
	}

	.tips-content {
		flex: 1;
	}

	.tips-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 8rpx;
		display: block;
	}

	.tips-text {
		font-size: 24rpx;
		color: #666;
		line-height: 1.4;
	}

	.group-avatar {
		position: relative;
		width: 100rpx;
		height: 100rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.group-level {
		position: absolute;
		bottom: -10rpx;
		right: -10rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		padding: 5rpx 10rpx;
		border-radius: 10rpx;
		font-size: 20rpx;
		font-weight: 600;
	}

	.group-status-badge {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		font-weight: 500;
	}

	.stat-number {
		display: block;
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}

	.stat-label {
		font-size: 22rpx;
		color: #999;
	}

	.progress-bar {
		height: 8rpx;
		background: #f0f0f0;
		border-radius: 4rpx;
		overflow: hidden;
		margin: 15rpx 0;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 4rpx;
		transition: width 0.3s ease;
	}

	.group-actions {
		display: flex;
		gap: 15rpx;
		margin-top: 20rpx;
	}

	.action-btn {
		flex: 1;
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 15rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.action-icon {
		display: block;
		font-size: 24rpx;
		margin-bottom: 5rpx;
	}

	.action-text {
		font-size: 22rpx;
		color: #666;
	}

	.selected-group-content {
		background: white;
		margin: 30rpx 20rpx;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
	}

	.content-header {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.content-title {
		font-size: 28rpx;
		font-weight: 600;
	}

	.view-toggle {
		display: flex;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 20rpx;
		overflow: hidden;
	}

	.toggle-btn {
		padding: 10rpx 20rpx;
		font-size: 24rpx;
		transition: all 0.3s ease;
	}

	.toggle-btn.active {
		background: white;
		color: #667eea;
	}

	.date-filter {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.date-picker {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 15rpx 20rpx;
	}

	.date-icon {
		font-size: 24rpx;
		margin-right: 10rpx;
	}

	.date-text {
		flex: 1;
		font-size: 26rpx;
		color: #333;
	}

	.date-arrow {
		font-size: 20rpx;
		color: #999;
	}

	.course-list {
		padding: 20rpx 30rpx;
	}

	.course-item {
		display: flex;
		background: #f8f9fa;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		transition: all 0.3s ease;
	}

	.course-thumbnail {
		position: relative;
		width: 160rpx;
		height: 120rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-right: 20rpx;
	}

	.play-overlay {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.play-icon {
		color: white;
		font-size: 24rpx;
	}

	.course-duration {
		position: absolute;
		bottom: 8rpx;
		right: 8rpx;
		background: rgba(0, 0, 0, 0.7);
		color: white;
		padding: 4rpx 8rpx;
		border-radius: 6rpx;
		font-size: 20rpx;
	}

	.course-details {
		flex: 1;
	}

	.course-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 8rpx;
	}

	.course-date {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 8rpx;
	}

	.course-teacher {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.course-tags {
		display: flex;
		gap: 8rpx;
	}

	.tag {
		background: #667eea;
		color: white;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
		font-size: 20rpx;
	}
</style>