const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { CourseMedia, Course, CourseUnit, User } = require('../models');
const { auth, requireTeacher } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');
const AlicloudVodService = require('../services/alicloudVod');

const router = express.Router();
const vodService = new AlicloudVodService();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads/media');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 根据文件类型创建子目录
    const fileType = getFileType(file.mimetype);
    const typeDir = path.join(uploadDir, fileType);
    
    if (!fs.existsSync(typeDir)) {
      fs.mkdirSync(typeDir, { recursive: true });
    }
    
    cb(null, typeDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB限制
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = [
      // 视频格式
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
      // 音频格式
      'audio/mp3', 'audio/wav', 'audio/aac', 'audio/ogg', 'audio/m4a',
      // 文档格式
      'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      // 图片格式
      'image/jpeg', 'image/png', 'image/gif', 'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件格式: ${file.mimetype}`));
    }
  }
});

// 根据MIME类型确定文件类型
function getFileType(mimetype) {
  if (mimetype.startsWith('video/')) return 'video';
  if (mimetype.startsWith('audio/')) return 'audio';
  if (mimetype.startsWith('image/')) return 'image';
  return 'document';
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 上传媒体文件
router.post('/upload', auth, requireTeacher, upload.single('file'), asyncHandler(async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const { courseId, unitId } = req.body;
    
    // 验证课程是否存在
    if (courseId) {
      const course = await Course.findByPk(courseId);
      if (!course) {
        // 清理上传的文件
        fs.unlinkSync(req.file.path);
        return res.status(404).json({
          success: false,
          message: '课程不存在'
        });
      }
    }

    // 验证课程单元是否存在
    if (unitId) {
      const unit = await CourseUnit.findByPk(unitId);
      if (!unit) {
        // 清理上传的文件
        fs.unlinkSync(req.file.path);
        return res.status(404).json({
          success: false,
          message: '课程单元不存在'
        });
      }
    }

    const fileType = getFileType(req.file.mimetype);
    const fileUrl = `/uploads/media/${fileType}/${req.file.filename}`;

    // 创建媒体记录
    const media = await CourseMedia.create({
      courseId: courseId || null,
      unitId: unitId || null,
      fileName: req.file.filename,
      originalName: req.file.originalname,
      fileType: fileType,
      mimeType: req.file.mimetype,
      fileSize: req.file.size,
      filePath: req.file.path,
      fileUrl: fileUrl,
      status: 'ready',
      uploadedBy: req.user.id
    });

    logger.info('媒体文件上传成功', {
      mediaId: media.id,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      fileType: fileType,
      userId: req.user.id
    });

    res.json({
      success: true,
      message: '文件上传成功',
      data: {
        media: {
          id: media.id,
          fileName: media.fileName,
          originalName: media.originalName,
          fileType: media.fileType,
          fileSize: media.fileSize,
          fileSizeFormatted: formatFileSize(media.fileSize),
          fileUrl: media.fileUrl,
          status: media.status,
          uploadedAt: media.createdAt
        }
      }
    });

  } catch (error) {
    // 清理上传的文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    logger.error('媒体文件上传失败', {
      error: error.message,
      fileName: req.file ? req.file.originalname : 'unknown',
      userId: req.user.id
    });

    res.status(500).json({
      success: false,
      message: `文件上传失败: ${error.message}`
    });
  }
}));

// 获取媒体文件列表
router.get('/', auth, asyncHandler(async (req, res) => {
  const { courseId, unitId, fileType, page = 1, limit = 20 } = req.query;
  
  const where = {};
  if (courseId) where.courseId = courseId;
  if (unitId) where.unitId = unitId;
  if (fileType) where.fileType = fileType;
  
  const offset = (page - 1) * limit;
  
  const { count, rows: mediaFiles } = await CourseMedia.findAndCountAll({
    where,
    include: [
      {
        model: Course,
        as: 'course',
        attributes: ['id', 'title']
      },
      {
        model: CourseUnit,
        as: 'unit',
        attributes: ['id', 'title']
      },
      {
        model: User,
        as: 'uploader',
        attributes: ['id', 'username', 'realName']
      }
    ],
    order: [['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: offset
  });

  // 格式化文件大小
  const formattedFiles = mediaFiles.map(file => ({
    ...file.toJSON(),
    fileSizeFormatted: formatFileSize(file.fileSize)
  }));

  res.json({
    success: true,
    data: {
      mediaFiles: formattedFiles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    }
  });
}));

// 删除媒体文件
router.delete('/:id', auth, requireTeacher, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const media = await CourseMedia.findByPk(id);
  if (!media) {
    return res.status(404).json({
      success: false,
      message: '媒体文件不存在'
    });
  }

  // 删除物理文件
  if (fs.existsSync(media.filePath)) {
    fs.unlinkSync(media.filePath);
  }

  // 删除数据库记录
  await media.destroy();

  logger.info('媒体文件删除成功', {
    mediaId: id,
    fileName: media.originalName,
    userId: req.user.id
  });

  res.json({
    success: true,
    message: '媒体文件删除成功'
  });
}));

// 获取上传进度（WebSocket或Server-Sent Events可以实现实时进度）
router.get('/upload-progress/:uploadId', auth, asyncHandler(async (req, res) => {
  // 这里可以实现上传进度查询
  // 暂时返回模拟数据
  res.json({
    success: true,
    data: {
      progress: 100,
      status: 'completed'
    }
  });
}));

// ==================== 阿里云点播相关API ====================

// 获取上传凭证
router.post('/vod/upload-auth', auth, asyncHandler(async (req, res) => {
  const { title, fileName, fileSize } = req.body;

  if (!title || !fileName || !fileSize) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数: title, fileName, fileSize'
    });
  }

  const result = await vodService.getUploadAuth(title, fileName, fileSize);

  if (result.success) {
    res.json({
      success: true,
      data: result.data,
      message: '获取上传凭证成功'
    });
  } else {
    res.status(500).json({
      success: false,
      message: result.message
    });
  }
}));

// 刷新上传凭证
router.post('/vod/refresh-auth', auth, asyncHandler(async (req, res) => {
  const { videoId } = req.body;

  if (!videoId) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数: videoId'
    });
  }

  const result = await vodService.refreshUploadAuth(videoId);

  if (result.success) {
    res.json({
      success: true,
      data: result.data,
      message: '刷新上传凭证成功'
    });
  } else {
    res.status(500).json({
      success: false,
      message: result.message
    });
  }
}));

// 获取视频播放信息
router.get('/vod/play-info/:videoId', auth, asyncHandler(async (req, res) => {
  const { videoId } = req.params;

  const result = await vodService.getPlayInfo(videoId);

  if (result.success) {
    res.json({
      success: true,
      data: result.data,
      message: '获取播放信息成功'
    });
  } else {
    res.status(500).json({
      success: false,
      message: result.message
    });
  }
}));

// 获取视频信息
router.get('/vod/video-info/:videoId', auth, asyncHandler(async (req, res) => {
  const { videoId } = req.params;

  const result = await vodService.getVideoInfo(videoId);

  if (result.success) {
    res.json({
      success: true,
      data: result.data,
      message: '获取视频信息成功'
    });
  } else {
    res.status(500).json({
      success: false,
      message: result.message
    });
  }
}));

// 批量获取上传凭证
router.post('/vod/batch-upload-auth', auth, asyncHandler(async (req, res) => {
  const { videos } = req.body;

  if (!videos || !Array.isArray(videos)) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数: videos (数组)'
    });
  }

  const results = await vodService.batchGetUploadAuth(videos);

  res.json({
    success: true,
    data: results,
    message: '批量获取上传凭证成功'
  });
}));

// 更新课程单元的视频ID
router.post('/vod/update-unit-video', auth, asyncHandler(async (req, res) => {
  const { unitId, videoId, videoUrl, type } = req.body;

  if (!unitId || !videoId) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数: unitId, videoId'
    });
  }

  const updateData = {};
  if (type === 'video') {
    updateData.videoUrl = videoUrl;
    updateData.videoId = videoId;
  } else if (type === 'audio') {
    updateData.audioUrl = videoUrl;
    updateData.audioId = videoId;
  }

  await CourseUnit.update(updateData, {
    where: { id: unitId }
  });

  res.json({
    success: true,
    message: '更新课程单元媒体信息成功'
  });
}));

module.exports = router;
