{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/pages/article/detail/tran.vue?e0b3", "webpack:///D:/gst/gst-uniapp/pages/article/detail/tran.vue?2558", "webpack:///D:/gst/gst-uniapp/pages/article/detail/tran.vue?c238", "webpack:///D:/gst/gst-uniapp/pages/article/detail/tran.vue?9f30", "uni-app:///pages/article/detail/tran.vue", "webpack:///D:/gst/gst-uniapp/pages/article/detail/tran.vue?c739", "webpack:///D:/gst/gst-uniapp/pages/article/detail/tran.vue?c064"], "names": ["components", "lpRecord", "lpAudioPlayer", "props", "type", "default", "article", "data", "isTranslateEdit", "recordIsOpen", "dirty", "tran", "content", "file", "keyboardHeight", "created", "watch", "computed", "source", "methods", "checkInterpretFileUpload", "onUploadstarted", "self", "onUploadSucceed", "onUploadEnd", "url", "coverUrl", "uploader", "submit", "uni", "title", "success", "icon", "onSaveRecordHandler", "onResetInterpretHandler", "onSubmitHandler", "onShowTranHandler", "onTranChangeHandler", "apiSubmitTran", "article_id", "file_name", "video_id"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgnB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2CpoB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAA;IACAC;IACAC;EACA;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MAAA;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;MACAH;MACAC;IACA;EACA;EACAG;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACAC;YACAC;UACA;UACAC;YACAD;UACA;UACAE;YACAF;UACA;QACA;QACA;QACA;UACAG;UACAC;QACA;QACAC;MACA;QACAL;MACA;IACA;IAEAM;MAAA;MACA;QACAC;UACAC;UACAlB;UACAmB;YACA;cACAF;gBACAJ;cACA;YACA;cACA;AACA;AACA;YAFA;UAIA;QACA;MACA;QACA;UACA;QACA;QACA;UACAI;YACAC;UACA;UACA;QACA;QACA;QACA,qGACA;UACA;YACAD;cACAC;cACAE;YACA;UACA;YACAH;cACAC;cACAE;YACA;YACA;UACA;QAEA;MACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACAC;MACA;MACAP;MACAA;MACAA;QACAP;QACAA;MACA;IACA;IACA;AACA;AACA;IACAe;MACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACAC;QACA3B;QACA4B;QACAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvOA;AAAA;AAAA;AAAA;AAA+rC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACAntC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/article/detail/tran.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tran.vue?vue&type=template&id=411e30b2&scoped=true&\"\nvar renderjs\nimport script from \"./tran.vue?vue&type=script&lang=js&\"\nexport * from \"./tran.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tran.vue?vue&type=style&index=0&id=411e30b2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"411e30b2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article/detail/tran.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tran.vue?vue&type=template&id=411e30b2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tran.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tran.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"root-box\">\n\t\t<view class=\"border translate\" @tap=\"onShowTranHandler\">\n\t\t\t<textarea class=\"translate-input\" :value=\"!tran.content ? '点击输入...' : tran.content\" disabled=\"true\" maxlength=\"5000\" autoHeight=\"true\" >\n\t\t\t\t\n\t\t\t</textarea>\n\t\t</view>\n\n\t\t<!-- <view class=\"border interpret\">\n\t\t\t<template v-if=\"tran.file == ''\">\n\t\t\t\t<view class=\"tips\">请点击【录音】开始录音，最长录音10分钟</view>\n\n\t\t\t\t<lp-record class=\"lp-record\" :maxTime=\"600\" :minTime=\"5\" :canPuase=\"true\" :source=\"source\"\n\t\t\t\t\t@recconfirm=\"onSaveRecordHandler\" @open=\"recordIsOpen=1\" @close=\"recordIsOpen=0\">\n\t\t\t\t</lp-record>\n\t\t\t</template>\n\n\t\t\t<template v-if=\"tran.file != ''\">\n\t\t\t\t<view class=\"tips\">我的录音</view>\n\t\t\t\t<view class=\"btns-box lp-flex\">\n\t\t\t\t\t<view class=\"btn-box\" style=\"flex:1;\">\n\t\t\t\t\t\t<lp-audio-player class=\"my-audio\" :mini=\"false\" :nobroud=\"true\" :audio=\"{src:tran.file}\">\n\t\t\t\t\t\t</lp-audio-player>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn-box lp-flex-column lp-flex-center\">\n\t\t\t\t\t\t<view class=\"btn reset\" @tap=\"onResetInterpretHandler\"><text class=\"gui-icons\">&#xe684;</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"label\">重录</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</template>\n\n\t\t</view> -->\n\n\t\t<view class=\"btn-box\">\n\t\t\t<view class=\"btn\" :class=\"{disabled:!dirty}\" @tap=\"onSubmitHandler\">提交</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport lpRecord from '@/components/lp-record/lp-record.vue';\n\timport lpAudioPlayer from '@/components/audio-player/audio-player.vue';\n\timport Uploader from '@/common/js/uploader.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tlpRecord,\n\t\t\tlpAudioPlayer\n\t\t},\n\t\tprops: {\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'original'\n\t\t\t},\n\t\t\tarticle: { //默认地址\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: null\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 笔译编辑中\n\t\t\t\tisTranslateEdit: false,\n\t\t\t\trecordIsOpen: 0,\n\t\t\t\tdirty: false,\n\t\t\t\ttran: {\n\t\t\t\t\tcontent: '',\n\t\t\t\t\tfile: ''\n\t\t\t\t},\n\t\t\t\tkeyboardHeight: 0,\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.tran = this.article.trans.length ? this.article.trans[0] : {\n\t\t\t\tcontent: '',\n\t\t\t\tfile: ''\n\t\t\t};\n\t\t},\n\t\twatch: {},\n\t\tcomputed: {\n\t\t\tsource: function() {\n\t\t\t\treturn this.article[this.type][0].file;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 检查是否需要上传文件\n\t\t\t */\n\t\t\tcheckInterpretFileUpload: function() {\n\t\t\t\tconst self = this;\n\t\t\t\tif (this.tran.need_upload) {\n\t\t\t\t\tlet uploader = Uploader.getUploader({\n\t\t\t\t\t\tonUploadstarted: function(data) {\n\t\t\t\t\t\t\tself.tran.video_id = data.video_id;\n\t\t\t\t\t\t},\n\t\t\t\t\t\tonUploadSucceed: function(data) {\n\t\t\t\t\t\t\tself.tran.file_name = data.object;\n\t\t\t\t\t\t},\n\t\t\t\t\t\tonUploadEnd: function() {\n\t\t\t\t\t\t\tself.submit();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tlet title = \"人民中国打卡作业_\" + this.article.id;\n\t\t\t\t\tvar file = {\n\t\t\t\t\t\turl: this.tran.file,\n\t\t\t\t\t\tcoverUrl: title\n\t\t\t\t\t};\n\t\t\t\t\tuploader.addFile(file, null, null, null, '{\"Vod\":{}}');\n\t\t\t\t} else {\n\t\t\t\t\tself.submit();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsubmit: function() {\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (!this.dirty) {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.tran.content == '' && this.tran.file == '') {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"内容不能为空\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.dirty = false;\r\n\t\t\t\t\tthis.apiSubmitTran(this.article.id, this.tran.content, this.tran.file_name, this.tran.video_id).then(\r\n\t\t\t\t\t\tdata => {\r\n\t\t\t\t\t\t\tif(data.code==0 && data.data!=''){\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: \"提交成功\",\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: \"提交失败，请重新提交\",\r\n\t\t\t\t\t\t\t\t\ticon: 'warn'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.dirty = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}\n\t\t\t\t\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tonSaveRecordHandler: function(path) {\n\t\t\t\tthis.tran.need_upload = true;\n\t\t\t\tthis.tran.file = path;\n\t\t\t\tthis.onTranChangeHandler();\n\t\t\t},\n\t\t\t/**\n\t\t\t * 重置口译音频\n\t\t\t */\n\t\t\tonResetInterpretHandler: function() {\n\t\t\t\tthis.tran.need_upload = false;\n\t\t\t\tthis.tran.file = '';\n\t\t\t\tthis.tran.file_name = '';\n\t\t\t\tthis.tran.video_id = '';\n\t\t\t\tthis.onTranChangeHandler();\n\t\t\t},\n\t\t\t/**\n\t\t\t * 提交答案\n\t\t\t */\n\t\t\tonSubmitHandler: function() {\n\t\t\t\tthis.checkInterpretFileUpload();\n\t\t\t},\n\t\t\tonShowTranHandler: function() {\n\t\t\t\tlet self = this;\n\t\t\t\tuni.$off('lp-input-completed');\n\t\t\t\tuni.$emit('lp-get-input', this.tran.content);\n\t\t\t\tuni.$once('lp-input-completed', function(text) {\n\t\t\t\t\tself.tran.content = text;\n\t\t\t\t\tself.onTranChangeHandler();\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 笔译或者口译有变化时调用\n\t\t\t */\n\t\t\tonTranChangeHandler: function() {\n\t\t\t\tthis.dirty = true;\n\t\t\t\tthis.$emit('dirty');\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 提交作业\n\t\t\t * @param {Object} article_id\n\t\t\t * @param {Object} content\n\t\t\t * @param {Object} file_name\n\t\t\t * @param {Object} video_id\n\t\t\t */\n\t\t\tapiSubmitTran: function(article_id, content, file_name, video_id) {\n\t\t\t\treturn this.$http.post('/v1/article/store', {\n\t\t\t\t\tarticle_id,\n\t\t\t\t\tcontent,\n\t\t\t\t\tfile_name,\n\t\t\t\t\tvideo_id\n\t\t\t\t}).then(res => {\n\t\t\t\t\treturn Promise.resolve(res.data);\n\t\t\t\t});\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.root-box {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: stretch;\n\n\t\t.border {\n\t\t\tposition: relative;\n\t\t\tborder: solid 1px #eee;\n\t\t\tborder-radius: 10rpx;\n\t\t\tpadding: 30rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tz-index: 0;\n\t\t}\n\n\t\t.translate {\n\t\t\t.close-box {\n\t\t\t\tbackground: #28b28b;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tline-height: 100rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t\tpadding: 20rpx;\n\t\t\t}\n\n\t\t\t.translate-input {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tmin-height: 100rpx;\r\n\t\t\t\twidth:100%;\n\t\t\t}\n\t\t}\n\n\t\t.interpret {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\t/* align-items: center; */\n\n\t\t\t.lp-record {\n\t\t\t\tz-index: 99;\n\t\t\t}\n\n\t\t\t.my-audio {\n\t\t\t\tborder: unset;\n\t\t\t}\n\n\t\t\t.tips {\n\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t}\n\n\t\t\t.play {\n\t\t\t\tbackground-color: $uni-color-primary !important;\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 32rpx !important;\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.record {}\n\n\t\t\t.reset {}\n\n\t\t\t.btn-box {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tmargin: 20rpx;\n\n\t\t\t\t.btn {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tbackground-color: $uni-color-error;\n\t\t\t\t\twidth: 96rpx;\n\t\t\t\t\theight: 96rpx;\n\t\t\t\t\tpadding: unset;\n\t\t\t\t\tborder-radius: 50% !important;\n\n\t\t\t\t\t.ws-icon,\n\t\t\t\t\t.gui-icons {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.label {\n\t\t\t\ttext-align: center;\n\t\t\t\tline-height: 40rpx;\n\t\t\t}\n\t\t}\n\n\t\t.btns-box {\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t.btn-box {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\n\t\t\t.btn {\n\t\t\t\tborder: solid 1px $uni-color-error;\n\t\t\t\tbackground-color: $uni-color-error;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tpadding: 20rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder-radius: 40rpx;\n\t\t\t}\n\n\t\t\t.done {\n\t\t\t\tbackground-color: unset;\n\t\t\t\tcolor: $uni-color-error;\n\t\t\t}\n\n\t\t\t.disabled {\n\t\t\t\tbackground-color: $uni-bg-color-grey;\n\t\t\t\tcolor: #aaa;\n\t\t\t\tborder: solid 1px #aaa;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tran.vue?vue&type=style&index=0&id=411e30b2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tran.vue?vue&type=style&index=0&id=411e30b2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689564288\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}