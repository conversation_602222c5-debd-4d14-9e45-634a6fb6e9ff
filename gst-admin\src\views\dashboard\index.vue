<template>
  <div class="page-container">
    <div class="page-header">
      <h1>系统概览</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 加载骨架屏 -->
    <Skeleton v-if="loading" type="dashboard" />

    <!-- 仪表板内容 -->
    <div v-else>
      <!-- 统计卡片 -->
      <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :style="{ backgroundColor: stat.color }">
          <el-icon :size="24">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.changeType">
            <el-icon><component :is="stat.changeIcon" /></el-icon>
            {{ stat.change }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>用户增长趋势</h3>
              <el-select v-model="userChartPeriod" size="small">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </div>
            <div class="chart-content">
              <v-chart 
                class="chart" 
                :option="userGrowthOption" 
                :loading="chartLoading"
                autoresize
              />
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>课程分类分布</h3>
            </div>
            <div class="chart-content">
              <v-chart 
                class="chart" 
                :option="courseDistributionOption" 
                :loading="chartLoading"
                autoresize
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h3>快速操作</h3>
      </div>
      <div class="actions-grid">
        <div 
          class="action-item" 
          v-for="action in quickActions" 
          :key="action.key"
          @click="handleQuickAction(action)"
        >
          <div class="action-icon" :style="{ backgroundColor: action.color }">
            <el-icon :size="20">
              <component :is="action.icon" />
            </el-icon>
          </div>
          <div class="action-content">
            <div class="action-title">{{ action.title }}</div>
            <div class="action-desc">{{ action.description }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 最近活动 -->
    <div class="recent-activities">
      <div class="section-header">
        <h3>最近活动</h3>
        <el-link type="primary" @click="$router.push('/system/logs')">
          查看全部
        </el-link>
      </div>
      <div class="activities-list">
        <div 
          class="activity-item" 
          v-for="activity in recentActivities" 
          :key="activity.id"
        >
          <div class="activity-avatar">
            <el-avatar :size="32" :src="activity.user?.avatar">
              {{ activity.user?.name?.charAt(0) }}
            </el-avatar>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>{{ activity.user?.name }}</strong>
              {{ activity.action }}
              <span class="activity-target">{{ activity.target }}</span>
            </div>
            <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
          </div>
          <div class="activity-status" :class="activity.status">
            <el-tag :type="getStatusType(activity.status)" size="small">
              {{ getStatusText(activity.status) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { get } from '@/utils/request'
import Skeleton from '@/components/Skeleton/index.vue'
import { ElMessage } from 'element-plus'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const userChartPeriod = ref('30d')

// 统计数据
const stats = ref([
  {
    key: 'users',
    label: '用户总数',
    value: 0,
    icon: 'User',
    color: '#409eff',
    change: '+12%',
    changeType: 'increase',
    changeIcon: 'ArrowUp'
  },
  {
    key: 'groups',
    label: '学习小组',
    value: 0,
    icon: 'UserFilled',
    color: '#67c23a',
    change: '+8%',
    changeType: 'increase',
    changeIcon: 'ArrowUp'
  },
  {
    key: 'courses',
    label: '课程总数',
    value: 0,
    icon: 'Reading',
    color: '#e6a23c',
    change: '+15%',
    changeType: 'increase',
    changeIcon: 'ArrowUp'
  },
  {
    key: 'records',
    label: '学习记录',
    value: 0,
    icon: 'DataBoard',
    color: '#f56c6c',
    change: '+25%',
    changeType: 'increase',
    changeIcon: 'ArrowUp'
  }
])

// 快速操作
const quickActions = ref([
  {
    key: 'create-group',
    title: '创建小组',
    description: '快速创建新的学习小组',
    icon: 'Plus',
    color: '#409eff',
    route: '/content/groups'
  },
  {
    key: 'create-course',
    title: '添加课程',
    description: '添加新的课程内容',
    icon: 'DocumentAdd',
    color: '#67c23a',
    route: '/content/courses'
  },
  {
    key: 'create-assignment',
    title: '布置作业',
    description: '为学生布置新作业',
    icon: 'EditPen',
    color: '#e6a23c',
    route: '/content/assignments'
  },
  {
    key: 'manage-users',
    title: '用户管理',
    description: '管理系统用户',
    icon: 'Setting',
    color: '#f56c6c',
    route: '/system/users'
  }
])

// 最近活动
const recentActivities = ref([])

// 图表配置
const userGrowthOption = computed(() => ({
  title: {
    show: false
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '新增用户',
      type: 'line',
      smooth: true,
      data: [12, 19, 15, 25, 32, 28, 35],
      itemStyle: {
        color: '#409eff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ]
        }
      }
    }
  ]
}))

const courseDistributionOption = computed(() => ({
  title: {
    show: false
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '课程分类',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 35, name: '语法', itemStyle: { color: '#409eff' } },
        { value: 28, name: '词汇', itemStyle: { color: '#67c23a' } },
        { value: 20, name: '听力', itemStyle: { color: '#e6a23c' } },
        { value: 12, name: '口语', itemStyle: { color: '#f56c6c' } },
        { value: 5, name: '其他', itemStyle: { color: '#909399' } }
      ]
    }
  ]
}))

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await loadDashboardData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

const loadDashboardData = async () => {
  try {
    const response = await get('/api/dashboard/stats', {}, { showLoading: true })
    if (response.success) {
      // 更新统计数据
      const data = response.data
      stats.value.forEach(stat => {
        if (data[stat.key] !== undefined) {
          stat.value = data[stat.key]
        }
      })
      
      // 更新最近活动
      if (data.recentActivities) {
        recentActivities.value = data.recentActivities
      }
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    // 使用模拟数据
    stats.value[0].value = 156
    stats.value[1].value = 23
    stats.value[2].value = 89
    stats.value[3].value = 1247
    
    recentActivities.value = [
      {
        id: 1,
        user: { name: '张老师', avatar: '' },
        action: '创建了课程',
        target: '日语N5语法基础',
        status: 'success',
        createdAt: new Date(Date.now() - 1000 * 60 * 30)
      },
      {
        id: 2,
        user: { name: '李同学', avatar: '' },
        action: '加入了小组',
        target: 'N4学习小组',
        status: 'success',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2)
      },
      {
        id: 3,
        user: { name: '王老师', avatar: '' },
        action: '发布了作业',
        target: '第三课练习题',
        status: 'pending',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 4)
      }
    ]
  }
}

const handleQuickAction = (action) => {
  if (action.route) {
    router.push(action.route)
  }
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - new Date(time)
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

const getStatusType = (status) => {
  const types = {
    success: 'success',
    pending: 'warning',
    error: 'danger',
    info: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    success: '成功',
    pending: '处理中',
    error: '失败',
    info: '信息'
  }
  return texts[status] || '未知'
}

// 监听图表周期变化
watch(userChartPeriod, () => {
  // 重新加载图表数据
  chartLoading.value = true
  setTimeout(() => {
    chartLoading.value = false
  }, 1000)
})

// 组件挂载时加载数据
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-base);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-light);
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-base);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
  
  .stat-content {
    flex: 1;
    
    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: var(--text-primary);
      line-height: 1;
      margin-bottom: var(--spacing-xs);
    }
    
    .stat-label {
      font-size: var(--font-size-base);
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xs);
    }
    
    .stat-change {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: var(--font-size-small);
      font-weight: 500;
      
      &.increase {
        color: var(--success-color);
      }
      
      &.decrease {
        color: var(--danger-color);
      }
    }
  }
}

.charts-section {
  margin-bottom: var(--spacing-xl);
}

.chart-card {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  overflow: hidden;
  
  .chart-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-base);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    h3 {
      margin: 0;
      font-size: var(--font-size-medium);
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .chart-content {
    padding: var(--spacing-lg);
    
    .chart {
      width: 100%;
      height: 300px;
    }
  }
}

.quick-actions {
  margin-bottom: var(--spacing-xl);
  
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-large);
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-md);
  }
  
  .action-item {
    background: var(--bg-color);
    border-radius: var(--border-radius-base);
    padding: var(--spacing-lg);
    box-shadow: var(--box-shadow-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--box-shadow-light);
    }
    
    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-base);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    
    .action-content {
      flex: 1;
      
      .action-title {
        font-size: var(--font-size-medium);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
      }
      
      .action-desc {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
      }
    }
  }
}

.recent-activities {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-large);
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .activities-list {
    background: var(--bg-color);
    border-radius: var(--border-radius-base);
    box-shadow: var(--box-shadow-base);
    overflow: hidden;
  }
  
  .activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-base);
    
    &:last-child {
      border-bottom: none;
    }
    
    .activity-avatar {
      flex-shrink: 0;
    }
    
    .activity-content {
      flex: 1;
      
      .activity-text {
        font-size: var(--font-size-base);
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        
        .activity-target {
          color: var(--primary-color);
          font-weight: 500;
        }
      }
      
      .activity-time {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
      }
    }
    
    .activity-status {
      flex-shrink: 0;
    }
  }
}
</style>
