{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?8bcc", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?7529", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?de7d", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?0743", "uni-app:///components/custom-tabbar.vue", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?bbc4", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?2bdd"], "names": ["name", "data", "currentTab", "safeAreaBottom", "allTabs", "pagePath", "iconPath", "selected<PERSON><PERSON><PERSON><PERSON>", "text", "member<PERSON><PERSON><PERSON>", "computed", "hasGroupPermission", "visibleTabs", "mounted", "methods", "getCurrentTab", "switchTab", "uni", "url", "success", "fail", "console", "checkMemberPermission", "showMemberTip", "title", "content", "confirmText", "cancelText"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4B/mB;EACAA;EACAC;IACA;MACAC;MACAC;MACA;MACAC,UACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACAC;QACAC;QACAC;UACA;QACA;QACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACAN;QACAO;QACAC;QACAC;QACAC;QACAR;UACA;YACA;YACAF;cACAC;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtLA;AAAA;AAAA;AAAA;AAAg4B,CAAgB,o4BAAG,EAAC,C;;;;;;;;;;;ACAp5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/custom-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./custom-tabbar.vue?vue&type=template&id=da768fe2&scoped=true&\"\nvar renderjs\nimport script from \"./custom-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./custom-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./custom-tabbar.vue?vue&type=style&index=0&id=da768fe2&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"da768fe2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/custom-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=template&id=da768fe2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"custom-tabbar\" :style=\"{ paddingBottom: safeAreaBottom + 'px' }\">\n\t\t<view class=\"tabbar-content\">\n\t\t\t<view\n\t\t\t\tclass=\"tab-item\"\n\t\t\t\tv-for=\"(item, index) in visibleTabs\"\n\t\t\t\t:key=\"index\"\n\t\t\t\t:class=\"{ 'active': currentTab === item.pagePath }\"\n\t\t\t\t@click=\"switchTab(item)\"\n\t\t\t>\n\t\t\t\t<image\n\t\t\t\t\tclass=\"tab-icon\"\n\t\t\t\t\t:src=\"currentTab === item.pagePath ? item.selectedIconPath : item.iconPath\"\n\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t/>\n\t\t\t\t<text class=\"tab-text\" :class=\"{ 'active': currentTab === item.pagePath }\">\n\t\t\t\t\t{{ item.text }}\n\t\t\t\t</text>\n\t\t\t\t<!-- 会员专享标识 -->\n\t\t\t\t<view v-if=\"item.memberOnly && hasGroupPermission\" class=\"member-badge\">\n\t\t\t\t\t<text class=\"member-text\">VIP</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'CustomTabbar',\n\tdata() {\n\t\treturn {\n\t\t\tcurrentTab: '',\n\t\t\tsafeAreaBottom: 0,\n\t\t\t// 所有可能的tab配置\n\t\t\tallTabs: [\n\t\t\t\t{\n\t\t\t\t\tpagePath: \"pages/index/index\",\n\t\t\t\t\ticonPath: \"/static/tab-home.png\",\n\t\t\t\t\tselectedIconPath: \"/static/tab-home-current.png\",\n\t\t\t\t\ttext: \"首页\",\n\t\t\t\t\tmemberOnly: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tpagePath: \"pages/category/list-page\",\n\t\t\t\t\ticonPath: \"/static/tab-search.png\",\n\t\t\t\t\tselectedIconPath: \"/static/tab-search-current.png\",\n\t\t\t\t\ttext: \"找课\",\n\t\t\t\t\tmemberOnly: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tpagePath: \"pages/study/study\",\n\t\t\t\t\ticonPath: \"/static/tab-cate.png\",\n\t\t\t\t\tselectedIconPath: \"/static/tab-cate-current.png\",\n\t\t\t\t\ttext: \"学习\",\n\t\t\t\t\tmemberOnly: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tpagePath: \"pages/groups/index\",\n\t\t\t\t\ticonPath: \"/static/tab-my.png\",\n\t\t\t\t\tselectedIconPath: \"/static/tab-my-current.png\",\n\t\t\t\t\ttext: \"小组\",\n\t\t\t\t\tmemberOnly: true // 标记为会员专享\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tpagePath: \"pages/user/user\",\n\t\t\t\t\ticonPath: \"/static/tab-my.png\",\n\t\t\t\t\tselectedIconPath: \"/static/tab-my-current.png\",\n\t\t\t\t\ttext: \"我的\",\n\t\t\t\t\tmemberOnly: false\n\t\t\t\t}\n\t\t\t]\n\t\t};\n\t},\n\tcomputed: {\n\t\t// 获取用户权限状态\n\t\thasGroupPermission() {\n\t\t\tconst userInfo = this.$store.state.user.userInfo;\n\t\t\tconst userMember = this.$store.state.user.member;\n\t\t\tconst hasLogin = this.$store.getters.hasLogin;\n\t\t\tconst isLoggedIn = this.$store.getters.isLoggedIn;\n\t\t\t\n\t\t\t// 检查用户是否登录\n\t\t\tconst userLoggedIn = hasLogin || isLoggedIn || (userInfo && userInfo.id);\n\t\t\tif (!userLoggedIn) return false;\n\t\t\t\n\t\t\t// 检查会员权限\n\t\t\treturn this.checkMemberPermission(userMember);\n\t\t},\n\t\t\n\t\t// 根据权限过滤可见的tabs\n\t\tvisibleTabs() {\n\t\t\treturn this.allTabs.filter(tab => {\n\t\t\t\t// 如果不是会员专享功能，直接显示\n\t\t\t\tif (!tab.memberOnly) return true;\n\t\t\t\t// 如果是会员专享功能，检查权限\n\t\t\t\treturn this.hasGroupPermission;\n\t\t\t});\n\t\t}\n\t},\n\tmounted() {\n\t\t// 获取安全区域\n\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\tthis.safeAreaBottom = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0;\n\t\t\n\t\t// 获取当前页面路径\n\t\tthis.getCurrentTab();\n\t},\n\tmethods: {\n\t\t// 获取当前tab\n\t\tgetCurrentTab() {\n\t\t\tconst pages = getCurrentPages();\n\t\t\tif (pages.length > 0) {\n\t\t\t\tconst currentPage = pages[pages.length - 1];\n\t\t\t\tthis.currentTab = currentPage.route;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换tab\n\t\tswitchTab(item) {\n\t\t\tif (this.currentTab === item.pagePath) return;\n\t\t\t\n\t\t\t// 如果是会员专享功能但没有权限，显示提示\n\t\t\tif (item.memberOnly && !this.hasGroupPermission) {\n\t\t\t\tthis.showMemberTip();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 切换页面\n\t\t\tuni.reLaunch({\n\t\t\t\turl: `/${item.pagePath}`,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.currentTab = item.pagePath;\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('切换tab失败:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 检查会员权限\n\t\tcheckMemberPermission(member) {\n\t\t\tif (!member) return false;\n\t\t\t\n\t\t\t// 检查会员等级\n\t\t\tif (member.level && ['premium', 'vip', 'svip'].includes(member.level.toLowerCase())) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查会员到期时间\n\t\t\tif (member.expireDate) {\n\t\t\t\tconst expireDate = new Date(member.expireDate);\n\t\t\t\tconst now = new Date();\n\t\t\t\treturn expireDate > now;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查会员状态\n\t\t\tif (member.status === 'active' || member.status === 1) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\t\n\t\t\treturn false;\n\t\t},\n\t\t\n\t\t// 显示会员提示\n\t\tshowMemberTip() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '会员专享',\n\t\t\t\tcontent: '小组功能为会员专享，请先开通会员',\n\t\t\t\tconfirmText: '开通会员',\n\t\t\t\tcancelText: '暂不开通',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 跳转到会员开通页面\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/user/member'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.custom-tabbar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: #ffffff;\n\tborder-top: 1rpx solid #e5e5e5;\n\tz-index: 1000;\n}\n\n.tabbar-content {\n\tdisplay: flex;\n\theight: 100rpx;\n\talign-items: center;\n}\n\n.tab-item {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\tpadding: 8rpx 0;\n\ttransition: all 0.3s ease;\n}\n\n.tab-item:active {\n\tbackground: rgba(0, 0, 0, 0.05);\n}\n\n.tab-icon {\n\twidth: 44rpx;\n\theight: 44rpx;\n\tmargin-bottom: 4rpx;\n}\n\n.tab-text {\n\tfont-size: 20rpx;\n\tcolor: #C0C4CC;\n\ttransition: color 0.3s ease;\n}\n\n.tab-text.active {\n\tcolor: #2094CE;\n\tfont-weight: 600;\n}\n\n.member-badge {\n\tposition: absolute;\n\ttop: 2rpx;\n\tright: 20rpx;\n\tbackground: linear-gradient(135deg, #ff6b6b, #ffa500);\n\tborder-radius: 20rpx;\n\tpadding: 2rpx 8rpx;\n\ttransform: scale(0.8);\n}\n\n.member-text {\n\tfont-size: 16rpx;\n\tcolor: #fff;\n\tfont-weight: 600;\n}\n\n/* 动画效果 */\n.tab-item.active .tab-icon {\n\ttransform: scale(1.1);\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=style&index=0&id=da768fe2&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=style&index=0&id=da768fe2&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039746313\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}