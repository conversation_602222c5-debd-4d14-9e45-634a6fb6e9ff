{"version": 3, "sources": [null, "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?7529", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?de7d", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?0743", "uni-app:///components/custom-tabbar.vue", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?bbc4", "webpack:///D:/gst/gst-uniapp/components/custom-tabbar.vue?2bdd"], "names": ["name", "data", "currentTab", "mounted", "console", "uni", "animation", "methods", "goToPage", "url", "success", "fail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8B/mB;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAJ;MACAC;QACAI;QACAC;UACAN;QACA;QACAO;UACAP;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAg4B,CAAgB,o4BAAG,EAAC,C;;;;;;;;;;;ACAp5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/custom-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./custom-tabbar.vue?vue&type=template&id=da768fe2&scoped=true&\"\nvar renderjs\nimport script from \"./custom-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./custom-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./custom-tabbar.vue?vue&type=style&index=0&id=da768fe2&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"da768fe2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/custom-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=template&id=da768fe2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"custom-tabbar\">\n\t\t<!-- 测试：先显示一个简单的红色条 -->\n\t\t<view class=\"test-bar\">\n\t\t\t<text style=\"color: white;\">自定义TabBar测试</text>\n\t\t</view>\n\n\t\t<view class=\"tabbar-content\">\n\t\t\t<!-- 固定显示4个基础菜单，不依赖权限 -->\n\t\t\t<view class=\"tab-item\" @click=\"goToPage('pages/index/index')\">\n\t\t\t\t<text class=\"tab-text\">🏠</text>\n\t\t\t\t<text class=\"tab-text\">首页</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" @click=\"goToPage('pages/category/list-page')\">\n\t\t\t\t<text class=\"tab-text\">🔍</text>\n\t\t\t\t<text class=\"tab-text\">找课</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" @click=\"goToPage('pages/study/study')\">\n\t\t\t\t<text class=\"tab-text\">📚</text>\n\t\t\t\t<text class=\"tab-text\">学习</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" @click=\"goToPage('pages/user/user')\">\n\t\t\t\t<text class=\"tab-text\">👤</text>\n\t\t\t\t<text class=\"tab-text\">我的</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'CustomTabbar',\n\tdata() {\n\t\treturn {\n\t\t\tcurrentTab: ''\n\t\t};\n\t},\n\tmounted() {\n\t\tconsole.log('CustomTabbar mounted - 测试版本');\n\n\t\t// 隐藏原生tabBar\n\t\tuni.hideTabBar({\n\t\t\tanimation: false\n\t\t});\n\t},\n\tmethods: {\n\t\t// 简单的页面跳转\n\t\tgoToPage(pagePath) {\n\t\t\tconsole.log('跳转到页面:', pagePath);\n\t\t\tuni.reLaunch({\n\t\t\t\turl: `/${pagePath}`,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log('跳转成功');\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.custom-tabbar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: #ffffff;\n\tborder-top: 2rpx solid #e5e5e5;\n\tz-index: 99999;\n\tbox-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.15);\n}\n\n.test-bar {\n\tbackground: red;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.tabbar-content {\n\tdisplay: flex;\n\theight: 120rpx;\n\talign-items: center;\n\tbackground: #ffffff;\n}\n\n.tab-item {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 16rpx 0;\n\tcursor: pointer;\n}\n\n.tab-item:active {\n\tbackground: rgba(0, 0, 0, 0.1);\n}\n\n.tab-text {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\tmargin: 4rpx 0;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=style&index=0&id=da768fe2&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./custom-tabbar.vue?vue&type=style&index=0&id=da768fe2&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041277517\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}