{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/scroll-x/index.vue?4286", "webpack:///D:/gst/gst-uniapp/components/scroll-x/index.vue?6e9f", "webpack:///D:/gst/gst-uniapp/components/scroll-x/index.vue?92be", "webpack:///D:/gst/gst-uniapp/components/scroll-x/index.vue?ec5e", "uni-app:///components/scroll-x/index.vue", "webpack:///D:/gst/gst-uniapp/components/scroll-x/index.vue?7662", "webpack:///D:/gst/gst-uniapp/components/scroll-x/index.vue?a0dd"], "names": ["props", "list", "type", "default", "nums", "col", "isShowDot", "size", "height", "watch", "handler", "deep", "mounted", "data", "listdivInfo", "width", "showDot", "curDot", "circular", "tip", "moduleContent", "methods", "listdiv", "arr", "that", "console", "swiper<PERSON><PERSON>e", "leftImg", "rightImg", "navToPage", "uni", "url", "confirm"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCkCtnB;EACAA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IAEA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IAEA;IACAI;MACAL;MACAC;IACA;IAEA;IACAK;MACAN;MACAC;IACA;EAEA;EAEAM;IACAR;MACAS;QACA;MACA;MACAC;IACA;EACA;EAEAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBACAC;gBACAC;gBAAA;gBAAA,OACA;kBACA;kBACA;oBACAF;oBACAA;kBACA;oBACAA;kBACA;gBACA;cAAA;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAGAG;MACA;IACA;IACAC;MAEA;MAEA;MACA;QAEA;QAEA;MACA;QACA;MACA;IACA;IACAC;MAEA;MAEA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAC;UACAC;QACA;MACA;QACA;QACAD;UACAC;QACA;MACA;QACA;QACAD;UACAC;QACA;MACA;QACA;QACAD;UACAC;QACA;MACA,OACA;QACA;MACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvLA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/scroll-x/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=422519f6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=422519f6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"422519f6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/scroll-x/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=422519f6&scoped=true&\"", "var components\ntry {\n  components = {\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <swiper :indicator-dots=\"isShowDot && showDot\" class=\"swiper\" :style=\"{height:(height*col)+'rpx'}\" :current=\"curDot\" @change=\"swiperChange\" :circular='circular'>\n      <swiper-item v-for=\"(item, index) in listdivInfo\" :key=\"index\" class=\"swiper-item\">\n        <view v-for=\"(child, code) in item\" class=\"smallItem\" :key=\"code\" :style=\"{ width: width + '%' }\" @tap=\"navToPage(child)\">\n          <view class=\"image\">\n            <u-image :src=\"child.icon\" :width=\"size+'rpx'\" :height=\"size+'rpx'\" >\n            </u-image>\n          </view>\n          <view class=\"name\">{{ child.name }}</view>\n        </view>\n      </swiper-item>\n    </swiper>\r\n\t<!--弹出层1 提示 开始-->\r\n\t<u-popup v-model=\"tip\" width=\"80%\" mode=\"center\" border-radius=\"10\" :show=\"tip\" :safeAreaInsetBottom=\"false\">\r\n\t\t<view class=\"view-pup2\">\r\n\t\t\t<view class=\"view-pup2-box\"></view>\r\n\t\t\t<view class=\"view-pup2-warn\">\r\n\t\t\t\t<view class=\"view-pup2-warn-title\">温馨提示</view>\r\n\t\t\t\t<view class=\"view-pup2-warn-text\" style=\"text-align: left;\">{{moduleContent}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"view-pup2-button\">\r\n\t\t\t\t<view class=\"view-pup2-button-list view-pup2-button-list1\"\r\n\t\t\t\t\t@click=\"confirm()\">确定</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-popup>\n  </view>\r\n  \n</template>\n\n\n\n<script>\nexport default {\n  props: {\n    list: {\n      type: Array,\n      default: () => {\n        return [];\n      },\n    },\n    //一行排列数\n    nums: {\n      type: Number,\n      default: 4,\n    },\n\n    //排列行数\n    col: {\n      type: Number,\n      default: 1,\n    },\n    //是否展示指示灯\n    isShowDot: {\n      type: Boolean,\n      default: false,\n    },\r\n\t\r\n\t//图标大小\r\n\tsize: {\r\n\t  type: Number,\r\n\t  default: 80,\r\n\t},\r\n\t\r\n\t//高度\r\n\theight: {\r\n\t  type: Number,\r\n\t  default: 150,\r\n\t},\r\n\t\n  },\n\n  watch: {\n    list: {\n      handler: function (newVal, oldVal) {\n        this.listdiv();\n      },\n      deep: true,\n    },\n  },\n\n  mounted() {\n    this.listdiv();\n  },\n  data() {\n    return {\n      listdivInfo: [],\n      width: 25,\n      showDot: true,\r\n\t  curDot :0,\r\n\t  circular: true,\r\n\t  tip:false,\r\n\t  moduleContent:'感谢您的关注和支持，本课程正在精心准备中。',\n    };\n  },\n  methods: {\n    async listdiv() {\n      this.width = 100 / this.nums;\n      var arr = [];\n      let that = this;\n      console.log(that.nums * that.col);\n      await this.list.forEach((v, index) => {\n        var num = Math.floor(index / (that.nums * that.col));\n        if (!arr[num]) {\n          arr[num] = [];\n          arr[num].push(v);\n        } else {\n          arr[num].push(v);\n        }\n      });\n      this.listdivInfo = arr;\n      if (this.listdivInfo.length > 1) {\n        this.showDot = true;\n      } else {\n        this.showDot = false;\n      }\n    },\r\n\t\r\n\t\r\n\tswiperChange(e) {\r\n\t\tthis.curDot = e.detail.current;\r\n\t},\r\n\tleftImg(){\r\n\t \r\n\t    this.circular = false\r\n\t \r\n\t\tlet num = this.listdivInfo.length - 1\r\n\t\tif (this.curDot <= 0) {\r\n\t \r\n\t        this.circular = true\r\n\t \r\n\t\t\tthis.curDot = num\r\n\t\t} else {\r\n\t\t\tthis.curDot--\r\n\t\t}\r\n\t},\r\n\trightImg(){\r\n\t \r\n\t    this.circular = true\r\n\t \r\n\t\tlet num = this.listdivInfo.length - 1\r\n\t\tif (this.curDot >= num) {\r\n\t\t\tthis.curDot = 0\r\n\t\t} else {\r\n\t\t\tthis.curDot++\r\n\t\t}\r\n\t},\r\n\t//详情页\r\n\tnavToPage(item) {\r\n\t\tlet jump_type = item.jump_type;\r\n\t\tlet title = item.name;\r\n\t\tlet son_type = item.son_type\r\n\t\tif(jump_type==1){\r\n\t\t\tlet id = item.id;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/category/list?id=${id}&type=${jump_type}&title=${title}&son_type=${son_type}`\r\n\t\t\t})\r\n\t\t}else if(jump_type==2){\r\n\t\t\tlet id = item.jump_pid;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/category/list?id=${id}&type=${jump_type}&title=${title}&son_type=${son_type}`\r\n\t\t\t})\r\n\t\t}else if(jump_type==3){\r\n\t\t\tlet id = item.jump_course_id;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t})\r\n\t\t}else if(jump_type==4){\r\n\t\t\tlet id = item.jump_course_id;\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/category/list-recommend?id=${id}&title=${title}`,\r\n\t\t\t})\r\n\t\t}\r\n\t\telse{\r\n\t\t\tthis.tip = true;\r\n\t\t}\r\n\t\t\r\n\t},\r\n\tconfirm() {\r\n\t\tthis.tip = false;\r\n\t},\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\r\n.top{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\n.swiper {\r\n  width: 92%;\n  margin: 8rpx auto;\n  background: white;\n  border-radius: 32rpx;\n}\n\n.swiper-item {\n  display: flex;\n  flex-wrap: wrap;\n\n  .smallItem {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    margin: 16rpx 0;\n    overflow: hidden;\n\n    image {\n      width: 80rpx;\n      height: 80rpx;\n    }\n\n    .name {\n      margin-top: 8rpx;\n      font-size: 26rpx;\n    }\n  }\n}\r\n.ceshi_prew text {\n  color: #fff;\n  font-size: 30rpx;\n  float: left;\n  margin-top: 25rpx;\n}\n \n.ceshi_next text {\n  color: #fff;\n  font-size: 30rpx;\n  display: block;\n  float: right;\n  margin-top: 25rpx;\n}\n \n.ceshi_next {\n  width: 40rpx;\n  height: 80rpx;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-top-left-radius: 80rpx;\n  border-bottom-left-radius: 80rpx;\n}\n \n.ceshi_prew {\n  width: 40rpx;\n  height: 80rpx;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-top-right-radius: 80rpx;\n  border-bottom-right-radius: 80rpx;\n}\n\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=422519f6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=422519f6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689563927\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}