{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/groups/lesson-player.vue?2bde", "webpack:///D:/gst/gst-uniapp/pages/groups/lesson-player.vue?04ba", "webpack:///D:/gst/gst-uniapp/pages/groups/lesson-player.vue?2a7b", "webpack:///D:/gst/gst-uniapp/pages/groups/lesson-player.vue?e222", "uni-app:///pages/groups/lesson-player.vue", "webpack:///D:/gst/gst-uniapp/pages/groups/lesson-player.vue?3845", "webpack:///D:/gst/gst-uniapp/pages/groups/lesson-player.vue?6186"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "lessonInfo", "id", "title", "subtitle", "categoryName", "lessonNumber", "type", "mediaUrl", "poster", "duration", "completed", "viewCount", "likeCount", "isPlaying", "showOverlay", "currentTime", "totalTime", "playbackRate", "isFavorite", "showMenuPopup", "notes", "relatedLessons", "computed", "progressPercent", "onLoad", "methods", "goBack", "uni", "showMenu", "hideMenu", "loadLessonInfo", "console", "params", "res", "onPlay", "onPause", "onEnded", "onTimeUpdate", "onError", "icon", "onFullscreenChange", "onAudioPlay", "onAudioPause", "onAudioEnded", "onAudioTimeUpdate", "onAudioError", "togglePlay", "video", "toggleAudioPlay", "audio", "changeSpeed", "formatTime", "addNote", "editable", "placeholderText", "success", "time", "content", "timestamp", "toggleFavorite", "shareLesson", "itemList", "downloadLesson", "markComplete", "playRelated", "url", "adjustSpeed", "adjustQuality", "reportIssue"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AAA0mB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0K9nB;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,iBACA;QACApB;QACAC;QACAC;QACAK;MACA,GACA;QACAP;QACAC;QACAC;QACAK;MACA;IAEA;EACA;EACAc;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAC;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACAC;UACA/B;QAEA;MACA;QACA;UACA;UACA,iEACA,mBACAgC;YACA;YACA3B;UAAA,EACA;QACA;MACA;IACA;IAEA;IACA4B;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACAP;MACAJ;QACAzB;QACAqC;MACA;IACA;IAEAC;MACAT;IACA;IAEA;IACAU;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACAd;MACAJ;QACAzB;QACAqC;MACA;IACA;IAEA;IACAO;MACA;MACA;QACAC;MACA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MAEA;QACAA;MACA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;QACAH;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAzB;QACAzB;QACAmD;QACAC;QACAC;UACA;YACA;cACAC;cACAC;cACAC;YACA;YAEA/B;cACAzB;cACAqC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAoB;MACA;MACAhC;QACAzB;QACAqC;MACA;IACA;IAEA;IACAqB;MACAjC;QACAkC;QACAN;UACA;UACA5B;YACAzB;YACAqC;UACA;QACA;MACA;IACA;IAEA;IACAuB;MACAnC;QACAzB;QACAuD;QACAF;UACA;YACA5B;cACAzB;cACAqC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAwB;MACA;MACApC;QACAzB;QACAqC;MACA;IACA;IAEA;IACAyB;MACArC;QACAsC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAxC;QACAkC;QACAN;UACA;UACA5B;YACAzB;YACAqC;UACA;QACA;MACA;IACA;IAEA;IACA6B;MACA;MACAzC;QACAzB;QACAuD;QACAJ;QACAE;UACA;YACA5B;cACAzB;cACAqC;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpfA;AAAA;AAAA;AAAA;AAAq5B,CAAgB,o4BAAG,EAAC,C;;;;;;;;;;;ACAz6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/lesson-player.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/lesson-player.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./lesson-player.vue?vue&type=template&id=1d657ad8&scoped=true&\"\nvar renderjs\nimport script from \"./lesson-player.vue?vue&type=script&lang=js&\"\nexport * from \"./lesson-player.vue?vue&type=script&lang=js&\"\nimport style0 from \"./lesson-player.vue?vue&type=style&index=0&id=1d657ad8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1d657ad8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/lesson-player.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lesson-player.vue?vue&type=template&id=1d657ad8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !(_vm.lessonInfo.type === \"video\") && _vm.lessonInfo.type === \"audio\"\n      ? _vm.formatTime(_vm.currentTime)\n      : null\n  var m1 =\n    !(_vm.lessonInfo.type === \"video\") && _vm.lessonInfo.type === \"audio\"\n      ? _vm.formatTime(_vm.totalTime)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lesson-player.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lesson-player.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"lesson-player\">\n\t\t\n\n\t\t<!-- 播放器容器 -->\n\t\t<view class=\"player-container\">\n\t\t\t<!-- 视频播放器 -->\n\t\t\t<view v-if=\"lessonInfo.type === 'video'\" class=\"video-player-wrapper\">\n\t\t\t\t<video\n\t\t\t\t\tid=\"lessonVideo\"\n\t\t\t\t\t:src=\"lessonInfo.url\"\n\t\t\t\t\t:poster=\"lessonInfo.poster\"\n\t\t\t\t\t:controls=\"true\"\n\t\t\t\t\t:autoplay=\"false\"\n\t\t\t\t\t:show-center-play-btn=\"true\"\n\t\t\t\t\t:enable-play-gesture=\"true\"\n\t\t\t\t\t:object-fit=\"'contain'\"\n\t\t\t\t\t@play=\"onPlay\"\n\t\t\t\t\t@pause=\"onPause\"\n\t\t\t\t\t@ended=\"onEnded\"\n\t\t\t\t\t@timeupdate=\"onTimeUpdate\"\n\t\t\t\t\t@error=\"onError\"\n\t\t\t\t\t@fullscreenchange=\"onFullscreenChange\"\n\t\t\t\t\tclass=\"video-player\"\n\t\t\t\t/>\n\t\t\t\t<view class=\"player-overlay\" v-if=\"showOverlay\">\n\t\t\t\t\t<view class=\"play-button\" @click=\"togglePlay\">\n\t\t\t\t\t\t<text class=\"play-icon\">{{isPlaying ? '⏸' : '▶'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 音频播放器 -->\n\t\t\t<view v-else-if=\"lessonInfo.type === 'audio'\" class=\"audio-player-wrapper\">\n\t\t\t\t<view class=\"audio-cover\">\n\t\t\t\t\t<image :src=\"lessonInfo.poster || '/static/imgs/audio-default.png'\" mode=\"aspectFit\" />\n\t\t\t\t\t<view class=\"audio-play-btn\" @click=\"toggleAudioPlay\">\n\t\t\t\t\t\t<text class=\"audio-play-icon\">{{isPlaying ? '⏸' : '▶'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"audio-controls\">\n\t\t\t\t\t<view class=\"audio-progress\">\n\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: progressPercent + '%' }\"></view>\n\t\t\t\t\t\t\t<view class=\"progress-thumb\" :style=\"{ left: progressPercent + '%' }\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"time-display\">\n\t\t\t\t\t\t\t<text class=\"current-time\">{{formatTime(currentTime)}}</text>\n\t\t\t\t\t\t\t<text class=\"total-time\">{{formatTime(totalTime)}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"audio-actions\">\n\t\t\t\t\t\t<view class=\"speed-control\" @click=\"changeSpeed\">\n\t\t\t\t\t\t\t<text class=\"speed-text\">{{playbackRate}}x</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view v-else class=\"loading-wrapper\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 课程信息 -->\n\t\t<view class=\"lesson-info\">\n\t\t\t<view class=\"lesson-header\">\n\t\t\t\t<view class=\"lesson-meta\">\n\t\t\t\t\t<!-- <text class=\"lesson-category\">{{lessonInfo.categoryName}}</text> -->\n\t\t\t\t\t<text class=\"lesson-number\">{{lessonInfo.title}}</text>\n\t\t\t\t</view>\n\t\t\t\t<!-- <view class=\"lesson-status\" :class=\"lessonInfo.completed ? 'completed' : 'pending'\">\n\t\t\t\t\t<text class=\"status-text\">{{lessonInfo.completed ? '已完成' : '学习中'}}</text>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t\t<text class=\"lesson-title\">{{lessonInfo.course.title}}</text>\n\t\t\t<!-- <text class=\"lesson-subtitle\">{{lessonInfo.subtitle}}</text> -->\n\t\t\t<!-- <view class=\"lesson-stats\">\n\t\t\t\t<text class=\"stat-item\">⏱ {{formatTime(lessonInfo.duration)}}</text>\n\t\t\t\t<text class=\"stat-item\">👁 {{lessonInfo.viewCount || 0}}次播放</text>\n\t\t\t\t<text class=\"stat-item\">❤ {{lessonInfo.likeCount || 0}}点赞</text>\n\t\t\t</view> -->\n\t\t</view>\n\n\t\t<!-- 学习笔记 -->\n\t\t<!-- <view class=\"notes-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">学习笔记</text>\n\t\t\t\t<text class=\"add-note\" @click=\"addNote\">+ 添加笔记</text>\n\t\t\t</view>\n\t\t\t<view class=\"notes-list\" v-if=\"notes.length > 0\">\n\t\t\t\t<view class=\"note-item\" v-for=\"(note, index) in notes\" :key=\"index\">\n\t\t\t\t\t<view class=\"note-time\">{{formatTime(note.time)}}</view>\n\t\t\t\t\t<text class=\"note-content\">{{note.content}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"empty-notes\">\n\t\t\t\t<text class=\"empty-text\">暂无学习笔记</text>\n\t\t\t</view>\n\t\t</view> -->\n\n\t\t<!-- 相关课程 -->\n\t\t<!-- <view class=\"related-lessons\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">相关课程</text>\n\t\t\t</view>\n\t\t\t<scroll-view class=\"related-list\" scroll-x=\"true\">\n\t\t\t\t<view class=\"related-item\" v-for=\"(item, index) in relatedLessons\" :key=\"index\" @click=\"playRelated(item)\">\n\t\t\t\t\t<image class=\"related-thumb\" :src=\"item.poster\" mode=\"aspectFit\" />\n\t\t\t\t\t<text class=\"related-title\">{{item.title}}</text>\n\t\t\t\t\t<text class=\"related-subtitle\">{{item.subtitle}}</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view> -->\n\n\t\t<!-- 底部操作栏 -->\n\t\t<!-- <view class=\"bottom-actions\">\n\t\t\t<view class=\"action-btn\" @click=\"toggleFavorite\">\n\t\t\t\t<text class=\"action-icon\">{{isFavorite ? '❤' : '♡'}}</text>\n\t\t\t\t<text class=\"action-text\">收藏</text>\n\t\t\t</view>\n\t\t\t<view class=\"action-btn\" @click=\"shareLesson\">\n\t\t\t\t<text class=\"action-icon\">📤</text>\n\t\t\t\t<text class=\"action-text\">分享</text>\n\t\t\t</view>\n\t\t\t<view class=\"action-btn\" @click=\"downloadLesson\">\n\t\t\t\t<text class=\"action-icon\">⬇</text>\n\t\t\t\t<text class=\"action-text\">下载</text>\n\t\t\t</view>\n\t\t\t<view class=\"action-btn primary\" @click=\"markComplete\">\n\t\t\t\t<text class=\"action-text\">{{lessonInfo.completed ? '已完成' : '标记完成'}}</text>\n\t\t\t</view>\n\t\t</view> -->\n\n\t\t<!-- 菜单弹窗 -->\n\t\t<view class=\"menu-overlay\" v-if=\"showMenuPopup\" @click=\"hideMenu\">\n\t\t\t<view class=\"menu-popup\" @click.stop>\n\t\t\t\t<view class=\"menu-item\" @click=\"adjustSpeed\">\n\t\t\t\t\t<text class=\"menu-icon\">⚡</text>\n\t\t\t\t\t<text class=\"menu-text\">播放速度</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"adjustQuality\">\n\t\t\t\t\t<text class=\"menu-icon\">🎬</text>\n\t\t\t\t\t<text class=\"menu-text\">画质设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"reportIssue\">\n\t\t\t\t\t<text class=\"menu-icon\">⚠</text>\n\t\t\t\t\t<text class=\"menu-text\">反馈问题</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 音频播放器（隐藏） -->\n\t\t<audio\n\t\t\tv-if=\"lessonInfo.type === 'audio'\"\n\t\t\tid=\"lessonAudio\"\n\t\t\t:src=\"lessonInfo.mediaUrl\"\n\t\t\t@play=\"onAudioPlay\"\n\t\t\t@pause=\"onAudioPause\"\n\t\t\t@ended=\"onAudioEnded\"\n\t\t\t@timeupdate=\"onAudioTimeUpdate\"\n\t\t\t@error=\"onAudioError\"\n\t\t\tstyle=\"display: none;\"\n\t\t/>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tstatusBarHeight: 0,\n\t\t\tlessonInfo: {\n\t\t\t\tid: 1,\n\t\t\t\ttitle: '第一课',\n\t\t\t\tsubtitle: '基础问候语',\n\t\t\t\tcategoryName: '口语',\n\t\t\t\tlessonNumber: 1,\n\t\t\t\ttype: 'video', // 'video' 或 'audio'\n\t\t\t\tmediaUrl: '',\n\t\t\t\tposter: '/static/imgs/lesson-default.png',\n\t\t\t\tduration: 300, // 秒\n\t\t\t\tcompleted: false,\n\t\t\t\tviewCount: 128,\n\t\t\t\tlikeCount: 23\n\t\t\t},\n\t\t\tisPlaying: false,\n\t\t\tshowOverlay: true,\n\t\t\tcurrentTime: 0,\n\t\t\ttotalTime: 0,\n\t\t\tplaybackRate: 1.0,\n\t\t\tisFavorite: false,\n\t\t\tshowMenuPopup: false,\n\t\t\tnotes: [],\n\t\t\trelatedLessons: [\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '第二课',\n\t\t\t\t\tsubtitle: '自我介绍',\n\t\t\t\t\tposter: '/static/imgs/lesson-2.png'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '第三课',\n\t\t\t\t\tsubtitle: '日常对话',\n\t\t\t\t\tposter: '/static/imgs/lesson-3.png'\n\t\t\t\t}\n\t\t\t]\n\t\t};\n\t},\n\tcomputed: {\n\t\tprogressPercent() {\n\t\t\tif (this.totalTime === 0) return 0;\n\t\t\treturn (this.currentTime / this.totalTime) * 100;\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取系统信息\n\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\tthis.statusBarHeight = systemInfo.statusBarHeight;\n\t\t\n\t\t// 获取课程信息\n\t\tif (options.lessonId) {\n\t\t\tthis.loadLessonInfo(options.lessonId);\n\t\t}\n\t\tif (options.categoryId) {\n\t\t\tthis.lessonInfo.categoryName = options.categoryName || '未知分类';\n\t\t}\n\t},\n\tmethods: {\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 显示菜单\n\t\tshowMenu() {\n\t\t\tthis.showMenuPopup = true;\n\t\t},\n\t\t\n\t\t// 隐藏菜单\n\t\thideMenu() {\n\t\t\tthis.showMenuPopup = false;\n\t\t},\n\t\t\n\t\t// 加载课程信息\n\t\tloadLessonInfo(lessonId) {\n\t\t\t// 这里可以调用API获取课程信息\n\t\t\tconsole.log('加载课程信息:', lessonId);\n\t\t\t\n\t\t\t// 模拟数据\n\t\t\t// this.lessonInfo = {\n\t\t\t// \t...this.lessonInfo,\n\t\t\t// \tid: lessonId,\n\t\t\t// \tmediaUrl: 'https://example.com/lesson.mp4', // 实际的媒体URL\n\t\t\t// \ttype: Math.random() > 0.5 ? 'video' : 'audio' // 随机类型用于演示\n\t\t\t// };\n\t\t\t\n\t\t\tthis.$http.get(\"v1/course/groups_course_detail\", {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tid:lessonId\n\n\t\t\t\t\t}\n\t\t\t\t}).then(res => {\n\t\t\t\tif (res.data.code == 0) {\n\t\t\t\t\t// 合并数据，保留默认的type字段\n\t\t\t\t\tthis.lessonInfo = {\n\t\t\t\t\t\t...this.lessonInfo,\n\t\t\t\t\t\t...res.data.data,\n\t\t\t\t\t\t// 确保type字段存在，如果API没有返回则使用默认值\n\t\t\t\t\t\ttype: res.data.data.type || this.lessonInfo.type || 'video'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 视频播放事件\n\t\tonPlay() {\n\t\t\tthis.isPlaying = true;\n\t\t\tthis.showOverlay = false;\n\t\t},\n\t\t\n\t\tonPause() {\n\t\t\tthis.isPlaying = false;\n\t\t\tthis.showOverlay = true;\n\t\t},\n\t\t\n\t\tonEnded() {\n\t\t\tthis.isPlaying = false;\n\t\t\tthis.showOverlay = true;\n\t\t\tthis.markComplete();\n\t\t},\n\t\t\n\t\tonTimeUpdate(e) {\n\t\t\tthis.currentTime = e.detail.currentTime;\n\t\t\tthis.totalTime = e.detail.duration;\n\t\t},\n\t\t\n\t\tonError(e) {\n\t\t\tconsole.error('视频播放错误:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '播放失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\tonFullscreenChange(e) {\n\t\t\tconsole.log('全屏状态改变:', e.detail.fullScreen);\n\t\t},\n\t\t\n\t\t// 音频播放事件\n\t\tonAudioPlay() {\n\t\t\tthis.isPlaying = true;\n\t\t},\n\t\t\n\t\tonAudioPause() {\n\t\t\tthis.isPlaying = false;\n\t\t},\n\t\t\n\t\tonAudioEnded() {\n\t\t\tthis.isPlaying = false;\n\t\t\tthis.markComplete();\n\t\t},\n\t\t\n\t\tonAudioTimeUpdate(e) {\n\t\t\tthis.currentTime = e.detail.currentTime;\n\t\t\tthis.totalTime = e.detail.duration;\n\t\t},\n\t\t\n\t\tonAudioError(e) {\n\t\t\tconsole.error('音频播放错误:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '播放失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 切换播放状态\n\t\ttogglePlay() {\n\t\t\tconst video = uni.createVideoContext('lessonVideo', this);\n\t\t\tif (this.isPlaying) {\n\t\t\t\tvideo.pause();\n\t\t\t} else {\n\t\t\t\tvideo.play();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换音频播放状态\n\t\ttoggleAudioPlay() {\n\t\t\tconst audio = uni.createInnerAudioContext();\n\t\t\taudio.src = this.lessonInfo.mediaUrl;\n\t\t\t\n\t\t\tif (this.isPlaying) {\n\t\t\t\taudio.pause();\n\t\t\t} else {\n\t\t\t\taudio.play();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 改变播放速度\n\t\tchangeSpeed() {\n\t\t\tconst speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];\n\t\t\tconst currentIndex = speeds.indexOf(this.playbackRate);\n\t\t\tconst nextIndex = (currentIndex + 1) % speeds.length;\n\t\t\tthis.playbackRate = speeds[nextIndex];\n\t\t\t\n\t\t\t// 设置播放速度\n\t\t\tif (this.lessonInfo.type === 'video') {\n\t\t\t\tconst video = uni.createVideoContext('lessonVideo', this);\n\t\t\t\tvideo.playbackRate(this.playbackRate);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(seconds) {\n\t\t\tif (!seconds) return '00:00';\n\t\t\tconst mins = Math.floor(seconds / 60);\n\t\t\tconst secs = Math.floor(seconds % 60);\n\t\t\treturn `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n\t\t},\n\t\t\n\t\t// 添加笔记\n\t\taddNote() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '添加学习笔记',\n\t\t\t\teditable: true,\n\t\t\t\tplaceholderText: '请输入学习笔记...',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm && res.content) {\n\t\t\t\t\t\tthis.notes.push({\n\t\t\t\t\t\t\ttime: this.currentTime,\n\t\t\t\t\t\t\tcontent: res.content,\n\t\t\t\t\t\t\ttimestamp: new Date().getTime()\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '笔记添加成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 切换收藏状态\n\t\ttoggleFavorite() {\n\t\t\tthis.isFavorite = !this.isFavorite;\n\t\t\tuni.showToast({\n\t\t\t\ttitle: this.isFavorite ? '已收藏' : '已取消收藏',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 分享课程\n\t\tshareLesson() {\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['分享到微信', '分享到朋友圈', '复制链接'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst actions = ['微信', '朋友圈', '复制链接'];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `分享到${actions[res.tapIndex]}`,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 下载课程\n\t\tdownloadLesson() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '下载课程',\n\t\t\t\tcontent: '是否下载此课程到本地？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '开始下载',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 标记完成\n\t\tmarkComplete() {\n\t\t\tthis.lessonInfo.completed = true;\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '课程已完成',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 播放相关课程\n\t\tplayRelated(lesson) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/lesson-player?lessonId=${lesson.id}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 调整播放速度\n\t\tadjustSpeed() {\n\t\t\tthis.hideMenu();\n\t\t\tthis.changeSpeed();\n\t\t},\n\t\t\n\t\t// 调整画质\n\t\tadjustQuality() {\n\t\t\tthis.hideMenu();\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['自动', '高清', '标清', '流畅'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst qualities = ['自动', '高清', '标清', '流畅'];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `已切换到${qualities[res.tapIndex]}`,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 反馈问题\n\t\treportIssue() {\n\t\t\tthis.hideMenu();\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '反馈问题',\n\t\t\t\tcontent: '请描述您遇到的问题',\n\t\t\t\teditable: true,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '反馈已提交',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.lesson-player {\n\tbackground: #f8f9fa;\n\tmin-height: 100vh;\n}\n\n/* 状态栏 */\n.status-bar {\n\tbackground: #000;\n}\n\n/* 导航栏 */\n.nav-bar {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\theight: 88rpx;\n\tbackground: #fff;\n\tpadding: 0 20rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.nav-left, .nav-right {\n\twidth: 80rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.nav-center {\n\tflex: 1;\n\ttext-align: center;\n}\n\n.nav-icon {\n\tfont-size: 32rpx;\n\tcolor: #333;\n}\n\n.nav-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n/* 播放器容器 */\n.player-container {\n\tposition: relative;\n\tbackground: #000;\n}\n\n/* 视频播放器 */\n.video-player-wrapper {\n\tposition: relative;\n\twidth: 100%;\n\theight: 420rpx;\n}\n\n.video-player {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.player-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: rgba(0, 0, 0, 0.3);\n}\n\n.play-button {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);\n}\n\n.play-icon {\n\tfont-size: 48rpx;\n\tcolor: #333;\n}\n\n/* 音频播放器 */\n.audio-player-wrapper {\n\tpadding: 40rpx;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.audio-cover {\n\tposition: relative;\n\twidth: 300rpx;\n\theight: 300rpx;\n\tmargin: 0 auto 40rpx;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);\n}\n\n.audio-cover image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.audio-play-btn {\n\tposition: absolute;\n\tbottom: 20rpx;\n\tright: 20rpx;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);\n}\n\n.audio-play-icon {\n\tfont-size: 32rpx;\n\tcolor: #333;\n}\n\n.audio-controls {\n\tcolor: #fff;\n}\n\n.audio-progress {\n\tmargin-bottom: 30rpx;\n}\n\n.progress-bar {\n\tposition: relative;\n\theight: 6rpx;\n\tbackground: rgba(255, 255, 255, 0.3);\n\tborder-radius: 3rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: #fff;\n\tborder-radius: 3rpx;\n\ttransition: width 0.3s ease;\n}\n\n.progress-thumb {\n\tposition: absolute;\n\ttop: -8rpx;\n\twidth: 22rpx;\n\theight: 22rpx;\n\tbackground: #fff;\n\tborder-radius: 50%;\n\ttransform: translateX(-50%);\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);\n}\n\n.time-display {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tfont-size: 24rpx;\n}\n\n.audio-actions {\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.speed-control {\n\tpadding: 12rpx 24rpx;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tborder-radius: 20rpx;\n\tborder: 2rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.speed-text {\n\tfont-size: 24rpx;\n\tcolor: #fff;\n}\n\n/* 加载状态 */\n.loading-wrapper {\n\theight: 420rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: #000;\n}\n\n.loading-spinner {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder: 4rpx solid rgba(255, 255, 255, 0.3);\n\tborder-top: 4rpx solid #fff;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 20rpx;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n\tcolor: #fff;\n\tfont-size: 28rpx;\n}\n\n/* 课程信息 */\n.lesson-info {\n\tbackground: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.lesson-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.lesson-meta {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.lesson-category {\n\tfont-size: 24rpx;\n\tcolor: #667eea;\n\tbackground: rgba(102, 126, 234, 0.1);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n}\n\n.lesson-number {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tbackground: #f0f0f0;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n}\n\n.lesson-status {\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n.lesson-status.completed {\n\tbackground: rgba(82, 196, 26, 0.1);\n\tcolor: #52c41a;\n}\n\n.lesson-status.pending {\n\tbackground: rgba(255, 193, 7, 0.1);\n\tcolor: #ffc107;\n}\n\n.lesson-title {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 12rpx;\n}\n\n.lesson-subtitle {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-bottom: 20rpx;\n}\n\n.lesson-stats {\n\tdisplay: flex;\n\tgap: 30rpx;\n}\n\n.stat-item {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 学习笔记 */\n.notes-section {\n\tbackground: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.section-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 2rpx solid #f0f0f0;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.add-note {\n\tfont-size: 28rpx;\n\tcolor: #667eea;\n}\n\n.notes-list {\n\tpadding: 0 30rpx;\n}\n\n.note-item {\n\tdisplay: flex;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.note-item:last-child {\n\tborder-bottom: none;\n}\n\n.note-time {\n\twidth: 120rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-right: 20rpx;\n}\n\n.note-content {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tline-height: 1.5;\n}\n\n.empty-notes {\n\tpadding: 60rpx 30rpx;\n\ttext-align: center;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 相关课程 */\n.related-lessons {\n\tbackground: #fff;\n\tmargin-bottom: 120rpx;\n}\n\n.related-list {\n\tpadding: 20rpx 30rpx;\n\twhite-space: nowrap;\n}\n\n.related-item {\n\tdisplay: inline-block;\n\twidth: 200rpx;\n\tmargin-right: 20rpx;\n\tvertical-align: top;\n}\n\n.related-thumb {\n\twidth: 100%;\n\theight: 120rpx;\n\tborder-radius: 12rpx;\n\tbackground: #f0f0f0;\n\tmargin-bottom: 12rpx;\n}\n\n.related-title {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.related-subtitle {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n/* 底部操作栏 */\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tdisplay: flex;\n\tbackground: #fff;\n\tpadding: 20rpx;\n\tbox-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);\n\tsafe-area-inset-bottom: env(safe-area-inset-bottom);\n}\n\n.action-btn {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 16rpx;\n\tmargin: 0 8rpx;\n\tborder-radius: 12rpx;\n\ttransition: all 0.3s ease;\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n}\n\n.action-btn.primary {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: #fff;\n}\n\n.action-icon {\n\tfont-size: 32rpx;\n\tmargin-bottom: 8rpx;\n}\n\n.action-text {\n\tfont-size: 24rpx;\n}\n\n.action-btn.primary .action-text {\n\tcolor: #fff;\n}\n\n/* 菜单弹窗 */\n.menu-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: flex-start;\n\tjustify-content: flex-end;\n\tpadding: 100rpx 20rpx 0 0;\n\tz-index: 1000;\n}\n\n.menu-popup {\n\tbackground: #fff;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);\n\tmin-width: 200rpx;\n}\n\n.menu-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 24rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item:last-child {\n\tborder-bottom: none;\n}\n\n.menu-item:active {\n\tbackground: #f8f9fa;\n}\n\n.menu-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 16rpx;\n}\n\n.menu-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lesson-player.vue?vue&type=style&index=0&id=1d657ad8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lesson-player.vue?vue&type=style&index=0&id=1d657ad8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041063198\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}