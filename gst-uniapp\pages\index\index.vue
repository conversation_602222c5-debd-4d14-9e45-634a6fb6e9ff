<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<view style="padding:10rpx 30rpx;">
				<view class="search-box" @click="search">
					<text class="grace-icons icon-search " style="margin-right: 20rpx;"></text>
					搜索关键词
				</view>
			</view>
			<!-- 			<view class="u-demo-block" style="padding:10rpx 30rpx;background-color: #fff;">
				<view class="u-demo-block__content">
					<u-tabs :list="list1" @change="nav" :current="current" keyName="title" > -->
			<!-- <view slot="right" style="padding-right: 4px;" @click="">
							<u-icon name="list" size="21" bold></u-icon>
						</view> -->
			<!-- 		</u-tabs>
				</view>
			</view> -->
			<!--分类-->
			<swiper bind:change="handleChange" class="swiper-container-small" previousMargin="6rpx">
				<swiper-item class="swiper-item" data-index="index">
					<view bind:tap="handleTap" class="srv-item-small" v-for="(item,index) in list1" :key="index"
						@tap="navTo(item,'link')">
						<text class="srv-item-title nowrap">{{item.title}}</text>
					</view>
				</swiper-item>
			</swiper>
			<!-- 头部轮播 -->
			<view class="carousel-section">
				<!-- 标题栏和状态栏占位符 -->
				<!-- <view class="titleNview-placing"></view> -->
				<!-- 背景色区域 -->
				<!-- <view class="titleNview-background" :style="{backgroundColor:titleNViewBackground}"></view> -->
				<swiper class="carousel" circular @change="swiperChange" autoplay="true">
					<swiper-item v-for="(item, index) in carouselList" :key="index" class="carousel-item"
						@tap="navToWeb(item,'swiper')">
						<image :src="item.thumb" />
					</swiper-item>
				</swiper>
				<!-- 自定义swiper指示器 -->
				<view class="swiper-dots">
					<text class="num">{{swiperCurrent+1}}</text>
					<text class="sign">/</text>
					<text class="num">{{swiperLength}}</text>
				</view>
			</view>
			<!--分类1-->
			<scrollX :list="iconList1" :nums="5" :col="2" />
			<view class="hot-service" style="margin: auto;">
				<image ariaHidden="true" class="bg-img" lazyLoad="true" mode="aspectFill" :src="moreBgUrl"></image>
				<view ariaRole="heading" class="hot-service-title">
					<view class="hot-service-title-h3">更多课程</view>
				</view>
			</view>
			<!--分类2-->
			<scrollX :list="iconList2" :nums="5" :col="3" :size="60" :height="130" />
			<!--更多服务-->
			<!-- <view class="bg-box hbclass" v-if="otherList.length > 0">
				<view class="hot-service">
					<image ariaHidden="true" class="bg-img" lazyLoad="true" mode="aspectFill" :src="rmfwBgUrl"></image>
					<view ariaRole="heading" class="hot-service-title">
					<view class="hot-service-title-h3">更多服务</view>
					</view>
					<view class="hot-service-content">
						<swiper bind:change="handleChange" class="swiper-container" previousMargin="6rpx" >
							<swiper-item class="swiper-item" data-index="index" >
								<view bind:tap="handleTap" class="srv-item" v-for="(item,index) in otherList"
									:key="index" @tap="navToWeb(item,'link')">
									<image class="srv-item-icon" :src="item.thumb" style="width: 80rpx;height:80rpx;" mode="heightFix"></image>
									<text class="srv-item-title nowrap">{{item.title}}</text>
								</view>
							</swiper-item>
						</swiper> -->
			<!-- <view class="indicator" wx:if="{{list[1]}}">
			                <view style="margin: 0 auto">
			                    <view :class="indicator-child {{current==index?'active':''}}" :style="indicatorStyle" v-for="(item,index) in superInList" :key="index"></view>
			                </view>
			            </view> -->
			<!-- 	</view>
				</view>
			</view> -->

			<view style="padding: 20rpx 0;">

				<view class="everyone-doing bg hbclass" v-if="isShow">
					<view class="service-main">
						<view class="listbox service-list">
							<view class="titlebox">
								<view class="h2title viewtitle">推荐阅读</view>

							</view>
							<view class="content service-hot-list">
								<view class="list-box">
									<view class="item-box lp-flex-column" v-for="(item1, index1) in courseList"
										:key="index1" @tap="navToWeb(item1)">
										<view class="top-box lp-flex">

											<view class="cover-box lp-flex-center">
												<image class="cover" :src="item1.thumb"></image>
											</view>
											<view class="info-box lp-flex-column">
												<view class="title">{{item1.title}}</view>

												<view class="end"><text
														style="text-align: right;float: right;">更多</text>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!--弹出层1 提示 开始-->
			<u-popup v-model="tip" width="80%" mode="center" border-radius="10" :show="tip"
				:safeAreaInsetBottom="false">
				<view class="view-pup2">
					<view class="view-pup2-box"></view>
					<view class="view-pup2-warn">
						<view class="view-pup2-warn-title">温馨提示</view>
						<view class="view-pup2-warn-text" style="text-align: left;">{{moduleContent}}</view>
					</view>
					<view class="view-pup2-button">
						<view class="view-pup2-button-list view-pup2-button-list1" @click="confirm()">确定</view>
					</view>
				</view>
			</u-popup>
		</view>

		<!-- 测试底部导航 -->
		<test-tabbar />
	</gui-page>
</template>
<script>
	var graceJs = require('@/GraceUI5/js/grace.js');
	// 模拟 api 请求数据，格式见 article.js
	var artciles = require('@/GraceUI5/demoData/article.js');
	import u_button from '@/components/uview-ui/components/u-button/u-button.vue';
	import scrollX from "@/components/scroll-x/index.vue";
	import CustomTabbar from '@/components/custom-tabbar.vue';
	import TestTabbar from '@/components/test-tabbar.vue';
	import {
		showLoading
	} from '@/GraceUI5/js/grace.js';
	export default {
		components: {
			scrollX,
			CustomTabbar,
			TestTabbar,
		},
		data() {
			return {
				name: "",
				userInfo: {},
				ruleInfo: {},
				news: [], // 新闻推荐
				swiperCurrent: 0,
				swiperLength: 3,
				carouselList: [{
						src: "/static/temp/banner3.jpg",
						background: "rgb(203, 87, 60)",
					},
					{
						src: "/static/temp/banner2.jpg",
						background: "rgb(205, 215, 218)",
					},
					{
						src: "/static/temp/banner4.jpg",
						background: "rgb(183, 73, 69)",
					}
				],
				goodsList: [],
				iconList1: [],
				iconList2: [],
				newList: [],
				otherList: [],
				isPlaying: false,
				isPaused: true,
				courseList: [],
				noticeList: [],
				page: 1,
				rmfwBgUrl: "https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20221105124531.png",
				list1: [
					// {
					// 	name: '主页',
					// }
					// , 
					// {
					// 	name: '考研升博',
					// }
					// , 
					// {
					// 	name: '日语实习',
					// }
					// // , {
					// // 	name: '日语好工作',
					// // 	// badge: {
					// // 	// 	isDot: true
					// // 	// }
					// // }
					// , {
					// 	name: '外教口语',
					// 	// badge: {
					// 	// 	value: 5,
					// 	// }
					// },
					// {
					// 	name: '日语研修'
					// }
				],
				tip: false,
				moduleContent: '敬请期待..',
				module: {},
				moreBgUrl: "https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20230302112933.png",
				current: 0,
				isShow:false
			}
		},
		onLoad: function() {
			// 01. 获取页面主体高度
			graceJs.getRefs('guiPage', this, 0, (ref) => {
				ref.getDomSize('guiPageBody', (e) => {
					// 主体高度 = 页面高度 - 自定义区域高度
					graceJs.select('#myheader', (e2) => {
						// 如果自定义区域有 margin 尺寸获取不到请加上这个值，可以利用 uni.upx2px() 转换
						// this.mainHeight = e.height - e2.height;
						this.pageLoading = false;
						uni.getSystemInfo({
							success: (res) => {
								// windows | mac为pc端
								// android | ios为手机端
								console.log('getSystemInfo,', res.platform);
								this.platform = res.platform
								this.$store.commit('setSystemInfo', {
									platform: res.platform || '',
									saveStorage: true
								});
							}
						});
						this.getNews();
					});
				});
			});
			wx.showShareMenu({
				withShareTicket: true,
				menus: ["shareAppMessage", "shareTimeline"]
			})


		},
		onShareAppMessage() {
			return {
				title: "日语云课", //标题
				imageUrl: "", //封面
				path: "/pages/index/index" //此处链接为要分享的页面链接	
			};
		},
		// 分享到朋友圈
		onShareTimeline() {
			return {
				title: "日语云课", //标题
				imageUrl: "https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/shixilogo.jpg", //封面
				path: "/pages/index/index" //此处链接为要分享的页面链接	
			};
		},
		methods: {
			// 获取新闻推荐
			getNews() {
				this.$http.get("v1/course/index").then(res => {
					console.log(res);
					if (res.data.code == 0) {
						//this.goodsList = res.data.data.list
						this.carouselList = res.data.data.banner
						this.swiperLength = res.data.data.banner.length
						this.iconList1 = res.data.data.course1;
						this.iconList2 = res.data.data.course2;
						this.newList = res.data.data.new;
						this.otherList = res.data.data.game_show_footer;
						this.courseList = res.data.data.game_foot_post[0].lists;
						this.noticeList = res.data.data.notice;
						this.list1 = res.data.data.top_post;
						this.isShow = res.data.data.isShow;
					}
				});
			},
			navToWeb(item) {
				switch (item.type) {
					case "web": // 项目外部跳转，需要使用web-view跳转外部H5页面
						uni.navigateTo({
							url: "/pages/webView/webView?data=" + encodeURIComponent(JSON.stringify(item))
						})
						break;
					case "mini_app": // 项目内部跳转
						uni.navigateTo({
							url: item.link
						})
						break;
					case "popu": // 当前页内部弹窗，不跳转
						this.module = "show";
						this.moduleTitle = item.title;
						this.moduleContent = item.description;
						this.tip = true;
						break;
					case "other_mini": // 当前页内部弹窗，不跳转
						this.getUrl(item.app_id, item.link)
						break;
				}
				return;
			},
			//轮播图切换修改背景色
			swiperChange(e) {
				const index = e.detail.current;
				this.swiperCurrent = index;
			},
			//详情页
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.id;
				uni.navigateTo({
					url: `/pages/product/product?id=${id}`
				})
			},
			//详情页
			navToPage(item) {
				//测试数据没有写id，用title代替
				let id = item.app_id;
				let title = item.title;
				uni.navigateTo({
					url: `/pages/category/list?id=${id}&title=${title}&style=2`
				})
			},
			toCate(item) {
				let id = item.id;
				let title = item.name;
				let path = item.path;
				let style = item.style;
				// if(path == null || path == ''){
				// 	uni.navigateTo({
				// 		url: `/pages/category/list?id=${id}&title=${title}`,
				// 		})
				// }else{
				// 	uni.navigateTo({
				// 		url: `${path}?id=${id}&title=${title}`,
				// 		})
				// }
				if (style == 4) {
					// uni.navigateTo({
					// 	url: `/pages/course/list?id=${id}&title=${title}`,
					// 	})
					uni.navigateTo({
						url: `/pages/category/list?id=${id}&title=${title}&style=${style}`,
					})
				} else if (style == 1 || style == 2) {
					uni.navigateTo({
						url: `/pages/category/list?id=${id}&title=${title}&style=${style}`,
					})
				} else if (style == 3) {
					uni.navigateTo({
						url: `/pages/category/list-page?id=${id}&title=${title}&style=${style}`,
					})
				} else if (style == 5) {
					uni.navigateTo({
						url: `/pages/category/list-other?id=${id}&title=${title}&style=${style}`,
					})
				} else if (style == 6) {
					uni.navigateTo({
						url: `/pages/category/list-recommend?id=${id}&title=${title}&style=${style}`,
					})
				}

			},
			declick(type) {
				this.loadHotData()
			},
			getUrl(appId, path) {
				console.log(appId)
				uni.navigateToMiniProgram({
					appId: appId,
					path: path,
					success(res) {
						// 打开成功
					}
				})
			},
			search() {
				uni.navigateTo({
					url: `/pages/category/search`,
				})
			},
			nav(index) {
				switch (index.type) {
					case "web": // 项目外部跳转，需要使用web-view跳转外部H5页面
						uni.navigateTo({
							url: "/pages/webView/webView?data=" + encodeURIComponent(JSON.stringify(index))
						})
						break;
					case "mini_app": // 项目内部跳转
						uni.navigateTo({
							url: index.link
						})
						break;
					case "popu": // 当前页内部弹窗，不跳转
						this.module = "show";
						this.moduleTitle = index.title;
						this.moduleContent = index.description;
						this.tip = true;
						break;
					case "other_mini": // 当前页内部弹窗，不跳转
						this.getUrl(index.app_id, index.link)
						break;
				}
				return;
			},
			confirm() {
				this.tip = false;
			},
			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			// 跳转
			navTo(item, type) {
				console.log(item, type)
				if (type == 'info') {
					// 作品详情跳转
					this.comJs.navToInfo(item.id)
					return
				}
				if (type == 'link') {
					switch (item.type) {
						case "web":
							uni.navigateTo({
								url: "/pages/webView/webView?data=" + encodeURIComponent(JSON.stringify(item))
							})
							break;
						case "mini_app":
							console.log(item, type)
							uni.navigateTo({
								url: item.link
							})
							break;
						case "popu":
							this.module = "show";
							this.moduleTitle = item.title;
							this.moduleContent = item.description;
							this.tip = true;
							break;
						case "other_mini": // 当前页内部弹窗，不跳转
							this.getUrl(item.app_id, item.link)
							break;
					}
					return;
				}
				if (type == 'swiper') {
					switch (item.type) {
						case "web": // 项目外部跳转，需要使用web-view跳转外部H5页面
							uni.navigateTo({
								url: "/pages/webView/webView?data=" + encodeURIComponent(JSON.stringify(item))
							})
							break;
						case "mini_app": // 项目内部跳转
							uni.navigateTo({
								url: item.link
							})
							break;
						case "popu": // 当前页内部弹窗，不跳转
							this.module = "show";
							this.moduleTitle = item.title;
							this.moduleContent = item.description;
							this.tip = true;
							break;

					}
					return;
				}
				if (type == 'other') {
					this.getUrl(item.app_id, '/pages/index/index')
				}
			},
		}

	}
</script>
<style lang="scss">
	.header {
		padding: 15rpx 30rpx;
		height: 100rpx;
	}

	/* 头部 轮播图 */
	.carousel-section {
		position: relative;
		padding-top: 10rpx;

		.titleNview-placing {
			height: var(--status-bar-height);
			padding-top: 44px;
			box-sizing: content-box;
		}

		.titleNview-background {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 426upx;
			transition: .4s;
		}
	}

	.carousel {
		width: 100%;
		height: 350upx;

		.carousel-item {
			width: 100%;
			height: 100%;
			padding: 0 28upx;
			overflow: hidden;
		}

		image {
			width: 100%;
			height: 100%;
			border-radius: 10upx;
		}
	}

	.swiper-dots {
		display: flex;
		position: absolute;
		left: 60upx;
		bottom: 15upx;
		width: 72upx;
		height: 36upx;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;

		.num {
			width: 36upx;
			height: 36upx;
			border-radius: 50px;
			font-size: 24upx;
			color: #fff;
			text-align: center;
			line-height: 36upx;
		}

		.sign {
			position: absolute;
			top: 0;
			left: 50%;
			line-height: 36upx;
			font-size: 12upx;
			color: #fff;
			transform: translateX(-50%);
		}
	}

	/* 分类 */
	.cate-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		flex-wrap: wrap;
		padding: 30upx 22upx;
		background: #fff;

		.cate-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: $font-sm + 2upx;
			color: $font-color-dark;
		}

		/* 原图标颜色太深,不想改图了,所以加了透明度 */
		image {
			width: 88upx;
			height: 88upx;
			margin-bottom: 14upx;
			border-radius: 50%;
			opacity: .7;
			box-shadow: 4upx 4upx 20upx rgba(250, 67, 106, 0.3);
		}
	}

	.ad-1 {
		width: 100%;
		height: 210upx;
		padding: 10upx 0;
		background: #fff;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.rich-text {
		padding: 30rpx;
		margin-top: 30rpx;
	}

	.content {
		padding: 10rpx 30rpx;
	}

	.flexbox {
		width: 700rpx;
		border-radius: 20rpx;
		background: #fff;
		margin: 15rpx auto;
		padding: 10rpx;
	}

	.flex1,
	.flexbox {
		display: flex;
	}

	.flex1 {
		align-items: center;
		flex-direction: column;
		justify-content: center;
		width: 25%;
	}

	.icon120 {
		height: 80rpx;
		width: 80rpx;
	}

	.nowrap {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.txt26 {
		color: #000;
		display: block;
		font-size: 26rpx;
		height: 36rpx;
		line-height: 36rpx;
		margin-top: 6rpx;
		text-align: center;
		width: 130rpx;
	}

	.bg-box {
		align-items: center;
		display: flex;
		justify-content: center;
		width: 100%;
	}

	.hot-service {
		background: #fff;
		border-radius: 8rpx;
		box-sizing: border-box;
		// margin: 18rpx;
		overflow: hidden;
		width: 700rpx;
	}

	.bg-img {
		height: 88rpx;
		position: absolute;
		width: 700rpx;
		z-index: 1;
	}

	.hot-service-content {
		padding: 6rpx 6rpx 0;
	}

	.hot-service-title {
		background-position: 50%;
		background-size: cover;
		border-radius: 8rpx 8rpx 0 0;
		display: block;
		height: 88rpx;
		position: relative;
		width: 700rpx;
		z-index: 2;
	}

	.hot-service-title-h3 {
		color: #2e3f56;
		font-size: 32rpx;
		font-weight: 700;
		line-height: 88rpx;
		margin-left: 30rpx;
	}

	.swiper-container {
		height: 300rpx;
		width: 700rpx;
		border-radius: 20rpx;
		background: #fff;
		margin: 10rpx auto;

	}

	.swiper-container-large {
		margin: 20rpx 0;
		height: 350rpx;
	}

	.swiper-container-small {
		margin: 20rpx 0;
		height: 100rpx;
	}

	.swiper-container .swiper-item {
		display: flex;
		flex-wrap: wrap;
	}

	.swiper-container-large .swiper-item {
		display: flex;
		flex-wrap: wrap;
	}

	.swiper-container-small .swiper-item {
		display: flex;
		flex-wrap: wrap;
	}


	.swiper-container-row {
		height: 120rpx;
	}

	.swiper-container-row .swiper-item {
		display: flex;
	}

	.srv-col {
		box-sizing: border-box;
		flex: 1;
		width: 160rpx;
	}

	.srv-item {
		align-items: center;
		display: flex;
		flex-direction: column;
		height: 130rpx;
		justify-content: center;
		text-align: center;
		width: 25%;
		margin-top: 10rpx;
	}

	.srv-item-large {
		align-items: center;
		display: flex;
		flex-direction: column;
		height: 170rpx;
		justify-content: center;
		text-align: center;
		width: 25%;
	}

	.srv-item-small {
		align-items: center;
		display: flex;
		flex-direction: column;
		height: 50rpx;
		justify-content: center;
		text-align: center;
		width: 20%;
	}

	.srv-item:nth-child(4n) {
		margin-right: 0rpx;
	}

	.srv-item-icon {
		height: 80rpx;
		margin-bottom: 20rpx;
		margin-top: 10rpx;
		width: 80rpx;
		border-radius: 80rpx;
	}

	.srv-item-icon-large {
		height: 120rpx;
		margin-bottom: 12rpx;
		margin-top: 6rpx;
		width: 120rpx;
		// border-radius: 80rpx;
	}

	.srv-item-title {
		box-sizing: border-box;
		color: #000;
		display: block;
		font-size: 26rpx;
		height: 36rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-align: center;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 100%;
		margin-top: 10rpx;
	}

	.indicator {
		display: flex;
		height: 8rpx;
		margin-bottom: 30rpx;
		margin-top: 12rpx;
		width: 670rpx;
	}

	.indicator-child {
		background: rgba(56, 136, 255, .5);
		border-radius: 4rpx;
		float: left;
		height: 8rpx;
		margin-right: 10rpx;
		transition: all .3s ease;
		width: 8rpx;
	}

	.active {
		background-color: #3888ff;
		width: 50rpx;
	}

	.bg {
		width: 100%;
	}

	.service-main {
		margin: 0 auto;
	}

	.h2title {
		color: #000;
		display: block;
		font-size: 32rpx;
		height: 60rpx;
		line-height: 60rpx;
		margin-left: 40rpx;
	}

	.listbox {
		background: #fff;
		border: 1rpx solid #ebebeb;
		border-radius: 8rpx;
		// margin: 40rpx 40rpx 0;
		// padding-bottom: 20rpx;
	}

	.titlebox {
		align-items: center;
		color: #3888ff;
		display: flex;
		height: 60rpx;
		justify-content: space-between;
		// padding-bottom: 18rpx;
		// padding-top: 36rpx;
	}

	.service-list {
		background-color: initial !important;
		border: 1rpx solid transparent !important;
		box-shadow: none !important;
		margin-top: 0 !important;
	}

	.service-list-title {
		padding-left: 0rpx !important;
	}

	.viewtitle {
		font-weight: 700;
	}

	.service-main .service-hot-title {
		align-items: center;
		color: #3888ff;
		display: inline-flex;
		font-size: 30rpx;
		height: 40rpx;
		justify-content: space-between;
		line-height: 40rpx;
		width: 133rpx;
	}

	.content.service-hot-list {
		// background-color: #fff;
		border-radius: 8rpx;
		// margin-top: 20rpx;
	}

	.service-main .service-hot-title .refresh-icon {
		height: 27rpx;
		width: 30rpx;
	}

	.service-main .service-hot-list .service-hot-item {
		align-items: center;
		box-shadow: inset 0 -1rpx 0 0 #ebebeb;
		display: flex;
		margin: 0 40rpx;
		padding: 36rpx 0;
		position: relative;
	}

	.service-main .service-hot-list .service-hot-item .title {
		color: #000;
		font-family: PingFangSC-Regular;
		font-size: 30rpx;
		line-height: 40rpx;
		max-width: 540rpx;
	}

	.service-main .service-hot-list .service-hot-item .tag {
		background: rgba(66, 147, 244, .1);
		border-radius: 4rpx;
		color: #4293f4;
		display: inline-block;
		font-family: PingFangSC-Regular;
		font-size: 26rpx;
		font-weight: 700;
		height: 36rpx;
		line-height: 36rpx;
		margin-left: 12rpx;
		padding: 0 12rpx;
	}

	.service-main .service-hot-list .service-hot-item .arrow {
		height: 24rpx;
		position: absolute;
		right: 0;
		width: 14rpx;
	}

	.service-main .service-hot-list .service-hot-item:last-child {
		box-shadow: none;
	}

	.twoNoWrap {
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		display: -webkit-box;
	}

	.nowrap,
	.twoNoWrap {
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.nowrap {
		white-space: nowrap;
	}

	.item {
		border-bottom: 1rpx solid #ebebeb;
		box-sizing: border-box;
		display: flex;
		height: 160rpx;
		margin: 0 auto;
		width: 610rpx;
	}

	.pop-box .item {
		padding-left: 0rpx;
		width: 670rpx;
	}

	.item-icon {
		height: 150rpx;
		margin-right: 30rpx;
		// margin-top: 39rpx;
		vertical-align: middle;
		width: 150rpx;
	}

	.item-text {
		display: flex;
		flex-direction: column;
	}

	.item-title {
		color: #000;
		font-size: 34rpx;
		height: 48rpx;
		line-height: 48rpx;
		margin-bottom: 6rpx;
		margin-top: 36rpx;
	}

	.item-title .nowrap {
		display: inline-block;
		font-weight: 700;
		margin-right: 10rpx;
		max-width: 500rpx;
		vertical-align: middle;
	}

	.item-desc {
		color: rgba(0, 0, 0, .3);
		font-size: 24rpx;
		height: 34rpx;
		line-height: 34rpx;
		margin-bottom: 20rpx;
		width: 456rpx;
	}

	.item-title .topic-tip {
		background: rgba(69, 154, 255, .1);
		border-radius: 4rpx;
		color: #3888ff;
		display: inline-block;
		font-size: 26rpx;
		font-weight: 700;
		height: 36rpx;
		line-height: 36rpx;
		text-align: center;
		width: 50rpx;
	}

	.nowrap {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.service-banner__list {
		padding-left: 0;
	}

	.content__desc {
		color: rgba(0, 0, 0, .3) !important;
		font-size: 24rpx !important;
	}

	.pop-item {
		margin: 0;
	}

	.pop-item:last-child {
		border-bottom: none;
	}

	.list-box {
		// padding-bottom: 20rpx;

		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding: 10rpx 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
			border-bottom: 1rpx solid #ebebeb;


			.top-box {
				position: relative;

				.cover-box-hot {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;

					.cover {
						width: 100%;
						height: 100%;
						min-height: 150rpx;
						border-radius: 10rpx;
					}

					.cover :after {
						background-color: red;
						border-radius: 10rpx;
						color: #fff;
						content: "hot";
						font-size: 25rpx;
						line-height: 1;
						padding: 2rpx 6rpx;
						position: absolute;
						left: 5rpx;
						top: 5rpx;
					}

					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}

				.cover-box {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;

					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
						min-height: 150rpx;
					}

					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}

				.cover-large-box {
					width: 50%;
					height: auto;
					min-height: 200rpx;
					position: relative;

					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}

					.button {
						position: absolute;
						bottom: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: rgba(0, 0, 0, .5) !important;
						color: white;
						padding: 15rpx 20rpx;
						font-size: 20rpx;
					}
				}

				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;

					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}

					.lang-box {
						color: $uni-text-color-grey;
						font-size: 24rpx;
					}

					.title {
						font-weight: bold;
						font-size: 26rpx;
						color: #666666;
					}

					.end-date {
						font-size: 20rpx;
						color: #999999;
					}

					.total {
						font-size: 20rpx;
						color: #39b54a;
					}

					.des {
						font-size: 22rpx;
						color: #8f8f94;


					}

					.price {
						font-size: 24rpx;
						color: red;
						float: right;
					}

					.end {
						font-size: 24rpx;
						color: #0070C0;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}

			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;

				.text-sm {
					font-size: 24upx;
				}

				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}

				.uni-row {
					flex-direction: row;
				}

				.align-center {
					align-items: center;
				}

				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}

		:last-child {
			// border-bottom: 1rpx solid #fff;
		}
	}

	.lp-flex {
		display: flex;
		flex-direction: row;
	}

	.lp-flex-column {
		display: flex;
		flex-direction: column;
	}

	/*弹出层2 交卷 开始*/
	.view-pup2 {
		width: 100%;
		background-color: #FFFFFF;

	}

	.view-pup2-box {
		height: 28rpx;
		background-color: #FFFFFF;
		// background: linear-gradient(to right, #37c788,#5ae4a8);
	}

	.view-pup2-warn {
		font-size: 26rpx;
		text-align: center;
		padding: 20rpx 40rpx;

		.view-pup2-warn-title {
			font-size: 40rpx;
			font-weight: 700;
		}

		.view-pup2-warn-text {
			color: #999;
			padding: 20rpx 0;
			max-width: 600rpx;
		}

	}

	.view-pup2-button {
		display: flex;
		align-items: center;
		font-size: 16px;
		font-weight: 700;
		border: 4rpx solid #f5f5f5;
		margin-top: 10rpx;
		height: 80rpx;
		line-height: 80rpx;

		.view-pup2-button-list {
			width: 100%;
			text-align: center;
		}

		.view-pup2-button-list1 {
			border-left: 4rpx solid #f5f5f5;
			color: #079B48;
		}
	}

	/*弹出层2 交卷  结束*/
	/*公告*/
	.announce {
		width: 700rpx;
		height: 70rpx;
		border-radius: 20rpx;
		padding: 0 35rpx;
		line-height: 70rpx;
		background: #fff;
		margin: 10rpx auto;
		display: flex;

		.announce-title {
			width: 100rpx;
			height: 70rpx;
			line-height: 70rpx;
			font-size: 32rpx;
			font-weight: 800;

			.font1 {
				margin-right: 6rpx;
			}

			.font2 {
				color: #5C7DFF;
				font-size: 35rpx;
			}
		}

		.announce-item {
			position: relative;
			width: 530rpx;
			height: 70rpx;

			.announce-swiper {
				width: 100%;
				height: 70rpx;

				.announce-swiper-item {
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					letter-spacing: 1rpx;
				}
			}
		}
	}

	.an {
		animation: rotation 2s infinite linear;
	}

	.pause {
		animation-play-state: paused;
	}

	.top {
		width: 200rpx;
		margin: auto;
		height: 60rpx;
		border-radius: 20rpx;
		padding: 0 35rpx;
		line-height: 60rpx;
		background: #f1f1f1;
		text-align: center;
		color: blue;

		image {
			width: 30upx;
			height: 22upx;
		}

	}

	.f-header {
		display: flex;
		align-items: center;
		height: 60upx;
		padding: 6upx 30upx 8upx;

		// background: #fff;
		image {
			flex-shrink: 0;
			width: 80upx;
			height: 80upx;
			margin-right: 20upx;
		}

		.tit-box {
			flex: 1;
			display: flex;
			flex-direction: column;
		}

		.tit {
			font-size: 32rpx;
			color: #font-color-dark;
			line-height: 1.3;
			font-weight: bold;
			margin-left: 10rpx;
		}

		.tit2 {
			font-size: $font-sm;
			color: $font-color-light;
		}

		.icon-you {
			font-size: $font-lg +2upx;
			color: $font-color-light;
		}
	}

	// 搜索框
	.search-box {
		height: 70rpx;
		line-height: 70rpx;
		background-color: #fff;
		text-align: left;
		padding-left: 20rpx;
		border-radius: 70rpx;
		color: #666666;
	}

	/*弹出层2 交卷 开始*/
	.view-pup2 {
		width: 100%;
		background-color: #FFFFFF;

	}

	.view-pup2-box {
		height: 28rpx;
		background-color: #FFFFFF;
		// background: linear-gradient(to right, #37c788,#5ae4a8);
	}

	.view-pup2-warn {
		font-size: 26rpx;
		text-align: center;
		padding: 20rpx 40rpx;

		.view-pup2-warn-title {
			font-size: 40rpx;
			font-weight: 700;
		}

		.view-pup2-warn-text {
			color: #999;
			padding: 20rpx 0;
			max-width: 600rpx;
		}

	}

	.view-pup2-button {
		display: flex;
		align-items: center;
		font-size: 16px;
		font-weight: 700;
		border: 4rpx solid #f5f5f5;
		margin-top: 10rpx;
		height: 80rpx;
		line-height: 80rpx;

		.view-pup2-button-list {
			width: 100%;
			text-align: center;
		}

		.view-pup2-button-list1 {
			border-left: 4rpx solid #f5f5f5;
			color: #079B48;
		}
	}

	/*弹出层2 交卷  结束*/

	/* 自定义tabBar适配 */
	.gui-page {
		padding-bottom: 120rpx; /* 为自定义tabBar留出空间 */
	}
</style>