{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/index/index.vue?e672", "webpack:///D:/gst/gst-uniapp/pages/index/index.vue?82bf", "webpack:///D:/gst/gst-uniapp/pages/index/index.vue?2703", "webpack:///D:/gst/gst-uniapp/pages/index/index.vue?9a7d", "uni-app:///pages/index/index.vue", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-button/u-button.vue?5a0d", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-button/u-button.vue?e04f", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-button/u-button.vue?2e23", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-button/u-button.vue?36d5", "uni-app:///components/uview-ui/components/u-button/u-button.vue", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-button/u-button.vue?4f14", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-button/u-button.vue?dfe9", "webpack:///D:/gst/gst-uniapp/pages/index/index.vue?df40", "webpack:///D:/gst/gst-uniapp/pages/index/index.vue?dd50"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "scrollX", "CustomTabbar", "data", "name", "userInfo", "ruleInfo", "news", "swiper<PERSON><PERSON>rent", "swiper<PERSON><PERSON><PERSON>", "carouselList", "src", "background", "goodsList", "iconList1", "iconList2", "newList", "otherList", "isPlaying", "isPaused", "courseList", "noticeList", "page", "rmfwBgUrl", "list1", "tip", "moduleContent", "module", "moreBgUrl", "current", "isShow", "onLoad", "grace<PERSON>s", "ref", "uni", "success", "console", "platform", "saveStorage", "withShareTicket", "menus", "onShareAppMessage", "title", "imageUrl", "path", "onShareTimeline", "methods", "getNews", "navToWeb", "url", "swiper<PERSON><PERSON>e", "navToDetailPage", "navToPage", "toCate", "declick", "getUrl", "appId", "search", "nav", "confirm", "navTo", "props", "hairLine", "type", "default", "size", "shape", "plain", "disabled", "loading", "openType", "formType", "appParameter", "hoverStopPropagation", "lang", "sessionFrom", "sendMessageTitle", "sendMessagePath", "sendMessageImg", "showMessageCard", "hoverBgColor", "rippleBgColor", "ripple", "hoverClass", "customStyle", "dataName", "throttleTime", "hoverStartTime", "hoverStayTime", "computed", "getHoverClass", "showHairLineBorder", "rippleTop", "rippleLeft", "fields", "waveActive", "click", "getWave<PERSON><PERSON>y", "touchesY", "touchesX", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "queryInfo", "resolve", "getphonenumber", "getuserinfo", "error", "opensetting", "launchapp"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2ItnB;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAOA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;QACA;QACAD;UACA;UACA;UACA;UACAE;YACAC;cACA;cACA;cACAC;cACA;cACA;gBACAC;gBACAC;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IACA3C;MACA4C;MACAC;IACA;EAGA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACA;EACAC;IACA;MACAH;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAE;IACA;IACAC;MAAA;MACA;QACAX;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAY;MACA;QACA;UAAA;UACAd;YACAe;UACA;UACA;QACA;UAAA;UACAf;YACAe;UACA;UACA;QACA;UAAA;UACA;UACA;UACA;UACA;UACA;QACA;UAAA;UACA;UACA;MAAA;MAEA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAjB;QACAe;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;MACAlB;QACAe;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAnB;UACAe;QACA;MACA;QACAf;UACAe;QACA;MACA;QACAf;UACAe;QACA;MACA;QACAf;UACAe;QACA;MACA;QACAf;UACAe;QACA;MACA;IAEA;IACAK;MACA;IACA;IACAC;MACAnB;MACAF;QACAsB;QACAZ;QACAT;UACA;QAAA;MAEA;IACA;IACAsB;MACAvB;QACAe;MACA;IACA;IACAS;MACA;QACA;UAAA;UACAxB;YACAe;UACA;UACA;QACA;UAAA;UACAf;YACAe;UACA;UACA;QACA;UAAA;UACA;UACA;UACA;UACA;UACA;QACA;UAAA;UACA;UACA;MAAA;MAEA;IACA;IACAU;MACA;IACA;IACA;AACA;AACA;AACA;IACA;IACAC;MACAxB;MACA;QACA;QACA;QACA;MACA;MACA;QACA;UACA;YACAF;cACAe;YACA;YACA;UACA;YACAb;YACAF;cACAe;YACA;YACA;UACA;YACA;YACA;YACA;YACA;YACA;UACA;YAAA;YACA;YACA;QAAA;QAEA;MACA;MACA;QACA;UACA;YAAA;YACAf;cACAe;YACA;YACA;UACA;YAAA;YACAf;cACAe;YACA;YACA;UACA;YAAA;YACA;YACA;YACA;YACA;YACA;QAAA;QAGA;MACA;MACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwDvpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA,gBA4BA;EACA7C;EACAyD;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;EACA;EACAyB;IACA;IACAC;MACA;MACA;MACA;MACAP;MACA;IACA;IACA;IACAQ;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAxF;IACA;MACAyF;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAjD;IACA;IACAkD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA9F;QACA;QACA;QACA;UACA+F;QAUAC;QACAD;;QAEA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAE;MAAA;MACA;QACA;QACA;QACA;QACAC;QAIAA;QACAA;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpVA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAlvC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\r\n\t\t\t<view style=\"padding:10rpx 30rpx;\">\r\n\t\t\t\t<view class=\"search-box\" @click=\"search\">\r\n\t\t\t\t\t<text class=\"grace-icons icon-search \" style=\"margin-right: 20rpx;\"></text>\r\n\t\t\t\t\t搜索关键词\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- \t\t\t<view class=\"u-demo-block\" style=\"padding:10rpx 30rpx;background-color: #fff;\">\r\n\t\t\t\t<view class=\"u-demo-block__content\">\r\n\t\t\t\t\t<u-tabs :list=\"list1\" @change=\"nav\" :current=\"current\" keyName=\"title\" > -->\r\n\t\t\t<!-- <view slot=\"right\" style=\"padding-right: 4px;\" @click=\"\">\r\n\t\t\t\t\t\t\t<u-icon name=\"list\" size=\"21\" bold></u-icon>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t<!-- \t\t</u-tabs>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<!--分类-->\r\n\t\t\t<swiper bind:change=\"handleChange\" class=\"swiper-container-small\" previousMargin=\"6rpx\">\r\n\t\t\t\t<swiper-item class=\"swiper-item\" data-index=\"index\">\r\n\t\t\t\t\t<view bind:tap=\"handleTap\" class=\"srv-item-small\" v-for=\"(item,index) in list1\" :key=\"index\"\r\n\t\t\t\t\t\t@tap=\"navTo(item,'link')\">\r\n\t\t\t\t\t\t<text class=\"srv-item-title nowrap\">{{item.title}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<!-- 头部轮播 -->\r\n\t\t\t<view class=\"carousel-section\">\r\n\t\t\t\t<!-- 标题栏和状态栏占位符 -->\r\n\t\t\t\t<!-- <view class=\"titleNview-placing\"></view> -->\r\n\t\t\t\t<!-- 背景色区域 -->\r\n\t\t\t\t<!-- <view class=\"titleNview-background\" :style=\"{backgroundColor:titleNViewBackground}\"></view> -->\r\n\t\t\t\t<swiper class=\"carousel\" circular @change=\"swiperChange\" autoplay=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in carouselList\" :key=\"index\" class=\"carousel-item\"\r\n\t\t\t\t\t\t@tap=\"navToWeb(item,'swiper')\">\r\n\t\t\t\t\t\t<image :src=\"item.thumb\" />\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<!-- 自定义swiper指示器 -->\r\n\t\t\t\t<view class=\"swiper-dots\">\r\n\t\t\t\t\t<text class=\"num\">{{swiperCurrent+1}}</text>\r\n\t\t\t\t\t<text class=\"sign\">/</text>\r\n\t\t\t\t\t<text class=\"num\">{{swiperLength}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--分类1-->\r\n\t\t\t<scrollX :list=\"iconList1\" :nums=\"5\" :col=\"2\" />\r\n\t\t\t<view class=\"hot-service\" style=\"margin: auto;\">\r\n\t\t\t\t<image ariaHidden=\"true\" class=\"bg-img\" lazyLoad=\"true\" mode=\"aspectFill\" :src=\"moreBgUrl\"></image>\r\n\t\t\t\t<view ariaRole=\"heading\" class=\"hot-service-title\">\r\n\t\t\t\t\t<view class=\"hot-service-title-h3\">更多课程</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--分类2-->\r\n\t\t\t<scrollX :list=\"iconList2\" :nums=\"5\" :col=\"3\" :size=\"60\" :height=\"130\" />\r\n\t\t\t<!--更多服务-->\r\n\t\t\t<!-- <view class=\"bg-box hbclass\" v-if=\"otherList.length > 0\">\r\n\t\t\t\t<view class=\"hot-service\">\r\n\t\t\t\t\t<image ariaHidden=\"true\" class=\"bg-img\" lazyLoad=\"true\" mode=\"aspectFill\" :src=\"rmfwBgUrl\"></image>\r\n\t\t\t\t\t<view ariaRole=\"heading\" class=\"hot-service-title\">\r\n\t\t\t\t\t<view class=\"hot-service-title-h3\">更多服务</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"hot-service-content\">\r\n\t\t\t\t\t\t<swiper bind:change=\"handleChange\" class=\"swiper-container\" previousMargin=\"6rpx\" >\r\n\t\t\t\t\t\t\t<swiper-item class=\"swiper-item\" data-index=\"index\" >\r\n\t\t\t\t\t\t\t\t<view bind:tap=\"handleTap\" class=\"srv-item\" v-for=\"(item,index) in otherList\"\r\n\t\t\t\t\t\t\t\t\t:key=\"index\" @tap=\"navToWeb(item,'link')\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"srv-item-icon\" :src=\"item.thumb\" style=\"width: 80rpx;height:80rpx;\" mode=\"heightFix\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"srv-item-title nowrap\">{{item.title}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</swiper> -->\r\n\t\t\t<!-- <view class=\"indicator\" wx:if=\"{{list[1]}}\">\r\n\t\t\t                <view style=\"margin: 0 auto\">\r\n\t\t\t                    <view :class=\"indicator-child {{current==index?'active':''}}\" :style=\"indicatorStyle\" v-for=\"(item,index) in superInList\" :key=\"index\"></view>\r\n\t\t\t                </view>\r\n\t\t\t            </view> -->\r\n\t\t\t<!-- \t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\r\n\t\t\t<view style=\"padding: 20rpx 0;\">\r\n\r\n\t\t\t\t<view class=\"everyone-doing bg hbclass\" v-if=\"isShow\">\r\n\t\t\t\t\t<view class=\"service-main\">\r\n\t\t\t\t\t\t<view class=\"listbox service-list\">\r\n\t\t\t\t\t\t\t<view class=\"titlebox\">\r\n\t\t\t\t\t\t\t\t<view class=\"h2title viewtitle\">推荐阅读</view>\r\n\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content service-hot-list\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item1, index1) in courseList\"\r\n\t\t\t\t\t\t\t\t\t\t:key=\"index1\" @tap=\"navToWeb(item1)\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item1.thumb\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"title\">{{item1.title}}</view>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"end\"><text\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"text-align: right;float: right;\">更多</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--弹出层1 提示 开始-->\r\n\t\t\t<u-popup v-model=\"tip\" width=\"80%\" mode=\"center\" border-radius=\"10\" :show=\"tip\"\r\n\t\t\t\t:safeAreaInsetBottom=\"false\">\r\n\t\t\t\t<view class=\"view-pup2\">\r\n\t\t\t\t\t<view class=\"view-pup2-box\"></view>\r\n\t\t\t\t\t<view class=\"view-pup2-warn\">\r\n\t\t\t\t\t\t<view class=\"view-pup2-warn-title\">温馨提示</view>\r\n\t\t\t\t\t\t<view class=\"view-pup2-warn-text\" style=\"text-align: left;\">{{moduleContent}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"view-pup2-button\">\r\n\t\t\t\t\t\t<view class=\"view-pup2-button-list view-pup2-button-list1\" @click=\"confirm()\">确定</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</u-popup>\r\n\t\t</view>\r\n\r\n\t\t<!-- 自定义底部导航 -->\r\n\t\t<custom-tabbar />\r\n\t</gui-page>\r\n</template>\r\n<script>\r\n\tvar graceJs = require('@/GraceUI5/js/grace.js');\r\n\t// 模拟 api 请求数据，格式见 article.js\r\n\tvar artciles = require('@/GraceUI5/demoData/article.js');\r\n\timport u_button from '@/components/uview-ui/components/u-button/u-button.vue';\r\n\timport scrollX from \"@/components/scroll-x/index.vue\";\r\n\timport CustomTabbar from '@/components/custom-tabbar.vue';\r\n\timport {\r\n\t\tshowLoading\r\n\t} from '@/GraceUI5/js/grace.js';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tscrollX,\r\n\t\t\tCustomTabbar,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tname: \"\",\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\truleInfo: {},\r\n\t\t\t\tnews: [], // 新闻推荐\r\n\t\t\t\tswiperCurrent: 0,\r\n\t\t\t\tswiperLength: 3,\r\n\t\t\t\tcarouselList: [{\r\n\t\t\t\t\t\tsrc: \"/static/temp/banner3.jpg\",\r\n\t\t\t\t\t\tbackground: \"rgb(203, 87, 60)\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: \"/static/temp/banner2.jpg\",\r\n\t\t\t\t\t\tbackground: \"rgb(205, 215, 218)\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: \"/static/temp/banner4.jpg\",\r\n\t\t\t\t\t\tbackground: \"rgb(183, 73, 69)\",\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\ticonList1: [],\r\n\t\t\t\ticonList2: [],\r\n\t\t\t\tnewList: [],\r\n\t\t\t\totherList: [],\r\n\t\t\t\tisPlaying: false,\r\n\t\t\t\tisPaused: true,\r\n\t\t\t\tcourseList: [],\r\n\t\t\t\tnoticeList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\trmfwBgUrl: \"https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20221105124531.png\",\r\n\t\t\t\tlist1: [\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tname: '主页',\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// , \r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tname: '考研升博',\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// , \r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tname: '日语实习',\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// // , {\r\n\t\t\t\t\t// // \tname: '日语好工作',\r\n\t\t\t\t\t// // \t// badge: {\r\n\t\t\t\t\t// // \t// \tisDot: true\r\n\t\t\t\t\t// // \t// }\r\n\t\t\t\t\t// // }\r\n\t\t\t\t\t// , {\r\n\t\t\t\t\t// \tname: '外教口语',\r\n\t\t\t\t\t// \t// badge: {\r\n\t\t\t\t\t// \t// \tvalue: 5,\r\n\t\t\t\t\t// \t// }\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tname: '日语研修'\r\n\t\t\t\t\t// }\r\n\t\t\t\t],\r\n\t\t\t\ttip: false,\r\n\t\t\t\tmoduleContent: '敬请期待..',\r\n\t\t\t\tmodule: {},\r\n\t\t\t\tmoreBgUrl: \"https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20230302112933.png\",\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tisShow:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function() {\r\n\t\t\t// 01. 获取页面主体高度\r\n\t\t\tgraceJs.getRefs('guiPage', this, 0, (ref) => {\r\n\t\t\t\tref.getDomSize('guiPageBody', (e) => {\r\n\t\t\t\t\t// 主体高度 = 页面高度 - 自定义区域高度\r\n\t\t\t\t\tgraceJs.select('#myheader', (e2) => {\r\n\t\t\t\t\t\t// 如果自定义区域有 margin 尺寸获取不到请加上这个值，可以利用 uni.upx2px() 转换\r\n\t\t\t\t\t\t// this.mainHeight = e.height - e2.height;\r\n\t\t\t\t\t\tthis.pageLoading = false;\r\n\t\t\t\t\t\tuni.getSystemInfo({\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t// windows | mac为pc端\r\n\t\t\t\t\t\t\t\t// android | ios为手机端\r\n\t\t\t\t\t\t\t\tconsole.log('getSystemInfo,', res.platform);\r\n\t\t\t\t\t\t\t\tthis.platform = res.platform\r\n\t\t\t\t\t\t\t\tthis.$store.commit('setSystemInfo', {\r\n\t\t\t\t\t\t\t\t\tplatform: res.platform || '',\r\n\t\t\t\t\t\t\t\t\tsaveStorage: true\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.getNews();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\twx.showShareMenu({\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: [\"shareAppMessage\", \"shareTimeline\"]\r\n\t\t\t})\r\n\r\n\r\n\t\t},\r\n\t\tonShareAppMessage() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: \"日语云课\", //标题\r\n\t\t\t\timageUrl: \"\", //封面\r\n\t\t\t\tpath: \"/pages/index/index\" //此处链接为要分享的页面链接\t\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 分享到朋友圈\r\n\t\tonShareTimeline() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: \"日语云课\", //标题\r\n\t\t\t\timageUrl: \"https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/shixilogo.jpg\", //封面\r\n\t\t\t\tpath: \"/pages/index/index\" //此处链接为要分享的页面链接\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取新闻推荐\r\n\t\t\tgetNews() {\r\n\t\t\t\tthis.$http.get(\"v1/course/index\").then(res => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\t//this.goodsList = res.data.data.list\r\n\t\t\t\t\t\tthis.carouselList = res.data.data.banner\r\n\t\t\t\t\t\tthis.swiperLength = res.data.data.banner.length\r\n\t\t\t\t\t\tthis.iconList1 = res.data.data.course1;\r\n\t\t\t\t\t\tthis.iconList2 = res.data.data.course2;\r\n\t\t\t\t\t\tthis.newList = res.data.data.new;\r\n\t\t\t\t\t\tthis.otherList = res.data.data.game_show_footer;\r\n\t\t\t\t\t\tthis.courseList = res.data.data.game_foot_post[0].lists;\r\n\t\t\t\t\t\tthis.noticeList = res.data.data.notice;\r\n\t\t\t\t\t\tthis.list1 = res.data.data.top_post;\r\n\t\t\t\t\t\tthis.isShow = res.data.data.isShow;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavToWeb(item) {\r\n\t\t\t\tswitch (item.type) {\r\n\t\t\t\t\tcase \"web\": // 项目外部跳转，需要使用web-view跳转外部H5页面\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: \"/pages/webView/webView?data=\" + encodeURIComponent(JSON.stringify(item))\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"mini_app\": // 项目内部跳转\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: item.link\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"popu\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\tthis.moduleTitle = item.title;\r\n\t\t\t\t\t\tthis.moduleContent = item.description;\r\n\t\t\t\t\t\tthis.tip = true;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"other_mini\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tthis.getUrl(item.app_id, item.link)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t},\r\n\t\t\t//轮播图切换修改背景色\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tconst index = e.detail.current;\r\n\t\t\t\tthis.swiperCurrent = index;\r\n\t\t\t},\r\n\t\t\t//详情页\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/product/product?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情页\r\n\t\t\tnavToPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.app_id;\r\n\t\t\t\tlet title = item.title;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/category/list?id=${id}&title=${title}&style=2`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoCate(item) {\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tlet title = item.name;\r\n\t\t\t\tlet path = item.path;\r\n\t\t\t\tlet style = item.style;\r\n\t\t\t\t// if(path == null || path == ''){\r\n\t\t\t\t// \tuni.navigateTo({\r\n\t\t\t\t// \t\turl: `/pages/category/list?id=${id}&title=${title}`,\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// }else{\r\n\t\t\t\t// \tuni.navigateTo({\r\n\t\t\t\t// \t\turl: `${path}?id=${id}&title=${title}`,\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// }\r\n\t\t\t\tif (style == 4) {\r\n\t\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t\t// \turl: `/pages/course/list?id=${id}&title=${title}`,\r\n\t\t\t\t\t// \t})\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/category/list?id=${id}&title=${title}&style=${style}`,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (style == 1 || style == 2) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/category/list?id=${id}&title=${title}&style=${style}`,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (style == 3) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/category/list-page?id=${id}&title=${title}&style=${style}`,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (style == 5) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/category/list-other?id=${id}&title=${title}&style=${style}`,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (style == 6) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/category/list-recommend?id=${id}&title=${title}&style=${style}`,\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tdeclick(type) {\r\n\t\t\t\tthis.loadHotData()\r\n\t\t\t},\r\n\t\t\tgetUrl(appId, path) {\r\n\t\t\t\tconsole.log(appId)\r\n\t\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\t\tappId: appId,\r\n\t\t\t\t\tpath: path,\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t// 打开成功\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsearch() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/category/search`,\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tnav(index) {\r\n\t\t\t\tswitch (index.type) {\r\n\t\t\t\t\tcase \"web\": // 项目外部跳转，需要使用web-view跳转外部H5页面\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: \"/pages/webView/webView?data=\" + encodeURIComponent(JSON.stringify(index))\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"mini_app\": // 项目内部跳转\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: index.link\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"popu\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\tthis.moduleTitle = index.title;\r\n\t\t\t\t\t\tthis.moduleContent = index.description;\r\n\t\t\t\t\t\tthis.tip = true;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"other_mini\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tthis.getUrl(index.app_id, index.link)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t},\r\n\t\t\tconfirm() {\r\n\t\t\t\tthis.tip = false;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 统一跳转接口,拦截未登录路由\r\n\t\t\t * navigator标签现在默认没有转场动画，所以用view\r\n\t\t\t */\r\n\t\t\t// 跳转\r\n\t\t\tnavTo(item, type) {\r\n\t\t\t\tconsole.log(item, type)\r\n\t\t\t\tif (type == 'info') {\r\n\t\t\t\t\t// 作品详情跳转\r\n\t\t\t\t\tthis.comJs.navToInfo(item.id)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (type == 'link') {\r\n\t\t\t\t\tswitch (item.type) {\r\n\t\t\t\t\t\tcase \"web\":\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/webView/webView?data=\" + encodeURIComponent(JSON.stringify(item))\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"mini_app\":\r\n\t\t\t\t\t\t\tconsole.log(item, type)\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: item.link\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"popu\":\r\n\t\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\t\tthis.moduleTitle = item.title;\r\n\t\t\t\t\t\t\tthis.moduleContent = item.description;\r\n\t\t\t\t\t\t\tthis.tip = true;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"other_mini\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\t\tthis.getUrl(item.app_id, item.link)\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (type == 'swiper') {\r\n\t\t\t\t\tswitch (item.type) {\r\n\t\t\t\t\t\tcase \"web\": // 项目外部跳转，需要使用web-view跳转外部H5页面\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/webView/webView?data=\" + encodeURIComponent(JSON.stringify(item))\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"mini_app\": // 项目内部跳转\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: item.link\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"popu\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\t\tthis.moduleTitle = item.title;\r\n\t\t\t\t\t\t\tthis.moduleContent = item.description;\r\n\t\t\t\t\t\t\tthis.tip = true;\r\n\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (type == 'other') {\r\n\t\t\t\t\tthis.getUrl(item.app_id, '/pages/index/index')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.header {\r\n\t\tpadding: 15rpx 30rpx;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t/* 头部 轮播图 */\r\n\t.carousel-section {\r\n\t\tposition: relative;\r\n\t\tpadding-top: 10rpx;\r\n\r\n\t\t.titleNview-placing {\r\n\t\t\theight: var(--status-bar-height);\r\n\t\t\tpadding-top: 44px;\r\n\t\t\tbox-sizing: content-box;\r\n\t\t}\r\n\r\n\t\t.titleNview-background {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 426upx;\r\n\t\t\ttransition: .4s;\r\n\t\t}\r\n\t}\r\n\r\n\t.carousel {\r\n\t\twidth: 100%;\r\n\t\theight: 350upx;\r\n\r\n\t\t.carousel-item {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tpadding: 0 28upx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tborder-radius: 10upx;\r\n\t\t}\r\n\t}\r\n\r\n\t.swiper-dots {\r\n\t\tdisplay: flex;\r\n\t\tposition: absolute;\r\n\t\tleft: 60upx;\r\n\t\tbottom: 15upx;\r\n\t\twidth: 72upx;\r\n\t\theight: 36upx;\r\n\t\tbackground-image: url(data:image/png;base64,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);\r\n\t\tbackground-size: 100% 100%;\r\n\r\n\t\t.num {\r\n\t\t\twidth: 36upx;\r\n\t\t\theight: 36upx;\r\n\t\t\tborder-radius: 50px;\r\n\t\t\tfont-size: 24upx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 36upx;\r\n\t\t}\r\n\r\n\t\t.sign {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 50%;\r\n\t\t\tline-height: 36upx;\r\n\t\t\tfont-size: 12upx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 分类 */\r\n\t.cate-section {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 30upx 22upx;\r\n\t\tbackground: #fff;\r\n\r\n\t\t.cate-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t}\r\n\r\n\t\t/* 原图标颜色太深,不想改图了,所以加了透明度 */\r\n\t\timage {\r\n\t\t\twidth: 88upx;\r\n\t\t\theight: 88upx;\r\n\t\t\tmargin-bottom: 14upx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\topacity: .7;\r\n\t\t\tbox-shadow: 4upx 4upx 20upx rgba(250, 67, 106, 0.3);\r\n\t\t}\r\n\t}\r\n\r\n\t.ad-1 {\r\n\t\twidth: 100%;\r\n\t\theight: 210upx;\r\n\t\tpadding: 10upx 0;\r\n\t\tbackground: #fff;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.rich-text {\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.content {\r\n\t\tpadding: 10rpx 30rpx;\r\n\t}\r\n\r\n\t.flexbox {\r\n\t\twidth: 700rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tmargin: 15rpx auto;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.flex1,\r\n\t.flexbox {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.flex1 {\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\twidth: 25%;\r\n\t}\r\n\r\n\t.icon120 {\r\n\t\theight: 80rpx;\r\n\t\twidth: 80rpx;\r\n\t}\r\n\r\n\t.nowrap {\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.txt26 {\r\n\t\tcolor: #000;\r\n\t\tdisplay: block;\r\n\t\tfont-size: 26rpx;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-top: 6rpx;\r\n\t\ttext-align: center;\r\n\t\twidth: 130rpx;\r\n\t}\r\n\r\n\t.bg-box {\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.hot-service {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\t// margin: 18rpx;\r\n\t\toverflow: hidden;\r\n\t\twidth: 700rpx;\r\n\t}\r\n\r\n\t.bg-img {\r\n\t\theight: 88rpx;\r\n\t\tposition: absolute;\r\n\t\twidth: 700rpx;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.hot-service-content {\r\n\t\tpadding: 6rpx 6rpx 0;\r\n\t}\r\n\r\n\t.hot-service-title {\r\n\t\tbackground-position: 50%;\r\n\t\tbackground-size: cover;\r\n\t\tborder-radius: 8rpx 8rpx 0 0;\r\n\t\tdisplay: block;\r\n\t\theight: 88rpx;\r\n\t\tposition: relative;\r\n\t\twidth: 700rpx;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.hot-service-title-h3 {\r\n\t\tcolor: #2e3f56;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 700;\r\n\t\tline-height: 88rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\r\n\t.swiper-container {\r\n\t\theight: 300rpx;\r\n\t\twidth: 700rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tmargin: 10rpx auto;\r\n\r\n\t}\r\n\r\n\t.swiper-container-large {\r\n\t\tmargin: 20rpx 0;\r\n\t\theight: 350rpx;\r\n\t}\r\n\r\n\t.swiper-container-small {\r\n\t\tmargin: 20rpx 0;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t.swiper-container .swiper-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.swiper-container-large .swiper-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.swiper-container-small .swiper-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\r\n\t.swiper-container-row {\r\n\t\theight: 120rpx;\r\n\t}\r\n\r\n\t.swiper-container-row .swiper-item {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.srv-col {\r\n\t\tbox-sizing: border-box;\r\n\t\tflex: 1;\r\n\t\twidth: 160rpx;\r\n\t}\r\n\r\n\t.srv-item {\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 130rpx;\r\n\t\tjustify-content: center;\r\n\t\ttext-align: center;\r\n\t\twidth: 25%;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.srv-item-large {\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 170rpx;\r\n\t\tjustify-content: center;\r\n\t\ttext-align: center;\r\n\t\twidth: 25%;\r\n\t}\r\n\r\n\t.srv-item-small {\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 50rpx;\r\n\t\tjustify-content: center;\r\n\t\ttext-align: center;\r\n\t\twidth: 20%;\r\n\t}\r\n\r\n\t.srv-item:nth-child(4n) {\r\n\t\tmargin-right: 0rpx;\r\n\t}\r\n\r\n\t.srv-item-icon {\r\n\t\theight: 80rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\twidth: 80rpx;\r\n\t\tborder-radius: 80rpx;\r\n\t}\r\n\r\n\t.srv-item-icon-large {\r\n\t\theight: 120rpx;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tmargin-top: 6rpx;\r\n\t\twidth: 120rpx;\r\n\t\t// border-radius: 80rpx;\r\n\t}\r\n\r\n\t.srv-item-title {\r\n\t\tbox-sizing: border-box;\r\n\t\tcolor: #000;\r\n\t\tdisplay: block;\r\n\t\tfont-size: 26rpx;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-align: center;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.indicator {\r\n\t\tdisplay: flex;\r\n\t\theight: 8rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t\twidth: 670rpx;\r\n\t}\r\n\r\n\t.indicator-child {\r\n\t\tbackground: rgba(56, 136, 255, .5);\r\n\t\tborder-radius: 4rpx;\r\n\t\tfloat: left;\r\n\t\theight: 8rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\ttransition: all .3s ease;\r\n\t\twidth: 8rpx;\r\n\t}\r\n\r\n\t.active {\r\n\t\tbackground-color: #3888ff;\r\n\t\twidth: 50rpx;\r\n\t}\r\n\r\n\t.bg {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.service-main {\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.h2title {\r\n\t\tcolor: #000;\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-left: 40rpx;\r\n\t}\r\n\r\n\t.listbox {\r\n\t\tbackground: #fff;\r\n\t\tborder: 1rpx solid #ebebeb;\r\n\t\tborder-radius: 8rpx;\r\n\t\t// margin: 40rpx 40rpx 0;\r\n\t\t// padding-bottom: 20rpx;\r\n\t}\r\n\r\n\t.titlebox {\r\n\t\talign-items: center;\r\n\t\tcolor: #3888ff;\r\n\t\tdisplay: flex;\r\n\t\theight: 60rpx;\r\n\t\tjustify-content: space-between;\r\n\t\t// padding-bottom: 18rpx;\r\n\t\t// padding-top: 36rpx;\r\n\t}\r\n\r\n\t.service-list {\r\n\t\tbackground-color: initial !important;\r\n\t\tborder: 1rpx solid transparent !important;\r\n\t\tbox-shadow: none !important;\r\n\t\tmargin-top: 0 !important;\r\n\t}\r\n\r\n\t.service-list-title {\r\n\t\tpadding-left: 0rpx !important;\r\n\t}\r\n\r\n\t.viewtitle {\r\n\t\tfont-weight: 700;\r\n\t}\r\n\r\n\t.service-main .service-hot-title {\r\n\t\talign-items: center;\r\n\t\tcolor: #3888ff;\r\n\t\tdisplay: inline-flex;\r\n\t\tfont-size: 30rpx;\r\n\t\theight: 40rpx;\r\n\t\tjustify-content: space-between;\r\n\t\tline-height: 40rpx;\r\n\t\twidth: 133rpx;\r\n\t}\r\n\r\n\t.content.service-hot-list {\r\n\t\t// background-color: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\t// margin-top: 20rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-title .refresh-icon {\r\n\t\theight: 27rpx;\r\n\t\twidth: 30rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item {\r\n\t\talign-items: center;\r\n\t\tbox-shadow: inset 0 -1rpx 0 0 #ebebeb;\r\n\t\tdisplay: flex;\r\n\t\tmargin: 0 40rpx;\r\n\t\tpadding: 36rpx 0;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item .title {\r\n\t\tcolor: #000;\r\n\t\tfont-family: PingFangSC-Regular;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tmax-width: 540rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item .tag {\r\n\t\tbackground: rgba(66, 147, 244, .1);\r\n\t\tborder-radius: 4rpx;\r\n\t\tcolor: #4293f4;\r\n\t\tdisplay: inline-block;\r\n\t\tfont-family: PingFangSC-Regular;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 700;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-left: 12rpx;\r\n\t\tpadding: 0 12rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item .arrow {\r\n\t\theight: 24rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\twidth: 14rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item:last-child {\r\n\t\tbox-shadow: none;\r\n\t}\r\n\r\n\t.twoNoWrap {\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\tdisplay: -webkit-box;\r\n\t}\r\n\r\n\t.nowrap,\r\n\t.twoNoWrap {\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t}\r\n\r\n\t.nowrap {\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.item {\r\n\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\theight: 160rpx;\r\n\t\tmargin: 0 auto;\r\n\t\twidth: 610rpx;\r\n\t}\r\n\r\n\t.pop-box .item {\r\n\t\tpadding-left: 0rpx;\r\n\t\twidth: 670rpx;\r\n\t}\r\n\r\n\t.item-icon {\r\n\t\theight: 150rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t\t// margin-top: 39rpx;\r\n\t\tvertical-align: middle;\r\n\t\twidth: 150rpx;\r\n\t}\r\n\r\n\t.item-text {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.item-title {\r\n\t\tcolor: #000;\r\n\t\tfont-size: 34rpx;\r\n\t\theight: 48rpx;\r\n\t\tline-height: 48rpx;\r\n\t\tmargin-bottom: 6rpx;\r\n\t\tmargin-top: 36rpx;\r\n\t}\r\n\r\n\t.item-title .nowrap {\r\n\t\tdisplay: inline-block;\r\n\t\tfont-weight: 700;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmax-width: 500rpx;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\r\n\t.item-desc {\r\n\t\tcolor: rgba(0, 0, 0, .3);\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 34rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\twidth: 456rpx;\r\n\t}\r\n\r\n\t.item-title .topic-tip {\r\n\t\tbackground: rgba(69, 154, 255, .1);\r\n\t\tborder-radius: 4rpx;\r\n\t\tcolor: #3888ff;\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 700;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\ttext-align: center;\r\n\t\twidth: 50rpx;\r\n\t}\r\n\r\n\t.nowrap {\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.service-banner__list {\r\n\t\tpadding-left: 0;\r\n\t}\r\n\r\n\t.content__desc {\r\n\t\tcolor: rgba(0, 0, 0, .3) !important;\r\n\t\tfont-size: 24rpx !important;\r\n\t}\r\n\r\n\t.pop-item {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.pop-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.list-box {\r\n\t\t// padding-bottom: 20rpx;\r\n\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\r\n\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.cover-large-box {\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, .5) !important;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.des {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.end {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #0070C0;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t:last-child {\r\n\t\t\t// border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.lp-flex {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.lp-flex-column {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/*弹出层2 交卷 开始*/\r\n\t.view-pup2 {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #FFFFFF;\r\n\r\n\t}\r\n\r\n\t.view-pup2-box {\r\n\t\theight: 28rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\t// background: linear-gradient(to right, #37c788,#5ae4a8);\r\n\t}\r\n\r\n\t.view-pup2-warn {\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx 40rpx;\r\n\r\n\t\t.view-pup2-warn-title {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tfont-weight: 700;\r\n\t\t}\r\n\r\n\t\t.view-pup2-warn-text {\r\n\t\t\tcolor: #999;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tmax-width: 600rpx;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.view-pup2-button {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 700;\r\n\t\tborder: 4rpx solid #f5f5f5;\r\n\t\tmargin-top: 10rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\r\n\t\t.view-pup2-button-list {\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.view-pup2-button-list1 {\r\n\t\t\tborder-left: 4rpx solid #f5f5f5;\r\n\t\t\tcolor: #079B48;\r\n\t\t}\r\n\t}\r\n\r\n\t/*弹出层2 交卷  结束*/\r\n\t/*公告*/\r\n\t.announce {\r\n\t\twidth: 700rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 0 35rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tbackground: #fff;\r\n\t\tmargin: 10rpx auto;\r\n\t\tdisplay: flex;\r\n\r\n\t\t.announce-title {\r\n\t\t\twidth: 100rpx;\r\n\t\t\theight: 70rpx;\r\n\t\t\tline-height: 70rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 800;\r\n\r\n\t\t\t.font1 {\r\n\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.font2 {\r\n\t\t\t\tcolor: #5C7DFF;\r\n\t\t\t\tfont-size: 35rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.announce-item {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 530rpx;\r\n\t\t\theight: 70rpx;\r\n\r\n\t\t\t.announce-swiper {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 70rpx;\r\n\r\n\t\t\t\t.announce-swiper-item {\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tletter-spacing: 1rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.an {\r\n\t\tanimation: rotation 2s infinite linear;\r\n\t}\r\n\r\n\t.pause {\r\n\t\tanimation-play-state: paused;\r\n\t}\r\n\r\n\t.top {\r\n\t\twidth: 200rpx;\r\n\t\tmargin: auto;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 0 35rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tbackground: #f1f1f1;\r\n\t\ttext-align: center;\r\n\t\tcolor: blue;\r\n\r\n\t\timage {\r\n\t\t\twidth: 30upx;\r\n\t\t\theight: 22upx;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.f-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 60upx;\r\n\t\tpadding: 6upx 30upx 8upx;\r\n\r\n\t\t// background: #fff;\r\n\t\timage {\r\n\t\t\tflex-shrink: 0;\r\n\t\t\twidth: 80upx;\r\n\t\t\theight: 80upx;\r\n\t\t\tmargin-right: 20upx;\r\n\t\t}\r\n\r\n\t\t.tit-box {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.tit {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #font-color-dark;\r\n\t\t\tline-height: 1.3;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t}\r\n\r\n\t\t.tit2 {\r\n\t\t\tfont-size: $font-sm;\r\n\t\t\tcolor: $font-color-light;\r\n\t\t}\r\n\r\n\t\t.icon-you {\r\n\t\t\tfont-size: $font-lg +2upx;\r\n\t\t\tcolor: $font-color-light;\r\n\t\t}\r\n\t}\r\n\r\n\t// 搜索框\r\n\t.search-box {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tbackground-color: #fff;\r\n\t\ttext-align: left;\r\n\t\tpadding-left: 20rpx;\r\n\t\tborder-radius: 70rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/*弹出层2 交卷 开始*/\r\n\t.view-pup2 {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #FFFFFF;\r\n\r\n\t}\r\n\r\n\t.view-pup2-box {\r\n\t\theight: 28rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\t// background: linear-gradient(to right, #37c788,#5ae4a8);\r\n\t}\r\n\r\n\t.view-pup2-warn {\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx 40rpx;\r\n\r\n\t\t.view-pup2-warn-title {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tfont-weight: 700;\r\n\t\t}\r\n\r\n\t\t.view-pup2-warn-text {\r\n\t\t\tcolor: #999;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tmax-width: 600rpx;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.view-pup2-button {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 700;\r\n\t\tborder: 4rpx solid #f5f5f5;\r\n\t\tmargin-top: 10rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\r\n\t\t.view-pup2-button-list {\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.view-pup2-button-list1 {\r\n\t\t\tborder-left: 4rpx solid #f5f5f5;\r\n\t\t\tcolor: #079B48;\r\n\t\t}\r\n\t}\r\n\r\n\t/*弹出层2 交卷  结束*/\r\n\r\n\t/* 自定义tabBar适配 */\r\n\t.gui-page {\r\n\t\tpadding-bottom: 120rpx; /* 为自定义tabBar留出空间 */\r\n\t}\r\n</style>", "import { render, staticRenderFns, recyclableRender, components } from \"./u-button.vue?vue&type=template&id=8ef4aa1a&scoped=true&\"\nvar renderjs\nimport script from \"./u-button.vue?vue&type=script&lang=js&\"\nexport * from \"./u-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-button.vue?vue&type=style&index=0&id=8ef4aa1a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8ef4aa1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-button/u-button.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=template&id=8ef4aa1a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    _vm.customStyle,\n    {\n      overflow: _vm.ripple ? \"hidden\" : \"visible\",\n    },\n  ])\n  var m0 = Number(_vm.hoverStartTime)\n  var m1 = Number(_vm.hoverStayTime)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=script&lang=js&\"", "<template>\n\t<button\n\t\tid=\"u-wave-btn\"\n\t\tclass=\"u-btn u-line-1 u-fix-ios-appearance\"\n\t\t:class=\"[\n\t\t\t'u-size-' + size,\n\t\t\tplain ? 'u-btn--' + type + '--plain' : '',\n\t\t\tloading ? 'u-loading' : '',\n\t\t\tshape == 'circle' ? 'u-round-circle' : '',\n\t\t\thairLine ? showHairLineBorder : 'u-btn--bold-border',\n\t\t\t'u-btn--' + type,\n\t\t\tdisabled ? `u-btn--${type}--disabled` : '',\n\t\t]\"\n\t\t:hover-start-time=\"Number(hoverStartTime)\"\n\t\t:hover-stay-time=\"Number(hoverStayTime)\"\n\t\t:disabled=\"disabled\"\n\t\t:form-type=\"formType\"\n\t\t:open-type=\"openType\"\n\t\t:app-parameter=\"appParameter\"\n\t\t:hover-stop-propagation=\"hoverStopPropagation\"\n\t\t:send-message-title=\"sendMessageTitle\"\n\t\tsend-message-path=\"sendMessagePath\"\n\t\t:lang=\"lang\"\n\t\t:data-name=\"dataName\"\n\t\t:session-from=\"sessionFrom\"\n\t\t:send-message-img=\"sendMessageImg\"\n\t\t:show-message-card=\"showMessageCard\"\n\t\t@getphonenumber=\"getphonenumber\"\n\t\t@getuserinfo=\"getuserinfo\"\n\t\t@error=\"error\"\n\t\t@opensetting=\"opensetting\"\n\t\t@launchapp=\"launchapp\"\n\t\t:style=\"[customStyle, {\n\t\t\toverflow: ripple ? 'hidden' : 'visible'\n\t\t}]\"\n\t\**********=\"click($event)\"\n\t\t:hover-class=\"getHoverClass\"\n\t\t:loading=\"loading\"\n\t>\n\t\t<slot></slot>\n\t\t<view\n\t\t\tv-if=\"ripple\"\n\t\t\tclass=\"u-wave-ripple\"\n\t\t\t:class=\"[waveActive ? 'u-wave-active' : '']\"\n\t\t\t:style=\"{\n\t\t\t\ttop: rippleTop + 'px',\n\t\t\t\tleft: rippleLeft + 'px',\n\t\t\t\twidth: fields.targetWidth + 'px',\n\t\t\t\theight: fields.targetWidth + 'px',\n\t\t\t\t'background-color': rippleBgColor || 'rgba(0, 0, 0, 0.15)'\n\t\t\t}\"\n\t\t></view>\n\t</button>\n</template>\n\n<script>\n/**\n * button 按钮\n * @description Button 按钮\n * @tutorial https://www.uviewui.com/components/button.html\n * @property {String} size 按钮的大小\n * @property {Boolean} ripple 是否开启点击水波纹效果\n * @property {String} ripple-bg-color 水波纹的背景色，ripple为true时有效\n * @property {String} type 按钮的样式类型\n * @property {Boolean} plain 按钮是否镂空，背景色透明\n * @property {Boolean} disabled 是否禁用\n * @property {Boolean} hair-line 是否显示按钮的细边框(默认true)\n * @property {Boolean} shape 按钮外观形状，见文档说明\n * @property {Boolean} loading 按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈)\n * @property {String} form-type 用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\n * @property {String} open-type 开放能力\n * @property {String} data-name 额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\n * @property {String} hover-class 指定按钮按下去的样式类。当 hover-class=\"none\" 时，没有点击态效果(App-nvue 平台暂不支持)\n * @property {Number} hover-start-time 按住后多久出现点击态，单位毫秒\n * @property {Number} hover-stay-time 手指松开后点击态保留时间，单位毫秒\n * @property {Object} custom-style 对按钮的自定义样式，对象形式，见文档说明\n * @event {Function} click 按钮点击\n * @event {Function} getphonenumber open-type=\"getPhoneNumber\"时有效\n * @event {Function} getuserinfo 用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo\n * @event {Function} error 当使用开放能力时，发生错误的回调\n * @event {Function} opensetting 在打开授权设置页并关闭后回调\n * @event {Function} launchapp 打开 APP 成功的回调\n * @example <u-button>月落</u-button>\n */\nexport default {\n\tname: 'u-button',\n\tprops: {\n\t\t// 是否细边框\n\t\thairLine: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 按钮的预置样式，default，primary，error，warning，success\n\t\ttype: {\n\t\t\ttype: String,\n\t\t\tdefault: 'default'\n\t\t},\n\t\t// 按钮尺寸，default，medium，mini\n\t\tsize: {\n\t\t\ttype: String,\n\t\t\tdefault: 'default'\n\t\t},\n\t\t// 按钮形状，circle（两边为半圆），square（带圆角）\n\t\tshape: {\n\t\t\ttype: String,\n\t\t\tdefault: 'square'\n\t\t},\n\t\t// 按钮是否镂空\n\t\tplain: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否禁止状态\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否加载中\n\t\tloading: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 开放能力，具体请看uniapp稳定关于button组件部分说明\n\t\t// https://uniapp.dcloud.io/component/button\n\t\topenType: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\n\t\t// 取值为submit（提交表单），reset（重置表单）\n\t\tformType: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效\n\t\t// 只微信小程序、QQ小程序有效\n\t\tappParameter: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 指定是否阻止本节点的祖先节点出现点击态，微信小程序有效\n\t\thoverStopPropagation: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文。只微信小程序有效\n\t\tlang: {\n\t\t\ttype: String,\n\t\t\tdefault: 'en'\n\t\t},\n\t\t// 会话来源，open-type=\"contact\"时有效。只微信小程序有效\n\t\tsessionFrom: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 会话内消息卡片标题，open-type=\"contact\"时有效\n\t\t// 默认当前标题，只微信小程序有效\n\t\tsendMessageTitle: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 会话内消息卡片点击跳转小程序路径，open-type=\"contact\"时有效\n\t\t// 默认当前分享路径，只微信小程序有效\n\t\tsendMessagePath: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 会话内消息卡片图片，open-type=\"contact\"时有效\n\t\t// 默认当前页面截图，只微信小程序有效\n\t\tsendMessageImg: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，\n\t\t// 用户点击后可以快速发送小程序消息，open-type=\"contact\"时有效\n\t\tshowMessageCard: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 手指按（触摸）按钮时按钮时的背景颜色\n\t\thoverBgColor: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 水波纹的背景颜色\n\t\trippleBgColor: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 是否开启水波纹效果\n\t\tripple: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 按下的类名\n\t\thoverClass: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 自定义样式，对象形式\n\t\tcustomStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {};\n\t\t\t}\n\t\t},\n\t\t// 额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\n\t\tdataName: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 节流，一定时间内只能触发一次\n\t\tthrottleTime: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 1000\n\t\t},\n\t\t// 按住后多久出现点击态，单位毫秒\n\t\thoverStartTime: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 20\n\t\t},\n\t\t// 手指松开后点击态保留时间，单位毫秒\n\t\thoverStayTime: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 150\n\t\t},\n\t},\n\tcomputed: {\n\t\t// 当没有传bgColor变量时，按钮按下去的颜色类名\n\t\tgetHoverClass() {\n\t\t\t// 如果开启水波纹效果，则不启用hover-class效果\n\t\t\tif (this.loading || this.disabled || this.ripple || this.hoverClass) return '';\n\t\t\tlet hoverClass = '';\n\t\t\thoverClass = this.plain ? 'u-' + this.type + '-plain-hover' : 'u-' + this.type + '-hover';\n\t\t\treturn hoverClass;\n\t\t},\n\t\t// 在'primary', 'success', 'error', 'warning'类型下，不显示边框，否则会造成四角有毛刺现象\n\t\tshowHairLineBorder() {\n\t\t\tif (['primary', 'success', 'error', 'warning'].indexOf(this.type) >= 0 && !this.plain) {\n\t\t\t\treturn '';\n\t\t\t} else {\n\t\t\t\treturn 'u-hairline-border';\n\t\t\t}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\trippleTop: 0, // 水波纹的起点Y坐标到按钮上边界的距离\n\t\t\trippleLeft: 0, // 水波纹起点X坐标到按钮左边界的距离\n\t\t\tfields: {}, // 波纹按钮节点信息\n\t\t\twaveActive: false // 激活水波纹\n\t\t};\n\t},\n\tmethods: {\n\t\t// 按钮点击\n\t\tclick(e) {\n\t\t\t// 进行节流控制，每this.throttle毫秒内，只在开始处执行\n\t\t\t//this.$u.throttle(() => {\n\t\t\t\t// 如果按钮时disabled和loading状态，不触发水波纹效果\n\t\t\t//\tif (this.loading === true || this.disabled === true) return;\n\t\t\t\t// 是否开启水波纹效果\n\t\t\t\t//if (this.ripple) {\n\t\t\t\t\t// 每次点击时，移除上一次的类，再次添加，才能触发动画效果\n\t\t\t\t//\tthis.waveActive = false;\n\t\t\t\t//\tthis.$nextTick(function() {\n\t\t\t\t//\t\tthis.getWaveQuery(e);\n\t\t\t\t//\t});\n\t\t\t\t//}\n\t\t\t\tthis.$emit('click', e);\n\t\t\t//}, this.throttleTime);\n\t\t},\n\t\t// 查询按钮的节点信息\n\t\tgetWaveQuery(e) {\n\t\t\tthis.getElQuery().then(res => {\n\t\t\t\t// 查询返回的是一个数组节点\n\t\t\t\tlet data = res[0];\n\t\t\t\t// 查询不到节点信息，不操作\n\t\t\t\tif (!data.width || !data.width) return;\n\t\t\t\t// 水波纹的最终形态是一个正方形(通过border-radius让其变为一个圆形)，这里要保证正方形的边长等于按钮的最长边\n\t\t\t\t// 最终的方形（变换后的圆形）才能覆盖整个按钮\n\t\t\t\tdata.targetWidth = data.height > data.width ? data.height : data.width;\n\t\t\t\tif (!data.targetWidth) return;\n\t\t\t\tthis.fields = data;\n\t\t\t\tlet touchesX = '',\n\t\t\t\t\ttouchesY = '';\n\t\t\t\t// #ifdef MP-BAIDU\n\t\t\t\ttouchesX = e.changedTouches[0].clientX;\n\t\t\t\ttouchesY = e.changedTouches[0].clientY;\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\ttouchesX = e.detail.clientX;\n\t\t\t\ttouchesY = e.detail.clientY;\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY\n\t\t\t\ttouchesX = e.touches[0].clientX;\n\t\t\t\ttouchesY = e.touches[0].clientY;\n\t\t\t\t// #endif\n\t\t\t\t// 获取触摸点相对于按钮上边和左边的x和y坐标，原理是通过屏幕的触摸点（touchesY），减去按钮的上边界data.top\n\t\t\t\t// 但是由于`transform-origin`默认是center，所以这里再减去半径才是水波纹view应该的位置\n\t\t\t\t// 总的来说，就是把水波纹的矩形（变换后的圆形）的中心点，移动到我们的触摸点位置\n\t\t\t\tthis.rippleTop = touchesY - data.top - data.targetWidth / 2;\n\t\t\t\tthis.rippleLeft = touchesX - data.left - data.targetWidth / 2;\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.waveActive = true;\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// 获取节点信息\n\t\tgetElQuery() {\n\t\t\treturn new Promise(resolve => {\n\t\t\t\tlet queryInfo = '';\n\t\t\t\t// 获取元素节点信息，请查看uniapp相关文档\n\t\t\t\t// https://uniapp.dcloud.io/api/ui/nodes-info?id=nodesrefboundingclientrect\n\t\t\t\tqueryInfo = uni.createSelectorQuery().in(this);\n\t\t\t\t//#ifdef MP-ALIPAY\n\t\t\t\tqueryInfo = uni.createSelectorQuery();\n\t\t\t\t//#endif\n\t\t\t\tqueryInfo.select('.u-btn').boundingClientRect();\n\t\t\t\tqueryInfo.exec(data => {\n\t\t\t\t\tresolve(data);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// 下面为对接uniapp官方按钮开放能力事件回调的对接\n\t\tgetphonenumber(res) {\n\t\t\tthis.$emit('getphonenumber', res);\n\t\t},\n\t\tgetuserinfo(res) {\n\t\t\tthis.$emit('getuserinfo', res);\n\t\t},\n\t\terror(res) {\n\t\t\tthis.$emit('error', res);\n\t\t},\n\t\topensetting(res) {\n\t\t\tthis.$emit('opensetting', res);\n\t\t},\n\t\tlaunchapp(res) {\n\t\t\tthis.$emit('launchapp', res);\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import '@/components/uview-ui/libs/css/style.components.scss';\r\n@import '@/components/uview-ui/theme.scss';\n.u-btn::after {\n\tborder: none;\n}\n\n.u-btn {\n\tposition: relative;\n\tborder: 0;\n\t//border-radius: 10rpx;\n\t/* #ifndef APP-NVUE */\n\tdisplay: inline-flex;\t\t\n\t/* #endif */\n\t// 避免边框某些场景可能被“裁剪”，不能设置为hidden\n\toverflow: visible;\n\tline-height: 1;\n\t@include vue-flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcursor: pointer;\n\tpadding: 0 40rpx;\n\tz-index: 1;\n\tbox-sizing: border-box;\n\ttransition: all 0.15s;\n\t\n\t&--bold-border {\n\t\tborder: 1px solid #ffffff;\n\t}\n\t\n\t&--default {\n\t\tcolor: $u-content-color;\n\t\tborder-color: #c0c4cc;\n\t\tbackground-color: #ffffff;\n\t}\n\t\n\t&--primary {\n\t\tcolor: #ffffff;\n\t\tborder-color: $u-type-primary;\n\t\tbackground-color: $u-type-primary;\n\t}\n\t\n\t&--success {\n\t\tcolor: #ffffff;\n\t\tborder-color: $u-type-success;\n\t\tbackground-color: $u-type-success;\n\t}\n\t\n\t&--error {\n\t\tcolor: #ffffff;\n\t\tborder-color: $u-type-error;\n\t\tbackground-color: $u-type-error;\n\t}\n\t\n\t&--warning {\n\t\tcolor: #ffffff;\n\t\tborder-color: $u-type-warning;\n\t\tbackground-color: $u-type-warning;\n\t}\n\t\n\t&--default--disabled {\n\t\tcolor: #ffffff;\n\t\tborder-color: #e4e7ed;\n\t\tbackground-color: #ffffff;\n\t}\n\t\n\t&--primary--disabled {\n\t\tcolor: #ffffff!important;\n\t\tborder-color: $u-type-primary-disabled!important;\n\t\tbackground-color: $u-type-primary-disabled!important;\n\t}\n\t\n\t&--success--disabled {\n\t\tcolor: #ffffff!important;\n\t\tborder-color: $u-type-success-disabled!important;\n\t\tbackground-color: $u-type-success-disabled!important;\n\t}\n\t\n\t&--error--disabled {\n\t\tcolor: #ffffff!important;\n\t\tborder-color: $u-type-error-disabled!important;\n\t\tbackground-color: $u-type-error-disabled!important;\n\t}\n\t\n\t&--warning--disabled {\n\t\tcolor: #ffffff!important;\n\t\tborder-color: $u-type-warning-disabled!important;\n\t\tbackground-color: $u-type-warning-disabled!important;\n\t}\n\t\n\t&--primary--plain {\n\t\tcolor: $u-type-primary!important;\n\t\tborder-color: $u-type-primary-disabled!important;\n\t\tbackground-color: $u-type-primary-light!important;\n\t}\n\t\n\t&--success--plain {\n\t\tcolor: $u-type-success!important;\n\t\tborder-color: $u-type-success-disabled!important;\n\t\tbackground-color: $u-type-success-light!important;\n\t}\n\t\n\t&--error--plain {\n\t\tcolor: $u-type-error!important;\n\t\tborder-color: $u-type-error-disabled!important;\n\t\tbackground-color: $u-type-error-light!important;\n\t}\n\t\n\t&--warning--plain {\n\t\tcolor: $u-type-warning!important;\n\t\tborder-color: $u-type-warning-disabled!important;\n\t\tbackground-color: $u-type-warning-light!important;\n\t}\n}\n\n.u-hairline-border:after {\n\tcontent: ' ';\n\tposition: absolute;\n\tpointer-events: none;\n\t// 设置为border-box，意味着下面的scale缩小为0.5，实际上缩小的是伪元素的内容（border-box意味着内容不含border）\n\tbox-sizing: border-box;\n\t// 中心点作为变形(scale())的原点\n\t-webkit-transform-origin: 0 0;\n\ttransform-origin: 0 0;\n\tleft: 0;\n\ttop: 0;\n\twidth: 199.8%;\n\theight: 199.7%;\n\t-webkit-transform: scale(0.5, 0.5);\n\ttransform: scale(0.5, 0.5);\n\tborder: 1px solid currentColor;\n\tz-index: 1;\n}\n\n.u-wave-ripple {\n\tz-index: 0;\n\tposition: absolute;\n\tborder-radius: 100%;\n\tbackground-clip: padding-box;\n\tpointer-events: none;\n\tuser-select: none;\n\ttransform: scale(0);\n\topacity: 1;\n\ttransform-origin: center;\n}\n\n.u-wave-ripple.u-wave-active {\n\topacity: 0;\n\ttransform: scale(2);\n\ttransition: opacity 1s linear, transform 0.4s linear;\n}\n\n.u-round-circle {\n\tborder-radius: 100rpx;\n}\n\n.u-round-circle::after {\n\tborder-radius: 100rpx;\n}\n\n.u-loading::after {\n\tbackground-color: hsla(0, 0%, 100%, 0.35);\n}\n\n.u-size-default {\n\tfont-size: 30rpx;\n\theight: 80rpx;\n\tline-height: 80rpx;\n}\n\n.u-size-medium {\n\t/* #ifndef APP-NVUE */\n\tdisplay: inline-flex;\t\t\n\t/* #endif */\n\twidth: auto;\n\tfont-size: 26rpx;\n\theight: 70rpx;\n\tline-height: 70rpx;\n\tpadding: 0 80rpx;\n}\n\n.u-size-mini {\n\t/* #ifndef APP-NVUE */\n\tdisplay: inline-flex;\t\t\n\t/* #endif */\n\twidth: auto;\n\tfont-size: 22rpx;\n\tpadding-top: 1px;\n\theight: 50rpx;\n\tline-height: 50rpx;\n\tpadding: 0 20rpx;\n}\n\n.u-primary-plain-hover {\n\tcolor: #ffffff !important;\n\tbackground: $u-type-primary-dark !important;\n}\n\n.u-default-plain-hover {\n\tcolor: $u-type-primary-dark !important;\n\tbackground: $u-type-primary-light !important;\n}\n\n.u-success-plain-hover {\n\tcolor: #ffffff !important;\n\tbackground: $u-type-success-dark !important;\n}\n\n.u-warning-plain-hover {\n\tcolor: #ffffff !important;\n\tbackground: $u-type-warning-dark !important;\n}\n\n.u-error-plain-hover {\n\tcolor: #ffffff !important;\n\tbackground: $u-type-error-dark !important;\n}\n\n.u-info-plain-hover {\n\tcolor: #ffffff !important;\n\tbackground: $u-type-info-dark !important;\n}\n\n.u-default-hover {\n\tcolor: $u-type-primary-dark !important;\n\tborder-color: $u-type-primary-dark !important;\n\tbackground-color: $u-type-primary-light !important;\n}\n\n.u-primary-hover {\n\tbackground: $u-type-primary-dark !important;\n\tcolor: #fff;\n}\n\n.u-success-hover {\n\tbackground: $u-type-success-dark !important;\n\tcolor: #fff;\n}\n\n.u-info-hover {\n\tbackground: $u-type-info-dark !important;\n\tcolor: #fff;\n}\n\n.u-warning-hover {\n\tbackground: $u-type-warning-dark !important;\n\tcolor: #fff;\n}\n\n.u-error-hover {\n\tbackground: $u-type-error-dark !important;\n\tcolor: #fff;\n}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=style&index=0&id=8ef4aa1a&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=style&index=0&id=8ef4aa1a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041066707\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041066109\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}