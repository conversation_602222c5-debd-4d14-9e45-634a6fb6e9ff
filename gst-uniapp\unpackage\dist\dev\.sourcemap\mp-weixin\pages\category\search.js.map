{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/category/search.vue?f1dd", "webpack:///D:/gst/gst-uniapp/pages/category/search.vue?d5d7", "webpack:///D:/gst/gst-uniapp/pages/category/search.vue?e240", "webpack:///D:/gst/gst-uniapp/pages/category/search.vue?0e38", "uni-app:///pages/category/search.vue", "webpack:///D:/gst/gst-uniapp/pages/category/search.vue?86f8", "webpack:///D:/gst/gst-uniapp/pages/category/search.vue?c62f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "mEmptyData", "computed", "data", "searchValue", "placeholder", "popularShow", "searchRecent", "searchRecentList", "page", "limit", "currentIndex", "type", "list", "role", "optionVal1", "def", "optionList1", "onLoad", "onReachBottom", "methods", "getList", "getPositionList", "param", "postName", "res", "getResumeList", "keyword", "changeStatus", "positionDetail", "route", "query", "id", "detail", "recentClick", "search", "uni", "params", "console", "icon", "title", "clearRecent", "content", "success", "that", "historySearch", "history", "popular", "cancelSearch", "searchInput", "navToDetailPage", "url", "confirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC0DvnB;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;AAAA,eACA;EACAC;IACAC;IACA;IACA;EACA;;EACAC,0CACA,oCACA,oCACA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAd;kBACAC;kBACAc;gBACA;gBACAD;gBACAA;gBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAE;gBACA;kBACAtB;kBACA;oBACA;sBACAA;oBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;kBACAd;kBACAC;kBACAiB;gBACA;gBACAJ;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAE;gBACA;kBACAtB;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyB;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACAC;QACAC;UACAC;QACA;MACA;IACA;IAEAC;MACA;QACAH;QACAC;UACAC;QACA;MACA;IACA;IAEA;IACAE;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UAAAC;YACAV;UACA;QAAA;UACAS;UACAE;UACA;YACA;UACA;QACA;MACA;QACAF;UACAG;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAL;QACAI;QACAE;QACAC;UACA;YACAP;YACA;YACAQ;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAP;MACA;QACAQ;MACA;MACA;MACA;QACA;UACAR;UACAQ;QACA;MACA;MACA;MACA;QACAA;MACA;MACAA;MACA;IACA;IAEAC;MACAT;MACA;IACA;IAEA;IACAU;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAd;QACAe;MACA;IACA;IACAC;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChSA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/category/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=6af323f0&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=6af323f0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6af323f0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/search.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=6af323f0&scoped=true&\"", "var components\ntry {\n  components = {\n    umDropdown: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/um-dropdown/components/um-dropdown/um-dropdown\" */ \"@/uni_modules/um-dropdown/components/um-dropdown/um-dropdown.vue\"\n      )\n    },\n    mEmptyData: function () {\n      return import(\n        /* webpackChunkName: \"components/m-empty-data/m-empty-data\" */ \"@/components/m-empty-data/m-empty-data.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length == 0 && _vm.searchRecent == false\n  var g1 = _vm.list.length > 0 && _vm.searchRecent == false\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"column \">\r\n\t\t<view class=\"search\">\r\n\t\t\t<view>\r\n\t\t\t\t<um-dropdown width=\"200rpx\" @change=\"fnChange\" :defaultIndex=\"def\" rangeKey=\"label\" :optionList=\"optionList1\"></um-dropdown>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search-input\">\r\n\t\t\t\t<text class=\"yzb yzb-search\"></text>\r\n\t\t\t\t<input class=\"text-normal\" type=\"text\" :value=\"searchValue\" :placeholder=\"placeholder\" @input=\"searchInput\" @confirm=\"confirm\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn-search\" v-if=\"popularShow == false\" @click=\"search\">搜索</view>\r\n\t\t\t<view class=\"btn-cancel\" v-if=\"popularShow == true\" @click=\"cancelSearch\">取消</view>\r\n\t\t</view>\r\n\t\t<!-- 占位 -->\r\n\t\t<view class=\"placeholder-90\"></view>\r\n\t\t<view class=\"center-algin\" style=\"margin-top: 40%;\" v-if=\"list.length == 0 && searchRecent == false\">\r\n\t\t\t<m-empty-data :coverUrl=\"'/static/null.png'\" noTxt=\"暂无搜索记录\"></m-empty-data>\r\n\t\t</view>\r\n\t\t<view class=\"searchRecent padding-20\" v-if=\"searchRecent == true\">\r\n\t\t\t<view class=\"searchRecent-title text-grey space-between-algin\">\r\n\t\t\t\t<text>最近搜索</text>\r\n\t\t\t\t<text class=\"\" @click=\"clearRecent\">清空</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"history\">\r\n\t\t\t\t<view class=\"searchRecent-content\" v-for=\"(item, index1) in searchRecentList\" :key=\"index1\" @click=\"recentClick(item)\">\r\n\t\t\t\t\t<text class=\"text-size-mim\">{{ item }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"background-color:#efefef ;width: 100%;\" class=\"top-line\" v-if=\"list.length > 0 && searchRecent==false\">\r\n\t\t\t<!-- <yzb-position v-if=\"role==0\" :positions=\"list\" @click=\"positionDetail\"></yzb-position>\r\n\t\t\t<yzb-resume v-else :list=\"list\" @click=\"detail\"></yzb-resume> -->\r\n\t\t\t<!-- <view class=\"load-more-box\">\r\n\t\t\t\t<uni-load-more v-if=\"status == '请求中'\" status=\"正在加载...\" :showIcon=\"true\"></uni-load-more>\r\n\t\t\t\t<uni-load-more v-if=\"status == '没有更多'\" status=\"没有更多了\" :showIcon=\"false\"></uni-load-more>\r\n\t\t\t\t<uni-load-more v-if=\"status == '暂无数据'\" status=\"暂无数据\" :showIcon=\"false\"></uni-load-more>\r\n\t\t\t\t<uni-load-more v-if=\"status == '请求失败'\" status=\"加载失败，点我重试\" :showIcon=\"false\" @click=\"reLoad\"></uni-load-more>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"list-box\" >\r\n\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item1, index1) in list\"\r\n\t\t\t\t\t:key=\"index1\" @tap=\"navToDetailPage(item1)\">\r\n\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\r\n\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item1.picture\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t<view class=\"title\">{{item1.title}}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { mapState, mapGetters } from 'vuex';\r\nimport mEmptyData from '@/components/m-empty-data/m-empty-data.vue';\r\n// import yzbResume from '@/components/yzb/yzb-resume.vue';\r\n// import yzbPosition from '@/components/yzb/yzb-position.vue';\r\nexport default {\r\n\tcomponents: {\r\n\t\tmEmptyData,\r\n\t\t// yzbResume,\r\n\t\t// yzbPosition\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState(['userInfo']),\r\n\t\t...mapGetters(['hasLogin'])\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t//no_order_1: this.$mAssetsPath.no_order_1,\r\n\t\t\tsearchValue: '',\r\n\t\t\tplaceholder: '请输入关键词搜索相关内容',\r\n\t\t\tpopularShow: false, // 热门搜索\r\n\t\t\tsearchRecent: false, // 最近搜索\r\n\t\t\tsearchRecentList: [], // 最近搜索\r\n\t\t\tpage: 1,\r\n\t\t\tlimit: 15,\r\n\t\t\tcurrentIndex: 0,\r\n\t\t\ttype: 1, // 1-札记，2-曲谱，3-视频\r\n\t\t\tlist: [], //搜索结果列表\r\n\t\t\trole:0,//0-求职，1-招聘\r\n\t\t\toptionVal1: '',\r\n\t\t\tdef: 0,\r\n\t\t\toptionList1: ['课程','资料'],\r\n\t\t};\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.searchRecentList = this.$db.get('historySearch');\r\n\t\tif (this.searchValue == '' && this.searchRecentList != null && this.searchRecentList != '') {\r\n\t\t\tthis.searchRecent = true;\r\n\t\t}\r\n\t\tif(this.hasLogin && this.userInfo.memberRole==1){\r\n\t\t\tthis.role=1;\r\n\t\t}else{\r\n\t\t\tthis.role=0\r\n\t\t}\r\n\t},\r\n\tonReachBottom() {\r\n\t\tthis.page++;\r\n\t\tthis.getList();\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\tgetList(){\r\n\t\t\tif(this.role==0){\r\n\t\t\t\tthis.getPositionList();\r\n\t\t\t}else{\r\n\t\t\t\tthis.getResumeList();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync getPositionList() {\r\n\t\t\tlet param = {\r\n\t\t\t\tpage: this.page,\r\n\t\t\t\tlimit: this.limit,\r\n\t\t\t\tpostName:this.searchValue,\r\n\t\t\t};\r\n\t\t\tparam.latitude = getApp().globalData.location.latitude;\r\n\t\t\tparam.longitude = getApp().globalData.location.longitude;\r\n\t\t\tparam.pcitycode = getApp().globalData.location.pcitycode;\r\n\t\t\tthis.status = '请求中';\r\n\t\t\tlet res = await this.$apis.getPositionList(param);\r\n\t\t\tif (res) {\r\n\t\t\t\tlet data = res.data;\r\n\t\t\t\tfor (let i in data) {\r\n\t\t\t\t\tif (data[i].skill) {\r\n\t\t\t\t\t\tdata[i].skill = data[i].skill.split(',');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.list = this.list.concat(data || []);\r\n\t\t\t\tthis.changeStatus(res);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync getResumeList() {\r\n\t\t\tlet param = {\r\n\t\t\t\tpage: this.page,\r\n\t\t\t\tlimit: this.limit,\r\n\t\t\t\tkeyword:this.searchValue,\r\n\t\t\t};\r\n\t\t\tparam.pcitycode = getApp().globalData.location.pcitycode;\r\n\t\t\tthis.status = '请求中';\r\n\t\t\tlet res = await this.$apis.getResumeList(param);\r\n\t\t\tif (res) {\r\n\t\t\t\tlet data = res.data;\r\n\t\t\t\tthis.list = this.list.concat(data || []);\r\n\t\t\t\tthis.changeStatus(res);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 修改请求状态\r\n\t\tchangeStatus(data) {\r\n\t\t\tif (this.list.length === 0) {\r\n\t\t\t\tthis.status = '暂无数据';\r\n\t\t\t} else if (this.page >= Math.ceil(data.count / this.limit)) {\r\n\t\t\t\tthis.status = '没有更多';\r\n\t\t\t} else {\r\n\t\t\t\tthis.status = '请求更多';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tpositionDetail(item) {\r\n\t\t\tthis.$mRouter.push({\r\n\t\t\t\troute: this.$mRoutesConfig.positionDetail,\r\n\t\t\t\tquery: {\r\n\t\t\t\t\tid: item.id\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tdetail(item) {\r\n\t\t\tthis.$mRouter.push({\r\n\t\t\t\troute: this.$mRoutesConfig.resumeDetail,\r\n\t\t\t\tquery: {\r\n\t\t\t\t\tid: item.id\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 最近搜索点击\r\n\t\trecentClick(item) {\r\n\t\t\tthis.searchValue = item;\r\n\t\t\tthis.search();\r\n\t\t},\r\n\r\n\t\t// 搜索\r\n\t\tsearch() {\r\n\t\t\tif (this.searchValue) {\r\n\t\t\t\tuni.showLoading();\r\n\t\t\t\t// this.searchRecent = false;\r\n\t\t\t\t// this.historySearch();\r\n\t\t\t\t// this.page=1;\r\n\t\t\t\t// this.list = [];\r\n\t\t\t\t// this.getList();\r\n\t\t\t\t// this.popularShow=true;\r\n\t\t\t\tthis.$http.get(\"v1/course/search\",{params: {\r\n\t\t\t\t\t\tkeyword: this.searchValue\r\n\t\t\t\t\t}}).then(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.list = res.data.data\r\n\t\t\t\t\t} \r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请输入要搜索的关键字'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 清空最近搜索\r\n\t\tclearRecent() {\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确定清空搜索记录吗',\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tuni.removeStorageSync('historySearch');\r\n\t\t\t\t\t\t// 隐藏最近搜索\r\n\t\t\t\t\t\tthat.searchRecent = false;\r\n\t\t\t\t\t\tthat.currentIndex = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 保存最近搜索\r\n\t\thistorySearch() {\r\n\t\t\tvar history = this.$db.get('historySearch');\r\n\t\t\tconsole.log(history);\r\n\t\t\tif (history == null || history == '') {\r\n\t\t\t\thistory = [];\r\n\t\t\t}\r\n\t\t\t//判定是否已经看过,先删除\r\n\t\t\tfor (var i = 0; i < history.length; i++) {\r\n\t\t\t\tif (history[i] == this.searchValue) {\r\n\t\t\t\t\tconsole.log('删除该元素' + history[i]);\r\n\t\t\t\t\thistory.splice(i, 1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t//控制最多保存10个\r\n\t\t\tif (history.length == 10) {\r\n\t\t\t\thistory.splice(9, 1);\r\n\t\t\t}\r\n\t\t\thistory.unshift(this.searchValue);\r\n\t\t\tthis.$db.set('historySearch', history);\r\n\t\t},\r\n\r\n\t\tpopular(item) {\r\n\t\t\tconsole.log(item);\r\n\t\t\tthis.searchValue = item.text;\r\n\t\t},\r\n\r\n\t\t// 取消搜索\r\n\t\tcancelSearch() {\r\n\t\t\tthis.popularShow = false;\r\n\t\t\tthis.searchValue = '';\r\n\t\t\tthis.searchRecent = true;\r\n\t\t},\r\n\r\n\t\tsearchInput(e) {\r\n\t\t\tthis.searchValue = e.detail.value;\r\n\t\t\tthis.searchRecentList = this.$db.get('historySearch');\r\n\t\t\tthis.popularShow=false;\r\n\t\t\tif (this.searchValue == '' && this.searchRecentList != null && this.searchRecentList != '') {\r\n\t\t\t\tthis.searchRecent = true;\r\n\t\t\t\tthis.currentIndex = 0;\r\n\t\t\t}\r\n\t\t},\r\n\t\tnavToDetailPage(item){\r\n\t\t\tlet id = item.id\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t});\r\n\t\t},\r\n\t\tconfirm(){\r\n\t\t\tthis.search()\r\n\t\t}\r\n\t\t\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\npage {\r\n\tbackground: #fff;\r\n}\r\n.search {\r\n\theight: 90upx;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tz-index: 5;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\tbackground-color: #2094CE;\r\n\tcolor: #ffffff;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.yzb-search {\r\n\tcolor: #999999;\r\n\tfont-size: 28rpx;\r\n\tmargin: 0 15upx;\r\n\tmargin-top: 8upx;\r\n}\r\n.search-input {\r\n\twidth: 77%;\r\n\theight: 65upx;\r\n\tborder-radius: 50upx;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\tbackground-color: #ffffff;\r\n\talign-items: center;\r\n}\r\n.search-input image {\r\n\twidth: 35upx;\r\n\theight: 35upx;\r\n\tmargin-right: 10upx;\r\n}\r\n.search-input input {\r\n\twidth: 65%;\r\n\tfont-size: 28upx;\r\n\tcolor: #333333;\r\n}\r\n\r\n.btn-search {\r\n\tmargin-left: 20upx;\r\n}\r\n\r\n.btn-cancel {\r\n\tmargin-left: 20upx;\r\n}\r\n\r\n.searchRecent{\r\n}\r\n\r\n.searchRecent-title {\r\n\theight: 60upx;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\tjustify-content: space-between;\r\n\ttext {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n}\r\n\r\n.history {\r\n\tflex-wrap: wrap;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n}\r\n\r\n.searchRecent-content {\r\n\tmargin: 15upx 20upx 15upx 0;\r\n\tpadding: 8upx 30upx;\r\n\tbackground-color: #f4f4f4;\r\n\tdisplay: flex;\r\n\tborder-radius: 5upx;\r\n}\r\n\r\n.type {\r\n\twidth: 100%;\r\n\theight: 80upx;\r\n\tposition: fixed;\r\n\ttop: 90upx;\r\n\tz-index: 5;\r\n}\r\n.type-view {\r\n\twidth: 25%;\r\n\theight: 80upx;\r\n}\r\n.type-view-line {\r\n\twidth: 45upx;\r\n\theight: 4upx;\r\n\tborder-radius: 15upx;\r\n}\r\n\r\n.wonderful {\r\n\twidth: 95%;\r\n\tflex-wrap: wrap;\r\n}\r\n\r\n.wonderful-content {\r\n\twidth: 47%;\r\n\theight: 340upx;\r\n\tposition: relative;\r\n}\r\n\r\n.wonderful-playImg {\r\n\twidth: 80upx;\r\n\theight: 80upx;\r\n\tposition: absolute;\r\n\ttop: 60upx;\r\n\tleft: 125upx;\r\n}\r\n\r\n.wonderful-content-img {\r\n\twidth: 100%;\r\n\theight: 190upx;\r\n\tborder-radius: 12upx;\r\n}\r\n\r\n.placeholder-90 {\r\n\twidth: 100%;\r\n\theight: 90upx;\r\n}\r\n\r\n.placeholder-170 {\r\n\twidth: 100%;\r\n\theight: 170upx;\r\n}\r\n\r\n.forum {\r\n\t/* margin-top: 100upx; */\r\n}\r\n\r\n.forum-line {\r\n\tborder-top: 5upx #efefef solid;\r\n}\r\n\r\n.forum-top {\r\n\twidth: 94.5%;\r\n}\r\n.forum-top-left image {\r\n\twidth: 100upx;\r\n\theight: 100upx;\r\n\tborder-radius: 50%;\r\n}\r\n.share {\r\n\twidth: 50upx;\r\n\theight: 50upx;\r\n}\r\n.forum-top-content {\r\n\twidth: 94.5%;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\tdisplay: -webkit-box;\r\n\t-webkit-line-clamp: 3;\r\n\t-webkit-box-orient: vertical;\r\n}\r\n.forum-img {\r\n\twidth: 94.5%;\r\n\tflex-wrap: wrap;\r\n\tpadding-bottom: 0;\r\n\t/* justify-content: space-around; */\r\n}\r\n.forum-img-image {\r\n\twidth: 226upx;\r\n\theight: 226upx;\r\n\tmargin-right: 15upx;\r\n\tmargin-bottom: 15upx;\r\n}\r\n.forum-img-image:nth-of-type(3n) {\r\n\tmargin-right: 0upx;\r\n}\r\n.forum-btn {\r\n\theight: 100upx;\r\n}\r\n.btn {\r\n\twidth: 250upx;\r\n}\r\n.btn image {\r\n\twidth: 40upx;\r\n\theight: 40upx;\r\n\tmargin-right: 10upx;\r\n}\r\n.list-box {\r\n\t\t// padding-bottom: 20rpx;\r\n\t\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\t\r\n\t\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 150rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-large-box {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, .5) !important;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\t//color: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.des {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\r\n\t\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: blue;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t:last-child {\r\n\t\t\t// border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=6af323f0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=6af323f0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039748708\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}