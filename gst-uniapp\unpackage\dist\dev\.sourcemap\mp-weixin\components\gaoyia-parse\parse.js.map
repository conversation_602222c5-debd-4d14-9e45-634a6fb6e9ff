{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/parse.vue?28d3", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/parse.vue?61a9", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/parse.vue?24f7", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/parse.vue?94f2", "uni-app:///components/gaoyia-parse/parse.vue"], "names": ["name", "props", "userSelect", "type", "default", "imgOptions", "loop", "indicator", "longPressActions", "loading", "className", "content", "noData", "startHandler", "node", "end<PERSON><PERSON><PERSON>", "ch<PERSON><PERSON><PERSON><PERSON>", "imageProp", "mode", "padding", "lazyLoad", "domain", "components", "wxParseTemplate", "data", "nodes", "imageUrls", "wx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "computed", "mounted", "methods", "setHtml", "start", "end", "chars", "results", "setTimeout", "getWidth", "uni", "in", "select", "fields", "size", "scrollOffset", "res", "navigate", "console", "preview", "current", "urls", "removeImageUrl", "provide", "parse<PERSON>idth", "parseSelect", "watch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;;;AAGpD;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqBtnB;;;;;;;;;;;;;;;;;;;;;;;;;;gBAIA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;;IACAC;MACAF;MACAC;QACA;UACAE;UACAC;UACAC;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;;IACAC;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;QACA;UACAU;UACAA;QACA;MACA;IACA;IACAC;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;QACA;UACAc;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA,IACArB,UAMA,KANAA;QACAC,SAKA,KALAA;QACAK,YAIA,KAJAA;QACAJ,eAGA,KAHAA;QACAE,aAEA,KAFAA;QACAC,eACA,KADAA;MAEA;MACA;QACAiB;QACAC;QACAC;MACA;MACA;MAEA;MACA;;MAGA;MACAC;QACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QAEAC,0BACAC,WACAC,mBACAC;UACAC;UACAC;QACA,GACA;UACAC;QACA,EACA;MAmBA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA,qEAEA;QACAT;UACAU;UACAC;UACA5C;UACAC;UACAC;QACA;MACA;MACA;IACA;IACA2C;MACA,IACAzB,YACA,KADAA;MAEAA;IACA;EACA;EACA;EACA0B;IACA;MACAC;MACAC;MACA;IACA;EACA;;EACAC;IACA5C;MACA;IACA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,4B", "file": "components/gaoyia-parse/parse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./parse.vue?vue&type=template&id=588189e1&\"\nvar renderjs\nimport script from \"./parse.vue?vue&type=script&lang=js&\"\nexport * from \"./parse.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gaoyia-parse/parse.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./parse.vue?vue&type=template&id=588189e1&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./parse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./parse.vue?vue&type=script&lang=js&\"", "<!--**\r\n * forked from：https://github.com/F-loat/mpvue-wxParse\r\n *\r\n * github地址: https://github.com/dcloudio/uParse\r\n *\r\n * for: uni-app框架下 富文本解析\r\n * \r\n * 优化 by <EMAIL>  https://github.com/gaoyia/parse\r\n */-->\r\n\r\n<template>\r\n\t\r\n\t<!--基础元素-->\r\n\t<div class=\"wxParse\" :class=\"className\" :style=\"'user-select:' + userSelect\">\r\n\t\t<block v-for=\"(node, index) of nodes\" :key=\"index\" v-if=\"!loading\">\r\n\t\t\t<wxParseTemplate :node=\"node\" />\r\n\t\t</block>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport HtmlToJson from './libs/html2json';\r\nimport wxParseTemplate from './components/wxParseTemplate0';\r\n\r\n\t\r\n\texport default {\r\n\t\tname: 'wxParse',\r\n\t\tprops: {\r\n\t\t\t// user-select:none;\r\n\t\t\tuserSelect: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'text' //none |text| all | element\r\n\t\t\t},\r\n\t\t\timgOptions: {\r\n\t\t\t\ttype: [Object, Boolean],\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tloop: false,\r\n\t\t\t\t\t\tindicator: 'number',\r\n\t\t\t\t\t\tlongPressActions: false\r\n\t\t\t\t\t\t// longPressActions: {\r\n\t\t\t\t\t\t// \t itemList: ['发送给朋友', '保存图片', '收藏'],\r\n\t\t\t\t\t\t// \t\tsuccess: function (res) {\r\n\t\t\t\t\t\t// \t\t\tconsole.log('选中了第' + (res.tapIndex + 1) + '个按钮');\r\n\t\t\t\t\t\t// \t\t},\r\n\t\t\t\t\t\t// \t\tfail: function (res) {\r\n\t\t\t\t\t\t// \t\t\tconsole.log(res.errMsg);\r\n\t\t\t\t\t\t// \t\t}    \r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloading: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tclassName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcontent: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tnoData: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '<div style=\"color: red;\">数据不能为空</div>'\r\n\t\t\t},\r\n\t\t\tstartHandler: {\r\n\t\t\t\ttype: Function,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn node => {\r\n\t\t\t\t\t\tnode.attr.class = null;\r\n\t\t\t\t\t\tnode.attr.style = null;\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tendHandler: {\r\n\t\t\t\ttype: Function,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\tcharsHandler: {\r\n\t\t\t\ttype: Function,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\timageProp: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tmode: 'aspectFit',\r\n\t\t\t\t\t\tpadding: 0,\r\n\t\t\t\t\t\tlazyLoad: false,\r\n\t\t\t\t\t\tdomain: ''\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\twxParseTemplate\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnodes: {},\r\n\t\t\t\timageUrls: [],\r\n\t\t\t\twxParseWidth: {\r\n\t\t\t\t\tvalue: 0\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {},\r\n\t\tmounted() {\r\n\t\t\tthis.setHtml()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetHtml() {\r\n\t\t\t\tthis.getWidth().then((data) => {\r\n\t\t\t\t\tthis.wxParseWidth.value = data;\r\n\t\t\t\t})\r\n\t\t\t\tlet {\r\n\t\t\t\t\tcontent,\r\n\t\t\t\t\tnoData,\r\n\t\t\t\t\timageProp,\r\n\t\t\t\t\tstartHandler,\r\n\t\t\t\t\tendHandler,\r\n\t\t\t\t\tcharsHandler\r\n\t\t\t\t} = this;\r\n\t\t\t\tlet parseData = content || noData;\r\n\t\t\t\tlet customHandler = {\r\n\t\t\t\t\tstart: startHandler,\r\n\t\t\t\t\tend: endHandler,\r\n\t\t\t\t\tchars: charsHandler\r\n\t\t\t\t};\r\n\t\t\t\tlet results = HtmlToJson(parseData, customHandler, imageProp, this);\r\n\r\n\t\t\t\tthis.imageUrls = results.imageUrls;\r\n\t\t\t\t// this.nodes = results.nodes;\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tthis.nodes = [];\r\n\t\t\t\tresults.nodes.forEach((item) => {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.nodes.push(item)\r\n\t\t\t\t\t}, 0);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetWidth() {\r\n\t\t\t\treturn new Promise((res, rej) => {\r\n\t\t\t\t\t// #ifndef MP-ALIPAY || MP-BAIDU\r\n\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t.select('.wxParse')\r\n\t\t\t\t\t\t.fields({\r\n\t\t\t\t\t\t\t\tsize: true,\r\n\t\t\t\t\t\t\t\tscrollOffset: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tdata => {\r\n\t\t\t\t\t\t\t\tres(data.width);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t).exec();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef MP-BAIDU\r\n\t\t\t\t\tconst query = swan.createSelectorQuery();\r\n\t\t\t\t\tquery.select('.wxParse').boundingClientRect();\r\n\t\t\t\t\tquery.exec(obj => {\r\n\t\t\t\t\t\tconst rect = obj[0]\r\n\t\t\t\t\t\tif (rect) {\r\n\t\t\t\t\t\t\tres(rect.width);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\tmy.createSelectorQuery()\r\n\t\t\t\t\t\t.select('.wxParse')\r\n\t\t\t\t\t\t.boundingClientRect().exec((ret) => {\r\n\t\t\t\t\t\t\tres(ret[0].width);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavigate(href, $event, attr) {\r\n\t\t\t\tconsole.log(href, attr);\r\n\t\t\t\tthis.$emit('navigate', href, $event);\r\n\t\t\t},\r\n\t\t\tpreview(src, $event) {\r\n\t\t\t\tif (!this.imageUrls.length || typeof this.imgOptions === 'boolean') {\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\tcurrent: src,\r\n\t\t\t\t\t\turls: this.imageUrls,\r\n\t\t\t\t\t\tloop: this.imgOptions.loop,\r\n\t\t\t\t\t\tindicator: this.imgOptions.indicator,\r\n\t\t\t\t\t\tlongPressActions: this.imgOptions.longPressActions\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('preview', src, $event);\r\n\t\t\t},\r\n\t\t\tremoveImageUrl(src) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\timageUrls\r\n\t\t\t\t} = this;\r\n\t\t\t\timageUrls.splice(imageUrls.indexOf(src), 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 父组件中提供\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tparseWidth: this.wxParseWidth,\r\n\t\t\t\tparseSelect: this.userSelect\r\n\t\t\t\t// 提示：provide 和 inject 绑定并不是可响应的。这是刻意为之的。然而，如果你传入了一个可监听的对象，那么其对象的属性还是可响应的。\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tcontent(){\r\n\t\t\t\tthis.setHtml()\r\n\t\t\t}\r\n\t\t\t// content: {\r\n\t\t\t// \thandler: function(newVal, oldVal) {\r\n\t\t\t// \t\tif (newVal !== oldVal) {\r\n\t\t\t// \t\t\t\r\n\t\t\t// \t\t}\r\n\t\t\t// \t},\r\n\t\t\t// \tdeep: true\r\n\t\t\t// }\r\n\t\t}\r\n\t};\r\n</script>\r\n"], "sourceRoot": ""}