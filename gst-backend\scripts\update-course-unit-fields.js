const { sequelize } = require('../src/models');

async function updateCourseUnitFields() {
  try {
    console.log('🔄 开始更新course_units表字段...');

    const queryInterface = sequelize.getQueryInterface();
    const courseUnitsTableInfo = await queryInterface.describeTable('course_units');
    
    console.log('📋 当前course_units表字段:', Object.keys(courseUnitsTableInfo));

    // 需要添加的字段列表
    const fieldsToAdd = [
      {
        name: 'video_url',
        definition: {
          type: sequelize.Sequelize.STRING(500),
          comment: '视频URL'
        }
      },
      {
        name: 'audio_url',
        definition: {
          type: sequelize.Sequelize.STRING(500),
          comment: '音频URL'
        }
      },
      {
        name: 'audio_id',
        definition: {
          type: sequelize.Sequelize.STRING(100),
          comment: '阿里云音频ID'
        }
      },
      {
        name: 'attachments',
        definition: {
          type: sequelize.Sequelize.TEXT,
          comment: '附件列表(JSON格式)'
        }
      },
      {
        name: 'is_required',
        definition: {
          type: sequelize.Sequelize.BOOLEAN,
          defaultValue: true,
          comment: '是否必修'
        }
      }
    ];

    // 逐个添加缺失的字段
    for (const field of fieldsToAdd) {
      if (!courseUnitsTableInfo[field.name]) {
        console.log(`📝 添加 course_units.${field.name} 字段...`);
        try {
          await queryInterface.addColumn('course_units', field.name, field.definition);
          console.log(`✅ course_units.${field.name} 字段添加成功`);
        } catch (error) {
          console.error(`❌ 添加 course_units.${field.name} 字段失败:`, error.message);
        }
      } else {
        console.log(`✅ course_units.${field.name} 字段已存在`);
      }
    }

    console.log('🎉 course_units表字段更新完成！');

  } catch (error) {
    console.error('❌ course_units表字段更新失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  updateCourseUnitFields().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = updateCourseUnitFields;
