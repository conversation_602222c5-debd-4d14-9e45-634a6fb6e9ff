<template>
  <el-dialog
    v-model="dialogVisible"
    title="提交列表"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div class="submission-list" v-if="assignment">
      <div class="detail-placeholder">
        <el-empty description="提交列表组件开发中..." :image-size="80" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  assignment: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style lang="scss" scoped>
.detail-placeholder {
  padding: var(--spacing-xl);
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}
</style>
