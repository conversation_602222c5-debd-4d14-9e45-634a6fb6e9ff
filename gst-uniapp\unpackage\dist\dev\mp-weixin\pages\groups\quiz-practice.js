(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/groups/quiz-practice"],{

/***/ 521:
/*!***************************************************************************!*\
  !*** D:/gst/gst-uniapp/main.js?{"page":"pages%2Fgroups%2Fquiz-practice"} ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _quizPractice = _interopRequireDefault(__webpack_require__(/*! ./pages/groups/quiz-practice.vue */ 522));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_quizPractice.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 522:
/*!********************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/quiz-practice.vue ***!
  \********************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quiz-practice.vue?vue&type=template&id=5012569e&scoped=true& */ 523);
/* harmony import */ var _quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quiz-practice.vue?vue&type=script&lang=js& */ 525);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css& */ 527);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "5012569e",
  null,
  false,
  _quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/groups/quiz-practice.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 523:
/*!***************************************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/quiz-practice.vue?vue&type=template&id=5012569e&scoped=true& ***!
  \***************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=template&id=5012569e&scoped=true& */ 524);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_template_id_5012569e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 524:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/groups/quiz-practice.vue?vue&type=template&id=5012569e&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    guiPage: function () {
      return __webpack_require__.e(/*! import() | GraceUI5/components/gui-page */ "GraceUI5/components/gui-page").then(__webpack_require__.bind(null, /*! @/GraceUI5/components/gui-page.vue */ 529))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = !_vm.currentPractice
    ? _vm.__map(_vm.practiceList, function (item, index) {
        var $orig = _vm.__get_orig(item)
        var m0 = _vm.getIconClass(item.type)
        var m1 = _vm.getTypeText(item.type)
        var m2 = _vm.getDifficultyText(item.difficulty)
        return {
          $orig: $orig,
          m0: m0,
          m1: m1,
          m2: m2,
        }
      })
    : null
  var g0 =
    !!_vm.currentPractice && !_vm.practiceFinished
      ? _vm.currentPractice.questions.length
      : null
  var m3 =
    !!_vm.currentPractice && !_vm.practiceFinished && _vm.timeLimit > 0
      ? _vm.formatTime(_vm.timeLeft)
      : null
  var m4 =
    !!_vm.currentPractice && !_vm.practiceFinished
      ? _vm.getTypeText(_vm.currentQuestion.type)
      : null
  var l1 =
    !!_vm.currentPractice &&
    !_vm.practiceFinished &&
    _vm.currentQuestion.type === "choice"
      ? _vm.__map(_vm.currentQuestion.options, function (option, index) {
          var $orig = _vm.__get_orig(option)
          var g1 = String.fromCharCode(65 + index)
          return {
            $orig: $orig,
            g1: g1,
          }
        })
      : null
  var m5 = _vm.practiceFinished ? _vm.getScoreClass(_vm.finalScore) : null
  var m6 = _vm.practiceFinished ? _vm.getGrade(_vm.finalScore) : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        g0: g0,
        m3: m3,
        m4: m4,
        l1: l1,
        m5: m5,
        m6: m6,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 525:
/*!*********************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/quiz-practice.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=script&lang=js& */ 526);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 526:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/groups/quiz-practice.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      pageLoading: false,
      groupId: '',
      practiceId: '',
      currentPractice: null,
      currentQuestionIndex: 0,
      selectedAnswer: null,
      inputAnswer: '',
      showResult: false,
      practiceFinished: false,
      answers: [],
      startTime: null,
      timeLimit: 0,
      // 时间限制（秒）
      timeLeft: 0,
      timer: null,
      practiceList: [{
        id: 1,
        title: '语法基础练习',
        description: '助词、动词变位等基础语法',
        type: 'grammar',
        questionCount: 10,
        difficulty: 'easy',
        bestScore: 92
      }, {
        id: 2,
        title: '词汇记忆练习',
        description: '常用词汇的读音和意思',
        type: 'vocabulary',
        questionCount: 15,
        difficulty: 'medium',
        bestScore: null
      }, {
        id: 3,
        title: '阅读理解练习',
        description: '短文阅读理解能力训练',
        type: 'reading',
        questionCount: 8,
        difficulty: 'hard',
        bestScore: 78
      }, {
        id: 4,
        title: '综合能力测试',
        description: '语法、词汇、阅读综合测试',
        type: 'comprehensive',
        questionCount: 20,
        difficulty: 'hard',
        bestScore: 85
      }]
    };
  },
  computed: {
    currentQuestion: function currentQuestion() {
      if (!this.currentPractice || !this.currentPractice.questions) return null;
      return this.currentPractice.questions[this.currentQuestionIndex];
    },
    progressPercent: function progressPercent() {
      if (!this.currentPractice) return 0;
      return (this.currentQuestionIndex + 1) / this.currentPractice.questions.length * 100;
    },
    isLastQuestion: function isLastQuestion() {
      if (!this.currentPractice) return false;
      return this.currentQuestionIndex === this.currentPractice.questions.length - 1;
    },
    finalScore: function finalScore() {
      if (this.answers.length === 0) return 0;
      var totalPoints = this.answers.reduce(function (sum, answer) {
        return sum + answer.points;
      }, 0);
      var earnedPoints = this.answers.reduce(function (sum, answer) {
        return sum + (answer.isCorrect ? answer.points : 0);
      }, 0);
      return Math.round(earnedPoints / totalPoints * 100);
    },
    correctCount: function correctCount() {
      return this.answers.filter(function (answer) {
        return answer.isCorrect;
      }).length;
    },
    totalQuestions: function totalQuestions() {
      return this.answers.length;
    },
    correctRate: function correctRate() {
      if (this.totalQuestions === 0) return 0;
      return Math.round(this.correctCount / this.totalQuestions * 100);
    },
    practiceTime: function practiceTime() {
      if (!this.startTime) return '0分0秒';
      var elapsed = Math.floor((Date.now() - this.startTime) / 1000);
      return this.formatTime(elapsed);
    },
    averageTime: function averageTime() {
      if (!this.startTime || this.totalQuestions === 0) return '0秒';
      var elapsed = Math.floor((Date.now() - this.startTime) / 1000);
      var average = Math.round(elapsed / this.totalQuestions);
      return "".concat(average, "\u79D2");
    }
  },
  onLoad: function onLoad(options) {
    if (options.groupId) {
      this.groupId = options.groupId;
    }
    if (options.practiceId) {
      this.practiceId = options.practiceId;
      this.loadSpecificPractice(options.practiceId);
    }
  },
  onUnload: function onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 开始练习
    startPractice: function startPractice(practice) {
      this.currentPractice = _objectSpread(_objectSpread({}, practice), {}, {
        questions: this.generateQuestions(practice.id, practice.type)
      });
      this.currentQuestionIndex = 0;
      this.selectedAnswer = null;
      this.inputAnswer = '';
      this.showResult = false;
      this.practiceFinished = false;
      this.answers = [];
      this.startTime = Date.now();

      // 设置时间限制
      if (practice.timeLimit) {
        this.timeLimit = practice.timeLimit;
        this.timeLeft = practice.timeLimit;
        this.startTimer();
      }
    },
    // 生成题目
    generateQuestions: function generateQuestions(practiceId, type) {
      var _this$practiceList$fi;
      // 这里应该从API获取真实题目
      var questions = [];
      var questionCount = ((_this$practiceList$fi = this.practiceList.find(function (p) {
        return p.id === practiceId;
      })) === null || _this$practiceList$fi === void 0 ? void 0 : _this$practiceList$fi.questionCount) || 5;
      for (var i = 0; i < questionCount; i++) {
        questions.push(this.createSampleQuestion(type, i + 1));
      }
      return questions;
    },
    // 创建示例题目
    createSampleQuestion: function createSampleQuestion(type, index) {
      var baseQuestion = {
        id: index,
        points: 5
      };
      switch (type) {
        case 'grammar':
          return _objectSpread(_objectSpread({}, baseQuestion), {}, {
            type: 'choice',
            question: "\u8BED\u6CD5\u9898".concat(index, "\uFF1A\u4E0B\u5217\u54EA\u4E2A\u52A9\u8BCD\u4F7F\u7528\u6B63\u786E\uFF1F"),
            options: ['私は学校に行きます', '私は学校で行きます', '私は学校を行きます', '私は学校が行きます'],
            correctAnswer: 0,
            explanation: '「に」表示移动的目的地，所以用「学校に行きます」。'
          });
        case 'vocabulary':
          return _objectSpread(_objectSpread({}, baseQuestion), {}, {
            type: 'choice',
            question: "\u8BCD\u6C47\u9898".concat(index, "\uFF1A\u300C\u3042\u308A\u304C\u3068\u3046\u300D\u7684\u610F\u601D\u662F\uFF1F"),
            options: ['对不起', '谢谢', '再见', '你好'],
            correctAnswer: 1,
            explanation: '「ありがとう」是表示感谢的常用语。'
          });
        case 'reading':
          return _objectSpread(_objectSpread({}, baseQuestion), {}, {
            type: 'choice',
            question: "\u9605\u8BFB\u9898".concat(index, "\uFF1A\u6839\u636E\u77ED\u6587\u5185\u5BB9\uFF0C\u4F5C\u8005\u7684\u89C2\u70B9\u662F\u4EC0\u4E48\uFF1F"),
            options: ['支持', '反对', '中立', '不明确'],
            correctAnswer: 0,
            explanation: '从文中的关键词可以看出作者的支持态度。'
          });
        default:
          return _objectSpread(_objectSpread({}, baseQuestion), {}, {
            type: 'judge',
            question: "\u5224\u65AD\u9898".concat(index, "\uFF1A\u65E5\u8BED\u4E2D\u300C\u3067\u3059\u300D\u662F\u656C\u8BED\u5F62\u5F0F\u3002"),
            correctAnswer: true,
            explanation: '「です」确实是日语中的敬语形式，用于正式场合。'
          });
      }
    },
    // 选择答案
    selectAnswer: function selectAnswer(answer) {
      if (this.showResult) return;
      this.selectedAnswer = answer;
    },
    // 检查答案
    checkAnswer: function checkAnswer() {
      var userAnswer = this.selectedAnswer;
      if (this.currentQuestion.type === 'fill') {
        userAnswer = this.inputAnswer.trim();
      }
      if (userAnswer === null || userAnswer === '') {
        uni.showToast({
          title: '请选择或输入答案',
          icon: 'none'
        });
        return;
      }
      this.showResult = true;
    },
    // 下一题
    nextQuestion: function nextQuestion() {
      // 记录答案
      var userAnswer = this.selectedAnswer;
      if (this.currentQuestion.type === 'fill') {
        userAnswer = this.inputAnswer.trim();
      }
      var isCorrect = false;
      if (this.currentQuestion.type === 'fill') {
        isCorrect = userAnswer.toLowerCase() === this.currentQuestion.correctAnswer.toLowerCase();
      } else {
        isCorrect = userAnswer === this.currentQuestion.correctAnswer;
      }
      this.answers.push({
        questionId: this.currentQuestion.id,
        userAnswer: userAnswer,
        correctAnswer: this.currentQuestion.correctAnswer,
        isCorrect: isCorrect,
        points: this.currentQuestion.points
      });
      if (this.isLastQuestion) {
        this.finishPractice();
      } else {
        this.currentQuestionIndex++;
        this.selectedAnswer = null;
        this.inputAnswer = '';
        this.showResult = false;
      }
    },
    // 上一题
    prevQuestion: function prevQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--;
        // 恢复之前的答案
        var prevAnswer = this.answers[this.currentQuestionIndex];
        if (prevAnswer) {
          if (this.currentQuestion.type === 'fill') {
            this.inputAnswer = prevAnswer.userAnswer;
          } else {
            this.selectedAnswer = prevAnswer.userAnswer;
          }
        } else {
          this.selectedAnswer = null;
          this.inputAnswer = '';
        }
        this.showResult = false;
      }
    },
    // 完成练习
    finishPractice: function finishPractice() {
      // 记录最后一题答案
      if (!this.showResult) {
        this.checkAnswer();
        return;
      }
      var userAnswer = this.selectedAnswer;
      if (this.currentQuestion.type === 'fill') {
        userAnswer = this.inputAnswer.trim();
      }
      var isCorrect = false;
      if (this.currentQuestion.type === 'fill') {
        isCorrect = userAnswer.toLowerCase() === this.currentQuestion.correctAnswer.toLowerCase();
      } else {
        isCorrect = userAnswer === this.currentQuestion.correctAnswer;
      }
      this.answers.push({
        questionId: this.currentQuestion.id,
        userAnswer: userAnswer,
        correctAnswer: this.currentQuestion.correctAnswer,
        isCorrect: isCorrect,
        points: this.currentQuestion.points
      });
      this.practiceFinished = true;
      if (this.timer) {
        clearInterval(this.timer);
      }
    },
    // 重新练习
    restartPractice: function restartPractice() {
      this.currentQuestionIndex = 0;
      this.selectedAnswer = null;
      this.inputAnswer = '';
      this.showResult = false;
      this.practiceFinished = false;
      this.answers = [];
      this.startTime = Date.now();
      if (this.timeLimit > 0) {
        this.timeLeft = this.timeLimit;
        this.startTimer();
      }
    },
    // 返回列表
    backToList: function backToList() {
      this.currentPractice = null;
      this.practiceFinished = false;
    },
    // 查看解析
    reviewAnswers: function reviewAnswers() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    },
    // 开始计时器
    startTimer: function startTimer() {
      var _this = this;
      this.timer = setInterval(function () {
        _this.timeLeft--;
        if (_this.timeLeft <= 0) {
          _this.finishPractice();
        }
      }, 1000);
    },
    // 格式化时间
    formatTime: function formatTime(seconds) {
      var mins = Math.floor(seconds / 60);
      var secs = seconds % 60;
      return "".concat(mins, "\u5206").concat(secs, "\u79D2");
    },
    // 获取图标类名
    getIconClass: function getIconClass(type) {
      var iconMap = {
        'grammar': 'icon-book',
        'vocabulary': 'icon-word',
        'reading': 'icon-read',
        'comprehensive': 'icon-star'
      };
      return iconMap[type] || 'icon-edit';
    },
    // 获取类型文本
    getTypeText: function getTypeText(type) {
      var typeMap = {
        'grammar': '语法',
        'vocabulary': '词汇',
        'reading': '阅读',
        'comprehensive': '综合',
        'choice': '选择题',
        'fill': '填空题',
        'judge': '判断题'
      };
      return typeMap[type] || '未知';
    },
    // 获取难度文本
    getDifficultyText: function getDifficultyText(difficulty) {
      var map = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      };
      return map[difficulty] || '未知';
    },
    // 获取分数等级
    getGrade: function getGrade(score) {
      if (score >= 90) return '优秀';
      if (score >= 80) return '良好';
      if (score >= 70) return '中等';
      if (score >= 60) return '及格';
      return '不及格';
    },
    // 获取分数样式类
    getScoreClass: function getScoreClass(score) {
      if (score >= 90) return 'excellent';
      if (score >= 80) return 'good';
      if (score >= 60) return 'pass';
      return 'fail';
    },
    loadSpecificPractice: function loadSpecificPractice(practiceId) {
      var practice = this.practiceList.find(function (p) {
        return p.id == practiceId;
      });
      if (practice) {
        this.startPractice(practice);
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 527:
/*!*****************************************************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/groups/quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css& ***!
  \*****************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css& */ 528);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_quiz_practice_vue_vue_type_style_index_0_id_5012569e_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 528:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/groups/quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[521,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/groups/quiz-practice.js.map