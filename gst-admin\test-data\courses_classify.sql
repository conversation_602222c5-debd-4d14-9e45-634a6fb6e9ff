/*
 测试用课程分类数据
 基于您提供的SQL结构简化版本
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for courses_classify
-- ----------------------------
DROP TABLE IF EXISTS `courses_classify`;
CREATE TABLE `courses_classify`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0,
  `sort` tinyint(4) NOT NULL DEFAULT 1,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  `level` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp(0) DEFAULT NULL,
  `updated_at` timestamp(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of courses_classify
-- ----------------------------
INSERT INTO `courses_classify` VALUES (1, 0, 1, '日语学习', '基础日语学习课程分类', 1, 1, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (2, 0, 2, '日语考级', 'JLPT考级相关课程', 1, 1, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (3, 0, 3, '实用口语', '日常生活口语练习', 1, 1, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (4, 0, 4, '升学考试', '升学相关日语考试', 1, 1, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (5, 1, 5, 'N5语法', 'N5级别语法课程', 1, 2, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (6, 1, 6, 'N5词汇', 'N5级别词汇课程', 1, 2, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (7, 2, 7, 'N4考试', 'N4级别考试准备', 1, 2, '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_classify` VALUES (8, 2, 8, 'N3考试', 'N3级别考试准备', 1, 2, '2024-07-28 10:00:00', '2024-07-28 10:00:00');

SET FOREIGN_KEY_CHECKS = 1;
