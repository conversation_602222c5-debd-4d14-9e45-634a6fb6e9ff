/*
 测试用课程分类数据
 基于您提供的SQL结构简化版本
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for courses_item (课程项目表)
-- ----------------------------
DROP TABLE IF EXISTS `courses_item`;
CREATE TABLE `courses_item`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` int(11) NOT NULL DEFAULT 0,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'N5',
  `duration` int(11) DEFAULT 60,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'published',
  `created_at` timestamp(0) DEFAULT NULL,
  `updated_at` timestamp(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of courses_item
-- ----------------------------
INSERT INTO `courses_item` VALUES (1, '五十音图学习', 1, '学习日语基础的五十音图，包括平假名和片假名的发音和写法', 'N5', 60, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (2, '基础问候语', 1, '学习日常基础问候用语，如おはよう、こんにちは等', 'N5', 45, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (3, '数字表达', 1, '学习日语中的数字表达方法，包括基数词和序数词', 'N5', 30, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (4, '时间表达', 1, '学习时间相关的语法和词汇，包括时、分、秒的表达', 'N5', 50, 'draft', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (5, '自我介绍', 1, '学习如何用日语进行自我介绍，包括姓名、年龄、职业等', 'N5', 40, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (6, '家族称呼', 2, '学习日语中的家族成员称呼方式', 'N5', 35, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (7, '购物用语', 3, '学习在商店购物时的常用日语表达', 'N4', 55, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');
INSERT INTO `courses_item` VALUES (8, '餐厅点餐', 3, '学习在餐厅点餐时的日语会话', 'N4', 45, 'published', '2024-07-28 10:00:00', '2024-07-28 10:00:00');

SET FOREIGN_KEY_CHECKS = 1;
