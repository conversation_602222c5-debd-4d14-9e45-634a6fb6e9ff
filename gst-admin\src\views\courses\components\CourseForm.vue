<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑课程' : '创建课程'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="large"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入课程标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select v-model="form.category" placeholder="选择分类" style="width: 100%">
              <el-option label="语法" value="grammar" />
              <el-option label="词汇" value="vocabulary" />
              <el-option label="听力" value="listening" />
              <el-option label="口语" value="speaking" />
              <el-option label="阅读" value="reading" />
              <el-option label="写作" value="writing" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="等级" prop="level">
            <el-select v-model="form.level" placeholder="选择等级" style="width: 100%">
              <el-option label="N5" value="N5" />
              <el-option label="N4" value="N4" />
              <el-option label="N3" value="N3" />
              <el-option label="N2" value="N2" />
              <el-option label="N1" value="N1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时长(分钟)" prop="duration">
            <el-input-number
              v-model="form.duration"
              :min="1"
              :max="300"
              :step="5"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="课程描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入课程描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="课程内容">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="8"
          placeholder="请输入课程内容"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status" v-if="isEdit">
        <el-radio-group v-model="form.status">
          <el-radio label="draft">草稿</el-radio>
          <el-radio label="published">已发布</el-radio>
          <el-radio label="archived">已下架</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { post, put } from '@/utils/request'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  course: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const authStore = useAuthStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.course?.id)

// 表单数据
const form = reactive({
  title: '',
  category: '',
  level: '',
  duration: 30,
  description: '',
  content: '',
  status: 'draft'
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入课程标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择等级', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请输入时长', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '时长范围在 1 到 300 分钟', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    title: '',
    category: '',
    level: '',
    duration: 30,
    description: '',
    content: '',
    status: 'draft'
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 填充表单数据
const fillForm = (course) => {
  if (course) {
    Object.assign(form, {
      title: course.title || '',
      category: course.category || '',
      level: course.level || '',
      duration: course.duration || 30,
      description: course.description || '',
      content: course.content || '',
      status: course.status || 'draft'
    })
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    const data = { ...form }
    let response
    
    if (isEdit.value) {
      response = await put(`/api/courses/${props.course.id}`, data, { showSuccess: true })
    } else {
      response = await post('/api/courses', data, { showSuccess: true })
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    if (props.course) {
      fillForm(props.course)
    } else {
      resetForm()
    }
  }
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
