const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'gst_japanese_training.sqlite');

console.log('📍 检查categories表中的数据...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 连接到SQLite数据库成功');
});

// 查询categories表中的数据
db.all("SELECT COUNT(*) as count FROM categories", [], (err, rows) => {
  if (err) {
    console.error('❌ 查询categories表失败:', err.message);
  } else {
    console.log(`\n📊 categories表总记录数: ${rows[0].count}`);
  }
  
  // 查询前10条记录
  db.all("SELECT id, name, pid, sort, status, level, created_by FROM categories ORDER BY id LIMIT 10", [], (err, categoryRows) => {
    if (err) {
      console.error('❌ 查询categories记录失败:', err.message);
    } else {
      console.log('\n📋 前10条分类记录:');
      categoryRows.forEach((row, index) => {
        console.log(`${index + 1}. ID:${row.id} 名称:"${row.name}" 父ID:${row.pid} 排序:${row.sort} 状态:${row.status} 层级:${row.level} 创建者:${row.created_by}`);
      });
    }
    
    // 查询顶级分类
    db.all("SELECT id, name, status FROM categories WHERE pid = 0 ORDER BY sort", [], (err, topRows) => {
      if (err) {
        console.error('❌ 查询顶级分类失败:', err.message);
      } else {
        console.log(`\n🔝 顶级分类 (${topRows.length}个):`);
        topRows.forEach((row, index) => {
          console.log(`${index + 1}. ID:${row.id} 名称:"${row.name}" 状态:${row.status}`);
        });
      }
      
      // 关闭数据库连接
      db.close((err) => {
        if (err) {
          console.error('❌ 关闭数据库失败:', err.message);
        } else {
          console.log('\n✅ 数据库连接已关闭');
        }
      });
    });
  });
});
