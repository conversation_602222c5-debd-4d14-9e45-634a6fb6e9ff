const express = require('express');
const router = express.Router();

// 课程相关路由
const courseRouter = express.Router();

// 首页数据接口 - 匹配小程序 v1/course/index 调用
courseRouter.get('/index', async (req, res) => {
  try {
    // 返回完全匹配小程序期望的数据结构
    const homeData = {
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        // 轮播图数据
        banner: [
          {
            id: 1,
            title: '基础语法课程',
            image: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=3',
            sort: 1
          },
          {
            id: 2,
            title: '加入学习小组',
            image: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/groups/index',
            sort: 2
          },
          {
            id: 3,
            title: '免费试听课程',
            image: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=5',
            sort: 3
          }
        ],
        
        // 第一行课程分类
        course1: [
          {
            id: 1,
            name: '基础语法',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=3',
            sort: 1
          },
          {
            id: 2,
            name: '词汇学习',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=4',
            sort: 2
          },
          {
            id: 3,
            name: '听力训练',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=5',
            sort: 3
          },
          {
            id: 4,
            name: '口语练习',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=6',
            sort: 4
          }
        ],
        
        // 第二行课程分类
        course2: [
          {
            id: 5,
            name: '进阶课程',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=2',
            sort: 1
          },
          {
            id: 6,
            name: '考试辅导',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=7',
            sort: 2
          },
          {
            id: 7,
            name: '商务日语',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/course/course?id=8',
            sort: 3
          },
          {
            id: 8,
            name: '学习小组',
            icon: '/static/imgs/placeholder.svg',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/groups/index',
            sort: 4
          }
        ],
        
        // 最新课程（注意：新概念不在首页展示）
        new: [
          {
            id: 3,
            title: 'N5基础语法精讲',
            thumb: '/static/imgs/placeholder.svg',
            price: 199,
            original_price: 299,
            level: 'N5',
            students: 1560,
            rating: 4.6,
            url: '/pages/course/course?id=3'
          },
          {
            id: 4,
            title: 'N5核心词汇1000',
            thumb: '/static/imgs/placeholder.svg',
            price: 159,
            original_price: 229,
            level: 'N5',
            students: 2100,
            rating: 4.5,
            url: '/pages/course/course?id=4'
          },
          {
            id: 5,
            title: '日语听力入门',
            thumb: '/static/imgs/placeholder.svg',
            price: 229,
            original_price: 299,
            level: 'N5',
            students: 780,
            rating: 4.4,
            url: '/pages/course/course?id=5'
          },
          {
            id: 6,
            title: '日语口语发音纠正',
            thumb: '/static/imgs/placeholder.svg',
            price: 359,
            original_price: 429,
            level: 'N5',
            students: 650,
            rating: 4.9,
            url: '/pages/course/course?id=6'
          }
        ],
        
        // 底部推荐内容
        game_show_footer: [
          {
            id: 1,
            title: '学习小组',
            description: '加入学习小组，和小伙伴一起学习',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/groups/index'
          },
          {
            id: 2,
            title: '学习记录',
            description: '查看你的学习进度和成就',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/user/learning-records'
          },
          {
            id: 3,
            title: '在线测试',
            description: '测试你的日语水平',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/test/index'
          }
        ],

        // 添加缺失的字段
        game_foot_post: [
          {
            id: 1,
            title: '推荐阅读',
            lists: [
              {
                id: 1,
                title: '日语学习方法分享',
                thumb: '/static/imgs/placeholder.svg',
                summary: '分享一些高效的日语学习方法和技巧',
                author: '田中老师',
                publishTime: '2024-01-20',
                readCount: 1250,
                url: '/pages/article/detail?id=1'
              },
              {
                id: 2,
                title: '五十音图记忆法',
                thumb: '/static/imgs/placeholder.svg',
                summary: '快速记忆五十音图的实用方法',
                author: '佐藤老师',
                publishTime: '2024-01-18',
                readCount: 890,
                url: '/pages/article/detail?id=2'
              },
              {
                id: 3,
                title: 'N5考试备考指南',
                thumb: '/static/imgs/placeholder.svg',
                summary: 'N5日语能力考试的备考策略和重点',
                author: '山田老师',
                publishTime: '2024-01-15',
                readCount: 2100,
                url: '/pages/article/detail?id=3'
              }
            ]
          }
        ],

        // 公告通知
        notice: [
          {
            id: 1,
            title: '欢迎来到GST日语培训班！',
            content: '开始您的日语学习之旅',
            type: 'info',
            showTime: 3000
          }
        ],

        // 顶部导航
        top_post: [
          {
            id: 1,
            title: '全部',
            type: 'all',
            active: true
          },
          {
            id: 2,
            title: '基础',
            type: 'basic',
            active: false
          },
          {
            id: 3,
            title: '进阶',
            type: 'advanced',
            active: false
          },
          {
            id: 4,
            title: '考试',
            type: 'exam',
            active: false
          }
        ]
      }
    };

    res.json(homeData);

  } catch (error) {
    console.error('获取首页数据失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取首页数据失败',
      error: error.message
    });
  }
});

// 课程详情接口
courseRouter.get('/detail/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟课程详情数据
    const courseDetails = {
      1: {
        id: 1,
        title: '新概念日语第一册',
        thumb: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=新概念日语1',
        picture: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=新概念日语1',
        Cost: 299,
        price: 299,
        original_price: 399,
        content: `<div style="padding:20px;font-size:14px;line-height:1.6;color:#333;">
          <h3>课程介绍</h3>
          <p>适合零基础学员，从五十音图开始，系统学习日语基础知识。</p>
          <h3>课程内容</h3>
          <ul>
            <li>五十音图完整学习</li>
            <li>基础语法讲解</li>
            <li>日常会话练习</li>
            <li>发音纠正指导</li>
          </ul>
          <h3>适合人群</h3>
          <p>零基础日语学习者，想要系统学习日语的同学。</p>
        </div>`,
        level: 'N5',
        count: 20,
        viewnum: 1250,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        jianjie: '从零开始学习日语，系统掌握基础知识',
        teacher: {
          name: '田中老师',
          avatar: 'https://via.placeholder.com/100x100/4A90E2/FFFFFF?text=田中',
          bio: '资深日语教师，10年教学经验'
        },
        li: [
          {
            id: 1,
            title: '第1课：五十音图（あ行）',
            coursename: '第1课：五十音图（あ行）',
            url: 'https://example.com/video1.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 2,
            title: '第2课：五十音图（か行）',
            coursename: '第2课：五十音图（か行）',
            url: 'https://example.com/video2.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 3,
            title: '第3课：五十音图（さ行）',
            coursename: '第3课：五十音图（さ行）',
            url: 'https://example.com/video3.mp4',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          }
        ]
      }
    };

    const course = courseDetails[id];
    if (!course) {
      return res.status(404).json({
        code: 404,
        status: false,
        message: '课程不存在'
      });
    }

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: course
    });

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程详情失败',
      error: error.message
    });
  }
});

// 课程列表接口
courseRouter.get('/list', async (req, res) => {
  try {
    const { page = 1, limit = 10, category, level } = req.query;

    // 模拟课程列表数据
    const courses = [
      {
        id: 1,
        title: '新概念日语第一册',
        thumb: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=新概念1',
        price: 299,
        level: 'N5',
        students: 1250,
        rating: 4.8
      },
      {
        id: 2,
        title: '新概念日语第二册',
        thumb: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=新概念2',
        price: 399,
        level: 'N4',
        students: 890,
        rating: 4.7
      }
    ];

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        list: courses,
        total: courses.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取课程列表失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程列表失败',
      error: error.message
    });
  }
});

// 注册课程路由
router.use('/course', courseRouter);

// 添加index路由组
const indexRouter = express.Router();

// 课程相关的index路由
const indexCoursesRouter = express.Router();

// 课程详情 - 匹配小程序 getCourseDetails 调用
indexCoursesRouter.post('/index', async (req, res) => {
  try {
    const { id } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '课程ID不能为空'
      });
    }

    // 使用与 /course/detail/:id 相同的数据结构
    const courseDetails = {
      1: {
        id: 1,
        title: '新概念日语第一册',
        thumb: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=新概念日语1',
        picture: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=新概念日语1',
        Cost: 299,
        price: 299,
        original_price: 399,
        content: `<div style="padding:20px;font-size:14px;line-height:1.6;color:#333;">
          <h3>课程介绍</h3>
          <p>适合零基础学员，从五十音图开始，系统学习日语基础知识。</p>
          <h3>课程内容</h3>
          <ul>
            <li>五十音图完整学习</li>
            <li>基础语法讲解</li>
            <li>日常会话练习</li>
            <li>发音纠正指导</li>
          </ul>
          <h3>适合人群</h3>
          <p>零基础日语学习者，想要系统学习日语的同学。</p>
        </div>`,
        level: 'N5',
        count: 20,
        viewnum: 1250,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        jianjie: '从零开始学习日语，系统掌握基础知识',
        teacher: {
          name: '田中老师',
          avatar: 'https://via.placeholder.com/100x100/4A90E2/FFFFFF?text=田中',
          bio: '资深日语教师，10年教学经验'
        },
        li: [
          {
            id: 1,
            title: '第1课：五十音图（あ行）',
            coursename: '第1课：五十音图（あ行）',
            url: 'https://example.com/video1.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 2,
            title: '第2课：五十音图（か行）',
            coursename: '第2课：五十音图（か行）',
            url: 'https://example.com/video2.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 3,
            title: '第3课：五十音图（さ行）',
            coursename: '第3课：五十音图（さ行）',
            url: 'https://example.com/video3.mp4',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          },
          {
            id: 4,
            title: '第4课：五十音图（た行）',
            coursename: '第4课：五十音图（た行）',
            url: 'https://example.com/video4.mp4',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          },
          {
            id: 5,
            title: '第5课：基础问候语',
            coursename: '第5课：基础问候语',
            url: 'https://example.com/video5.mp4',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          }
        ]
      },
      2: {
        id: 2,
        title: '新概念日语第二册',
        thumb: 'https://via.placeholder.com/400x300/50C878/FFFFFF?text=新概念日语2',
        picture: 'https://via.placeholder.com/400x300/50C878/FFFFFF?text=新概念日语2',
        Cost: 399,
        price: 399,
        original_price: 499,
        content: `<div style="padding:20px;font-size:14px;line-height:1.6;color:#333;">
          <h3>课程介绍</h3>
          <p>进阶课程，深入学习日语语法和表达方式。</p>
          <p>在第一册基础上，学习更复杂的语法结构和表达方式，提高日语综合能力。</p>
        </div>`,
        level: 'N4',
        count: 25,
        viewnum: 890,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        jianjie: '进阶课程，深入学习日语语法和表达方式',
        teacher: {
          name: '佐藤老师',
          avatar: 'https://via.placeholder.com/100x100/50C878/FFFFFF?text=佐藤',
          bio: '日语语法专家，15年教学经验'
        },
        li: [
          {
            id: 6,
            title: '第1课：动词变位基础',
            coursename: '第1课：动词变位基础',
            url: 'https://example.com/video6.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 7,
            title: '第2课：形容词活用',
            coursename: '第2课：形容词活用',
            url: 'https://example.com/video7.mp4',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          }
        ]
      }
    };

    const course = courseDetails[id];
    if (!course) {
      return res.status(404).json({
        code: 404,
        status: false,
        message: '课程不存在'
      });
    }

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: course
    });

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程详情失败',
      error: error.message
    });
  }
});

// 课程评价
indexCoursesRouter.post('/comment', async (req, res) => {
  try {
    const { courseId, rating, comment, uid } = req.body;

    if (!courseId || !rating || !comment || !uid) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '参数不完整'
      });
    }

    // 模拟评价成功
    res.json({
      code: 0,
      status: true,
      message: '评价成功',
      data: {
        id: Date.now(),
        courseId,
        uid,
        rating,
        comment,
        createTime: new Date()
      }
    });

  } catch (error) {
    console.error('课程评价失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '课程评价失败',
      error: error.message
    });
  }
});

// 用户相关的index路由
const indexUserRouter = express.Router();

// 我的课程
indexUserRouter.post('/my_course', async (req, res) => {
  try {
    const { uid, page = 1, limit = 10 } = req.body;

    if (!uid) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '用户ID不能为空'
      });
    }

    // 模拟用户课程数据
    const myCourses = [
      {
        id: 1,
        title: '新概念日语第一册',
        thumb: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=新概念1',
        progress: 60,
        lastStudyTime: '2024-01-20 14:30:00',
        totalLessons: 20,
        completedLessons: 12
      },
      {
        id: 3,
        title: 'N5基础语法精讲',
        thumb: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=N5语法',
        progress: 30,
        lastStudyTime: '2024-01-18 10:15:00',
        totalLessons: 15,
        completedLessons: 5
      }
    ];

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        list: myCourses,
        total: myCourses.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取我的课程失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取我的课程失败',
      error: error.message
    });
  }
});

// 我的收藏
indexUserRouter.post('/my_dingyue', async (req, res) => {
  try {
    const { uid, page = 1, limit = 10 } = req.body;

    if (!uid) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '用户ID不能为空'
      });
    }

    // 模拟收藏数据
    const myFavorites = [
      {
        id: 2,
        title: '新概念日语第二册',
        thumb: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=新概念2',
        price: 399,
        favoriteTime: '2024-01-15 16:20:00'
      },
      {
        id: 4,
        title: 'N5核心词汇1000',
        thumb: 'https://via.placeholder.com/300x200/FFD93D/FFFFFF?text=N5词汇',
        price: 159,
        favoriteTime: '2024-01-12 09:45:00'
      }
    ];

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        list: myFavorites,
        total: myFavorites.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取我的收藏失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取我的收藏失败',
      error: error.message
    });
  }
});

// 注册index路由
indexRouter.use('/courses', indexCoursesRouter);
indexRouter.use('/user', indexUserRouter);
router.use('/index', indexRouter);

module.exports = router;
