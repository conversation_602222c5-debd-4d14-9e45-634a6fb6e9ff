const express = require('express');
const router = express.Router();

// 辅助函数：获取首页分类数据
async function getCategoriesForHomePage(Category, Course, offset = 0, limit = 4) {
  try {
    const categories = await Category.findAll({
      where: { status: 'active' },
      order: [['sort', 'ASC']],
      limit: limit,
      offset: offset,
      attributes: ['id', 'name', 'image', 'description']
    });

    return categories.map((category, index) => ({
      id: category.id,
      name: category.name,
      icon: category.image || '/static/imgs/placeholder.svg',
      thumb: category.image || '/static/imgs/placeholder.svg',
      url: `/pages/course/course?categoryId=${category.id}`,
      sort: offset + index + 1
    }));
  } catch (error) {
    console.log('获取分类数据失败，使用默认数据');
    return [];
  }
}

// 辅助函数：获取最新课程数据
async function getLatestCoursesForHomePage(Course) {
  try {
    const courses = await Course.findAll({
      where: { status: 'published' },
      order: [['createdAt', 'DESC']],
      limit: 4,
      attributes: ['id', 'title', 'picture', 'Cost', 'level', 'studentCount', 'rating']
    });

    return courses.map(course => ({
      id: course.id,
      title: course.title,
      thumb: course.picture || '/static/imgs/placeholder.svg',
      price: course.Cost || 0,
      original_price: Math.round((course.Cost || 0) * 1.3), // 模拟原价
      level: course.level || 'N5',
      students: course.studentCount || 0,
      rating: course.rating || 0,
      url: `/pages/course/course?id=${course.id}`
    }));
  } catch (error) {
    console.log('获取最新课程数据失败，使用默认数据');
    return [];
  }
}

// 课程相关路由
const courseRouter = express.Router();

// 首页数据接口 - 匹配小程序 v1/course/index 调用
courseRouter.get('/index', async (req, res) => {
  try {
    const { Course, Category, Banner } = require('../models');

    // 获取轮播图数据
    let banners = [];
    try {
      const bannerData = await Banner.findAll({
        where: { status: 'active' },
        order: [['orderNum', 'ASC']],
        limit: 5,
        attributes: ['id', 'title', 'image', 'link', 'orderNum']
      });

      banners = bannerData.map(banner => ({
        id: banner.id,
        title: banner.title,
        image: banner.image || '/static/imgs/placeholder.svg',
        thumb: banner.image || '/static/imgs/placeholder.svg',
        url: banner.link || '#',
        sort: banner.orderNum || 0
      }));
    } catch (error) {
      console.log('轮播图数据获取失败，使用默认数据');
      banners = [
        {
          id: 1,
          title: '基础语法课程',
          image: '/static/imgs/placeholder.svg',
          thumb: '/static/imgs/placeholder.svg',
          url: '/pages/course/course?id=3',
          sort: 1
        },
        {
          id: 2,
          title: '加入学习小组',
          image: '/static/imgs/placeholder.svg',
          thumb: '/static/imgs/placeholder.svg',
          url: '/pages/groups/index',
          sort: 2
        }
      ];
    }

    const homeData = {
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        // 轮播图数据
        banner: banners,
        
        // 第一行课程分类 - 获取真实数据
        course1: await getCategoriesForHomePage(Category, Course, 0, 4),
        
        // 第二行课程分类 - 获取真实数据
        course2: await getCategoriesForHomePage(Category, Course, 4, 4),

        // 最新课程（注意：新概念不在首页展示）- 获取真实数据
        new: await getLatestCoursesForHomePage(Course),
        
        // 底部推荐内容
        game_show_footer: [
          {
            id: 1,
            title: '学习小组',
            description: '加入学习小组，和小伙伴一起学习',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/groups/index'
          },
          {
            id: 2,
            title: '学习记录',
            description: '查看你的学习进度和成就',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/user/learning-records'
          },
          {
            id: 3,
            title: '在线测试',
            description: '测试你的日语水平',
            thumb: '/static/imgs/placeholder.svg',
            url: '/pages/test/index'
          }
        ],

        // 添加缺失的字段
        game_foot_post: [
          {
            id: 1,
            title: '推荐阅读',
            lists: [
              {
                id: 1,
                title: '日语学习方法分享',
                thumb: '/static/imgs/placeholder.svg',
                summary: '分享一些高效的日语学习方法和技巧',
                author: '田中老师',
                publishTime: '2024-01-20',
                readCount: 1250,
                url: '/pages/article/detail?id=1'
              },
              {
                id: 2,
                title: '五十音图记忆法',
                thumb: '/static/imgs/placeholder.svg',
                summary: '快速记忆五十音图的实用方法',
                author: '佐藤老师',
                publishTime: '2024-01-18',
                readCount: 890,
                url: '/pages/article/detail?id=2'
              },
              {
                id: 3,
                title: 'N5考试备考指南',
                thumb: '/static/imgs/placeholder.svg',
                summary: 'N5日语能力考试的备考策略和重点',
                author: '山田老师',
                publishTime: '2024-01-15',
                readCount: 2100,
                url: '/pages/article/detail?id=3'
              }
            ]
          }
        ],

        // 公告通知
        notice: [
          {
            id: 1,
            title: '欢迎来到GST日语培训班！',
            content: '开始您的日语学习之旅',
            type: 'info',
            showTime: 3000
          }
        ],

        // 顶部导航
        top_post: [
          {
            id: 1,
            title: '全部',
            type: 'all',
            active: true
          },
          {
            id: 2,
            title: '基础',
            type: 'basic',
            active: false
          },
          {
            id: 3,
            title: '进阶',
            type: 'advanced',
            active: false
          },
          {
            id: 4,
            title: '考试',
            type: 'exam',
            active: false
          }
        ]
      }
    };

    res.json(homeData);

  } catch (error) {
    console.error('获取首页数据失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取首页数据失败',
      error: error.message
    });
  }
});

// 课程详情接口
courseRouter.get('/detail/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟课程详情数据
    const courseDetails = {
      1: {
        id: 1,
        title: '新概念日语第一册',
        thumb: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=新概念日语1',
        picture: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=新概念日语1',
        Cost: 299,
        price: 299,
        original_price: 399,
        content: `<div style="padding:20px;font-size:14px;line-height:1.6;color:#333;">
          <h3>课程介绍</h3>
          <p>适合零基础学员，从五十音图开始，系统学习日语基础知识。</p>
          <h3>课程内容</h3>
          <ul>
            <li>五十音图完整学习</li>
            <li>基础语法讲解</li>
            <li>日常会话练习</li>
            <li>发音纠正指导</li>
          </ul>
          <h3>适合人群</h3>
          <p>零基础日语学习者，想要系统学习日语的同学。</p>
        </div>`,
        level: 'N5',
        count: 20,
        viewnum: 1250,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        jianjie: '从零开始学习日语，系统掌握基础知识',
        teacher: {
          name: '田中老师',
          avatar: 'https://via.placeholder.com/100x100/4A90E2/FFFFFF?text=田中',
          bio: '资深日语教师，10年教学经验'
        },
        li: [
          {
            id: 1,
            title: '第1课：五十音图（あ行）',
            coursename: '第1课：五十音图（あ行）',
            url: 'https://example.com/video1.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 2,
            title: '第2课：五十音图（か行）',
            coursename: '第2课：五十音图（か行）',
            url: 'https://example.com/video2.mp4',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 3,
            title: '第3课：五十音图（さ行）',
            coursename: '第3课：五十音图（さ行）',
            url: 'https://example.com/video3.mp4',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          }
        ]
      }
    };

    const course = courseDetails[id];
    if (!course) {
      return res.status(404).json({
        code: 404,
        status: false,
        message: '课程不存在'
      });
    }

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: course
    });

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程详情失败',
      error: error.message
    });
  }
});

// 课程列表接口
courseRouter.get('/list', async (req, res) => {
  try {
    const { page = 1, limit = 10, category, level } = req.query;
    const { Course, Category } = require('../models');

    // 构建查询条件
    const whereCondition = {
      status: 'published'
    };

    if (category) {
      whereCondition.categoryId = category;
    }

    if (level) {
      whereCondition.level = level;
    }

    const offset = (page - 1) * limit;

    // 获取真实的课程列表数据
    const { rows: courses, count } = await Course.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Category,
          as: 'courseCategory',
          attributes: ['name']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        'id', 'title', 'description', 'level', 'duration',
        'picture', 'Cost', 'rating', 'studentCount', 'createdAt'
      ]
    });

    // 格式化数据以匹配小程序期望的格式
    const formattedCourses = courses.map(course => ({
      id: course.id,
      title: course.title,
      thumb: course.picture || '/static/imgs/placeholder.svg',
      price: course.Cost || 0,
      level: course.level,
      students: course.studentCount || 0,
      rating: course.rating || 0,
      duration: course.duration || 0,
      description: course.description
    }));

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        list: formattedCourses,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取课程列表失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程列表失败',
      error: error.message
    });
  }
});

// 获取课程分类接口
courseRouter.get('/getCate', async (req, res) => {
  try {
    const { Category, Course } = require('../models');

    // 获取真实的课程分类数据
    const categories = await Category.findAll({
      where: { status: 'active' },
      order: [['sort', 'ASC']],
      attributes: ['id', 'name', 'description', 'image', 'sort']
    });

    // 为每个分类计算课程数量
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const courseCount = await Course.count({
          where: {
            categoryId: category.id,
            status: 'published'
          }
        });

        return {
          id: category.id,
          name: category.name,
          icon: category.image || '/static/imgs/placeholder.svg',
          thumb: category.image || '/static/imgs/placeholder.svg',
          description: category.description || `${category.name}相关课程`,
          courseCount,
          sort: category.sort || 0
        };
      })
    );

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: categoriesWithCount
    });

  } catch (error) {
    console.error('获取课程分类失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程分类失败',
      error: error.message
    });
  }
});

// 获取子分类接口
courseRouter.get('/getSubClass', async (req, res) => {
  try {
    const { id } = req.query;

    if (!id) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '分类ID不能为空'
      });
    }

    const { Category, Course } = require('../models');

    // 获取真实的子分类数据
    const subCategories = await Category.findAll({
      where: {
        pid: id,
        status: 'active'
      },
      order: [['sort', 'ASC']],
      attributes: ['id', 'name', 'pid', 'description', 'sort']
    });

    // 为每个子分类计算课程数量
    const subCatesWithCount = await Promise.all(
      subCategories.map(async (subCategory) => {
        const courseCount = await Course.count({
          where: {
            categoryId: subCategory.id,
            status: 'published'
          }
        });

        return {
          id: subCategory.id,
          name: subCategory.name,
          parentId: subCategory.pid,
          courseCount,
          description: subCategory.description
        };
      })
    );

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: subCatesWithCount
    });

  } catch (error) {
    console.error('获取子分类失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取子分类失败',
      error: error.message
    });
  }
});

// 注册课程路由
router.use('/course', courseRouter);

// 添加index路由组
const indexRouter = express.Router();

// 课程相关的index路由
const indexCoursesRouter = express.Router();

// 课程详情 - 匹配小程序 getCourseDetails 调用
indexCoursesRouter.post('/index', async (req, res) => {
  try {
    const { id } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '课程ID不能为空'
      });
    }

    const { Course, CourseUnit, User } = require('../models');

    // 获取真实的课程详情数据
    const course = await Course.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'realName', 'avatar']
        }
      ],
      attributes: [
        'id', 'title', 'description', 'content', 'level', 'duration',
        'picture', 'Cost', 'rating', 'studentCount'
      ]
    });

    if (!course) {
      return res.status(404).json({
        code: 404,
        status: false,
        message: '课程不存在'
      });
    }

    // 获取课程单元
    const courseUnits = await CourseUnit.findAll({
      where: { courseId: id },
      order: [['orderNum', 'ASC']],
      attributes: [
        'id', 'title', 'description', 'duration', 'orderNum',
        'videoUrl', 'isFree'
      ]
    });

    // 格式化课程单元数据
    const formattedUnits = courseUnits.map((unit, index) => ({
      id: unit.id,
      title: unit.title,
      coursename: unit.title,
      url: unit.videoUrl || 'https://example.com/video.mp4',
      is_pay: unit.isFree ? 0 : 1,
      is_sk: unit.isFree ? 1 : 0,
      freesecond: unit.isFree ? 300 : 0
    }));

    // 格式化课程数据
    const formattedCourse = {
      id: course.id,
      title: course.title,
      thumb: course.picture || '/static/imgs/placeholder.svg',
      picture: course.picture || '/static/imgs/placeholder.svg',
      Cost: course.Cost || 0,
      price: course.Cost || 0,
      original_price: Math.round((course.Cost || 0) * 1.3),
      content: course.content || course.description || '',
      level: course.level || 'N5',
      count: courseUnits.length,
      viewnum: course.studentCount || 0,
      is_buy: 0, // 需要根据用户购买记录判断
      is_collect: 0, // 需要根据用户收藏记录判断
      media: 'video',
      jianjie: course.description || '',
      teacher: {
        name: course.creator?.realName || '专业老师',
        avatar: course.creator?.avatar || '/static/imgs/placeholder.svg',
        bio: '专业日语教师'
      },
      li: formattedUnits
    };

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: formattedCourse
    });

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取课程详情失败',
      error: error.message
    });
  }
});

// 课程评价
indexCoursesRouter.post('/comment', async (req, res) => {
  try {
    const { courseId, rating, comment, uid } = req.body;

    if (!courseId || !rating || !comment || !uid) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '参数不完整'
      });
    }

    // 模拟评价成功
    res.json({
      code: 0,
      status: true,
      message: '评价成功',
      data: {
        id: Date.now(),
        courseId,
        uid,
        rating,
        comment,
        createTime: new Date()
      }
    });

  } catch (error) {
    console.error('课程评价失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '课程评价失败',
      error: error.message
    });
  }
});

// 用户相关的index路由
const indexUserRouter = express.Router();

// 我的课程
indexUserRouter.post('/my_course', async (req, res) => {
  try {
    const { uid, page = 1, limit = 10 } = req.body;

    if (!uid) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '用户ID不能为空'
      });
    }

    // 模拟用户课程数据
    const myCourses = [
      {
        id: 1,
        title: '新概念日语第一册',
        thumb: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=新概念1',
        progress: 60,
        lastStudyTime: '2024-01-20 14:30:00',
        totalLessons: 20,
        completedLessons: 12
      },
      {
        id: 3,
        title: 'N5基础语法精讲',
        thumb: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=N5语法',
        progress: 30,
        lastStudyTime: '2024-01-18 10:15:00',
        totalLessons: 15,
        completedLessons: 5
      }
    ];

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        list: myCourses,
        total: myCourses.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取我的课程失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取我的课程失败',
      error: error.message
    });
  }
});

// 我的收藏
indexUserRouter.post('/my_dingyue', async (req, res) => {
  try {
    const { uid, page = 1, limit = 10 } = req.body;

    if (!uid) {
      return res.status(400).json({
        code: 400,
        status: false,
        message: '用户ID不能为空'
      });
    }

    // 模拟收藏数据
    const myFavorites = [
      {
        id: 2,
        title: '新概念日语第二册',
        thumb: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=新概念2',
        price: 399,
        favoriteTime: '2024-01-15 16:20:00'
      },
      {
        id: 4,
        title: 'N5核心词汇1000',
        thumb: 'https://via.placeholder.com/300x200/FFD93D/FFFFFF?text=N5词汇',
        price: 159,
        favoriteTime: '2024-01-12 09:45:00'
      }
    ];

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: {
        list: myFavorites,
        total: myFavorites.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取我的收藏失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取我的收藏失败',
      error: error.message
    });
  }
});

// 注册index路由
indexRouter.use('/courses', indexCoursesRouter);
indexRouter.use('/user', indexUserRouter);
router.use('/index', indexRouter);

// 会员信息API
router.get('/member', async (req, res) => {
  try {
    // 模拟会员信息数据
    const memberInfo = {
      id: 576,
      username: 'user576',
      nickname: '日语学习者',
      avatar: '/static/imgs/placeholder.svg',
      level: 'VIP',
      expireTime: '2024-12-31',
      coursesCount: 3,
      studyDays: 45,
      totalStudyTime: 2800, // 分钟
      points: 1250,
      badges: [
        { id: 1, name: '新手上路', icon: '/static/imgs/badge1.svg' },
        { id: 2, name: '坚持学习', icon: '/static/imgs/badge2.svg' }
      ]
    };

    res.json({
      code: 0,
      status: true,
      message: '获取成功',
      data: memberInfo
    });

  } catch (error) {
    console.error('获取会员信息失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取会员信息失败',
      error: error.message
    });
  }
});

module.exports = router;
