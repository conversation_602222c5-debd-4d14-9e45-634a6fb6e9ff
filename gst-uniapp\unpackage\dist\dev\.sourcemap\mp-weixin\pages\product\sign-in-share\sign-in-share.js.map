{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/product/sign-in-share/sign-in-share.vue?30b0", "webpack:///D:/gst/gst-uniapp/pages/product/sign-in-share/sign-in-share.vue?28a0", "webpack:///D:/gst/gst-uniapp/pages/product/sign-in-share/sign-in-share.vue?7a85", "webpack:///D:/gst/gst-uniapp/pages/product/sign-in-share/sign-in-share.vue?170d", "uni-app:///pages/product/sign-in-share/sign-in-share.vue", "webpack:///D:/gst/gst-uniapp/pages/product/sign-in-share/sign-in-share.vue?362e", "webpack:///D:/gst/gst-uniapp/pages/product/sign-in-share/sign-in-share.vue?e021"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "isReady", "project_id", "poster", "posterImage", "canvasId", "course", "project", "code", "onLoad", "created", "methods", "ready", "uni", "title", "shareFc", "_app", "_this", "type", "formData", "posterCanvasId", "delayTimeScale", "backgroundImage", "drawArray", "bgObj", "bgScale", "rs", "url", "alpha", "dx", "dy", "infoCallBack", "circleSet", "dWidth", "dHeight", "text", "fontWeight", "size", "color", "textAlign", "textBaseline", "serialNum", "setCanvasWH", "d", "console", "saveImage", "filePath", "success", "share", "hideQr", "onShareAppMessage", "path", "imageUrl", "apiGetShareDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAynB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsD7oB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IAEA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACAC;QACAC;MACA;MACA;QACAD;QACA;QACA;QACA;QAEA;MACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;kBACAC;kBAAA;kBACAC;kBACAC;oBACA;kBAAA,CAEA;kBACAC;kBAAA;kBACAC;kBAAA;kBACA;AACA;AACA;AACA;AACA;kBACAC;kBACAC,oCAIA;oBAAA,IAHAC;sBACAN;sBACAO;oBAEA;oBACA;oBACA;;oBAEA;oBACA;sBACAC;sBACA;sBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;;sBAEA;sBACA;wBACAR;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;0BACA;0BACA;4BACAC;4BAAA;4BACAC;4BAAA;4BACAC;4BACA;AACA;AACA;0BACA;wBACA;sBACA,GACA;wBACAhB;wBACAiB;wBACAC;wBACAC;wBACAC;wBACAV;wBACAW;wBACAC;wBACAC;wBACAZ;wBACAC;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;wBACAZ;wBACAS;wBACAC;wBACAC;wBACAC;wBACAG;wBACAC;sBACA,EACA;oBACA;kBACA;kBACAQ,yCAIA;oBAAA,IAHAlB;sBACAN;sBACAO;oBACA;oBACA;kBACA;gBACA;cAAA;gBAzMAkB;gBA0MA3B;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;gBACA4B;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACAD;MACA/B;QACAiC;QACAC;UACA/B;QACA;MACA;IACA;IACAgC;;MAMA;IAAA,CAEA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;QACArC;QACAsC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAF;QACAjD;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7WA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/product/sign-in-share/sign-in-share.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/product/sign-in-share/sign-in-share.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./sign-in-share.vue?vue&type=template&id=3df538f6&scoped=true&\"\nvar renderjs\nimport script from \"./sign-in-share.vue?vue&type=script&lang=js&\"\nexport * from \"./sign-in-share.vue?vue&type=script&lang=js&\"\nimport style0 from \"./sign-in-share.vue?vue&type=style&index=0&id=3df538f6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3df538f6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/product/sign-in-share/sign-in-share.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=template&id=3df538f6&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    guiHeaderLeading: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-header-leading\" */ \"@/GraceUI5/components/gui-header-leading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" :customHeader=\"true\" class=\"root-box\">\r\n\t\t<!-- 自定义头部导航 -->\r\n\t\t<view slot=\"gHeader\">\r\n\t\t\t<view class=\"gui-flex gui-nowrap gui-rows gui-align-items-center gui-padding head-box\">\r\n\t\t\t\t<!-- 使用组件实现返回按钮及返回首页按钮 -->\r\n\t\t\t\t<gui-header-leading></gui-header-leading>\r\n\t\t\t\t<!-- 导航文本此处也可以是其他自定义内容 -->\r\n\t\t\t\t<view class=\"gui-header-content nav-box\">\r\n\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 如果右侧有其他内容可以利用条件编译和定位来实现-->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 页面主体 -->\r\n\t\t<view slot=\"gBody\" class=\"body-box lp-flex-column\">\r\n\t\t\t<view class=\"content-box lp-flex-column\">\r\n\t\t\t\t<!-- 说明 -->\r\n\t\t\t\t<!-- 生成海报 -->\r\n\t\t\t\t<view class=\"loading\" v-if=\"!isReady\">海报生成中...</view>\r\n\t\t\t\t<!-- 图片展示由自己实现 -->\r\n\t\t\t\t<view class=\"pop-box lp-flex-column lp-flex-center\" v-else>\r\n\t\t\t\t\t<view class=\"poster-box lp-flex-center\">\r\n\t\t\t\t\t\t<image :src=\"posterImage || ''\" mode=\"heightFix\" class=\"posterImage\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"lp-flex-center btn-box\">\r\n\t\t\t\t\t\t<view class=\"lp-flex-column lp-flex-center btn\">\r\n\t\t\t\t\t\t\t<button type=\"primary\" size=\"mini\" @tap.prevent.stop=\"saveImage()\">\r\n\t\t\t\t\t\t\t\t<view class=\"lp-flex-center icon-box\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"gui-icons\">&#xe63d;</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<text>保存图片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"lp-flex-column lp-flex-center btn\">\r\n\t\t\t\t\t\t\t<button type=\"primary\" open-type=\"share\" size=\"mini\">\r\n\t\t\t\t\t\t\t\t<view class=\"lp-flex-center icon-box\"> \r\n\t\t\t\t\t\t\t\t\t<text class=\"gui-icons\">&#xe63e;</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<text>微信分享</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 画布 -->\r\n\t\t\t\t<view class=\"hideCanvasView\">\r\n\t\t\t\t\t<canvas class=\"hideCanvas\" id=\"default_PosterCanvasId\" canvas-id=\"default_PosterCanvasId\" :style=\"{width: (poster.width||10) + 'px', height: (poster.height||10) + 'px'}\"></canvas>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\timport _app from '@/js_sdk/QuShe-SharerPoster/QS-SharePoster/app.js';\r\n\timport {\r\n\t\tgetSharePoster\r\n\t} from '@/js_sdk/QuShe-SharerPoster/QS-SharePoster/QS-SharePoster.js';\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisReady: false,\r\n\t\t\t\tproject_id: 4,\r\n\t\t\t\tposter: {},\r\n\t\t\t\tposterImage: '',\r\n\t\t\t\tcanvasId: 'default_PosterCanvasId',\r\n\t\t\t\tcourse: null,\r\n\t\t\t\tproject: null,\r\n\t\t\t\tcode: null\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.project_id = options.project_id;\r\n\t\t\t// 获取分享数据\r\n\t\t\tthis.ready();\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.userInfo = this.$store.state.user.userInfo;\r\n\r\n\t\t\tconst monthEnglish = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Spt\", \"Oct\", \"Nov\", \"Dec\"];\r\n\t\t\tthis.month = monthEnglish[new Date().getMonth()];\r\n\t\t\tthis.date = new Date().getDate();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// action\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tready: function() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle:'加载数据...'\r\n\t\t\t\t});\r\n\t\t\t\tthis.apiGetShareDate(this.project_id).then(data => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.project = data;\r\n\t\t\t\t\t// this.course = this.project.course;\r\n\t\t\t\t\tthis.code = this.project.code;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.shareFc();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync shareFc() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t_app.log('准备生成:' + new Date())\r\n\t\t\t\t\tconst d = await getSharePoster({\r\n\t\t\t\t\t\t_this: this, //若在组件中使用 必传\r\n\t\t\t\t\t\ttype: 'testShareType',\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\t//访问接口获取背景图携带自定义数据\r\n\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tposterCanvasId: this.canvasId, //canvasId\r\n\t\t\t\t\t\tdelayTimeScale: 20, //延时系数\r\n\t\t\t\t\t\t/* background: {\r\n\t\t\t\t\t\t\twidth: 1080,\r\n\t\t\t\t\t\t\theight: 1920,\r\n\t\t\t\t\t\t\tbackgroundColor: '#666'\r\n\t\t\t\t\t\t}, */\r\n\t\t\t\t\t\tbackgroundImage: '/static/imgs/sign_in_bg.png',\r\n\t\t\t\t\t\tdrawArray: ({\r\n\t\t\t\t\t\t\tbgObj,\r\n\t\t\t\t\t\t\ttype,\r\n\t\t\t\t\t\t\tbgScale\r\n\t\t\t\t\t\t}) => {\r\n\t\t\t\t\t\t\tconst dx = bgObj.width * 0.3;\r\n\t\t\t\t\t\t\tconst fontSize = bgObj.width * 0.040;\r\n\t\t\t\t\t\t\tconst lineHeight = bgObj.height * 0.04;\r\n\r\n\t\t\t\t\t\t\t//可直接return数组，也可以return一个promise对象, 但最终resolve一个数组, 这样就可以方便实现后台可控绘制海报\r\n\t\t\t\t\t\t\treturn new Promise((rs, rj) => {\r\n\t\t\t\t\t\t\t\trs([\r\n\t\t\t\t\t\t\t\t\t// 标题\r\n\t\t\t\t\t\t\t\t\t/* {\r\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t\ttext: '坚持打卡天数',\r\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\r\n\t\t\t\t\t\t\t\t\t\tsize: fontSize,\r\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t\tdx: 410,\r\n\t\t\t\t\t\t\t\t\t\tdy: 200\r\n\t\t\t\t\t\t\t\t\t}, */\r\n\t\t\t\t\t\t\t\t\t// 天数\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t// \ttext: 1,\r\n\t\t\t\t\t\t\t\t\t// \tfontWeight: 'blod',\r\n\t\t\t\t\t\t\t\t\t// \tsize: fontSize * 3,\r\n\t\t\t\t\t\t\t\t\t// \tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t// \ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t// \tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t// \tdy: 350,\r\n\t\t\t\t\t\t\t\t\t// \tinfoCallBack(textLength) {\r\n\t\t\t\t\t\t\t\t\t// \t\tconsole.log(textLength,bgObj.width);\r\n\t\t\t\t\t\t\t\t\t// \t\treturn {\r\n\t\t\t\t\t\t\t\t\t// \t\t\tdx: (bgObj.width - textLength) / 2,\r\n\t\t\t\t\t\t\t\t\t// \t\t}\r\n\t\t\t\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// 内容框\r\n\r\n\t\t\t\t\t\t\t\t\t// 用户信息\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\t\t\turl: this.userInfo.avatar,\r\n\t\t\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\t\t\tdx: 80,\r\n\t\t\t\t\t\t\t\t\t\tdy: 90,\r\n\t\t\t\t\t\t\t\t\t\tinfoCallBack(imageInfo) {\r\n\t\t\t\t\t\t\t\t\t\t\tlet scale = bgObj.width * 0.2 / imageInfo.height;\r\n\t\t\t\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\t\t\t\tcircleSet: {}, // 圆形图片 , 若circleSet与roundRectSet一同设置 优先circleSet设置\r\n\t\t\t\t\t\t\t\t\t\t\t\tdWidth: 100, // 因为设置了圆形图片 所以要乘以2\r\n\t\t\t\t\t\t\t\t\t\t\t\tdHeight: 100,\r\n\t\t\t\t\t\t\t\t\t\t\t\t/* roundRectSet: { // 圆角矩形\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tr: imageInfo.width * .1\r\n\t\t\t\t\t\t\t\t\t\t\t\t} */\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t\ttext: this.userInfo.name,\r\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\r\n\t\t\t\t\t\t\t\t\t\tsize: fontSize*1.2,\r\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t\tdx: 340,\r\n\t\t\t\t\t\t\t\t\t\tdy: 105\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t// \ttext: this.project.name,\r\n\t\t\t\t\t\t\t\t\t// \tfontWeight: 'normal',\r\n\t\t\t\t\t\t\t\t\t// \tsize: fontSize * 0.75,\r\n\t\t\t\t\t\t\t\t\t// \tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t// \ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t// \tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t// \tdx: 240,\r\n\t\t\t\t\t\t\t\t\t// \tdy: 640\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// 打卡日期\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t// \ttext: this.month,\r\n\t\t\t\t\t\t\t\t\t// \tfontWeight: 'normal',\r\n\t\t\t\t\t\t\t\t\t// \tsize: fontSize * 0.6,\r\n\t\t\t\t\t\t\t\t\t// \tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t// \ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t// \tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t// \tdx: 830,\r\n\t\t\t\t\t\t\t\t\t// \tdy: 555\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t// \ttext: this.date,\r\n\t\t\t\t\t\t\t\t\t// \tfontWeight: 'normal',\r\n\t\t\t\t\t\t\t\t\t// \tsize: fontSize * 0.6,\r\n\t\t\t\t\t\t\t\t\t// \tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t// \ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t// \tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t// \tdx: this.date > 9 ? 837 : 845,\r\n\t\t\t\t\t\t\t\t\t// \tdy: 610\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// 课程封面\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'image',\r\n\t\t\t\t\t\t\t\t\t// \turl: this.project.picture,\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \tdx: 70,\r\n\t\t\t\t\t\t\t\t\t// \tdy: 720,\r\n\t\t\t\t\t\t\t\t\t// \tdWidth: bgObj.width - 140,\r\n\t\t\t\t\t\t\t\t\t// \tdHeight: 1000,\r\n\t\t\t\t\t\t\t\t\t// \tmode: 'aspectFill'\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// 课程信息\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t// \ttext: this.project.created_time,\r\n\t\t\t\t\t\t\t\t\t// \tfontWeight: 'blod',\r\n\t\t\t\t\t\t\t\t\t// \tsize: fontSize * 1.2,\r\n\t\t\t\t\t\t\t\t\t// \tcolor: '#333',\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \ttextAlign: 'center',\r\n\t\t\t\t\t\t\t\t\t// \ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t// \tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t// \tinfoCallBack(textLength) {\r\n\t\t\t\t\t\t\t\t\t// \t\treturn {\r\n\t\t\t\t\t\t\t\t\t// \t\t\tdx: bgObj.width / 2,\r\n\t\t\t\t\t\t\t\t\t// \t\t\tdy: 1780\r\n\t\t\t\t\t\t\t\t\t// \t\t}\r\n\t\t\t\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// {\r\n\t\t\t\t\t\t\t\t\t// \ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t// \ttext: this.project.name,\r\n\t\t\t\t\t\t\t\t\t// \tfontWeight: 'normal',\r\n\t\t\t\t\t\t\t\t\t// \tsize: fontSize,\r\n\t\t\t\t\t\t\t\t\t// \tcolor: '#666',\r\n\t\t\t\t\t\t\t\t\t// \talpha: 1,\r\n\t\t\t\t\t\t\t\t\t// \ttextAlign: 'center',\r\n\t\t\t\t\t\t\t\t\t// \ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t// \tserialNum: 1,\r\n\t\t\t\t\t\t\t\t\t// \tinfoCallBack(textLength) {\r\n\t\t\t\t\t\t\t\t\t// \t\treturn {\r\n\t\t\t\t\t\t\t\t\t// \t\t\tdx: bgObj.width / 2,\r\n\t\t\t\t\t\t\t\t\t// \t\t\tdy: 1840\r\n\t\t\t\t\t\t\t\t\t// \t\t}\r\n\t\t\t\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\t// 海报出处\r\n\t\t\t\t\t\t\t\t\t// 二维码\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\t\t\turl: this.code.url,\r\n\t\t\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\t\t\tdx: 100,\r\n\t\t\t\t\t\t\t\t\t\tdy: bgObj.height - 320,\r\n\t\t\t\t\t\t\t\t\t\tdWidth: 220,\r\n\t\t\t\t\t\t\t\t\t\tdHeight: 220,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t]);\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsetCanvasWH: ({\r\n\t\t\t\t\t\t\tbgObj,\r\n\t\t\t\t\t\t\ttype,\r\n\t\t\t\t\t\t\tbgScale\r\n\t\t\t\t\t\t}) => { // 为动态设置画布宽高的方法，\r\n\t\t\t\t\t\t\tthis.poster = bgObj;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t_app.log('海报生成成功, 时间:' + new Date() + '， 临时路径: ' + d.poster.tempFilePath)\r\n\t\t\t\t\tthis.posterImage = d.poster.tempFilePath;\r\n\t\t\t\t\tthis.isReady = true;\r\n\t\t\t\t\t//this.$refs.popup.show()\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t_app.hideLoading();\r\n\t\t\t\t\t_app.showToast(JSON.stringify(e));\r\n\t\t\t\t\tconsole.log(JSON.stringify(e));\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsaveImage() {\r\n\t\t\t\tconsole.log('saveImage', this.posterImage);\r\n\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\tfilePath: this.posterImage,\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t_app.showToast('保存成功');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshare() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t_app.getShare(false, false, 2, '', '', '', this.poster.finalPath, false, false);\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t//_app.showToast('分享了');\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\thideQr() {\r\n\t\t\t\tthis.$refs.popup.hide()\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// handler\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tonShareAppMessage(res) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tpath: \"/pages/product/product?id=\" + this.project_id,\r\n\t\t\t\t\ttitle: '我正在参加人民中国“' + this.project.name + '\"你也一起来学习吧！',\r\n\t\t\t\t\timageUrl: this.posterImage\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tapiGetShareDate: function(project_id) {\r\n\t\t\t\treturn this.$http.post('/v1/share/code', {\r\n\t\t\t\t\tpath: '/pages/product/product?id=' + project_id,\r\n\t\t\t\t\tproject_id\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.body-box {\r\n\t\tflex: 1;\r\n\r\n\t\t.content-box {\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.pop-box {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t.poster-box {\r\n\t\t\t\t\tmargin: 30rpx;\r\n\r\n\t\t\t\t\t.posterImage {\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\theight: 70vh;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn-box {\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t\t\t.btn {\r\n\t\t\t\t\t\tmargin: 20rpx 40rpx;\r\n\r\n\t\t\t\t\t\t.gui-icons {\r\n\t\t\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbutton {\r\n\t\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.loading {\r\n\t\tpadding: 50rpx;\r\n\t\tcolor: $uni-text-color-grey;\r\n\t}\r\n\r\n\t.hideCanvasView {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.hideCanvas {\r\n\t\tposition: fixed;\r\n\t\ttop: -99999rpx;\r\n\t\tleft: -99999rpx;\r\n\t\tz-index: -99999;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=style&index=0&id=3df538f6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=style&index=0&id=3df538f6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040521801\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}