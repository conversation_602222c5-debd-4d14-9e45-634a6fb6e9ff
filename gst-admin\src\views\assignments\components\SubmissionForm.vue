<template>
  <el-dialog
    v-model="dialogVisible"
    title="提交作业"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="submission-form" v-if="assignment">
      <div class="detail-placeholder">
        <el-empty description="作业提交组件开发中..." :image-size="80" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          提交作业
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  assignment: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSubmit = () => {
  ElMessage.success('作业提交成功')
  emit('success')
}
</script>

<style lang="scss" scoped>
.detail-placeholder {
  padding: var(--spacing-xl);
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}
</style>
