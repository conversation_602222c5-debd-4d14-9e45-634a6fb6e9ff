<gui-page vue-id="3fb520a8-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" class="data-v-59cfa49a vue-ref" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="groups-page data-v-59cfa49a" slot="gBody"><block wx:if="{{hasGroupPermission}}"><view class="page-content data-v-59cfa49a"><view class="page-header data-v-59cfa49a"><view class="header-content data-v-59cfa49a"><view class="title-section data-v-59cfa49a"><text class="page-title data-v-59cfa49a">🎓 GST派遣日语培训班</text><text class="page-subtitle data-v-59cfa49a">与同伴一起进步，共同成长</text></view><view class="stats-section data-v-59cfa49a"><view class="stat-item data-v-59cfa49a"><text class="stat-number data-v-59cfa49a">{{$root.g0}}</text><text class="stat-label data-v-59cfa49a">个小组</text></view><view class="stat-item data-v-59cfa49a"><text class="stat-number data-v-59cfa49a">{{totalMembers}}</text><text class="stat-label data-v-59cfa49a">名成员</text></view><view class="stat-item data-v-59cfa49a"><text class="stat-number data-v-59cfa49a">48</text><text class="stat-label data-v-59cfa49a">门课程</text></view></view></view></view><view class="split-layout data-v-59cfa49a"><view class="left-panel data-v-59cfa49a"><view class="panel-header data-v-59cfa49a"><text class="panel-title data-v-59cfa49a">学习内容</text><text class="panel-subtitle data-v-59cfa49a">{{$root.g1+1+"项"}}</text></view><scroll-view class="group-list data-v-59cfa49a" scroll-y="true"><view data-event-opts="{{[['tap',[['selectConceptTutorial']]]]}}" class="{{['concept-tutorial-item','data-v-59cfa49a',(selectedGroupId==='concept')?'active':'']}}" bindtap="__e"><view class="concept-content data-v-59cfa49a"><view class="concept-icon data-v-59cfa49a">📖</view><text class="concept-title data-v-59cfa49a">新概念教程</text><view class="concept-badge data-v-59cfa49a">公共</view></view></view><block wx:for="{{$root.l0}}" wx:for-item="group" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectGroupForDetail',['$0',index],[[['groupList','',index]]]]]]]}}" class="{{['group-item','data-v-59cfa49a',(selectedGroupId===group.$orig.id)?'active':'']}}" bindtap="__e"><view class="simple-group-content data-v-59cfa49a"><view class="group-level-badge data-v-59cfa49a" style="{{'background:'+(group.m0)+';'}}">{{''+group.$orig.level+''}}</view><text class="simple-group-name data-v-59cfa49a">{{group.$orig.name}}</text><view class="{{['simple-status-dot','data-v-59cfa49a',group.$orig.status]}}"></view></view></view></block></scroll-view></view><view class="right-panel data-v-59cfa49a"><block wx:if="{{selectedGroupId==='concept'}}"><view class="concept-detail-content data-v-59cfa49a"><view class="concept-detail-header data-v-59cfa49a"><view class="concept-bg data-v-59cfa49a"><view class="concept-overlay data-v-59cfa49a"><view class="concept-detail-icon data-v-59cfa49a">📖</view><view class="concept-detail-info data-v-59cfa49a"><text class="concept-detail-title data-v-59cfa49a">{{conceptTutorial.title}}</text><text class="concept-detail-subtitle data-v-59cfa49a">{{conceptTutorial.description}}</text><view class="concept-progress-info data-v-59cfa49a"><text class="progress-info-text data-v-59cfa49a">{{''+conceptTutorial.completedLessons+" / "+conceptTutorial.totalLessons+' 课时'}}</text></view></view></view></view></view><view class="concept-categories data-v-59cfa49a"><view class="categories-title data-v-59cfa49a">学习分类</view><view class="categories-grid data-v-59cfa49a"><block wx:for="{{conceptTutorial.categories}}" wx:for-item="category" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['enterConceptCategory',['$0'],[[['conceptTutorial.categories','',index]]]]]]]}}" class="category-card data-v-59cfa49a" bindtap="__e"><view class="category-icon data-v-59cfa49a">{{category.icon}}</view><view class="category-info data-v-59cfa49a"><text class="category-name data-v-59cfa49a">{{category.name}}</text><text class="category-desc data-v-59cfa49a">{{category.description}}</text><text class="category-lessons data-v-59cfa49a">{{category.lessons+"课时"}}</text></view><view class="category-arrow data-v-59cfa49a">→</view></view></block></view></view><view class="concept-actions data-v-59cfa49a"><view class="concept-action-title data-v-59cfa49a">快速开始</view><view class="concept-action-buttons data-v-59cfa49a"><view data-event-opts="{{[['tap',[['startConceptLearning']]]]}}" class="concept-btn primary data-v-59cfa49a" bindtap="__e"><view class="concept-btn-icon data-v-59cfa49a">🚀</view><view class="concept-btn-content data-v-59cfa49a"><text class="concept-btn-title data-v-59cfa49a">开始学习</text><text class="concept-btn-desc data-v-59cfa49a">从第一课开始</text></view></view><view data-event-opts="{{[['tap',[['continueConceptLearning']]]]}}" class="concept-btn secondary data-v-59cfa49a" bindtap="__e"><view class="concept-btn-icon data-v-59cfa49a">📖</view><view class="concept-btn-content data-v-59cfa49a"><text class="concept-btn-title data-v-59cfa49a">继续学习</text><text class="concept-btn-desc data-v-59cfa49a">从上次位置继续</text></view></view><view data-event-opts="{{[['tap',[['viewConceptProgress']]]]}}" class="concept-btn tertiary data-v-59cfa49a" bindtap="__e"><view class="concept-btn-icon data-v-59cfa49a">📊</view><view class="concept-btn-content data-v-59cfa49a"><text class="concept-btn-title data-v-59cfa49a">学习进度</text><text class="concept-btn-desc data-v-59cfa49a">查看详细进度</text></view></view></view></view></view></block><block wx:else><block wx:if="{{selectedGroup}}"><view class="detail-content data-v-59cfa49a"><view class="detail-header data-v-59cfa49a"><view class="detail-bg data-v-59cfa49a" style="{{'background:'+($root.m1)+';'}}"><view class="detail-overlay data-v-59cfa49a"><view class="detail-avatar data-v-59cfa49a"><image src="{{selectedGroup.icon}}" mode="aspectFit" class="data-v-59cfa49a"></image></view><view class="detail-info data-v-59cfa49a"><text class="detail-title data-v-59cfa49a">{{selectedGroup.name}}</text><text class="detail-subtitle data-v-59cfa49a">{{selectedGroup.description}}</text><view class="detail-level data-v-59cfa49a">{{selectedGroup.level+"等级"}}</view></view></view></view></view><view class="detail-stats data-v-59cfa49a"><view class="stat-card-detail data-v-59cfa49a"><view class="stat-icon-detail data-v-59cfa49a">👥</view><text class="stat-number-detail data-v-59cfa49a">{{selectedGroup.members}}</text><text class="stat-label-detail data-v-59cfa49a">成员</text></view><view class="stat-card-detail data-v-59cfa49a"><view class="stat-icon-detail data-v-59cfa49a">📚</view><text class="stat-number-detail data-v-59cfa49a">{{selectedGroup.courses}}</text><text class="stat-label-detail data-v-59cfa49a">课程</text></view><view class="stat-card-detail data-v-59cfa49a"><view class="stat-icon-detail data-v-59cfa49a">⭐</view><text class="stat-number-detail data-v-59cfa49a">{{selectedGroup.progress+"%"}}</text><text class="stat-label-detail data-v-59cfa49a">进度</text></view></view><view class="progress-detail data-v-59cfa49a"><view class="progress-header data-v-59cfa49a"><text class="progress-title data-v-59cfa49a">学习进度</text><text class="progress-value data-v-59cfa49a">{{selectedGroup.progress+"%"}}</text></view><view class="progress-bar-detail data-v-59cfa49a"><view class="progress-fill-detail data-v-59cfa49a" style="{{'width:'+(selectedGroup.progress+'%')+';'+('background:'+($root.m2)+';')}}"></view></view><text class="progress-desc data-v-59cfa49a">{{"已完成 "+selectedGroup.completedCourses+" / "+selectedGroup.totalCourses+" 门课程"}}</text></view><view class="detail-actions data-v-59cfa49a"><view class="action-row data-v-59cfa49a"><view data-event-opts="{{[['tap',[['enterGroup',['$0'],['selectedGroup']]]]]}}" class="action-btn-large primary data-v-59cfa49a" bindtap="__e"><view class="btn-icon-large data-v-59cfa49a">🚀</view><view class="btn-content data-v-59cfa49a"><text class="btn-title data-v-59cfa49a">进入小组</text><text class="btn-desc data-v-59cfa49a">开始学习</text></view><view class="btn-arrow-large data-v-59cfa49a">→</view></view></view><view class="action-row data-v-59cfa49a"><view data-event-opts="{{[['tap',[['viewGroupProgress',['$0'],['selectedGroup']]]]]}}" class="action-btn-large secondary data-v-59cfa49a" bindtap="__e"><view class="btn-icon-large data-v-59cfa49a">📊</view><view class="btn-content data-v-59cfa49a"><text class="btn-title data-v-59cfa49a">学习进度</text><text class="btn-desc data-v-59cfa49a">查看详细进度</text></view><view class="btn-arrow-large data-v-59cfa49a">→</view></view></view></view></view></block><block wx:else><view class="empty-detail data-v-59cfa49a"><view class="empty-icon data-v-59cfa49a">👈</view><text class="empty-title data-v-59cfa49a">选择一个小组</text><text class="empty-desc data-v-59cfa49a">点击左侧小组查看详细信息</text></view></block></block></view></view></view></block><block wx:else><view class="no-permission-page data-v-59cfa49a"><view class="permission-container data-v-59cfa49a"><view class="permission-icon data-v-59cfa49a">🔒</view><text class="permission-title data-v-59cfa49a">访问受限</text><text class="permission-desc data-v-59cfa49a">您暂时没有访问学习小组的权限</text><text class="permission-hint data-v-59cfa49a">请联系管理员开通权限或使用授权账号登录</text><view class="permission-actions data-v-59cfa49a"><button data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="btn-login data-v-59cfa49a" bindtap="__e">重新登录</button><button data-event-opts="{{[['tap',[['contactAdmin',['$event']]]]]}}" class="btn-contact data-v-59cfa49a" bindtap="__e">联系管理员</button></view></view></view></block></view></gui-page>