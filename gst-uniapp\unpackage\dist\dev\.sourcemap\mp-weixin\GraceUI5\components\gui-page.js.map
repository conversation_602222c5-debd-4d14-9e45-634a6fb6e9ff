{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page.vue?a22e", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page.vue?aa99", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page.vue?e304", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page.vue?372c", "uni-app:///GraceUI5/components/gui-page.vue", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page.vue?df26", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page.vue?222c"], "names": ["name", "props", "fullPage", "type", "default", "customHeader", "headerSets", "height", "zIndex", "headerStyle", "isHeaderSized", "statusBarStyle", "customFooter", "footerSets", "bg", "pendantSets", "width", "right", "bottom", "isLoading", "isSwitchPage", "iphoneXButtomStyle", "headerSizedStyle", "fixedTopZIndex", "refresh", "refreshText", "refreshBgColor", "refreshColor", "refreshFontSize", "loadmore", "loadMoreText", "loadMoreColor", "loadMoreFontSize", "apiLoading<PERSON><PERSON><PERSON>", "data", "footerHeight", "iphoneXButtomHeight", "statusBarHeight", "headerTapNumber", "fixedTop", "refreshBodyHeight", "loadMoreTimer", "fixedTopMargin", "scrollTop", "srcollTimer", "mounted", "watch", "created", "system", "res1", "res2", "methods", "touchstart", "touchmove", "touchend", "scroll", "clearTimeout", "setScrollTop", "endReload", "reload", "getDomSize", "setTimeout", "uni", "fun", "stopfun", "e", "headerTap", "loadmorefun", "stoploadmore", "nomore"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,aAAa,oMAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC4HznB;EACAA;EACAC;IACAC;MAAAC;MAAAC;IAAA;IACAC;MAAAF;MAAAC;IAAA;IACAE;MAAAH;MAAAC;QAAA;UAAAG;UAAAC;QAAA;MAAA;IAAA;IACAC;MAAAN;MAAAC;IAAA;IACAM;MAAAP;MAAAC;IAAA;IACAO;MAAAR;MAAAC;IAAA;IACAQ;MAAAT;MAAAC;IAAA;IACAS;MAAAV;MAAAC;QAAA;UAAAG;UAAAC;UAAAM;QAAA;MAAA;IAAA;IACAC;MAAAZ;MAAAC;QAAA;UAAAY;UAAAC;UAAAC;UAAAV;QAAA;MAAA;IAAA;IACAW;MAAAhB;MAAAC;IAAA;IACAgB;MAAAjB;MAAAC;IAAA;IACAiB;MAAAlB;MAAAC;IAAA;IACAkB;MAAAnB;MAAAC;IAAA;IACAmB;MAAApB;MAAAC;IAAA;IAEA;IACAoB;MAAArB;MAAAC;IAAA;IACAqB;MAAAtB;MAAAC;QACA;MACA;IAAA;IACAsB;MAAAvB;MAAAC;QACA;MACA;IAAA;IACAuB;MAAAxB;MAAAC;QACA;MACA;IAAA;IACAwB;MAAAzB;MAAAC;IAAA;IAEA;IACAyB;MAAA1B;MAAAC;IAAA;IACA0B;MAAA3B;MAAAC;QACA;MACA;IAAA;IACA2B;MAAA5B;MAAAC;QACA;MACA;IAAA;IACA4B;MAAA7B;MAAAC;IAAA;IACA6B;MAAA9B;MAAAC;IAAA;EACA;EACA8B;IACA;MACAC;MACAC;MACAC;MAIAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EAEAC;IAAA;IAEA;MACA;IACA;;IAEA;IACA;MAiBA;QACA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;IAEA;EACA;EAEAC;IACA3B;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EAEA4B;IAEA;IAUA;MACA;MACA;QACAC;QACAA;QACA;QACA;QACA;UAAAC;QAAA;QACA;QACA;UAAAC;QAAA;QACA;UACA;UACA;QACA;MACA;MAcA;QACA;QACA;MACA;MAGA;QACA;MACA;QACA;MACA;IAEA;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACAC;QAEAC;UACAC;QACA;MAQA;IAEA;IACAC;MAAAC;MAAA;IAAA;IACAC;MAAA;MACA;MACA;QACA;QACA;MACA;QACAL;UAAA;QAAA;MACA;IACA;IAiBAM;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACA;MACA;QAAAX;MAAA;MACA;QACA;QACA;UAAA;QAAA;QACA;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpYA;AAAA;AAAA;AAAA;AAAg5B,CAAgB,+3BAAG,EAAC,C;;;;;;;;;;;ACAp6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "GraceUI5/components/gui-page.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./gui-page.vue?vue&type=template&id=325fdc7c&scoped=true&\"\nvar renderjs\nimport script from \"./gui-page.vue?vue&type=script&lang=js&\"\nexport * from \"./gui-page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gui-page.vue?vue&type=style&index=0&id=325fdc7c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"325fdc7c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"GraceUI5/components/gui-page.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page.vue?vue&type=template&id=325fdc7c&scoped=true&\"", "var components\ntry {\n  components = {\n    guiRefresh: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-refresh\" */ \"@/GraceUI5/components/gui-refresh.vue\"\n      )\n    },\n    guiLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-loadmore\" */ \"@/GraceUI5/components/gui-loadmore.vue\"\n      )\n    },\n    guiPageLoading: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page-loading\" */ \"@/GraceUI5/components/gui-page-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"[\r\n\t\t'gui-flex', 'gui-columns', 'gui-sbody', \r\n\t\tfullPage ? 'gui-flex1':'' , \r\n\t\trefresh || loadmore ? 'gui-flex1' : ''\r\n\t]\">\r\n\t\t<!-- 自定义头部 -->\r\n\t\t<view class=\"gui-header gui-transition-all\" \r\n\t\tv-if=\"customHeader\" \r\n\t\tid=\"guiPageHeader\" ref=\"guiPageHeader\" \r\n\t\t:style=\"'height:'\r\n\t\t+(headerSets.height+statusBarHeight)+'px; z-index:'\r\n\t\t+headerSets.zIndex+';'+headerStyle\">\r\n\t\t\t<!-- 状态栏 -->\r\n\t\t\t<view class=\"gui-page-status-bar\" \r\n\t\t\t:style=\"'height:'+statusBarHeight+'px;'+statusBarStyle\"></view>\r\n\t\t\t<!-- 头部插槽 -->\r\n\t\t\t<view class=\"gui-flex gui-columns gui-justify-content-center\" \r\n\t\t\******************=\"headerTap\"\r\n\t\t\t:style=\"{height:headerSets.height+'px'}\">\r\n\t\t\t\t<slot name=\"gHeader\"></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 自定义头部占位 -->\r\n\t\t<view v-if=\"customHeader && isHeaderSized\"\r\n\t\t:style=\"'height:'+(headerSets.height+statusBarHeight)+'px; '+ headerSizedStyle + ';'\"></view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- 页面主体 -->\r\n\t\t<view class=\"gui-flex gui-columns\" v-if=\"!refresh && !loadmore\" \r\n\t\tid=\"guiPageBody\" ref=\"guiPageBody\" \r\n\t\t:class=\"[fullPage?'gui-flex1':'']\">\r\n\t\t\t<slot name=\"gBody\"></slot>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- 刷新加载主体 -->\r\n\t\t<view class=\"gui-flex gui-columns gui-flex1\" \r\n\t\tv-if=\"refresh || loadmore\" \r\n\t\tid=\"guiPageBody\" \r\n\t\tref=\"guiPageBody\" \r\n\t\t:style=\"{\r\n\t\t\tmarginTop:fixedTopMargin+'px', \r\n\t\t\theight:refreshBodyHeight+'px'\r\n\t\t}\">\r\n\t\t\t<scroll-view class=\"gui-relative\" \r\n\t\t\t:scroll-y=\"true\" \r\n\t\t\t:show-scrollbar=\"false\" \r\n\t\t\t:style=\"{\r\n\t\t\t\theight:refreshBodyHeight+'px',\r\n\t\t\t\topacity:refreshBodyHeight < 1 ? 0 : 1\r\n\t\t\t}\" \r\n\t\t\t@touchstart=\"touchstart\" \r\n\t\t\t@touchmove=\"touchmove\" \r\n\t\t\t@touchend=\"touchend\" \r\n\t\t\t@scroll=\"scroll\" \r\n\t\t\t:scroll-top=\"scrollTop\" \r\n\t\t\t@scrolltolower=\"loadmorefun\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<gui-refresh \r\n\t\t\t\t\tref=\"guiPageRefresh\" \r\n\t\t\t\t\t@reload=\"reload\"\r\n\t\t\t\t\t:refreshText=\"refreshText\" \r\n\t\t\t\t\t:refreshBgColor=\"refreshBgColor\" \r\n\t\t\t\t\t:refreshColor=\"refreshColor\" \r\n\t\t\t\t\t:refreshFontSize=\"refreshFontSize\"></gui-refresh>\r\n\t\t\t\t</view>\r\n\t\t\t\t<slot name=\"gBody\"></slot>\r\n\t\t\t\t<view \r\n\t\t\t\tv-if=\"loadmore\" \r\n\t\t\t\tclass=\"gui-page-loadmore\">\r\n\t\t\t\t\t<gui-loadmore \r\n\t\t\t\t\tref=\"guipageloadmore\" \r\n\t\t\t\t\t:loadMoreText=\"loadMoreText\" \r\n\t\t\t\t\t:loadMoreColor=\"loadMoreColor\" \r\n\t\t\t\t\t:loadMoreFontSize=\"loadMoreFontSize\"></gui-loadmore>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<!-- 页面底部 -->\r\n\t\t<!-- 底部占位 -->\r\n\t\t<view v-if=\"customFooter\" \r\n\t\t:style=\"{height:footerHeight}\"></view>\r\n\t\t<view class=\"gui-page-footer gui-border-box\" \r\n\t\t:class=\"[isSwitchPage?'gui-switch-page-footer':'']\" \r\n\t\tv-if=\"customFooter\" \r\n\t\tid=\"guiPageFooter\" \r\n\t\tref=\"guiPageFooter\" \r\n\t\t:style=\"{\r\n\t\t\theight:footerHeight, \r\n\t\t\t'background-image':footerSets.bg, \r\n\t\t\t'z-index':footerSets.zIndex\r\n\t\t}\">\r\n\t\t\t<view><slot name=\"gFooter\"></slot></view>\r\n\t\t\t<view \r\n\t\t\t:style=\"'height:'+iphoneXButtomHeight+'; '+ iphoneXButtomStyle\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 右下角悬浮挂件 -->\r\n\t\t<view class=\"gui-page-pendant\" \r\n\t\t:style=\"{\r\n\t\t\tright:pendantSets.right, bottom:pendantSets.bottom, \r\n\t\t\twidth:pendantSets.width, zIndex:pendantSets.zIndex}\">\r\n\t\t\t<slot name=\"gPendant\"></slot>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 吸顶元素 -->\r\n\t\t<view class=\"gui-page-fixed-top\" \r\n\t\tref=\"guiPageFixedTop\" \r\n\t\tid=\"guiPageFixedTop\" \r\n\t\t:style=\"{top:fixedTop+'px', zIndex:fixedTopZIndex}\">\r\n\t\t\t<slot name=\"gFixedTop\"></slot>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 全屏 loading -->\r\n\t\t<gui-page-loading ref=\"guipageloading\"></gui-page-loading>\r\n\t</view>\r\n</template>\r\n<script>  \r\n// #ifdef APP-NVUE\r\nconst dom     = weex.requireModule('dom');\r\n// #endif\r\nexport default{\r\n\tname  : 'gui-page',\r\n\tprops : {\r\n\t\tfullPage           : {type:Boolean, default:false},\r\n\t\tcustomHeader       : {type:Boolean, default:false},\r\n\t\theaderSets         : {type:Object , default:function(){return {height:44, zIndex:100}}},\r\n\t\theaderStyle        : {type:String , default:'background-color:#FFFFFF;'},\r\n\t\tisHeaderSized      : {type:Boolean, default:true},\r\n\t\tstatusBarStyle     : {type:String , default:'background-color:#FFFFFF;'},\r\n\t\tcustomFooter       : {type:Boolean, default:false},\r\n\t\tfooterSets         : {type:Object , default:function(){return {height:100, zIndex:100, bg:'linear-gradient(to bottom, #FFFFFF,#FFFFFF)'}}},\r\n\t\tpendantSets        : {type:Object , default:function(){return {width:'100rpx', right:'25rpx', bottom:'100rpx', zIndex:100};}},\r\n\t\tisLoading          : {type:Boolean, default:false},\r\n\t\tisSwitchPage       : {type:Boolean, default:false},\r\n\t\tiphoneXButtomStyle : {type:String,  default:''},\r\n\t\theaderSizedStyle   : {type:String,  default:''},\r\n\t\tfixedTopZIndex     : {type:Number,  default:2},\r\n\t\t\r\n\t\t/* 刷新 */\r\n\t\trefresh            : {type:Boolean, default:false},\r\n\t\trefreshText        : {type:Array,   default:function () {\r\n\t\t\treturn ['继续下拉刷新','松开手指开始刷新','数据刷新中','数据已刷新'];\r\n\t\t}},\r\n\t\trefreshBgColor     : {type:Array,   default:function () {\r\n\t\t\treturn ['#FFFFFF','#FFFFFF','#FFFFFF','#63D2BC'];\r\n\t\t}},\r\n\t\trefreshColor       : {type:Array,   default:function () {\r\n\t\t\treturn ['rgba(69, 90, 100, 0.6)','rgba(69, 90, 100, 0.6)','#63D2BC','#FFFFFF'];\r\n\t\t}},\r\n\t\trefreshFontSize    : {type:String, default:'26rpx'},\r\n\t\t\r\n\t\t/* 加载更多 */\r\n\t\tloadmore           : {type:Boolean, default:false},\r\n\t\tloadMoreText       : {type:Array, default:function () {\r\n\t\t\treturn ['','更多数据加载中', '已加载全部数据'];\r\n\t\t}},\r\n\t\tloadMoreColor      : {type:Array, default:function () {\r\n\t\t\treturn ['rgba(69, 90, 100, 0.6)', 'rgba(69, 90, 100, 0.6)', 'rgba(69, 90, 100, 0.8)'];\r\n\t\t}},\r\n\t\tloadMoreFontSize   : {type:String, default:'26rpx'},\r\n\t\tapiLoadingStatus   : {type:Boolean, default:false}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tfooterHeight        : '100rpx',\r\n\t\t\tiphoneXButtomHeight : '0rpx',\r\n\t\t\tstatusBarHeight     : 0,\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tanimateCount        : 0,\r\n\t\t\t// #endif\r\n\t\t\theaderTapNumber     : 0,\r\n\t\t\tfixedTop            : 0,\r\n\t\t\trefreshBodyHeight   : 0,\r\n\t\t\tloadMoreTimer       : null,\r\n\t\t\tfixedTopMargin      : 0,\r\n\t\t\tscrollTop           : 0,\r\n\t\t\tsrcollTimer         : null,\r\n\t\t\t\r\n\t\t}\r\n\t},\r\n\t\r\n\tmounted:function(){\r\n\t\t\r\n\t\tif(this.isLoading){\r\n\t\t\tthis.$refs.guipageloading.open();\r\n\t\t}\r\n\t\t\r\n\t\t// 刷新相关\r\n\t\tif(this.refresh || this.loadmore){\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.getRefs('guiPageBody', 0, (res)=>{\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.getDomSize('guiPageBody', (res2)=>{\r\n\t\t\t\t\t\tthis.refreshBodyHeight = res2.height;\r\n\t\t\t\t\t\tthis.getDomSize('guiPageFixedTop', (res)=>{\r\n\t\t\t\t\t\t\tif(res.height){\r\n\t\t\t\t\t\t\t\tthis.refreshBodyHeight -= res.height;\r\n\t\t\t\t\t\t\t\tthis.fixedTopMargin     = res.height;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t},100);\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.getDomSize('guiPageBody', (res)=>{\r\n\t\t\t\tthis.refreshBodyHeight = res.height;\r\n\t\t\t\tthis.getDomSize('guiPageFixedTop', (res)=>{\r\n\t\t\t\t\tif(res.height){\r\n\t\t\t\t\t\tthis.refreshBodyHeight -= res.height;\r\n\t\t\t\t\t\tthis.fixedTopMargin     = res.height;\r\n\t\t\t\t\t}\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t}\r\n\t},\r\n\t\r\n\twatch:{\r\n\t\tisLoading : function (val) {\r\n\t\t\tif(val){\r\n\t\t\t\tthis.$refs.guipageloading.open();\r\n\t\t\t}else{\r\n\t\t\t\tthis.$refs.guipageloading.close();\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t\r\n\tcreated:function(){\r\n\t\t\r\n\t\tthis.footerHeight = this.footerSets.height + 'rpx';\r\n\t\t\r\n\t\t// #ifdef H5\r\n\t\tif(this.customHeader){\r\n\t\t\tthis.fixedTop = this.headerSets.height;\r\n\t\t}else{\r\n\t\t\tthis.fixedTop = 44;\r\n\t\t}\r\n\t\t// #endif\r\n\t\t\r\n\t\ttry {\r\n\t\t\tvar system   = uni.getSystemInfoSync();\r\n\t\t\tif(system.model){\r\n\t\t\t\tsystem.model = system.model.replace(' ', '');\r\n\t\t\t\tsystem.model = system.model.toLowerCase();\r\n\t\t\t\tthis.statusBarHeight = system.statusBarHeight;\r\n\t\t\t\tvar res1 = system.model.indexOf('iphonex');\r\n\t\t\t\tif(res1 > 5){res1 = -1;}\r\n\t\t\t\tvar res2 = system.model.indexOf('iphone1');\r\n\t\t\t\tif(res2 > 5){res2 = -1;}\r\n\t\t\t\tif(res1 != -1 || res2 != -1){\r\n\t\t\t\t\tthis.iphoneXButtomHeight = '50rpx';\r\n\t\t\t\t\tthis.footerHeight        =  (this.footerSets.height + 50 ) + 'rpx';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\tthis.statusBarHeight = 0;\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.iphoneXButtomHeight = '0rpx';\r\n\t\t\tthis.footerHeight        =  this.footerSets.height + 'rpx';\r\n\t\t\tif(plus.navigator.isFullscreen()){\r\n\t\t\t\tthis.statusBarHeight = 0;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\tif(this.isSwitchPage){\r\n\t\t\t\tthis.iphoneXButtomHeight = '0rpx';\r\n\t\t\t\tthis.footerHeight        =  this.footerSets.height + 'rpx';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// #ifndef H5\r\n\t\t\tif(this.customHeader){\r\n\t\t\t\tthis.fixedTop = this.headerSets.height + this.statusBarHeight;\r\n\t\t\t}else{\r\n\t\t\t\tthis.fixedTop = 0;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t} catch (e){return null;}\r\n\t},\r\n\tmethods:{\r\n\t\t// 下拉刷新相关\r\n\t\ttouchstart : function (e){\r\n\t\t\tif(!this.refresh){return false;}\r\n\t\t\tif(this.apiLoadingStatus){return false;}\r\n\t\t\tthis.$refs.guiPageRefresh.touchstart(e);\r\n\t\t},\r\n\t\ttouchmove : function(e){\r\n\t\t\tif(!this.refresh){return false;}\r\n\t\t\tif(this.apiLoadingStatus){return false;}\r\n\t\t\tthis.$refs.guiPageRefresh.touchmove(e);\r\n\t\t},\r\n\t\ttouchend : function (e) {\r\n\t\t\tif(!this.refresh){return false;}\r\n\t\t\tif(this.apiLoadingStatus){return false;}\r\n\t\t\tthis.$refs.guiPageRefresh.touchend(e);\r\n\t\t},\r\n\t\tscroll:function(e){\r\n\t\t\tif(this.srcollTimer != null){\r\n\t\t\t\tclearTimeout(this.srcollTimer);\r\n\t\t\t}\r\n\t\t\tthis.srcollTimer = setTimeout(()=>{\r\n\t\t\t\tthis.$refs.guiPageRefresh.scroll(e);\r\n\t\t\t\tthis.$emit('scroll', e);\r\n\t\t\t\tthis.scrollTop = e.detail.scrollTop;\r\n\t\t\t}, 100);\r\n\t\t},\r\n\t\tsetScrollTop : function (scrollTop){\r\n\t\t\tthis.scrollTop = scrollTop;\r\n\t\t},\r\n\t\tendReload : function(){\r\n\t\t\tthis.$refs.guiPageRefresh.endReload();\r\n\t\t},\r\n\t\treload : function(){\r\n\t\t\tif(this.apiLoadingStatus){return false;}\r\n\t\t\tthis.$emit('reload');\r\n\t\t\tif(this.loadmore){this.$refs.guipageloadmore.stoploadmore();}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取元素尺寸\r\n\t\tgetDomSize : function(domIDOrRef, fun){\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tuni.createSelectorQuery().in(this).select('#'+domIDOrRef).boundingClientRect().exec((res)=>{\r\n\t\t\t\t\tfun(res[0]);\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tvar el = this.$refs[domIDOrRef];\r\n\t\t\t\tdom.getComponentRect(el, (res) => {\r\n\t\t\t\t\tfun(res.size);\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t}, 800);\r\n\t\t\t\r\n\t\t},\r\n\t\tstopfun : function(e){e.stopPropagation(); return null;},\r\n\t\theaderTap : function(){\r\n\t\t\tthis.headerTapNumber ++;\r\n\t\t\tif(this.headerTapNumber >= 2){\r\n\t\t\t\tthis.$emit('gotoTop');\r\n\t\t\t\tthis.headerTapNumber = 0;\r\n\t\t\t}else{\r\n\t\t\t\tsetTimeout(()=>{this.headerTapNumber = 0;}, 1000);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef APP-NVUE\r\n\t\tgetRefs : function(ref, count, fun){\r\n\t\t\tif(count >= 40){return null;}\r\n\t\t\tvar refReturn = this.$refs[ref];\r\n\t\t\tif(refReturn){\r\n\t\t\t\tfun(refReturn);\r\n\t\t\t\treturn;\r\n\t\t\t}else{\r\n\t\t\t\tcount++;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.getRefs(ref, count, fun);\r\n\t\t\t\t}, 100);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #endif\r\n\t\t\r\n\t\tloadmorefun : function () {\r\n\t\t\tif(!this.loadmore){return false;}\r\n\t\t\tif(this.apiLoadingStatus){return false;}\r\n\t\t\t// 获取加载组件状态看一下是否还能继续加载\r\n\t\t\t// 保证触底只执行一次加载\r\n\t\t\tif(this.loadMoreTimer != null){clearTimeout(this.loadMoreTimer);}\r\n\t\t\tthis.loadMoreTimer =  setTimeout(() => {\r\n\t\t\t\tvar status = this.$refs.guipageloadmore.loadMoreStatus;\r\n\t\t\t\tif(status != 0){return null;}\r\n\t\t\t\tthis.$refs.guipageloadmore.loading();\r\n\t\t\t\tthis.$emit('loadmorefun');\r\n\t\t\t}, 80);\r\n\t\t},\r\n\t\tstoploadmore : function(){\r\n\t\t\tthis.$refs.guipageloadmore.stoploadmore();\r\n\t\t},\r\n\t\tnomore : function () {\r\n\t\t\tthis.$refs.guipageloadmore.nomore();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n<style scoped>\r\n.gui-sbody{width:750rpx;}\r\n.gui-page-loading{width:750rpx; position:fixed; left:0; top:0; bottom:0; flex:1; z-index:99999;}\r\n.gui-page-loading-points{width:20rpx; height:20rpx; border-radius:50rpx; margin:10rpx;}\r\n/* #ifndef APP-NVUE */\r\n.gui-sbody{min-height:calc(100vh - var(--window-top) - var(--window-bottom));}\r\n@keyframes pageLoading1{0% {opacity:0.5; transform:scale(1);} 40% {opacity:1; transform:scale(1.5);}  60%{opacity:0.5; transform:scale(1);}}\r\n@keyframes pageLoading2{20% {opacity:0.5; transform:scale(1);} 60% {opacity:1; transform:scale(1.5);}  80% {opacity:0.5; transform:scale(1);}}\r\n@keyframes pageLoading3{40% {opacity:0.5; transform:scale(1);} 80% {opacity:1; transform:scale(1.5);}  100% {opacity:0.5; transform:scale(1);}}\r\n.animate1{animation:pageLoading1 1.2s infinite linear;}\r\n.animate2{animation:pageLoading2 1.2s infinite linear;}\r\n.animate3{animation:pageLoading3 1.2s infinite linear;}\r\n/* #endif */\r\n.gui-header{width:750rpx; position:fixed; left:0; top:0;}\r\n.gui-page-footer{width:750rpx; position:fixed; left:0; bottom:0;}\r\n/* #ifdef H5 */\r\n.gui-switch-page-footer{bottom:50px;}\r\n/* #endif */\r\n.gui-page-status-bar{width:750rpx;}\r\n.gui-page-pendant{position:fixed;}\r\n\r\n.gui-page-fixed-top{position:fixed; top:44px; left:0px;  width:750rpx; z-index:99998; overflow:hidden;}\r\n.gui-page-loadmore{padding-bottom:30rpx;}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page.vue?vue&type=style&index=0&id=325fdc7c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page.vue?vue&type=style&index=0&id=325fdc7c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689561872\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}