const express = require('express');
const router = express.Router();

// 模拟日志数据
const logs = [
  {
    id: 1,
    level: 'info',
    userName: '管理员',
    action: 'login',
    module: 'user',
    description: '用户登录系统',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    method: 'POST',
    url: '/api/auth/login',
    params: { username: 'admin' },
    response: { success: true, message: '登录成功' },
    duration: 120,
    requestId: 'req_123456789',
    createdAt: new Date()
  },
  {
    id: 2,
    level: 'info',
    userName: '教师A',
    action: 'create',
    module: 'course',
    description: '创建新课程：N5基础日语',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    method: 'POST',
    url: '/api/courses',
    params: { title: 'N5基础日语', level: 'N5' },
    response: { success: true, id: 123 },
    duration: 250,
    requestId: 'req_123456790',
    createdAt: new Date(Date.now() - 60 * 60 * 1000)
  },
  {
    id: 3,
    level: 'warning',
    userName: '学生B',
    action: 'view',
    module: 'course',
    description: '访问未授权课程',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
    method: 'GET',
    url: '/api/courses/456',
    params: {},
    response: { success: false, message: '权限不足' },
    duration: 50,
    requestId: 'req_123456791',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 4,
    level: 'error',
    userName: '系统',
    action: 'delete',
    module: 'system',
    description: '删除用户失败',
    ipAddress: '127.0.0.1',
    userAgent: 'System',
    method: 'DELETE',
    url: '/api/users/789',
    params: { id: 789 },
    response: { success: false, message: '数据库连接失败' },
    error: 'Database connection timeout after 30 seconds',
    duration: 30000,
    requestId: 'req_123456792',
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000)
  }
];

// 生成更多模拟数据
for (let i = 5; i <= 100; i++) {
  const levels = ['info', 'warning', 'error', 'debug'];
  const actions = ['login', 'logout', 'create', 'update', 'delete', 'view'];
  const modules = ['user', 'course', 'group', 'system', 'permission'];
  const users = ['管理员', '教师A', '教师B', '学生A', '学生B', '系统'];
  
  logs.push({
    id: i,
    level: levels[Math.floor(Math.random() * levels.length)],
    userName: users[Math.floor(Math.random() * users.length)],
    action: actions[Math.floor(Math.random() * actions.length)],
    module: modules[Math.floor(Math.random() * modules.length)],
    description: `模拟操作日志 ${i}`,
    ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
    url: `/api/test/${i}`,
    params: { id: i },
    response: { success: Math.random() > 0.2 },
    duration: Math.floor(Math.random() * 1000) + 50,
    requestId: `req_${Date.now()}_${i}`,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
  });
}

// 获取日志列表
router.get('/', (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      userName, 
      action, 
      module, 
      level,
      startDate,
      endDate
    } = req.query;
    
    let filteredLogs = [...logs];
    
    // 筛选
    if (userName) {
      filteredLogs = filteredLogs.filter(log => 
        log.userName.includes(userName)
      );
    }
    
    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action === action);
    }
    
    if (module) {
      filteredLogs = filteredLogs.filter(log => log.module === module);
    }
    
    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }
    
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      filteredLogs = filteredLogs.filter(log => {
        const logDate = new Date(log.createdAt);
        return logDate >= start && logDate <= end;
      });
    }
    
    // 按时间倒序排序
    filteredLogs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        logs: paginatedLogs,
        total: filteredLogs.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取日志列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取日志列表失败'
    });
  }
});

// 获取单个日志详情
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const log = logs.find(l => l.id === parseInt(id));
    
    if (!log) {
      return res.status(404).json({
        success: false,
        message: '日志不存在'
      });
    }
    
    res.json({
      success: true,
      data: { log }
    });
  } catch (error) {
    console.error('获取日志详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取日志详情失败'
    });
  }
});

// 清空日志
router.delete('/clear', (req, res) => {
  try {
    // 在实际应用中，这里应该清空数据库中的日志
    // 这里只是模拟，不实际清空数组
    
    res.json({
      success: true,
      message: '日志清空成功'
    });
  } catch (error) {
    console.error('清空日志失败:', error);
    res.status(500).json({
      success: false,
      message: '清空日志失败'
    });
  }
});

// 导出日志
router.post('/export', (req, res) => {
  try {
    const { format = 'csv', filters = {} } = req.body;
    
    // 这里应该实现日志导出逻辑
    // 返回下载链接或直接返回文件
    
    res.json({
      success: true,
      data: {
        downloadUrl: '/api/logs/download/export_' + Date.now() + '.' + format
      },
      message: '日志导出成功'
    });
  } catch (error) {
    console.error('导出日志失败:', error);
    res.status(500).json({
      success: false,
      message: '导出日志失败'
    });
  }
});

// 获取日志统计
router.get('/stats', (req, res) => {
  try {
    const { days = 7 } = req.query;
    
    const now = new Date();
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    
    const recentLogs = logs.filter(log => new Date(log.createdAt) >= startDate);
    
    // 统计各级别日志数量
    const levelStats = {
      info: recentLogs.filter(log => log.level === 'info').length,
      warning: recentLogs.filter(log => log.level === 'warning').length,
      error: recentLogs.filter(log => log.level === 'error').length,
      debug: recentLogs.filter(log => log.level === 'debug').length
    };
    
    // 统计各模块日志数量
    const moduleStats = {};
    recentLogs.forEach(log => {
      moduleStats[log.module] = (moduleStats[log.module] || 0) + 1;
    });
    
    // 统计各操作类型数量
    const actionStats = {};
    recentLogs.forEach(log => {
      actionStats[log.action] = (actionStats[log.action] || 0) + 1;
    });
    
    res.json({
      success: true,
      data: {
        total: recentLogs.length,
        levelStats,
        moduleStats,
        actionStats,
        period: `${days}天内`
      }
    });
  } catch (error) {
    console.error('获取日志统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取日志统计失败'
    });
  }
});

module.exports = router;
