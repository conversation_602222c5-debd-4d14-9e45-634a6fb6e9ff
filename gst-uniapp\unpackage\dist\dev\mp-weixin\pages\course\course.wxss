page.data-v-b1db4d34 {
  background-color: #f3f3f3;
  font-family: SimHei;
}
.container.data-v-b1db4d34 {
  background-color: #f3f3f3;
}
.video.data-v-b1db4d34 {
  width: 100%;
  height: auto;
}
.shipinpart.data-v-b1db4d34 {
  height: 400rpx;
  background-color: #fff;
}
.shipinpart-media.data-v-b1db4d34 {
  height: 400rpx;
  background-color: #fff;
}
.shipinpart-media .audio_bg.data-v-b1db4d34 {
  position: absolute;
  z-index: -19;
  transition: all 0.4s cubic-bezier(0.42, 0, 0.58, 1) 0s;
}
@-webkit-keyframes myDongHua-data-v-b1db4d34 {
0% {
    -webkit-filter: blur(0rpx);
            filter: blur(0rpx);
}
5% {
    -webkit-filter: blur(1rpx);
            filter: blur(1rpx);
}
10% {
    -webkit-filter: blur(2rpx);
            filter: blur(2rpx);
}
15% {
    -webkit-filter: blur(3rpx);
            filter: blur(3rpx);
}
20% {
    -webkit-filter: blur(4rpx);
            filter: blur(4rpx);
}
25% {
    -webkit-filter: blur(5rpx);
            filter: blur(5rpx);
}
30% {
    -webkit-filter: blur(6rpx);
            filter: blur(6rpx);
}
35% {
    -webkit-filter: blur(7rpx);
            filter: blur(7rpx);
}
40% {
    -webkit-filter: blur(8rpx);
            filter: blur(8rpx);
}
45% {
    -webkit-filter: blur(9rpx);
            filter: blur(9rpx);
}
50% {
    -webkit-filter: blur(10rpx);
            filter: blur(10rpx);
}
55% {
    -webkit-filter: blur(9rpx);
            filter: blur(9rpx);
}
60% {
    -webkit-filter: blur(8rpx);
            filter: blur(8rpx);
}
65% {
    -webkit-filter: blur(7rpx);
            filter: blur(7rpx);
}
70% {
    -webkit-filter: blur(6rpx);
            filter: blur(6rpx);
}
75% {
    -webkit-filter: blur(5rpx);
            filter: blur(5rpx);
}
80% {
    -webkit-filter: blur(4rpx);
            filter: blur(4rpx);
}
85% {
    -webkit-filter: blur(3rpx);
            filter: blur(3rpx);
}
90% {
    -webkit-filter: blur(2rpx);
            filter: blur(2rpx);
}
95% {
    -webkit-filter: blur(1rpx);
            filter: blur(1rpx);
}
100% {
    -webkit-filter: blur(0rpx);
            filter: blur(0rpx);
}
}
@keyframes myDongHua-data-v-b1db4d34 {
0% {
    -webkit-filter: blur(0rpx);
            filter: blur(0rpx);
}
5% {
    -webkit-filter: blur(1rpx);
            filter: blur(1rpx);
}
10% {
    -webkit-filter: blur(2rpx);
            filter: blur(2rpx);
}
15% {
    -webkit-filter: blur(3rpx);
            filter: blur(3rpx);
}
20% {
    -webkit-filter: blur(4rpx);
            filter: blur(4rpx);
}
25% {
    -webkit-filter: blur(5rpx);
            filter: blur(5rpx);
}
30% {
    -webkit-filter: blur(6rpx);
            filter: blur(6rpx);
}
35% {
    -webkit-filter: blur(7rpx);
            filter: blur(7rpx);
}
40% {
    -webkit-filter: blur(8rpx);
            filter: blur(8rpx);
}
45% {
    -webkit-filter: blur(9rpx);
            filter: blur(9rpx);
}
50% {
    -webkit-filter: blur(10rpx);
            filter: blur(10rpx);
}
55% {
    -webkit-filter: blur(9rpx);
            filter: blur(9rpx);
}
60% {
    -webkit-filter: blur(8rpx);
            filter: blur(8rpx);
}
65% {
    -webkit-filter: blur(7rpx);
            filter: blur(7rpx);
}
70% {
    -webkit-filter: blur(6rpx);
            filter: blur(6rpx);
}
75% {
    -webkit-filter: blur(5rpx);
            filter: blur(5rpx);
}
80% {
    -webkit-filter: blur(4rpx);
            filter: blur(4rpx);
}
85% {
    -webkit-filter: blur(3rpx);
            filter: blur(3rpx);
}
90% {
    -webkit-filter: blur(2rpx);
            filter: blur(2rpx);
}
95% {
    -webkit-filter: blur(1rpx);
            filter: blur(1rpx);
}
100% {
    -webkit-filter: blur(0rpx);
            filter: blur(0rpx);
}
}
.shipinpart-media .gaosi_filter.data-v-b1db4d34 {
  -webkit-animation: myDongHua-data-v-b1db4d34 4s infinite;
          animation: myDongHua-data-v-b1db4d34 4s infinite;
  transition: all 4s cubic-bezier(0.42, 0, 0.58, 1) 0s;
}
.shipinpart-media image.data-v-b1db4d34 {
  width: 100%;
  height: 400rpx;
}
.shipinpart-media .audio.data-v-b1db4d34 {
  width: 100%;
  height: 400rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 19;
  padding-bottom: 20rpx;
}
.shipinpart-media .audio .bofang.data-v-b1db4d34 {
  width: 80rpx;
  height: 80rpx;
}
.shipinpart-media .audio .bofang image.data-v-b1db4d34 {
  width: 80rpx;
  height: 80rpx;
}
.shipinpart-media .audio-wrapper.data-v-b1db4d34 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  color: #333;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), #fff);
}
.shipinpart-media .audio-wrapper .prev.data-v-b1db4d34,
.shipinpart-media .audio-wrapper .next.data-v-b1db4d34 {
  width: 48rpx;
  height: 44rpx;
  margin: 0 20rpx;
}
.shipinpart-media .audio-wrapper .audio-number.data-v-b1db4d34 {
  font-size: 24rpx;
}
.shipinpart-media .audio-wrapper .audio-slider.data-v-b1db4d34 {
  flex: 1;
}
.shipinpart-info.data-v-b1db4d34 {
  z-index: 19;
  padding: 0 20rpx;
  display: flex;
  flex-direction: column;
}
.shipinpart-info .info-span1.data-v-b1db4d34 {
  margin-top: 35rpx;
  font-size: 32rpx;
  font-weight: 700;
  color: #070707;
  letter-spacing: 5rpx;
}
.shipinpart-info .info-span2.data-v-b1db4d34 {
  letter-spacing: 5rpx;
  font-size: 26rpx;
  color: #373737;
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.shipinpart-info .info-span2 text.data-v-b1db4d34:nth-child(2) {
  letter-spacing: 1rpx;
  width: 110rpx;
  height: 43rpx;
  border-radius: 10rpx;
  background-color: #e4edff;
  color: #4b89ff;
  text-align: center;
  line-height: 43rpx;
  margin-left: 50rpx;
  margin-right: 30rpx;
  box-shadow: 0rpx 2rpx 3rpx 1rpx #8dbeff;
}
.shipinpart-info .info-span5.data-v-b1db4d34 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx;
}
.shipinpart-info .info-span3.data-v-b1db4d34 {
  margin-top: 18rpx;
  font-size: 24rpx;
  color: #b3b3b3;
  display: flex;
  align-items: center;
}
.shipinpart-info .info-span3 text.data-v-b1db4d34:nth-child(2) {
  margin: 0 15rpx;
}
.shipinpart-info .info-span4.data-v-b1db4d34 {
  margin-top: 22rpx;
  color: #E98438;
  font-size: 50rpx;
  margin-right: 40rpx;
}
.kechengpart.data-v-b1db4d34 {
  margin-top: 10rpx;
  background-color: #fff;
  min-height: 1000rpx;
}
.kechengpart-title.data-v-b1db4d34 {
  height: 125rpx;
  background-color: #fff;
  padding-top: 45rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
}
.kechengpart-title-item.data-v-b1db4d34 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.kechengpart-title-item view.data-v-b1db4d34 {
  font-size: 30rpx;
  color: #575757;
}
.kechengpart-title-item .btna.data-v-b1db4d34 {
  font-weight: 700;
  color: #131313;
}
.kechengpart-title-item ._underline.data-v-b1db4d34 {
  width: 70rpx;
  height: 7rpx;
  background-color: #2f77ff;
  border-radius: 5rpx;
  margin-top: 15rpx;
}
.kechengpart-content .kcjs.data-v-b1db4d34 {
  padding: 0 20rpx;
  background-color: #fff;
  display: none;
}
.kechengpart-content .kcjs-lecturer-top.data-v-b1db4d34 {
  height: 60rpx;
  display: inline-flex;
}
.kechengpart-content .kcjs-lecturer-top image.data-v-b1db4d34 {
  width: 47rpx;
  height: 33rpx;
  margin-top: 4rpx;
}
.kechengpart-content .kcjs-lecturer-top text.data-v-b1db4d34 {
  font-size: 26rpx;
  font-weight: 700;
  color: #020202;
  margin-left: 15rpx;
}
.kechengpart-content .kcjs-lecturer .jiangshi-right.data-v-b1db4d34 {
  display: inline-flex;
  align-items: center;
  float: right;
}
.kechengpart-content .kcjs-lecturer .jiangshi-right text.data-v-b1db4d34 {
  font-size: 22rpx;
  color: #6d6d6d;
  margin-right: 0;
}
.kechengpart-content .kcjs-lecturer .jiangshi-right image.data-v-b1db4d34 {
  width: 20rpx;
  height: 22rpx;
  margin-left: 5rpx;
}
.kechengpart-content .kcjs-lecturer-bottom.data-v-b1db4d34 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.kechengpart-content .kcjs-lecturer-bottom image.data-v-b1db4d34 {
  width: 100%;
  height: 400rpx;
}
.kechengpart-content .kcjs-lecturer-bottom .jiangshi-left.data-v-b1db4d34 {
  display: flex;
  align-items: center;
}
.kechengpart-content .kcjs-lecturer-bottom .jiangshi-left image.data-v-b1db4d34 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}
.kechengpart-content .kcjs-lecturer-bottom .jiangshi-left text.data-v-b1db4d34 {
  font-size: 26rpx;
  color: #333;
  margin-left: 20rpx;
}
.kechengpart-content .kcjs-lecturer-bottom .jiangshi-right.data-v-b1db4d34 {
  display: flex;
  align-items: center;
}
.kechengpart-content .kcjs-lecturer-bottom .jiangshi-right text.data-v-b1db4d34 {
  font-size: 26rpx;
  color: #C0C0C0;
  margin-right: 0;
}
.kechengpart-content .kcjs-lecturer-bottom .jiangshi-right image.data-v-b1db4d34 {
  width: 20rpx;
  height: 26rpx;
  margin-left: 5rpx;
}
.kechengpart-content .kcjs-brief.data-v-b1db4d34 {
  margin-top: 40rpx;
  margin-bottom: 150rpx;
}
.kechengpart-content .kcjs-brief-top.data-v-b1db4d34 {
  height: 75rpx;
  display: flex;
}
.kechengpart-content .kcjs-brief-top image.data-v-b1db4d34 {
  width: 53rpx;
  height: 49rpx;
}
.kechengpart-content .kcjs-brief-top text.data-v-b1db4d34 {
  font-size: 26rpx;
  font-weight: 700;
  color: #020202;
  margin-left: 15rpx;
  margin-top: 4rpx;
}
.kechengpart-content .kcjs-brief-center.data-v-b1db4d34 {
  padding-bottom: 30rpx;
}
.kechengpart-content .kcjs-brief-center text.data-v-b1db4d34 {
  font-size: 26rpx;
  color: #313131;
}
.kechengpart-content .kcml.data-v-b1db4d34 {
  display: none;
  padding: 0 20rpx;
  background-color: #fff;
}
.kechengpart-content .kcml-list.data-v-b1db4d34 {
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}
.kechengpart-content .kcml-list-left.data-v-b1db4d34 {
  width: 436rpx;
  height: 60rpx;
  line-height: 60rpx;
}
.kechengpart-content .kcml-list-left .yinpin.data-v-b1db4d34 {
  display: inline-block;
  width: 64rpx;
  height: 32rpx;
  font-size: 20rpx;
  color: #ff6969;
  border: 2rpx solid #ff6969;
  border-radius: 5rpx;
  box-sizing: border-box;
  text-align: center;
  line-height: 28rpx;
  margin-right: 15rpx;
  vertical-align: middle;
}
.kechengpart-content .kcml-list-left .shipin.data-v-b1db4d34 {
  display: inline-block;
  width: 64rpx;
  height: 32rpx;
  font-size: 20rpx;
  color: #398cff;
  border: 2rpx solid #398cff;
  border-radius: 5rpx;
  box-sizing: border-box;
  text-align: center;
  line-height: 24rpx;
  margin-right: 15rpx;
  vertical-align: middle;
}
.kechengpart-content .kcml-list-left .tuwen.data-v-b1db4d34 {
  display: inline-block;
  width: 64rpx;
  height: 32rpx;
  font-size: 20rpx;
  color: #8bc34a;
  border: 2rpx solid #8bc34a;
  border-radius: 5rpx;
  box-sizing: border-box;
  text-align: center;
  line-height: 24rpx;
  margin-right: 15rpx;
  vertical-align: middle;
}
.kechengpart-content .kcml-list-left text.data-v-b1db4d34:nth-child(2) {
  font-size: 25rpx;
  color: #070707;
  display: inline-block;
  white-space: nowrap;
  width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.kechengpart-content .kcml-list-right text.data-v-b1db4d34 {
  font-size: 24rpx;
  color: #007AFF;
  vertical-align: middle;
}
.kechengpart-content .kcml-list-right image.data-v-b1db4d34 {
  width: 30rpx;
  height: 30rpx;
  vertical-align: middle;
}
.kechengpart-content .yhpj.data-v-b1db4d34 {
  display: none;
  text-align: center;
  margin-bottom: 140rpx;
  background-color: #fff;
  padding: 30rpx 0;
}
.kechengpart-content .yhpj-list .item.data-v-b1db4d34 {
  display: flex;
  padding: 0 20rpx;
  margin-top: 40rpx;
}
.kechengpart-content .yhpj-list .item-left.data-v-b1db4d34 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.kechengpart-content .yhpj-list .item-right.data-v-b1db4d34 {
  display: flex;
  flex-direction: column;
}
.kechengpart-content .yhpj-list .item-right-top.data-v-b1db4d34 {
  display: flex;
  align-items: center;
}
.kechengpart-content .yhpj-list .item-right-top text.data-v-b1db4d34:nth-child(1) {
  font-size: 32rpx;
  color: #333;
}
.kechengpart-content .yhpj-list .item-right-top text.data-v-b1db4d34:nth-child(2) {
  font-size: 26rpx;
  color: #999;
  margin-left: 20rpx;
}
.kechengpart-content .yhpj-list .item-right-bottom.data-v-b1db4d34 {
  max-width: 500rpx;
  margin-top: 10rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  background-color: #fff;
  text-align: left;
}
.kechengpart-content .yhpj-list .item-right-bottom text.data-v-b1db4d34 {
  font-size: 30rpx;
  font-weight: 400;
  color: #333;
}
.kechengpart-content .nopl.data-v-b1db4d34 {
  width: 334rpx;
  height: 243rpx;
  margin-top: 20rpx;
}
.kechengpart-content .dis.data-v-b1db4d34 {
  display: block;
}
.buy.data-v-b1db4d34 {
  width: 100%;
  height: 122rpx;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  border-radius: 40rpx 40rpx 0 0;
  border-top: 1rpx solid #eee;
}
.buy-left.data-v-b1db4d34 {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
.buy-left image.data-v-b1db4d34 {
  width: 37rpx;
  height: 37rpx;
}
.buy-left text.data-v-b1db4d34 {
  font-size: 24rpx;
  color: #ff6229;
}
.buy-left .info-span4.data-v-b1db4d34 {
  color: #ff6229;
  font-size: 50rpx;
  margin-right: 40rpx;
}
.buy .sharebtn.data-v-b1db4d34 {
  margin: 0;
  padding: 0;
  outline: none;
  border-radius: 0;
  background-color: transparent;
  line-height: inherit;
  width: -webkit-max-content;
  width: max-content;
}
.buy .sharebtn.data-v-b1db4d34:after {
  border: none;
}
.buy-right.data-v-b1db4d34 {
  width: 450rpx;
  height: 80rpx;
  background-image: linear-gradient(to right, #4498ff, #1763ff);
  border-radius: 80rpx;
  font-size: 34rpx;
  font-weight: 700;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  box-shadow: 0rpx 2rpx 2rpx 1rpx #8dbeff;
  letter-spacing: 7rpx;
}
.fontcolor.data-v-b1db4d34 {
  color: #FF0066;
}
.cover-box.data-v-b1db4d34 {
  width: 100%;
  min-height: 200rpx;
  position: relative;
}
.cover-box .cover.data-v-b1db4d34 {
  width: 100%;
  height: 100%;
}
.cover-box .button.data-v-b1db4d34 {
  position: absolute;
  bottom: 5%;
  right: 5%;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 30rpx;
  background-color: blue;
  color: white;
  padding: 10rpx 20rpx;
  font-size: 40rpx;
}
.p-b-btn.data-v-b1db4d34 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 96rpx;
}
.p-b-btn .yticon.data-v-b1db4d34 {
  font-size: 40rpx;
  line-height: 48rpx;
}
.p-b-btn .icon-fenxiang2.data-v-b1db4d34 {
  font-size: 42rpx;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.p-b-btn .icon-shoucang.data-v-b1db4d34 {
  font-size: 46rpx;
}
.contact-btn.data-v-b1db4d34 {
  display: inline-block;
  position: absolute;
  width: 20%;
  background: salmon;
  opacity: 0;
}

