{"name": "gst-japanese-training-backend", "version": "1.0.0", "description": "GST日语培训班后端API服务", "main": "src/app.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "app": "node src/app.js", "test": "jest", "lint": "eslint src/", "migrate": "sequelize-cli db:migrate", "seed": "sequelize-cli db:seed:all", "db:init": "node scripts/init-database.js", "import:menus": "node scripts/import-menus.js"}, "keywords": ["japanese", "training", "education", "api", "uniapp"], "author": "GST Team", "license": "MIT", "dependencies": {"@alicloud/openapi-client": "^0.4.15", "@alicloud/vod20170321": "^3.8.4", "ali-oss": "^6.23.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-winston": "^4.2.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "redis": "^4.6.11", "sequelize": "^6.35.2", "sharp": "^0.33.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}