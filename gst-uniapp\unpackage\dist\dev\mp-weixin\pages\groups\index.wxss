





























































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































/* ===== 基础页面样式 ===== */
.clean-groups-page.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}
.page-content.data-v-6e8c2b60 {
	position: relative;
	z-index: 1;
}
/* ===== 页面头部样式 ===== */
.page-header.data-v-6e8c2b60 {
	background: white;
	border-radius: 0 0 30rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.header-content.data-v-6e8c2b60 {
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.title-section.data-v-6e8c2b60 {
	flex: 1;
}
.page-title.data-v-6e8c2b60 {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
	text-align: left;
}
.page-subtitle.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
}
.stats-section.data-v-6e8c2b60 {
	display: flex;
	gap: 25rpx;
}
.stat-item.data-v-6e8c2b60 {
	text-align: center;
}
.stat-number.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 700;
	color: #667eea;
	display: block;
	line-height: 1;
}
.stat-label.data-v-6e8c2b60 {
	font-size: 18rpx;
	color: #999;
	margin-top: 5rpx;
}
/* ===== 左右联动布局 ===== */
.split-layout.data-v-6e8c2b60 {
	display: flex;
	height: calc(100vh - 200rpx);
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
	margin: 0 20rpx;
	box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.1);
	/* iPad滚动优化 */
	-webkit-overflow-scrolling: touch;
	touch-action: pan-y;
}
/* ===== 左侧面板 ===== */
.left-panel.data-v-6e8c2b60 {
	width: 200rpx;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}
.panel-header.data-v-6e8c2b60 {
	padding: 25rpx 15rpx 20rpx;
	background: white;
	border-bottom: 1rpx solid #e9ecef;
	text-align: center;
}
.panel-title.data-v-6e8c2b60 {
	font-size: 22rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.panel-subtitle.data-v-6e8c2b60 {
	font-size: 18rpx;
	color: #999;
}
.group-list.data-v-6e8c2b60 {
	flex: 1;
	padding: 10rpx 0;
	/* iPad滚动优化 */
	-webkit-overflow-scrolling: touch;
	touch-action: pan-y;
	overscroll-behavior: contain;
}
/* ===== 小组列表项 ===== */
.group-item.data-v-6e8c2b60 {
	margin: 0 10rpx 15rpx;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	text-align: center;
	position: relative;
}
.group-item.active.data-v-6e8c2b60 {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	-webkit-transform: scale(1.05);
	        transform: scale(1.05);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.group-item.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.simple-group-content.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}
.group-level-badge.data-v-6e8c2b60 {
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	font-size: 18rpx;
	font-weight: 700;
	min-width: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.simple-group-name.data-v-6e8c2b60 {
	font-size: 20rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	line-height: 1.3;
	word-break: break-all;
}
.simple-status-dot.data-v-6e8c2b60 {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}
.simple-status-dot.active.data-v-6e8c2b60 {
	background: #4CAF50;
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}
.simple-status-dot.completed.data-v-6e8c2b60 {
	background: #2196F3;
	box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
}
.simple-status-dot.pending.data-v-6e8c2b60 {
	background: #FF9800;
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
}
.groups-container.data-v-6e8c2b60 {
	padding: 30rpx;
}
.page-title.data-v-6e8c2b60 {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 40rpx;
}
.groups-grid.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
.group-card.data-v-6e8c2b60 {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	position: relative;
}
.group-icon.data-v-6e8c2b60 {
	width: 120rpx;
	height: 120rpx;
	margin-right: 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}
.group-icon image.data-v-6e8c2b60 {
	width: 80rpx;
	height: 80rpx;
}
.group-info.data-v-6e8c2b60 {
	flex: 1;
}
.group-name.data-v-6e8c2b60 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.group-desc.data-v-6e8c2b60 {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
	line-height: 1.4;
}
.group-stats.data-v-6e8c2b60 {
	display: flex;
	gap: 20rpx;
}
.stat-item.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #999;
	background: #f8f8f8;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}
.group-status.data-v-6e8c2b60 {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}
.group-status.active.data-v-6e8c2b60 {
	background: #e8f5e8;
	color: #52c41a;
}
.group-status.completed.data-v-6e8c2b60 {
	background: #f0f0f0;
	color: #999;
}
.group-status.pending.data-v-6e8c2b60 {
	background: #fff7e6;
	color: #fa8c16;
}
/* 简洁清爽的样式 */
.clean-groups-page.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}
.page-content.data-v-6e8c2b60 {
	position: relative;
	z-index: 1;
}
/* 简洁的头部样式 */
.page-header.data-v-6e8c2b60 {
	background: white;
	border-radius: 0 0 30rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.header-content.data-v-6e8c2b60 {
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.title-section.data-v-6e8c2b60 {
	flex: 1;
}
.page-title.data-v-6e8c2b60 {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
	text-align: left;
}
.page-subtitle.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #666;
}
.stats-section.data-v-6e8c2b60 {
	display: flex;
	gap: 30rpx;
}
.stat-item.data-v-6e8c2b60 {
	text-align: center;
}
.stat-number.data-v-6e8c2b60 {
	font-size: 32rpx;
	font-weight: 700;
	color: #667eea;
	display: block;
	line-height: 1;
}
.stat-label.data-v-6e8c2b60 {
	font-size: 20rpx;
	color: #999;
	margin-top: 5rpx;
}
.header-content-wrapper.data-v-6e8c2b60 {
	position: relative;
	z-index: 2;
	padding: 60rpx 30rpx 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.title-section-enhanced.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	flex: 1;
}
.title-icon-container.data-v-6e8c2b60 {
	position: relative;
	margin-right: 25rpx;
}
.icon-bg-circle.data-v-6e8c2b60 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 50%;
	opacity: 0.2;
}
.title-icon-enhanced.data-v-6e8c2b60 {
	position: relative;
	z-index: 2;
	font-size: 70rpx;
	display: block;
	text-align: center;
	line-height: 100rpx;
}
.icon-pulse.data-v-6e8c2b60 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 100rpx;
	height: 100rpx;
	border: 3rpx solid rgba(102, 126, 234, 0.3);
	border-radius: 50%;
	-webkit-animation: iconPulse-data-v-6e8c2b60 3s ease-in-out infinite;
	        animation: iconPulse-data-v-6e8c2b60 3s ease-in-out infinite;
}
@-webkit-keyframes iconPulse-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		opacity: 0.7;
}
50% {
		-webkit-transform: translate(-50%, -50%) scale(1.2);
		        transform: translate(-50%, -50%) scale(1.2);
		opacity: 0.3;
}
}
@keyframes iconPulse-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translate(-50%, -50%) scale(1);
		        transform: translate(-50%, -50%) scale(1);
		opacity: 0.7;
}
50% {
		-webkit-transform: translate(-50%, -50%) scale(1.2);
		        transform: translate(-50%, -50%) scale(1.2);
		opacity: 0.3;
}
}
.title-text-enhanced.data-v-6e8c2b60 {
	position: relative;
}
.main-title-enhanced.data-v-6e8c2b60 {
	font-size: 52rpx;
	font-weight: 800;
	color: #333;
	line-height: 1.2;
	margin-bottom: 8rpx;
	background: linear-gradient(135deg, #333, #667eea);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.sub-title-enhanced.data-v-6e8c2b60 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 15rpx;
}
.title-decoration.data-v-6e8c2b60 {
	width: 80rpx;
	height: 6rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 3rpx;
	-webkit-animation: decorationGlow-data-v-6e8c2b60 2s ease-in-out infinite;
	        animation: decorationGlow-data-v-6e8c2b60 2s ease-in-out infinite;
}
@-webkit-keyframes decorationGlow-data-v-6e8c2b60 {
0%,
	100% {
		box-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);
}
50% {
		box-shadow: 0 0 20rpx rgba(102, 126, 234, 0.6);
}
}
@keyframes decorationGlow-data-v-6e8c2b60 {
0%,
	100% {
		box-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);
}
50% {
		box-shadow: 0 0 20rpx rgba(102, 126, 234, 0.6);
}
}
.stats-section-enhanced.data-v-6e8c2b60 {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}
.stat-card-enhanced.data-v-6e8c2b60 {
	position: relative;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 25rpx 20rpx;
	min-width: 120rpx;
	text-align: center;
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	box-shadow:
		0 8rpx 25rpx rgba(0, 0, 0, 0.1),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
}
.stat-card-enhanced.data-v-6e8c2b60:hover {
	-webkit-transform: translateY(-5rpx);
	        transform: translateY(-5rpx);
	box-shadow:
		0 15rpx 35rpx rgba(0, 0, 0, 0.15),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}
.stat-icon-bg.data-v-6e8c2b60 {
	font-size: 32rpx;
	margin-bottom: 10rpx;
	display: block;
}
.stat-number-enhanced.data-v-6e8c2b60 {
	font-size: 36rpx;
	font-weight: 800;
	color: #333;
	line-height: 1;
	margin-bottom: 5rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.stat-label-enhanced.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}
.stat-sparkle.data-v-6e8c2b60 {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 8rpx;
	height: 8rpx;
	background: #667eea;
	border-radius: 50%;
	-webkit-animation: sparkle-data-v-6e8c2b60 2s ease-in-out infinite;
	        animation: sparkle-data-v-6e8c2b60 2s ease-in-out infinite;
}
@-webkit-keyframes sparkle-data-v-6e8c2b60 {
0%,
	100% {
		opacity: 0.3;
		-webkit-transform: scale(1);
		        transform: scale(1);
}
50% {
		opacity: 1;
		-webkit-transform: scale(1.5);
		        transform: scale(1.5);
}
}
@keyframes sparkle-data-v-6e8c2b60 {
0%,
	100% {
		opacity: 0.3;
		-webkit-transform: scale(1);
		        transform: scale(1);
}
50% {
		opacity: 1;
		-webkit-transform: scale(1.5);
		        transform: scale(1.5);
}
}
/* 装饰元素 */
.header-decorations.data-v-6e8c2b60 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	z-index: 1;
}
.deco-circle.data-v-6e8c2b60 {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}
.deco-1.data-v-6e8c2b60 {
	width: 60rpx;
	height: 60rpx;
	top: 20rpx;
	right: 80rpx;
	-webkit-animation: float1-data-v-6e8c2b60 6s ease-in-out infinite;
	        animation: float1-data-v-6e8c2b60 6s ease-in-out infinite;
}
.deco-2.data-v-6e8c2b60 {
	width: 40rpx;
	height: 40rpx;
	bottom: 30rpx;
	left: 60rpx;
	-webkit-animation: float2-data-v-6e8c2b60 8s ease-in-out infinite;
	        animation: float2-data-v-6e8c2b60 8s ease-in-out infinite;
}
.deco-triangle.data-v-6e8c2b60 {
	position: absolute;
	width: 0;
	height: 0;
	border-left: 15rpx solid transparent;
	border-right: 15rpx solid transparent;
	border-bottom: 25rpx solid rgba(255, 255, 255, 0.1);
}
.deco-3.data-v-6e8c2b60 {
	top: 60rpx;
	left: 40rpx;
	-webkit-animation: float3-data-v-6e8c2b60 7s ease-in-out infinite;
	        animation: float3-data-v-6e8c2b60 7s ease-in-out infinite;
}
.deco-square.data-v-6e8c2b60 {
	position: absolute;
	background: rgba(255, 255, 255, 0.1);
	-webkit-transform: rotate(45deg);
	        transform: rotate(45deg);
}
.deco-4.data-v-6e8c2b60 {
	width: 20rpx;
	height: 20rpx;
	bottom: 60rpx;
	right: 40rpx;
	-webkit-animation: float4-data-v-6e8c2b60 5s ease-in-out infinite;
	        animation: float4-data-v-6e8c2b60 5s ease-in-out infinite;
}
@-webkit-keyframes float1-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateY(0) rotate(0deg);
		        transform: translateY(0) rotate(0deg);
}
50% {
		-webkit-transform: translateY(-20rpx) rotate(180deg);
		        transform: translateY(-20rpx) rotate(180deg);
}
}
@keyframes float1-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateY(0) rotate(0deg);
		        transform: translateY(0) rotate(0deg);
}
50% {
		-webkit-transform: translateY(-20rpx) rotate(180deg);
		        transform: translateY(-20rpx) rotate(180deg);
}
}
@-webkit-keyframes float2-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateX(0) rotate(0deg);
		        transform: translateX(0) rotate(0deg);
}
50% {
		-webkit-transform: translateX(15rpx) rotate(-180deg);
		        transform: translateX(15rpx) rotate(-180deg);
}
}
@keyframes float2-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateX(0) rotate(0deg);
		        transform: translateX(0) rotate(0deg);
}
50% {
		-webkit-transform: translateX(15rpx) rotate(-180deg);
		        transform: translateX(15rpx) rotate(-180deg);
}
}
@-webkit-keyframes float3-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateY(0) rotate(0deg);
		        transform: translateY(0) rotate(0deg);
}
50% {
		-webkit-transform: translateY(10rpx) rotate(120deg);
		        transform: translateY(10rpx) rotate(120deg);
}
}
@keyframes float3-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateY(0) rotate(0deg);
		        transform: translateY(0) rotate(0deg);
}
50% {
		-webkit-transform: translateY(10rpx) rotate(120deg);
		        transform: translateY(10rpx) rotate(120deg);
}
}
@-webkit-keyframes float4-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateY(0) rotate(45deg);
		        transform: translateY(0) rotate(45deg);
}
50% {
		-webkit-transform: translateY(-15rpx) rotate(225deg);
		        transform: translateY(-15rpx) rotate(225deg);
}
}
@keyframes float4-data-v-6e8c2b60 {
0%,
	100% {
		-webkit-transform: translateY(0) rotate(45deg);
		        transform: translateY(0) rotate(45deg);
}
50% {
		-webkit-transform: translateY(-15rpx) rotate(225deg);
		        transform: translateY(-15rpx) rotate(225deg);
}
}
.title-icon.data-v-6e8c2b60 {
	font-size: 60rpx;
	margin-right: 20rpx;
}
.title-text.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
}
.main-title.data-v-6e8c2b60 {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	line-height: 1.2;
}
.sub-title.data-v-6e8c2b60 {
	font-size: 26rpx;
	color: #666;
	margin-top: 5rpx;
}
.stats-section.data-v-6e8c2b60 {
	display: flex;
	gap: 30rpx;
}
.stat-item.data-v-6e8c2b60 {
	text-align: center;
}
.stat-number.data-v-6e8c2b60 {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #667eea;
	line-height: 1;
}
.stat-label.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #999;
	margin-top: 5rpx;
}
/* 左右联动布局 */
.split-layout.data-v-6e8c2b60 {
	display: flex;
	height: calc(100vh - 200rpx);
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
	margin: 0 20rpx;
	box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.1);
}
/* 左侧面板 */
.left-panel.data-v-6e8c2b60 {
	width: 200rpx;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}
.panel-header.data-v-6e8c2b60 {
	padding: 25rpx 15rpx 20rpx;
	background: white;
	border-bottom: 1rpx solid #e9ecef;
	text-align: center;
}
.panel-title.data-v-6e8c2b60 {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.panel-subtitle.data-v-6e8c2b60 {
	font-size: 20rpx;
	color: #999;
}
.group-list.data-v-6e8c2b60 {
	flex: 1;
	padding: 10rpx 0;
}
/* 简化的小组列表项 */
.group-item.data-v-6e8c2b60 {
	margin: 0 10rpx 15rpx;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	text-align: center;
	position: relative;
}
.group-item.active.data-v-6e8c2b60 {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	-webkit-transform: scale(1.05);
	        transform: scale(1.05);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.group-item.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.simple-group-content.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}
.group-level-badge.data-v-6e8c2b60 {
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 700;
	min-width: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.simple-group-name.data-v-6e8c2b60 {
	font-size: 22rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	line-height: 1.3;
	word-break: break-all;
}
.simple-status-dot.data-v-6e8c2b60 {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}
.simple-status-dot.active.data-v-6e8c2b60 {
	background: #4CAF50;
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}
.simple-status-dot.completed.data-v-6e8c2b60 {
	background: #2196F3;
	box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
}
.simple-status-dot.pending.data-v-6e8c2b60 {
	background: #FF9800;
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
}
/* 新概念教程样式 */
.concept-tutorial-item.data-v-6e8c2b60 {
	margin: 0 10rpx 20rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 15rpx;
	padding: 25rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	position: relative;
	overflow: hidden;
}
.concept-tutorial-item.data-v-6e8c2b60::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
	pointer-events: none;
}
.concept-tutorial-item.active.data-v-6e8c2b60 {
	-webkit-transform: scale(1.05);
	        transform: scale(1.05);
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
	border-color: rgba(255, 255, 255, 0.3);
}
.concept-tutorial-item.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.concept-content.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	position: relative;
	z-index: 1;
}
.concept-icon.data-v-6e8c2b60 {
	font-size: 32rpx;
	margin-bottom: 5rpx;
}
.concept-title.data-v-6e8c2b60 {
	font-size: 22rpx;
	font-weight: 600;
	color: white;
	text-align: center;
	line-height: 1.3;
}
.concept-badge.data-v-6e8c2b60 {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 18rpx;
	font-weight: 500;
}
/* 新概念教程详情页面 */
.concept-detail-content.data-v-6e8c2b60 {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.concept-detail-header.data-v-6e8c2b60 {
	position: relative;
	height: 200rpx;
	overflow: hidden;
}
.concept-bg.data-v-6e8c2b60 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
}
.concept-overlay.data-v-6e8c2b60 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.2);
	display: flex;
	align-items: center;
	padding: 30rpx;
}
.concept-detail-icon.data-v-6e8c2b60 {
	font-size: 60rpx;
	margin-right: 20rpx;
}
.concept-detail-info.data-v-6e8c2b60 {
	flex: 1;
	color: white;
}
.concept-detail-title.data-v-6e8c2b60 {
	font-size: 36rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}
.concept-detail-subtitle.data-v-6e8c2b60 {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}
.concept-progress-info.data-v-6e8c2b60 {
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	display: inline-block;
}
.progress-info-text.data-v-6e8c2b60 {
	font-size: 20rpx;
	font-weight: 600;
}
/* 学习分类 */
.concept-categories.data-v-6e8c2b60 {
	padding: 30rpx;
	flex: 1;
}
.categories-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}
/* 小组头部样式 */
.group-header.data-v-6e8c2b60 {
	padding: 20rpx;
	background: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.group-info.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
}
.group-avatar.data-v-6e8c2b60 {
	width: 100rpx;
	height: 100rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-right: 20rpx;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
}
.group-avatar image.data-v-6e8c2b60 {
	width: 100rpx;
	height: 100rpx;
}
.group-details.data-v-6e8c2b60 {
	flex: 1;
}
.group-name.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.group-desc.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 12rpx;
}
.group-stats.data-v-6e8c2b60 {
	display: flex;
	gap: 20rpx;
}
.stat-item.data-v-6e8c2b60 {
	font-size: 20rpx;
	color: #999;
	background: #f8f9fa;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	display: flex;
}
.group-categories.data-v-6e8c2b60 {
	background: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.group-categories .categories-title.data-v-6e8c2b60 {
	padding: 20rpx;
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	background: #f8f9fa;
	border-bottom: 1rpx solid #f0f0f0;
}
/* 吸顶分类导航样式 */
.sticky-nav.data-v-6e8c2b60 {
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 10;
}
.nav-scroll.data-v-6e8c2b60 {
	height: 88rpx;
}
.nav-items.data-v-6e8c2b60 {
	display: flex;
	padding: 0 20rpx;
	white-space: nowrap;
}
.nav-item.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx 24rpx;
	margin-right: 20rpx;
	border-radius: 12rpx;
	transition: all 0.3s ease;
	flex-shrink: 0;
}
.nav-item.active.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}
.nav-icon.data-v-6e8c2b60 {
	font-size: 24rpx;
	margin-bottom: 4rpx;
}
.nav-text.data-v-6e8c2b60 {
	font-size: 20rpx;
	font-weight: 500;
}
.nav-item.active .nav-icon.data-v-6e8c2b60,
.nav-item.active .nav-text.data-v-6e8c2b60 {
	color: #fff;
}
/* 滚动内容区域样式 */
.content-scroll.data-v-6e8c2b60 {
	height: 600rpx; /* 固定高度，可滚动 */
	background: #f8f9fa;
	/* iPad滚动优化 */
	-webkit-overflow-scrolling: touch;
	touch-action: pan-y;
	overscroll-behavior: contain;
}
.scroll-content.data-v-6e8c2b60 {
	padding-bottom: 40rpx;
}
.category-section.data-v-6e8c2b60 {
	margin-bottom: 20rpx;
}
/* 分类标题（吸顶）样式 */
.section-header.data-v-6e8c2b60 {
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 5;
}
.section-header.sticky.data-v-6e8c2b60 {
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header-content.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 30rpx;
}
.header-left.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
}
.header-icon.data-v-6e8c2b60 {
	font-size: 32rpx;
	margin-right: 16rpx;
}
.header-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}
.header-right.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
}
.lesson-count.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #999;
	background: #f0f0f0;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}
/* 课程列表样式 */
.section-lessons.data-v-6e8c2b60 {
	background: #fff;
}
.lesson-card.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid #f8f9fa;
	transition: background-color 0.3s ease;
}
.lesson-card.data-v-6e8c2b60:active {
	background: #f8f9fa;
}
.lesson-card.data-v-6e8c2b60:last-child {
	border-bottom: none;
}
.lesson-left.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
}
.lesson-number.data-v-6e8c2b60 {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
	color: #1890ff;
	font-size: 22rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	flex-shrink: 0;
}
.lesson-info.data-v-6e8c2b60 {
	flex: 1;
	min-width: 0;
}
.lesson-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.lesson-subtitle.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #666;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.lesson-right.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	margin-left: 20rpx;
}
.lesson-status.data-v-6e8c2b60 {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}
.lesson-status.completed.data-v-6e8c2b60 {
	background: #52c41a;
}
.lesson-status.pending.data-v-6e8c2b60 {
	background: #f0f0f0;
	border: 2rpx solid #d9d9d9;
}
.status-icon.data-v-6e8c2b60 {
	font-size: 20rpx;
	font-weight: 600;
}
.lesson-status.completed .status-icon.data-v-6e8c2b60 {
	color: #fff;
}
.lesson-status.pending .status-icon.data-v-6e8c2b60 {
	color: #999;
}
.simple-category-item.data-v-6e8c2b60 {
	background: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.category-header.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: #f8f9fa;
	transition: all 0.3s ease;
}
.category-header.data-v-6e8c2b60:active {
	background: #e9ecef;
}
.category-left.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	flex: 1;
}
.category-icon.data-v-6e8c2b60 {
	font-size: 28rpx;
	margin-right: 12rpx;
}
.category-name.data-v-6e8c2b60 {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
}
.category-toggle.data-v-6e8c2b60 {
	padding: 8rpx;
	transition: -webkit-transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.category-toggle.expanded.data-v-6e8c2b60 {
	-webkit-transform: rotate(0deg);
	        transform: rotate(0deg);
}
.toggle-icon.data-v-6e8c2b60 {
	font-size: 20rpx;
	color: #666;
}
/* 课程列表样式 */
.lessons-list.data-v-6e8c2b60 {
	background: #fff;
}
.lesson-item.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;
}
.lesson-item.data-v-6e8c2b60:last-child {
	border-bottom: none;
}
.lesson-item.data-v-6e8c2b60:active {
	background: #f8f9fa;
}
.lesson-number.data-v-6e8c2b60 {
	width: 40rpx;
	height: 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 20rpx;
	font-weight: 600;
	margin-right: 16rpx;
}
.lesson-content.data-v-6e8c2b60 {
	flex: 1;
}
.lesson-title.data-v-6e8c2b60 {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}
.lesson-subtitle.data-v-6e8c2b60 {
	font-size: 20rpx;
	color: #666;
}
.lesson-status.data-v-6e8c2b60 {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.lesson-status.completed.data-v-6e8c2b60 {
	background: #52c41a;
}
.lesson-status.pending.data-v-6e8c2b60 {
	background: #f0f0f0;
	border: 2rpx solid #d9d9d9;
}
.status-icon.data-v-6e8c2b60 {
	font-size: 16rpx;
	color: #fff;
}
.lesson-status.pending .status-icon.data-v-6e8c2b60 {
	color: #999;
}
/* 快速操作 */
.concept-actions.data-v-6e8c2b60 {
	padding: 0 30rpx 30rpx;
}
.concept-action-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}
.concept-action-buttons.data-v-6e8c2b60 {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}
.concept-btn.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}
.concept-btn.primary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #4CAF50, #45A049);
	color: white;
}
.concept-btn.secondary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}
.concept-btn.tertiary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}
.concept-btn.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.concept-btn-icon.data-v-6e8c2b60 {
	font-size: 32rpx;
	margin-right: 15rpx;
}
.concept-btn-content.data-v-6e8c2b60 {
	flex: 1;
}
.concept-btn-title.data-v-6e8c2b60 {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}
.concept-btn-desc.data-v-6e8c2b60 {
	font-size: 22rpx;
	opacity: 0.9;
}
/* 无权限页面样式 */
.no-permission-page.data-v-6e8c2b60 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.permission-container.data-v-6e8c2b60 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 80rpx 60rpx;
	text-align: center;
	max-width: 600rpx;
	margin: 0 40rpx;
	-webkit-backdrop-filter: blur(20rpx);
	        backdrop-filter: blur(20rpx);
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.permission-icon.data-v-6e8c2b60 {
	font-size: 120rpx;
	margin-bottom: 40rpx;
	opacity: 0.8;
}
.permission-title.data-v-6e8c2b60 {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}
.permission-desc.data-v-6e8c2b60 {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 15rpx;
	display: block;
	line-height: 1.5;
}
.permission-hint.data-v-6e8c2b60 {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 60rpx;
	display: block;
	line-height: 1.5;
}
.permission-actions.data-v-6e8c2b60 {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 30rpx;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-top: 40rpx;
}
/* 小屏幕适配 */
@media screen and (max-width: 750rpx) {
.permission-actions.data-v-6e8c2b60 {
		flex-direction: column;
		gap: 20rpx;
}
.btn-login.data-v-6e8c2b60,
	.btn-contact.data-v-6e8c2b60 {
		width: 280rpx;
		max-width: 100%;
}
}
.btn-login.data-v-6e8c2b60,
.btn-contact.data-v-6e8c2b60 {
	padding: 24rpx 48rpx;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	min-width: 200rpx;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	box-sizing: border-box;
}
.btn-login.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.btn-contact.data-v-6e8c2b60 {
	background: #f8f9fa;
	color: #495057;
	border: 2rpx solid #e9ecef;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.btn-login.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}
.btn-contact.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	background: #e9ecef;
}
/* 右侧面板 */
.right-panel.data-v-6e8c2b60 {
	flex: 1;
	background: white;
	overflow-y: auto;
}
.detail-content.data-v-6e8c2b60 {
	height: 100%;
	display: flex;
	flex-direction: column;
}
/* 详情头部 */
.detail-header.data-v-6e8c2b60 {
	position: relative;
	height: 200rpx;
	overflow: hidden;
}
.detail-bg.data-v-6e8c2b60 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.detail-overlay.data-v-6e8c2b60 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	padding: 30rpx;
}
.detail-avatar.data-v-6e8c2b60 {
	width: 80rpx;
	height: 80rpx;
	margin-right: 20rpx;
}
.detail-avatar image.data-v-6e8c2b60 {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
}
.detail-info.data-v-6e8c2b60 {
	flex: 1;
	color: white;
}
.detail-title.data-v-6e8c2b60 {
	font-size: 36rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}
.detail-subtitle.data-v-6e8c2b60 {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}
.detail-level.data-v-6e8c2b60 {
	background: rgba(255, 255, 255, 0.2);
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
	font-weight: 600;
	display: inline-block;
}
/* 统计卡片 */
.detail-stats.data-v-6e8c2b60 {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}
.stat-card-detail.data-v-6e8c2b60 {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
}
.stat-card-detail.data-v-6e8c2b60:active {
	background: #e9ecef;
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.stat-icon-detail.data-v-6e8c2b60 {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}
.stat-number-detail.data-v-6e8c2b60 {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.stat-label-detail.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
}
/* 进度详情 */
.progress-detail.data-v-6e8c2b60 {
	padding: 0 30rpx 30rpx;
}
.progress-header.data-v-6e8c2b60 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}
.progress-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}
.progress-value.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 700;
	color: #667eea;
}
.progress-bar-detail.data-v-6e8c2b60 {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 15rpx;
}
.progress-fill-detail.data-v-6e8c2b60 {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.progress-desc.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
	text-align: center;
}
/* 功能按钮 */
.detail-actions.data-v-6e8c2b60 {
	flex: 1;
	padding: 0 30rpx 30rpx;
}
.action-row.data-v-6e8c2b60 {
	margin-bottom: 15rpx;
}
.action-btn-large.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	padding: 25rpx;
	border-radius: 20rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}
.action-btn-large.primary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.action-btn-large.secondary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #4ECDC4, #44A08D);
	color: white;
}
.action-btn-large.tertiary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #45B7D1, #96C93D);
	color: white;
}
.action-btn-large.quaternary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #FFA726, #FB8C00);
	color: white;
}
.action-btn-large.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.btn-icon-large.data-v-6e8c2b60 {
	font-size: 40rpx;
	margin-right: 20rpx;
}
.btn-content.data-v-6e8c2b60 {
	flex: 1;
}
.btn-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}
.btn-desc.data-v-6e8c2b60 {
	font-size: 22rpx;
	opacity: 0.9;
}
.btn-arrow-large.data-v-6e8c2b60 {
	font-size: 24rpx;
	opacity: 0.8;
}
/* 空状态 */
.empty-detail.data-v-6e8c2b60 {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
	text-align: center;
}
.empty-icon.data-v-6e8c2b60 {
	font-size: 80rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}
.empty-title.data-v-6e8c2b60 {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 15rpx;
}
.empty-desc.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #999;
	line-height: 1.5;
}
/* 美化的小组卡片 */
.beautiful-group-card.data-v-6e8c2b60 {
	position: relative;
	background: white;
	border-radius: 25rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
}
.beautiful-group-card.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.beautiful-group-card.selected.data-v-6e8c2b60 {
	-webkit-transform: translateY(-8rpx);
	        transform: translateY(-8rpx);
	box-shadow: 0 20rpx 40rpx rgba(102, 126, 234, 0.2);
}
.card-decoration.data-v-6e8c2b60 {
	position: absolute;
	top: 0;
	right: 0;
	width: 100rpx;
	height: 100rpx;
	border-radius: 0 25rpx 0 100rpx;
	opacity: 0.1;
}
/* 美化的小组头部 */
.beautiful-group-header.data-v-6e8c2b60 {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 25rpx;
}
.group-avatar-section.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	flex: 1;
}
.avatar-container.data-v-6e8c2b60 {
	position: relative;
	margin-right: 20rpx;
}
.group-avatar-img.data-v-6e8c2b60 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
}
.level-badge.data-v-6e8c2b60 {
	position: absolute;
	bottom: -8rpx;
	right: -8rpx;
	color: white;
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}
.group-basic-info.data-v-6e8c2b60 {
	flex: 1;
}
.group-title.data-v-6e8c2b60 {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.3;
	margin-bottom: 8rpx;
}
.group-subtitle.data-v-6e8c2b60 {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}
.status-indicator.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: #f8f9fa;
}
.status-dot.data-v-6e8c2b60 {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}
.status-indicator.active .status-dot.data-v-6e8c2b60 {
	background: #4CAF50;
}
.status-indicator.completed .status-dot.data-v-6e8c2b60 {
	background: #2196F3;
}
.status-indicator.pending .status-dot.data-v-6e8c2b60 {
	background: #FF9800;
}
.status-text.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}
/* 美化的统计数据 */
.beautiful-stats.data-v-6e8c2b60 {
	display: flex;
	gap: 15rpx;
	margin-bottom: 25rpx;
}
.stat-card.data-v-6e8c2b60 {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx 15rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}
.stat-card.data-v-6e8c2b60:active {
	background: #e9ecef;
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.stat-icon.data-v-6e8c2b60 {
	font-size: 32rpx;
	margin-right: 12rpx;
}
.stat-info.data-v-6e8c2b60 {
	flex: 1;
}
.stat-value.data-v-6e8c2b60 {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	line-height: 1;
}
.stat-name.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #999;
	margin-top: 4rpx;
}
/* 美化的进度条 */
.beautiful-progress.data-v-6e8c2b60 {
	margin-bottom: 25rpx;
}
.progress-label.data-v-6e8c2b60 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}
.progress-text.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}
.progress-percent.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #333;
	font-weight: 600;
}
.progress-track.data-v-6e8c2b60 {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}
.progress-bar-fill.data-v-6e8c2b60 {
	height: 100%;
	border-radius: 4rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
/* 美化的操作按钮 */
.beautiful-actions.data-v-6e8c2b60 {
	display: flex;
	gap: 15rpx;
}
.action-button.data-v-6e8c2b60 {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 18rpx 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}
.action-button.primary.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.action-button.secondary.data-v-6e8c2b60 {
	background: #f8f9fa;
	color: #333;
	border: 2rpx solid #e9ecef;
}
.action-button.data-v-6e8c2b60:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.action-button.primary.data-v-6e8c2b60:active {
	background: linear-gradient(135deg, #5a6fd8, #6a42a0);
}
.action-button.secondary.data-v-6e8c2b60:active {
	background: #e9ecef;
}
.btn-icon.data-v-6e8c2b60 {
	font-size: 24rpx;
}
.btn-text.data-v-6e8c2b60 {
	flex: 1;
	text-align: center;
	font-size: 26rpx;
	font-weight: 500;
}
.btn-arrow.data-v-6e8c2b60 {
	font-size: 20rpx;
	opacity: 0.8;
}
/* 操作提示样式 */
.operation-tips.data-v-6e8c2b60 {
	padding: 20rpx;
	margin-top: 20rpx;
}
.tips-card.data-v-6e8c2b60 {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
}
.tips-icon.data-v-6e8c2b60 {
	font-size: 40rpx;
	margin-right: 20rpx;
}
.tips-content.data-v-6e8c2b60 {
	flex: 1;
}
.tips-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}
.tips-text.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}
.group-avatar.data-v-6e8c2b60 {
	position: relative;
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	overflow: hidden;
}
.group-level.data-v-6e8c2b60 {
	position: absolute;
	bottom: -10rpx;
	right: -10rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 5rpx 10rpx;
	border-radius: 10rpx;
	font-size: 20rpx;
	font-weight: 600;
}
.group-status-badge.data-v-6e8c2b60 {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
}
.stat-number.data-v-6e8c2b60 {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}
.stat-label.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #999;
}
.progress-bar.data-v-6e8c2b60 {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin: 15rpx 0;
}
.progress-fill.data-v-6e8c2b60 {
	height: 100%;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 4rpx;
	transition: width 0.3s ease;
}
.group-actions.data-v-6e8c2b60 {
	display: flex;
	gap: 15rpx;
	margin-top: 20rpx;
}
.action-btn.data-v-6e8c2b60 {
	flex: 1;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 15rpx;
	text-align: center;
	transition: all 0.3s ease;
}
.action-icon.data-v-6e8c2b60 {
	display: block;
	font-size: 24rpx;
	margin-bottom: 5rpx;
}
.action-text.data-v-6e8c2b60 {
	font-size: 22rpx;
	color: #666;
}
.selected-group-content.data-v-6e8c2b60 {
	background: white;
	margin: 30rpx 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}
.content-header.data-v-6e8c2b60 {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.content-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
}
.view-toggle.data-v-6e8c2b60 {
	display: flex;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	overflow: hidden;
}
.toggle-btn.data-v-6e8c2b60 {
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	transition: all 0.3s ease;
}
.toggle-btn.active.data-v-6e8c2b60 {
	background: white;
	color: #667eea;
}
.date-filter.data-v-6e8c2b60 {
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.date-picker.data-v-6e8c2b60 {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 15rpx 20rpx;
}
.date-icon.data-v-6e8c2b60 {
	font-size: 24rpx;
	margin-right: 10rpx;
}
.date-text.data-v-6e8c2b60 {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}
.date-arrow.data-v-6e8c2b60 {
	font-size: 20rpx;
	color: #999;
}
.course-list.data-v-6e8c2b60 {
	padding: 20rpx 30rpx;
}
.course-item.data-v-6e8c2b60 {
	display: flex;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	transition: all 0.3s ease;
}
.course-thumbnail.data-v-6e8c2b60 {
	position: relative;
	width: 160rpx;
	height: 120rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-right: 20rpx;
}
.play-overlay.data-v-6e8c2b60 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.play-icon.data-v-6e8c2b60 {
	color: white;
	font-size: 24rpx;
}
.course-duration.data-v-6e8c2b60 {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	font-size: 20rpx;
}
.course-details.data-v-6e8c2b60 {
	flex: 1;
}
.course-title.data-v-6e8c2b60 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}
.course-date.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}
.course-teacher.data-v-6e8c2b60 {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}
.course-tags.data-v-6e8c2b60 {
	display: flex;
	gap: 8rpx;
}
.tag.data-v-6e8c2b60 {
	background: #667eea;
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 20rpx;
}
/* 自定义tabBar适配 */
.gui-page.data-v-6e8c2b60 {
	padding-bottom: 120rpx; /* 为自定义tabBar留出空间 */
}

