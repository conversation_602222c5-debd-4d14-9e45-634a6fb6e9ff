const express = require('express');
const router = express.Router();

// 模拟页面数据
let pages = [
  {
    id: 1,
    name: '首页',
    path: '/home',
    type: 'home',
    status: 'published',
    visitCount: 1250,
    creatorName: '管理员',
    updatedAt: new Date(),
    createdAt: new Date(),
    description: '小程序首页',
    config: JSON.stringify({
      banner: true,
      categories: true,
      courses: true
    })
  },
  {
    id: 2,
    name: '课程分类',
    path: '/category',
    type: 'category',
    status: 'published',
    visitCount: 890,
    creatorName: '管理员',
    updatedAt: new Date(),
    createdAt: new Date(),
    description: '课程分类页面',
    config: JSON.stringify({
      layout: 'grid',
      columns: 2
    })
  },
  {
    id: 3,
    name: '个人中心',
    path: '/profile',
    type: 'profile',
    status: 'draft',
    visitCount: 0,
    creatorName: '开发者',
    updatedAt: new Date(),
    createdAt: new Date(),
    description: '用户个人中心页面',
    config: JSON.stringify({
      showAvatar: true,
      showStats: true
    })
  },
  {
    id: 4,
    name: '课程详情',
    path: '/course/:id',
    type: 'course',
    status: 'published',
    visitCount: 2340,
    creatorName: '管理员',
    updatedAt: new Date(),
    createdAt: new Date(),
    description: '课程详情页面',
    config: JSON.stringify({
      showComments: true,
      showRelated: true
    })
  },
  {
    id: 5,
    name: '关于我们',
    path: '/about',
    type: 'custom',
    status: 'offline',
    visitCount: 156,
    creatorName: '编辑',
    updatedAt: new Date(),
    createdAt: new Date(),
    description: '关于我们页面',
    config: JSON.stringify({
      showContact: true,
      showHistory: true
    })
  }
];

let nextId = 6;

// 获取页面列表
router.get('/', (req, res) => {
  try {
    const { page = 1, limit = 20, name, type, status } = req.query;
    
    let filteredPages = [...pages];
    
    // 筛选
    if (name) {
      filteredPages = filteredPages.filter(p => 
        p.name.includes(name)
      );
    }
    
    if (type) {
      filteredPages = filteredPages.filter(p => p.type === type);
    }
    
    if (status) {
      filteredPages = filteredPages.filter(p => p.status === status);
    }
    
    // 按更新时间倒序排序
    filteredPages.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedPages = filteredPages.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        pages: paginatedPages,
        total: filteredPages.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取页面列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取页面列表失败'
    });
  }
});

// 获取单个页面
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const page = pages.find(p => p.id === parseInt(id));
    
    if (!page) {
      return res.status(404).json({
        success: false,
        message: '页面不存在'
      });
    }
    
    res.json({
      success: true,
      data: { page }
    });
  } catch (error) {
    console.error('获取页面失败:', error);
    res.status(500).json({
      success: false,
      message: '获取页面失败'
    });
  }
});

// 创建页面
router.post('/', (req, res) => {
  try {
    const { name, path, type, description, config = '{}', status = 'draft' } = req.body;
    
    // 验证必填字段
    if (!name || !path || !type) {
      return res.status(400).json({
        success: false,
        message: '页面名称、路径和类型不能为空'
      });
    }
    
    // 检查路径是否已存在
    const existingPage = pages.find(p => p.path === path);
    if (existingPage) {
      return res.status(400).json({
        success: false,
        message: '页面路径已存在'
      });
    }
    
    // 验证JSON格式
    try {
      JSON.parse(config);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: '页面配置必须是有效的JSON格式'
      });
    }
    
    const newPage = {
      id: nextId++,
      name,
      path,
      type,
      description: description || '',
      config,
      status,
      visitCount: 0,
      creatorName: '管理员', // 这里应该从认证信息中获取
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    pages.push(newPage);
    
    res.status(201).json({
      success: true,
      data: { page: newPage },
      message: '页面创建成功'
    });
  } catch (error) {
    console.error('创建页面失败:', error);
    res.status(500).json({
      success: false,
      message: '创建页面失败'
    });
  }
});

// 更新页面
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, path, type, description, config, status } = req.body;
    
    const pageIndex = pages.findIndex(p => p.id === parseInt(id));
    if (pageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '页面不存在'
      });
    }
    
    // 检查路径是否已被其他页面使用
    if (path) {
      const existingPage = pages.find(p => p.path === path && p.id !== parseInt(id));
      if (existingPage) {
        return res.status(400).json({
          success: false,
          message: '页面路径已存在'
        });
      }
    }
    
    // 验证JSON格式
    if (config) {
      try {
        JSON.parse(config);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: '页面配置必须是有效的JSON格式'
        });
      }
    }
    
    // 更新页面信息
    const updatedPage = {
      ...pages[pageIndex],
      ...(name && { name }),
      ...(path && { path }),
      ...(type && { type }),
      ...(description !== undefined && { description }),
      ...(config && { config }),
      ...(status && { status }),
      updatedAt: new Date()
    };
    
    pages[pageIndex] = updatedPage;
    
    res.json({
      success: true,
      data: { page: updatedPage },
      message: '页面更新成功'
    });
  } catch (error) {
    console.error('更新页面失败:', error);
    res.status(500).json({
      success: false,
      message: '更新页面失败'
    });
  }
});

// 删除页面
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    const pageIndex = pages.findIndex(p => p.id === parseInt(id));
    if (pageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '页面不存在'
      });
    }
    
    pages.splice(pageIndex, 1);
    
    res.json({
      success: true,
      message: '页面删除成功'
    });
  } catch (error) {
    console.error('删除页面失败:', error);
    res.status(500).json({
      success: false,
      message: '删除页面失败'
    });
  }
});

// 复制页面
router.post('/:id/copy', (req, res) => {
  try {
    const { id } = req.params;
    const page = pages.find(p => p.id === parseInt(id));
    
    if (!page) {
      return res.status(404).json({
        success: false,
        message: '页面不存在'
      });
    }
    
    const newPage = {
      ...page,
      id: nextId++,
      name: `${page.name} - 副本`,
      path: `${page.path}-copy`,
      status: 'draft',
      visitCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    pages.push(newPage);
    
    res.status(201).json({
      success: true,
      data: { page: newPage },
      message: '页面复制成功'
    });
  } catch (error) {
    console.error('复制页面失败:', error);
    res.status(500).json({
      success: false,
      message: '复制页面失败'
    });
  }
});

// 更新页面状态
router.put('/:id/status', (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    const pageIndex = pages.findIndex(p => p.id === parseInt(id));
    if (pageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '页面不存在'
      });
    }
    
    pages[pageIndex] = {
      ...pages[pageIndex],
      status,
      updatedAt: new Date()
    };
    
    res.json({
      success: true,
      data: { page: pages[pageIndex] },
      message: '页面状态更新成功'
    });
  } catch (error) {
    console.error('更新页面状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新页面状态失败'
    });
  }
});

module.exports = router;
