(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/groups/lesson-player"],{"10cc":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},i=[]},"885d":function(t,e,n){},cc0b:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{statusBarHeight:0,lessonInfo:{id:1,title:"第一课",subtitle:"基础问候语",categoryName:"口语",lessonNumber:1,type:"video",mediaUrl:"",poster:"/static/imgs/lesson-default.png",duration:300,completed:!1,viewCount:128,likeCount:23},isPlaying:!1,showOverlay:!0,currentTime:0,totalTime:0,playbackRate:1,isFavorite:!1,showMenuPopup:!1,notes:[],relatedLessons:[{id:2,title:"第二课",subtitle:"自我介绍",poster:"/static/imgs/lesson-2.png"},{id:3,title:"第三课",subtitle:"日常对话",poster:"/static/imgs/lesson-3.png"}]}},computed:{progressPercent:function(){return 0===this.totalTime?0:this.currentTime/this.totalTime*100}},onLoad:function(e){var n=t.getSystemInfoSync();this.statusBarHeight=n.statusBarHeight,e.lessonId&&this.loadLessonInfo(e.lessonId),e.categoryId&&(this.lessonInfo.categoryName=e.categoryName||"未知分类")},methods:{goBack:function(){t.navigateBack()},showMenu:function(){this.showMenuPopup=!0},hideMenu:function(){this.showMenuPopup=!1},loadLessonInfo:function(t){var e=this;console.log("加载课程信息:",t),this.$http.get("v1/course/groups_course_detail",{params:{id:t}}).then((function(t){0==t.data.code&&(e.lessonInfo=t.data.data)}))},onPlay:function(){this.isPlaying=!0,this.showOverlay=!1},onPause:function(){this.isPlaying=!1,this.showOverlay=!0},onEnded:function(){this.isPlaying=!1,this.showOverlay=!0,this.markComplete()},onTimeUpdate:function(t){this.currentTime=t.detail.currentTime,this.totalTime=t.detail.duration},onError:function(e){console.error("视频播放错误:",e),t.showToast({title:"播放失败",icon:"none"})},onFullscreenChange:function(t){console.log("全屏状态改变:",t.detail.fullScreen)},onAudioPlay:function(){this.isPlaying=!0},onAudioPause:function(){this.isPlaying=!1},onAudioEnded:function(){this.isPlaying=!1,this.markComplete()},onAudioTimeUpdate:function(t){this.currentTime=t.detail.currentTime,this.totalTime=t.detail.duration},onAudioError:function(e){console.error("音频播放错误:",e),t.showToast({title:"播放失败",icon:"none"})},togglePlay:function(){var e=t.createVideoContext("lessonVideo",this);this.isPlaying?e.pause():e.play()},toggleAudioPlay:function(){var e=t.createInnerAudioContext();e.src=this.lessonInfo.mediaUrl,this.isPlaying?e.pause():e.play()},changeSpeed:function(){var e=[.5,.75,1,1.25,1.5,2],n=e.indexOf(this.playbackRate),o=(n+1)%e.length;if(this.playbackRate=e[o],"video"===this.lessonInfo.type){var i=t.createVideoContext("lessonVideo",this);i.playbackRate(this.playbackRate)}},formatTime:function(t){if(!t)return"00:00";var e=Math.floor(t/60),n=Math.floor(t%60);return"".concat(e.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"))},addNote:function(){var e=this;t.showModal({title:"添加学习笔记",editable:!0,placeholderText:"请输入学习笔记...",success:function(n){n.confirm&&n.content&&(e.notes.push({time:e.currentTime,content:n.content,timestamp:(new Date).getTime()}),t.showToast({title:"笔记添加成功",icon:"success"}))}})},toggleFavorite:function(){this.isFavorite=!this.isFavorite,t.showToast({title:this.isFavorite?"已收藏":"已取消收藏",icon:"success"})},shareLesson:function(){t.showActionSheet({itemList:["分享到微信","分享到朋友圈","复制链接"],success:function(e){t.showToast({title:"分享到".concat(["微信","朋友圈","复制链接"][e.tapIndex]),icon:"success"})}})},downloadLesson:function(){t.showModal({title:"下载课程",content:"是否下载此课程到本地？",success:function(e){e.confirm&&t.showToast({title:"开始下载",icon:"success"})}})},markComplete:function(){this.lessonInfo.completed=!0,t.showToast({title:"课程已完成",icon:"success"})},playRelated:function(e){t.navigateTo({url:"/pages/groups/lesson-player?lessonId=".concat(e.id)})},adjustSpeed:function(){this.hideMenu(),this.changeSpeed()},adjustQuality:function(){this.hideMenu(),t.showActionSheet({itemList:["自动","高清","标清","流畅"],success:function(e){t.showToast({title:"已切换到".concat(["自动","高清","标清","流畅"][e.tapIndex]),icon:"success"})}})},reportIssue:function(){this.hideMenu(),t.showModal({title:"反馈问题",content:"请描述您遇到的问题",editable:!0,success:function(e){e.confirm&&t.showToast({title:"反馈已提交",icon:"success"})}})}}};e.default=n}).call(this,n("df3c")["default"])},d734:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("5788");o(n("3240"));var i=o(n("f166"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},d92a:function(t,e,n){"use strict";n.r(e);var o=n("cc0b"),i=n.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},ed32:function(t,e,n){"use strict";var o=n("885d"),i=n.n(o);i.a},f166:function(t,e,n){"use strict";n.r(e);var o=n("10cc"),i=n("d92a");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("ed32");var a=n("828b"),c=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,"335db27b",null,!1,o["a"],void 0);e["default"]=c.exports}},[["d734","common/runtime","common/vendor"]]]);