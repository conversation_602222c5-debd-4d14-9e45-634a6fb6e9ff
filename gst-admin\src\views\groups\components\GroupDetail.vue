<template>
  <el-dialog
    v-model="dialogVisible"
    title="小组详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="group-detail" v-if="group">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>小组名称</label>
            <span>{{ group.name }}</span>
          </div>
          <div class="info-item">
            <label>等级</label>
            <el-tag :type="getLevelType(group.level)">{{ group.level }}</el-tag>
          </div>
          <div class="info-item">
            <label>状态</label>
            <el-tag :type="getStatusType(group.status)">{{ getStatusText(group.status) }}</el-tag>
          </div>
          <div class="info-item">
            <label>成员数量</label>
            <span>{{ group.currentMembers || 0 }}/{{ group.maxMembers || 30 }}人</span>
          </div>
          <div class="info-item">
            <label>指导老师</label>
            <span>{{ group.teacher?.realName || group.teacher?.username || '未分配' }}</span>
          </div>
          <div class="info-item">
            <label>创建时间</label>
            <span>{{ formatDateTime(group.createdAt) }}</span>
          </div>
        </div>
      </div>

      <!-- 描述信息 -->
      <div class="detail-section" v-if="group.description">
        <h3 class="section-title">小组描述</h3>
        <p class="description-text">{{ group.description }}</p>
      </div>

      <!-- 学习目标 -->
      <div class="detail-section" v-if="group.goals">
        <h3 class="section-title">学习目标</h3>
        <p class="description-text">{{ group.goals }}</p>
      </div>

      <!-- 成员列表 -->
      <div class="detail-section">
        <h3 class="section-title">
          成员列表
          <el-button 
            type="text" 
            size="small" 
            @click="loadMembers"
            :loading="membersLoading"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </h3>
        
        <div class="members-list" v-loading="membersLoading">
          <div 
            class="member-item" 
            v-for="member in members" 
            :key="member.id"
          >
            <el-avatar :size="40" :src="member.avatar">
              {{ member.realName?.charAt(0) || member.username?.charAt(0) }}
            </el-avatar>
            <div class="member-info">
              <div class="member-name">{{ member.realName || member.username }}</div>
              <div class="member-meta">
                <span class="member-role">{{ getRoleText(member.role) }}</span>
                <span class="member-join-time">{{ formatDate(member.joinedAt) }}加入</span>
              </div>
            </div>
            <div class="member-actions" v-if="authStore.hasRole(['admin', 'teacher'])">
              <el-button 
                type="text" 
                size="small" 
                @click="removeMember(member)"
                :disabled="member.role === 'teacher'"
              >
                移除
              </el-button>
            </div>
          </div>
          
          <el-empty 
            v-if="!membersLoading && members.length === 0" 
            description="暂无成员"
            :image-size="80"
          />
        </div>
      </div>

      <!-- 学习统计 -->
      <div class="detail-section">
        <h3 class="section-title">学习统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalCourses || 0 }}</div>
            <div class="stat-label">课程数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.completedCourses || 0 }}</div>
            <div class="stat-label">已完成课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalAssignments || 0 }}</div>
            <div class="stat-label">作业数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ Math.round(stats.avgProgress || 0) }}%</div>
            <div class="stat-label">平均进度</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="editGroup"
          v-if="authStore.hasRole(['admin', 'teacher'])"
        >
          编辑小组
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit'])

const authStore = useAuthStore()

// 响应式数据
const membersLoading = ref(false)
const members = ref([])
const stats = ref({})

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 加载成员列表
const loadMembers = async () => {
  if (!props.group?.id) return
  
  membersLoading.value = true
  try {
    const response = await get(`/api/groups/${props.group.id}/members`)
    if (response.success) {
      members.value = response.data.members || []
    }
  } catch (error) {
    console.error('加载成员列表失败:', error)
    // 使用模拟数据
    members.value = [
      {
        id: 1,
        username: 'student1',
        realName: '张三',
        role: 'student',
        avatar: '',
        joinedAt: new Date('2024-01-20')
      },
      {
        id: 2,
        username: 'student2',
        realName: '李四',
        role: 'student',
        avatar: '',
        joinedAt: new Date('2024-01-22')
      }
    ]
  } finally {
    membersLoading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  if (!props.group?.id) return
  
  try {
    const response = await get(`/api/groups/${props.group.id}/stats`)
    if (response.success) {
      stats.value = response.data || {}
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用模拟数据
    stats.value = {
      totalCourses: 12,
      completedCourses: 8,
      totalAssignments: 25,
      avgProgress: 67
    }
  }
}

// 移除成员
const removeMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${member.realName || member.username}"移出小组吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API
    ElMessage.success('移除成功')
    loadMembers()
  } catch {
    // 用户取消
  }
}

// 编辑小组
const editGroup = () => {
  emit('edit', props.group)
  dialogVisible.value = false
}

// 获取等级类型
const getLevelType = (level) => {
  const types = {
    N5: 'success',
    N4: 'primary',
    N3: 'warning',
    N2: 'danger',
    N1: 'info'
  }
  return types[level] || 'info'
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    active: 'success',
    completed: 'info',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    active: '活跃',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

// 获取角色文本
const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    teacher: '教师',
    student: '学生'
  }
  return texts[role] || '未知'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.group) {
    loadMembers()
    loadStats()
  }
})
</script>

<style lang="scss" scoped>
.group-detail {
  .detail-section {
    margin-bottom: var(--spacing-xl);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-medium);
      font-weight: 600;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    
    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      
      label {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
        font-weight: 500;
      }
      
      span {
        font-size: var(--font-size-base);
        color: var(--text-primary);
      }
    }
  }
  
  .description-text {
    margin: 0;
    line-height: 1.6;
    color: var(--text-regular);
    background: var(--bg-color-page);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-base);
    border-left: 4px solid var(--primary-color);
  }
  
  .members-list {
    max-height: 300px;
    overflow-y: auto;
    
    .member-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-radius: var(--border-radius-base);
      transition: background-color 0.3s ease;
      
      &:hover {
        background: var(--bg-color-page);
      }
      
      .member-info {
        flex: 1;
        
        .member-name {
          font-size: var(--font-size-base);
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .member-meta {
          display: flex;
          gap: var(--spacing-md);
          font-size: var(--font-size-small);
          color: var(--text-secondary);
        }
      }
      
      .member-actions {
        .el-button {
          color: var(--danger-color);
          
          &:hover {
            background: var(--danger-color);
            color: white;
          }
        }
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-lg);
    
    .stat-item {
      text-align: center;
      padding: var(--spacing-lg);
      background: var(--bg-color-page);
      border-radius: var(--border-radius-base);
      
      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: var(--spacing-xs);
      }
      
      .stat-label {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}
</style>
