/**
 * Excel模板生成工具
 */

// 模板数据定义
const templateData = {
  categories: {
    headers: ['ID', '分类名称', '分类描述', '排序', '状态'],
    fields: ['id', 'name', 'description', 'sort_order', 'status'],
    sampleData: [
      [1, 'N5语法', 'N5级别语法课程分类', 1, 'active'],
      [2, 'N5词汇', 'N5级别词汇课程分类', 2, 'active'],
      [3, 'N5听力', 'N5级别听力课程分类', 3, 'active'],
      [4, 'N5阅读', 'N5级别阅读课程分类', 4, 'active'],
      [5, 'N5口语', 'N5级别口语课程分类', 5, 'active']
    ],
    notes: [
      '字段说明：',
      '• ID: 分类唯一标识符（数字）',
      '• 分类名称: 课程分类的名称（必填，最长50字符）',
      '• 分类描述: 分类的详细描述（可选，最长200字符）',
      '• 排序: 显示顺序（数字，越小越靠前）',
      '• 状态: active=启用, inactive=禁用',
      '',
      '注意事项：',
      '• 请保持表头不变',
      '• ID可以为空，系统会自动生成',
      '• 分类名称不能重复',
      '• 排序值建议使用10的倍数，便于后续插入'
    ]
  },
  
  courses: {
    headers: ['ID', '课程标题', '分类ID', '课程描述', '难度等级', '时长(分钟)', '状态'],
    fields: ['id', 'title', 'category_id', 'description', 'level', 'duration', 'status'],
    sampleData: [
      [1, '五十音图学习', 1, '学习日语基础的五十音图，包括平假名和片假名', 'N5', 60, 'published'],
      [2, '基础问候语', 2, '学习日常基础问候用语', 'N5', 45, 'published'],
      [3, '数字表达', 2, '学习日语中的数字表达方法', 'N5', 30, 'published'],
      [4, '时间表达', 1, '学习时间相关的语法和词汇', 'N5', 50, 'draft'],
      [5, '自我介绍', 2, '学习如何用日语进行自我介绍', 'N5', 40, 'published']
    ],
    notes: [
      '字段说明：',
      '• ID: 课程唯一标识符（数字）',
      '• 课程标题: 课程的名称（必填，最长100字符）',
      '• 分类ID: 对应课程分类表的ID（必填，数字）',
      '• 课程描述: 课程的详细描述（可选，最长500字符）',
      '• 难度等级: N5, N4, N3, N2, N1',
      '• 时长: 课程时长，单位为分钟（数字）',
      '• 状态: draft=草稿, published=已发布, archived=已下架',
      '',
      '注意事项：',
      '• 请保持表头不变',
      '• ID可以为空，系统会自动生成',
      '• 分类ID必须在课程分类表中存在',
      '• 课程标题不能重复',
      '• 难度等级必须是有效值'
    ]
  },
  
  course_units: {
    headers: ['ID', '课程ID', '单元标题', '单元内容', '排序', '单元类型', '时长(分钟)'],
    fields: ['id', 'course_id', 'title', 'content', 'sort_order', 'type', 'duration'],
    sampleData: [
      [1, 1, '平假名あ行', '学习あ、い、う、え、お的发音和写法', 1, 'video', 15],
      [2, 1, '平假名か行', '学习か、き、く、け、こ的发音和写法', 2, 'video', 15],
      [3, 1, '平假名练习', 'あ行和か行的综合练习', 3, 'exercise', 10],
      [4, 2, '基本问候', '学习おはよう、こんにちは等基本问候语', 1, 'text', 20],
      [5, 2, '问候练习', '问候语的听力和口语练习', 2, 'audio', 15]
    ],
    notes: [
      '字段说明：',
      '• ID: 单元唯一标识符（数字）',
      '• 课程ID: 对应课程表的ID（必填，数字）',
      '• 单元标题: 单元的名称（必填，最长100字符）',
      '• 单元内容: 单元的详细内容（可选，最长2000字符）',
      '• 排序: 在课程中的显示顺序（数字，越小越靠前）',
      '• 单元类型: video=视频, audio=音频, text=文本, exercise=练习',
      '• 时长: 单元学习时长，单位为分钟（数字）',
      '',
      '注意事项：',
      '• 请保持表头不变',
      '• ID可以为空，系统会自动生成',
      '• 课程ID必须在课程表中存在',
      '• 同一课程内的单元标题不能重复',
      '• 排序值建议使用10的倍数'
    ]
  }
}

/**
 * 生成CSV格式的模板内容
 * @param {string} type 模板类型
 * @returns {string} CSV内容
 */
export function generateCSVTemplate(type) {
  const template = templateData[type]
  if (!template) {
    throw new Error(`未知的模板类型: ${type}`)
  }
  
  const lines = []
  
  // 添加表头
  lines.push(template.headers.join(','))
  
  // 添加示例数据
  template.sampleData.forEach(row => {
    const csvRow = row.map(cell => {
      // 如果包含逗号或引号，需要用引号包围并转义
      if (typeof cell === 'string' && (cell.includes(',') || cell.includes('"'))) {
        return `"${cell.replace(/"/g, '""')}"`
      }
      return cell
    })
    lines.push(csvRow.join(','))
  })
  
  // 添加空行和说明
  lines.push('')
  lines.push('=== 字段说明和注意事项 ===')
  template.notes.forEach(note => {
    lines.push(`"${note}"`)
  })
  
  return lines.join('\n')
}

/**
 * 下载模板文件
 * @param {string} type 模板类型
 * @param {string} format 文件格式 (csv)
 */
export function downloadTemplate(type, format = 'csv') {
  const template = templateData[type]
  if (!template) {
    throw new Error(`未知的模板类型: ${type}`)
  }
  
  let content = ''
  let mimeType = ''
  let extension = ''
  
  switch (format.toLowerCase()) {
    case 'csv':
      content = generateCSVTemplate(type)
      mimeType = 'text/csv;charset=utf-8'
      extension = 'csv'
      break
    default:
      throw new Error(`不支持的文件格式: ${format}`)
  }
  
  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + content], { type: mimeType })
  
  // 创建下载链接
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${getTemplateTitle(type)}_导入模板.${extension}`
  
  // 触发下载
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // 清理URL
  URL.revokeObjectURL(url)
}

/**
 * 获取模板标题
 * @param {string} type 模板类型
 * @returns {string} 模板标题
 */
function getTemplateTitle(type) {
  const titles = {
    categories: '课程分类表',
    courses: '课程表',
    course_units: '课程单元表'
  }
  return titles[type] || type
}

/**
 * 验证导入数据
 * @param {string} type 模板类型
 * @param {Array} data 导入的数据
 * @returns {Object} 验证结果
 */
export function validateImportData(type, data) {
  const template = templateData[type]
  if (!template) {
    return { valid: false, errors: [`未知的模板类型: ${type}`] }
  }
  
  const errors = []
  const warnings = []
  
  // 检查数据格式
  if (!Array.isArray(data) || data.length === 0) {
    errors.push('导入数据为空或格式不正确')
    return { valid: false, errors, warnings }
  }
  
  // 检查表头
  const headers = data[0]
  if (!Array.isArray(headers)) {
    errors.push('表头格式不正确')
    return { valid: false, errors, warnings }
  }
  
  // 验证表头字段
  const expectedHeaders = template.headers
  const missingHeaders = expectedHeaders.filter(header => !headers.includes(header))
  if (missingHeaders.length > 0) {
    errors.push(`缺少必要的表头字段: ${missingHeaders.join(', ')}`)
  }
  
  // 验证数据行
  for (let i = 1; i < data.length; i++) {
    const row = data[i]
    if (!Array.isArray(row)) continue
    
    // 检查必填字段
    const requiredFields = getRequiredFields(type)
    requiredFields.forEach((fieldIndex) => {
      if (!row[fieldIndex] || row[fieldIndex].toString().trim() === '') {
        errors.push(`第${i + 1}行缺少必填字段: ${expectedHeaders[fieldIndex]}`)
      }
    })
    
    // 类型特定的验证
    validateRowData(type, row, i + 1, errors, warnings)
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 获取必填字段索引
 * @param {string} type 模板类型
 * @returns {Array} 必填字段索引数组
 */
function getRequiredFields(type) {
  const requiredFields = {
    categories: [1], // name
    courses: [1, 2], // title, category_id
    course_units: [1, 2] // course_id, title
  }
  return requiredFields[type] || []
}

/**
 * 验证行数据
 * @param {string} type 模板类型
 * @param {Array} row 行数据
 * @param {number} rowNumber 行号
 * @param {Array} errors 错误数组
 * @param {Array} warnings 警告数组
 */
function validateRowData(type, row, rowNumber, errors, warnings) {
  switch (type) {
    case 'categories':
      // 验证状态字段
      if (row[4] && !['active', 'inactive'].includes(row[4])) {
        errors.push(`第${rowNumber}行状态字段值无效: ${row[4]}`)
      }
      break
      
    case 'courses':
      // 验证难度等级
      if (row[4] && !['N5', 'N4', 'N3', 'N2', 'N1'].includes(row[4])) {
        errors.push(`第${rowNumber}行难度等级无效: ${row[4]}`)
      }
      // 验证状态
      if (row[6] && !['draft', 'published', 'archived'].includes(row[6])) {
        errors.push(`第${rowNumber}行状态字段值无效: ${row[6]}`)
      }
      break
      
    case 'course_units':
      // 验证单元类型
      if (row[5] && !['video', 'audio', 'text', 'exercise'].includes(row[5])) {
        errors.push(`第${rowNumber}行单元类型无效: ${row[5]}`)
      }
      break
  }
}

export default {
  generateCSVTemplate,
  downloadTemplate,
  validateImportData,
  templateData
}
