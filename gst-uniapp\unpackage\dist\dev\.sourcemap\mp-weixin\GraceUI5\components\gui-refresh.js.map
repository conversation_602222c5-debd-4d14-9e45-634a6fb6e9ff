{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-refresh.vue?9282", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-refresh.vue?7b1c", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-refresh.vue?5785", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-refresh.vue?401a", "uni-app:///GraceUI5/components/gui-refresh.vue", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-refresh.vue?e3e8", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-refresh.vue?b031"], "names": ["name", "props", "refreshText", "type", "default", "refreshBgColor", "refreshColor", "refreshFontSize", "data", "reScrollTop", "refreshHeight", "refreshY", "refreshStatus", "refreshTimer", "methods", "touchstart", "touchmove", "moveY", "touchend", "scroll", "endReload", "setTimeout", "resetFresh", "rotate360", "animation", "styles", "transform", "duration", "timingFunction", "needLayout", "delay"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCsC5nB;EACAA;EACAC;IACAC;MAAAC;MAAAC;QACA;MACA;IAAA;IACAC;MAAAF;MAAAC;QACA;MACA;IAAA;IACAE;MAAAH;MAAAC;QACA;MACA;IAAA;IACAG;MAAAJ;MAAAC;IAAA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACAC;MACA;QACAA;QACA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MACA;QAAA;MAAA;MACA;QACA;MACA;QACA;QAMA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACAC;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACAC;QACAC;UAAAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAm5B,CAAgB,k4BAAG,EAAC,C;;;;;;;;;;;ACAv6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "GraceUI5/components/gui-refresh.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./gui-refresh.vue?vue&type=template&id=18336c10&scoped=true&\"\nvar renderjs\nimport script from \"./gui-refresh.vue?vue&type=script&lang=js&\"\nexport * from \"./gui-refresh.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gui-refresh.vue?vue&type=style&index=0&id=18336c10&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18336c10\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"GraceUI5/components/gui-refresh.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-refresh.vue?vue&type=template&id=18336c10&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-refresh.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-refresh.vue?vue&type=script&lang=js&\"", "<template>\n\t<view \n\tclass=\"gui-page-refresh gui-flex gui-rows gui-justify-content-center gui-align-items-center\" \n\t:style=\"{\n\theight:refreshHeight+'px', \n\tbackgroundColor:refreshBgColor[refreshStatus]}\" \n\t:class=\"[refreshStatus == 3 ? 'gload-hide' : '']\">\n\t\t<text class=\"gui-page-refresh-icon gui-icons gui-block-text\" \n\t\tv-if=\"refreshStatus == 0 || refreshStatus == 1\" \n\t\t:style=\"{\n\t\t\tfontSize:refreshFontSize,\n\t\t\tcolor:refreshColor[refreshStatus]\n\t\t}\">&#xe66c;</text>\n\t\t<view class=\"gui-page-refresh-icon\" ref=\"loadingIcon\" v-if=\"refreshStatus == 2\" >\n\t\t\t<text class=\"gui-icons gui-rotate360 gui-block-text\"\n\t\t\t:style=\"{\n\t\t\t\tfontSize:refreshFontSize,\n\t\t\t\tcolor:refreshColor[refreshStatus]\n\t\t\t}\">&#xe9db;</text>\n\t\t</view>\n\t\t<text class=\"gui-page-refresh-icon gui-icons\"\n\t\tv-if=\"refreshStatus == 3\" \n\t\t:style=\"{\n\t\t\tfontSize:refreshFontSize,\n\t\t\tcolor:refreshColor[refreshStatus]\n\t\t}\">&#xe7f8;</text>\n\t\t<text class=\"gui-page-refresh-text gui-block-text\" \n\t\t:style=\"{\n\t\t\tfontSize:refreshFontSize,\n\t\t\tcolor:refreshColor[refreshStatus]\n\t\t}\">{{refreshText[refreshStatus]}}</text>\n\t</view>\n</template>\n<script>\n// #ifdef APP-NVUE\nvar animation = weex.requireModule('animation');\nconst dom = weex.requireModule('dom');\n// #endif\nexport default{\n\tname  : \"gui-refresh\",\n\tprops : {\n\t\trefreshText    : {type:Array,   default:function () {\n\t\t\treturn ['继续下拉刷新','松开手指开始刷新','数据刷新中','数据已刷新'];\n\t\t}},\n\t\trefreshBgColor : {type:Array,   default:function () {\n\t\t\treturn ['#FFFFFF','#FFFFFF','#FFFFFF','#63D2BC'];\n\t\t}},\n\t\trefreshColor : {type:Array,   default:function () {\n\t\t\treturn ['rgba(69, 90, 100, 0.6)','rgba(69, 90, 100, 0.6)','#63D2BC','#FFFFFF'];\n\t\t}},\n\t\trefreshFontSize : {type:String, default:'26rpx'}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\treScrollTop         : 0,\n\t\t\trefreshHeight       : 0,\n\t\t\trefreshY            : 0,\n\t\t\trefreshStatus       : 0,\n\t\t\trefreshTimer        : 0\n\t\t}\n\t},\n\tmethods:{\n\t\ttouchstart : function (e){\n\t\t\tif(this.reScrollTop > 10){return ;}\n\t\t\tthis.refreshY = e.changedTouches[0].pageY;\n\t\t},\n\t\ttouchmove : function(e){\n\t\t\tif(this.refreshStatus >= 1){ return null;}\n\t\t\tif(this.reScrollTop > 10){return ;}\n\t\t\tvar moveY = e.changedTouches[0].pageY - this.refreshY;\n\t\t\tmoveY     = moveY / 2;\n\t\t\tif(moveY >= 50){\n\t\t\t\tmoveY = 50;\n\t\t\t\tthis.refreshStatus = 1;\n\t\t\t}\n\t\t\tif(moveY > 15){this.refreshHeight = moveY;}\n\t\t},\n\t\ttouchend : function (e) {\n\t\t\tif(this.reScrollTop > 10){return ;}\n\t\t\tif(this.refreshStatus < 1){\n\t\t\t\treturn this.resetFresh();\n\t\t\t}else if(this.refreshStatus == 1){\n\t\t\t\tthis.refreshStatus = 2;\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\tthis.rotate360();\n\t\t\t\t}, 200);\n\t\t\t\t// #endif\n\t\t\t\tthis.$emit('reload');\n\t\t\t}\n\t\t},\n\t\tscroll:function(e){\n\t\t\tthis.reScrollTop = e.detail.scrollTop;\n\t\t},\n\t\tendReload : function(){\n\t\t\tthis.refreshStatus = 3;\n\t\t\tsetTimeout(()=>{this.resetFresh()}, 1000);\n\t\t},\n\t\tresetFresh : function () {\n\t\t\tthis.refreshHeight = 0;\n\t\t\tthis.refreshStatus = 0;\n\t\t\treturn null;\n\t\t},\n\t\trotate360 : function(){\n\t\t\tvar el = this.$refs.loadingIcon;\n\t\t\tanimation.transition(el, {\n\t\t\t\tstyles     : {transform: 'rotate(7200deg)'},\n\t\t\t\tduration   : 20000,\n\t\t\t\ttimingFunction: 'linear',\n\t\t\t\tneedLayout :false,\n\t\t\t\tdelay: 0\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n<style scoped>\n.gui-page-refresh{overflow:hidden}\n.gui-page-refresh-text{line-height:32rpx;}\n.gui-page-refresh-icon{padding:0 12rpx; line-height:40rpx;}\n/* #ifndef APP-NVUE */\n@keyframes gload-hide{0%{opacity:1; height:50px;} 70%{opacity:1; height:50px;} 100%{opacity:0; height:0px;}}\n.gload-hide{animation:gload-hide 1s linear;}\n/* #endif */\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-refresh.vue?vue&type=style&index=0&id=18336c10&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-refresh.vue?vue&type=style&index=0&id=18336c10&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039746970\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}