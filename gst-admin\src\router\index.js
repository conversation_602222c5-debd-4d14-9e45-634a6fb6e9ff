import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 布局组件
const Layout = () => import('@/layout/index.vue')

// 页面组件
const Dashboard = () => import('@/views/dashboard/index.vue')
const Groups = () => import('@/views/groups/index.vue')
const Courses = () => import('@/views/courses/index.vue')
const Assignments = () => import('@/views/assignments/index.vue')
const Categories = () => import('@/views/categories/index.vue')
const Users = () => import('@/views/users/index.vue')
const Permissions = () => import('@/views/permissions/index.vue')
const Banners = () => import('@/views/banners/index.vue')
const HomeConfig = () => import('@/views/home-config/index.vue')
const VisualEditor = () => import('@/views/visual-editor/index.vue')
const PageManager = () => import('@/views/page-manager/index.vue')
const MenuManager = () => import('@/views/menu-manager/index.vue')
const DataImport = () => import('@/views/data-import/index.vue')
const Database = () => import('@/views/database/index.vue')
const Backup = () => import('@/views/backup/index.vue')
const Logs = () => import('@/views/logs/index.vue')
const Settings = () => import('@/views/settings/index.vue')
const Login = () => import('@/views/login/index.vue')
const NotFound = () => import('@/views/error/404.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { 
      title: '登录',
      requiresAuth: false 
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { 
          title: '仪表板',
          icon: 'DataBoard',
          category: 'main'
        }
      }
    ]
  },
  {
    path: '/content',
    component: Layout,
    name: 'Content',
    meta: { 
      title: '内容管理',
      icon: 'Document',
      category: 'content',
      requiresAuth: true,
      roles: ['admin', 'teacher']
    },
    children: [
      {
        path: 'groups',
        name: 'Groups',
        component: Groups,
        meta: { 
          title: '小组管理',
          icon: 'UserFilled'
        }
      },
      {
        path: 'courses',
        name: 'Courses',
        component: Courses,
        meta: { 
          title: '课程管理',
          icon: 'Reading'
        }
      },
      {
        path: 'assignments',
        name: 'Assignments',
        component: Assignments,
        meta: { 
          title: '作业管理',
          icon: 'EditPen'
        }
      },
      {
        path: 'categories',
        name: 'Categories',
        component: Categories,
        meta: { 
          title: '分类管理',
          icon: 'FolderOpened'
        }
      }
    ]
  },
  {
    path: '/miniprogram',
    component: Layout,
    name: 'Miniprogram',
    meta: { 
      title: '小程序管理',
      icon: 'Iphone',
      category: 'miniprogram',
      requiresAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: 'visual-editor',
        name: 'VisualEditor',
        component: VisualEditor,
        meta: { 
          title: '可视化编辑器',
          icon: 'Brush'
        }
      },
      {
        path: 'page-manager',
        name: 'PageManager',
        component: PageManager,
        meta: { 
          title: '页面管理',
          icon: 'Document'
        }
      },
      {
        path: 'banners',
        name: 'Banners',
        component: Banners,
        meta: { 
          title: '轮播图管理',
          icon: 'Picture'
        }
      },
      {
        path: 'home-config',
        name: 'HomeConfig',
        component: HomeConfig,
        meta: { 
          title: '首页配置',
          icon: 'HomeFilled'
        }
      },
      {
        path: 'menu-manager',
        name: 'MenuManager',
        component: MenuManager,
        meta: { 
          title: '菜单管理',
          icon: 'Menu'
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { 
      title: '系统管理',
      icon: 'Setting',
      category: 'system',
      requiresAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: 'users',
        name: 'Users',
        component: Users,
        meta: { 
          title: '用户管理',
          icon: 'User'
        }
      },
      {
        path: 'permissions',
        name: 'Permissions',
        component: Permissions,
        meta: { 
          title: '权限管理',
          icon: 'Lock'
        }
      },
      {
        path: 'data-import',
        name: 'DataImport',
        component: DataImport,
        meta: { 
          title: '数据导入',
          icon: 'Upload'
        }
      },
      {
        path: 'database',
        name: 'Database',
        component: Database,
        meta: { 
          title: '数据库管理',
          icon: 'Coin'
        }
      },
      {
        path: 'backup',
        name: 'Backup',
        component: Backup,
        meta: { 
          title: '备份管理',
          icon: 'FolderAdd'
        }
      },
      {
        path: 'logs',
        name: 'Logs',
        component: Logs,
        meta: { 
          title: '操作日志',
          icon: 'Document'
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: { 
          title: '系统设置',
          icon: 'Tools'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - GST日语培训班管理系统` : 'GST日语培训班管理系统'
  
  // 检查是否需要登录
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isLoggedIn) {
      next('/login')
      return
    }
    
    // 检查角色权限
    if (to.meta.roles && !to.meta.roles.includes(authStore.user?.role)) {
      ElMessage.error('您没有权限访问此页面')
      next('/dashboard')
      return
    }
  }
  
  // 如果已登录且访问登录页，重定向到仪表板
  if (to.path === '/login' && authStore.isLoggedIn) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
