const express = require('express');
const { body, validationResult } = require('express-validator');
const { Banner, Category, Course, SystemConfig } = require('../models');
const { auth, requireAdmin } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');

const router = express.Router();

// 获取首页配置数据
router.get('/', asyncHandler(async (req, res) => {
  try {
    // 获取首页配置
    const defaultConfig = {
      showBanners: true,
      showCategories: true,
      showRecommendedCourses: true,
      maxBanners: 5,
      maxCategoriesRow1: 4,
      maxCategoriesRow2: 4,
      maxRecommendedCourses: 4,
      categoryDisplayMode: 'top_level_only', // 'top_level_only' 或 'mixed'
      coursesSortBy: 'created_at', // 'created_at', 'rating', 'student_count'
      coursesSortOrder: 'DESC'
    };

    const config = await SystemConfig.getConfig('homepage_config', defaultConfig);

    // 获取当前的轮播图数据
    const banners = await Banner.findAll({
      where: { status: 'active' },
      order: [['orderNum', 'ASC']],
      limit: config.maxBanners || 5,
      attributes: ['id', 'title', 'image', 'link', 'orderNum', 'status']
    });

    // 获取当前的分类数据
    const whereCondition = config.categoryDisplayMode === 'top_level_only' 
      ? { status: 'active', pid: 0 }
      : { status: 'active' };

    const categories = await Category.findAll({
      where: whereCondition,
      order: [['sort', 'ASC']],
      attributes: ['id', 'name', 'description', 'image', 'sort', 'pid']
    });

    // 获取当前的推荐课程数据
    const courses = await Course.findAll({
      where: { status: 'published' },
      order: [[config.coursesSortBy || 'created_at', config.coursesSortOrder || 'DESC']],
      limit: config.maxRecommendedCourses || 4,
      attributes: ['id', 'title', 'picture', 'level', 'Cost', 'studentCount', 'rating']
    });

    res.json({
      success: true,
      data: {
        config,
        preview: {
          banners,
          categories,
          courses
        }
      }
    });

  } catch (error) {
    console.error('获取首页配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取首页配置失败',
      error: error.message
    });
  }
}));

// 更新首页配置
router.put('/', auth, requireAdmin, [
  body('showBanners').optional().isBoolean().withMessage('showBanners必须是布尔值'),
  body('showCategories').optional().isBoolean().withMessage('showCategories必须是布尔值'),
  body('showRecommendedCourses').optional().isBoolean().withMessage('showRecommendedCourses必须是布尔值'),
  body('maxBanners').optional().isInt({ min: 1, max: 10 }).withMessage('maxBanners必须在1-10之间'),
  body('maxCategoriesRow1').optional().isInt({ min: 1, max: 8 }).withMessage('maxCategoriesRow1必须在1-8之间'),
  body('maxCategoriesRow2').optional().isInt({ min: 1, max: 8 }).withMessage('maxCategoriesRow2必须在1-8之间'),
  body('maxRecommendedCourses').optional().isInt({ min: 1, max: 10 }).withMessage('maxRecommendedCourses必须在1-10之间'),
  body('categoryDisplayMode').optional().isIn(['top_level_only', 'mixed']).withMessage('categoryDisplayMode必须是top_level_only或mixed'),
  body('coursesSortBy').optional().isIn(['created_at', 'rating', 'student_count']).withMessage('coursesSortBy必须是created_at、rating或student_count'),
  body('coursesSortOrder').optional().isIn(['ASC', 'DESC']).withMessage('coursesSortOrder必须是ASC或DESC')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  try {
    const newConfig = req.body;

    // 获取当前配置
    const defaultConfig = {
      showBanners: true,
      showCategories: true,
      showRecommendedCourses: true,
      maxBanners: 5,
      maxCategoriesRow1: 4,
      maxCategoriesRow2: 4,
      maxRecommendedCourses: 4,
      categoryDisplayMode: 'top_level_only',
      coursesSortBy: 'created_at',
      coursesSortOrder: 'DESC'
    };

    const currentConfig = await SystemConfig.getConfig('homepage_config', defaultConfig);
    const updatedConfig = { ...currentConfig, ...newConfig };

    // 保存配置
    await SystemConfig.setConfig('homepage_config', updatedConfig, '首页数据配置', 'json');

    res.json({
      success: true,
      message: '首页配置更新成功',
      data: { config: updatedConfig }
    });

  } catch (error) {
    console.error('更新首页配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新首页配置失败',
      error: error.message
    });
  }
}));

// 获取首页预览数据（根据当前配置）
router.get('/preview', asyncHandler(async (req, res) => {
  try {
    // 获取配置
    const defaultConfig = {
      showBanners: true,
      showCategories: true,
      showRecommendedCourses: true,
      maxBanners: 5,
      maxCategoriesRow1: 4,
      maxCategoriesRow2: 4,
      maxRecommendedCourses: 4,
      categoryDisplayMode: 'top_level_only',
      coursesSortBy: 'created_at',
      coursesSortOrder: 'DESC'
    };

    const config = await SystemConfig.getConfig('homepage_config', defaultConfig);

    // 构建预览数据（与小程序API相同的逻辑）
    const previewData = {
      banner: [],
      course1: [],
      course2: [],
      new: []
    };

    if (config.showBanners) {
      const banners = await Banner.findAll({
        where: { status: 'active' },
        order: [['orderNum', 'ASC']],
        limit: config.maxBanners,
        attributes: ['id', 'title', 'image', 'link', 'orderNum']
      });

      previewData.banner = banners.map(banner => ({
        id: banner.id,
        title: banner.title,
        image: banner.image || '/static/imgs/placeholder.svg',
        thumb: banner.image || '/static/imgs/placeholder.svg',
        url: banner.link || '#',
        sort: banner.orderNum || 0
      }));
    }

    if (config.showCategories) {
      const whereCondition = config.categoryDisplayMode === 'top_level_only' 
        ? { status: 'active', pid: 0 }
        : { status: 'active' };

      const categories = await Category.findAll({
        where: whereCondition,
        order: [['sort', 'ASC']],
        attributes: ['id', 'name', 'image', 'description']
      });

      // 第一行分类
      previewData.course1 = categories.slice(0, config.maxCategoriesRow1).map((category, index) => ({
        id: category.id,
        name: category.name,
        icon: category.image || '/static/imgs/placeholder.svg',
        thumb: category.image || '/static/imgs/placeholder.svg',
        url: `/pages/course/course?categoryId=${category.id}`,
        sort: index + 1
      }));

      // 第二行分类
      previewData.course2 = categories.slice(config.maxCategoriesRow1, config.maxCategoriesRow1 + config.maxCategoriesRow2).map((category, index) => ({
        id: category.id,
        name: category.name,
        icon: category.image || '/static/imgs/placeholder.svg',
        thumb: category.image || '/static/imgs/placeholder.svg',
        url: `/pages/course/course?categoryId=${category.id}`,
        sort: config.maxCategoriesRow1 + index + 1
      }));
    }

    if (config.showRecommendedCourses) {
      const courses = await Course.findAll({
        where: { status: 'published' },
        order: [[config.coursesSortBy, config.coursesSortOrder]],
        limit: config.maxRecommendedCourses,
        attributes: ['id', 'title', 'picture', 'Cost', 'level', 'studentCount', 'rating']
      });

      previewData.new = courses.map(course => ({
        id: course.id,
        title: course.title,
        thumb: course.picture || '/static/imgs/placeholder.svg',
        price: course.Cost || 0,
        original_price: Math.round((course.Cost || 0) * 1.3),
        level: course.level || 'N5',
        students: course.studentCount || 0,
        rating: course.rating || 0,
        url: `/pages/course/course?id=${course.id}`
      }));
    }

    res.json({
      success: true,
      data: previewData
    });

  } catch (error) {
    console.error('获取首页预览数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取首页预览数据失败',
      error: error.message
    });
  }
}));

module.exports = router;
