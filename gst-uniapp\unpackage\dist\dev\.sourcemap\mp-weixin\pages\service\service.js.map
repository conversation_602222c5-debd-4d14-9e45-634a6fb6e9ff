{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/service/service.vue?d9fc", "webpack:///D:/gst/gst-uniapp/pages/service/service.vue?8e54", "webpack:///D:/gst/gst-uniapp/pages/service/service.vue?f8cb", "webpack:///D:/gst/gst-uniapp/pages/service/service.vue?71ea", "uni-app:///pages/service/service.vue", "webpack:///D:/gst/gst-uniapp/pages/service/service.vue?2542", "webpack:///D:/gst/gst-uniapp/pages/service/service.vue?6ba1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "onLoad", "uni", "query", "console", "mgUpHeight", "l", "wh", "data", "msgLoad", "anData", "animationData", "showTow", "msgList", "msg", "go", "srcollHeight", "list", "methods", "goPag", "msgMove", "duration", "timingFunction", "animation", "msgGo", "setTimeout", "answer", "my", "type", "sendMsg", "title", "msgKf", "questionList", "ckAdd", "hide", "<PERSON><PERSON><PERSON>", "upTowmn", "apiGetQuestion", "params", "key", "apiGetQuestionDetail", "id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAomB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4HxnB;AACA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IAAA;IACA;IACA;IACA;IACAC;MACA;MACAC;QACA;QACA;QACAC;QACA;UACA;UACA;UACA;UACAC;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IAEAC;IACAC;IACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;MAEA;IACA;IACA;IACAC;MACA;QACAC;QACAC;MACA;MAEA;MAEAC;MAEA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;QACAtB;UACA;UACA;YACA;UAEA;UACA;UACA;UACA;UACA;YACA;YACA;cACA;YACA;cACA;cACA;YACA;UACA;QAEA;MACA;IACA;IACA;IACAuB;MAAA;MACA;MACA;QAAA;QAAA;MAAA;MACA;MACA;MACAtB;;MAEA;MACA;MACA;MACA;QACA;QACA;UAAAuB;UAAAb;UAAAc;QAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;IACAC;MACA;MACA;QACA;QACA3B;UACA4B;QACA;QACA;MACA;MACA;MACA;QAAA;QAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAvB;QACA;UAAA;UAAA;QAAA;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;UACA;YAAAmB;YAAAb;YACAc;YAAAI;UAAA;QACA;UACA;YAAAL;YAAAb;YAAAc;YAAAI;UAAA;QACA;QAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAjC;IACA;IACA;IACAkC;MAEA;QACAf;QACAC;MACA;MAEA;MAEAC;MAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;IACAc;MACA;QACAC;UACAC;UACAX;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAY;MACA;QACAF;UACAG;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnXA;AAAA;AAAA;AAAA;AAAu3B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;ACA34B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/service/service.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/service/service.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./service.vue?vue&type=template&id=4c0f6f1c&\"\nvar renderjs\nimport script from \"./service.vue?vue&type=script&lang=js&\"\nexport * from \"./service.vue?vue&type=script&lang=js&\"\nimport style0 from \"./service.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/service/service.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=template&id=4c0f6f1c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- 空盒子用来防止消息过少时 拉起键盘会遮盖消息 -->\r\n\t\t<view  :animation=\"anData\"  style=\"height:0;\">\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<!-- 消息体 -->\r\n\t\t<scroll-view scroll-with-animation scroll-y=\"true\"  @touchmove=\"hideKey\"\r\n\t\tstyle=\"width: 750rpx;\" :style=\"{'height':srcollHeight}\" :scroll-top=\"go\" @tap=\"hide\">\r\n\t\t\t<!-- 用来获取消息体高度 -->\r\n\t\t\t<view id=\"okk\" scroll-with-animation >\r\n\t\t\t<!-- 消息 -->\r\n\t\t\t<view  class=\"flex-column-start\" v-for=\"(x,i) in msgList\" :key=\"i\">\r\n\r\n\t\t\t\t<!-- 用户消息 头像可选加入-->\r\n\t\t\t\t<view v-if=\"x.my\" class=\"flex justify-end padding-right one-show  align-start  padding-top\" >\r\n\t\t\t\t<!-- \t<image v-if=\"!x.my\" class=\"chat-img\" src=\"../../static/...\" mode=\"aspectFill\" ></image> -->\t\r\n\t\t\t\t\t<view class=\"flex justify-end\"  style=\"width: 400rpx;\">\r\n\t\t\t\t\t\t<view class=\"margin-left padding-chat bg-cyan\" style=\"border-radius: 35rpx;\">\r\n\t\t\t\t\t\t\t<text   style=\"word-break: break-all;\">{{x.msg}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t<!-- <image class=\"chat-img margin-left\" src=\"../../static/...\" mode=\"aspectFill\" ></image> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 机器人消息 -->\r\n\t\t\t\t<view v-if=\"!x.my\" class=\"margin-left margin-top one-show\"  style=\"display: flex;\">\r\n\t\t\t\t\t<view class=\"chat-img flex-row-center\">\r\n\t\t\t\t\t\t<image style=\"height: 75rpx;width: 75rpx;\" src=\"../../static/robt.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view  class=\"flex\"  style=\"width: 500rpx;\">\r\n\t\t\t\t\t\t<view class=\"margin-left padding-chat flex-column-start\" style=\"border-radius: 35rpx;background-color: #f9f9f9;\">\r\n\t\t\t\t\t\t\t<!-- <text  style=\"word-break: break-all;\" >{{x.msg}}</text> -->\r\n\t\t\t\t\t\t\t<rich-text :nodes=\"x.msg\"></rich-text>\r\n\t\t\t\t\t\t\t<!-- 消息模板 =>初次问候 -->\r\n\t\t\t\t\t\t\t<view class=\"flex-column-start\" v-if=\"x.type==1\" style=\"color: #2fa39b;\">\r\n\t\t\t\t\t\t\t\t<text style=\"color: #838383;font-size: 22rpx;margin-top: 15rpx;\">你可以这样问我:</text>\r\n\t\t\t\t\t\t\t\t<text @click=\"msgKf(item)\" style=\"margin-top: 30rpx;\" \r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in x.questionList\" :key=\"index\" >{{item.title}}</text>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"flex-row-start  padding-top-sm\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"my-neirong-sm\">没有你要的答案?</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"padding-left\" style=\"color: #007AFF;\">换一批</text>\r\n\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 消息模板 =>多个答案 -->\r\n\t\t\t\t\t\t\t<view class=\"flex-column-start\" v-if=\"x.type==2\" style=\"color: #2fa39b;\">\r\n\t\t\t\t\t\t\t\t<text style=\"color: #838383;font-size: 22rpx;margin-top: 15rpx;\">猜你想问:</text>\r\n\t\t\t\t\t\t\t\t<!-- 连接服务器应该用item.id -->\r\n\t\t\t\t\t\t\t\t<text @click=\"answer(item)\" style=\"margin-top: 30rpx;\" \r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in x.questionList\" :key=\"index\" >{{item.title}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 消息模板 => 无法回答-->\r\n\t\t\t\t\t\t\t<view class=\"flex-column-start\" v-if=\"x.type==0\">\r\n\t\t\t\t\t\t\t\t<text class=\"padding-top-sm\" style=\"color: #2fa39b;\">提交意见与反馈</text>\r\n\t\t\t\t\t\t\t\t<text style=\"color: #838383;font-size: 22rpx;margin-top: 15rpx;\">下面是一些常见问题,您可以点击对应的文字快速获取答案:</text>\r\n\t\t\t\t\t\t\t\t<text @click=\"answer(item)\" style=\"margin-top: 30rpx;color: #2fa39b;\" \r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in x.questionList\" :key=\"index\" >{{item.title}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-row-start  padding-top-sm\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"my-neirong-sm\">没有你要的答案?</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"padding-left\" style=\"color: #1396c5;\">换一批</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- loading是显示 -->\r\n\t\t<view v-show=\"msgLoad\" class=\"flex-row-start margin-left margin-top\">\r\n\t\t\t<view class=\"chat-img flex-row-center\">\r\n\t\t\t\t<image style=\"height: 75rpx;width: 75rpx;\" src=\"../../static/robt.png\" mode=\"aspectFit\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view  class=\"flex\"  style=\"width: 500rpx;\">\r\n\t\t\t\t<view class=\"margin-left padding-chat flex-column-start\" \r\n\t\t\t\tstyle=\"border-radius: 35rpx;background-color: #f9f9f9;\">\r\n\t\t\t\t\t<view class=\"cuIcon-loading turn-load\" style=\"font-size: 35rpx;color: #3e9982;\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\t\r\n\t\t</view>\r\n\t\t<!-- 防止消息底部被遮 -->\r\n\t\t<view style=\"height: 120rpx;\">\r\n\t\t\t\r\n\t\t</view>\r\n\t\t</view>\t\r\n\t\r\n\t\t</scroll-view>\t\t\r\n\r\n\t\t<!-- 底部导航栏 -->\r\n\t\t<view class=\"flex-column-center\" style=\"position: fixed;bottom: -180px;\"\r\n\t\t:animation=\"animationData\" >\t\t\r\n\t\t\t<view class=\"bottom-dh-char flex-row-around\" style=\"font-size: 25rpx;\">\r\n\t\t\t\t<!-- vue无法使用软键盘\"发送\" -->\r\n\t\t\t\t <input  v-model=\"msg\"  class=\"dh-input\" type=\"text\" style=\"background-color: #f0f0f0;\" \r\n\t\t\t\t @confirm=\"sendMsg\" confirm-type=\"search\" placeholder-class=\"my-neirong-sm\"\r\n\t\t\t\t placeholder=\"用一句简短的话描述您的问题\" /> \r\n\t\t\t\t <view @click=\"sendMsg\" class=\"cu-tag bg-cyan round\">\r\n\t\t\t\t \t发送\r\n\t\t\t\t </view>\r\n\t\t\t\t<text @click=\"ckAdd\" class=\"cuIcon-roundaddfill text-brown\"></text>\r\n\t\t\t</view>\t\t\r\n\t\t\t\t<!-- 附加栏(自定义) -->\r\n\t\t\t<view class=\"box-normal flex-row-around flex-wrap\">\r\n\t\t\t\t<!-- <view class=\"tb-text\">\r\n\t\t\t\t\t<view class=\"cuIcon-form\"></view>\r\n\t\t\t\t\t<text >问题反馈</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"tb-text\">\r\n\t\t\t\t\t<view class=\"cuIcon-form\"></view>\r\n\t\t\t\t\t<text>人工客服</text>\r\n\t\t\t\t\t<button class='contact-btn' open-type='contact'>a</button> \r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\r\n\t\t\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// rpx和px的比率\r\n\tvar l\r\n\t// 可用窗口高度\r\n\tvar wh\r\n\t// 顶部空盒子的高度\r\n\tvar mgUpHeight\r\n\texport default {\t\r\n\t\tonLoad(){\r\n\t\t\t// 如果需要缓存消息缓存msgList即可\r\n\t\t\t// 监听键盘拉起\r\n\t\t\t// 因为无法控制键盘拉起的速度,所以这里尽量以慢速处理\r\n\t\t\tuni.onKeyboardHeightChange(res => {\r\n\t\t\t\tconst query = uni.createSelectorQuery()\r\n\t\t\t\tquery.select('#okk').boundingClientRect(data => {\r\n\t\t\t\t\t// 若消息体没有超过2倍的键盘则向下移动差值,防止遮住消息体\r\n\t\t\t\t\tvar up=res.height*2-data.height-l*110\r\n\t\t\t\t\tconsole.log(up)\r\n\t\t\t\t  if(up>0){\r\n\t\t\t\t\t  // 动态改变空盒子高度\r\n\t\t\t\t\t this.msgMove(up,300)\r\n\t\t\t\t\t // 记录改变的值,若不收回键盘且发送了消息用来防止消息过多被遮盖\r\n\t\t\t\t\t mgUpHeight=up\r\n\t\t\t\t  }\r\n\t\t\t\t  // 收回\r\n\t\t\t\t  if(res.height==0){\r\n\t\t\t\t\t   this.msgMove(0,0)\t\r\n\t\t\t\t  }\r\n\t\t\t\t}).exec();\r\n\t\t\t })\r\n\t\t\tvar query=uni.getSystemInfoSync()\r\n\t\t\t\t\t\t\r\n\t\t\tl=query.screenWidth/750\t\t\r\n\t\t\twh=query.windowHeight\t\t\t\t\t\t\t\t\r\n\t\t\tthis.srcollHeight=query.windowHeight+\"px\"\r\n\t\t\tthis.msgKf('')\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmsgLoad:false,\r\n\t\t\t\tanData:{},\r\n\t\t\t\tanimationData:{},\r\n\t\t\t\tshowTow:false,\r\n\t\t\t\t// 消息体,定义机器人初次的消息(或者自定义出现时机)\r\n\t\t\t\t// my->谁发的消息 msg->消息文本 type->客服消息模板类型 questionList->快速获取问题答案的问题列表\r\n\t\t\t\tmsgList:[],\r\n\t\t\t\tmsg:\"\",\r\n\t\t\t\tgo:0,\r\n\t\t\t\tsrcollHeight:0,\r\n\t\t\t\tlist:[]\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tmethods: {\r\n\t\t\t// 切换输入法时移动输入框(按照官方的上推页面的原理应该会自动适应不同的键盘高度-->官方bug)\r\n\t\t\tgoPag(kh){\t\r\n\t\t\t\tthis.upTowmn(0,250)\r\n\t\t\t\tif(this.keyHeight!=0){\t\t\t\t\t\r\n\t\t\t\t\tif(kh-this.keyHeight>0){\r\n\t\t\t\t\t\tthis.upTowmn(this.keyHeight-kh,250)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 移动顶部的空盒子\r\n\t\t\tmsgMove(x,t){\r\n\t\t\t\tvar animation = uni.createAnimation({\r\n\t\t\t\t        duration: t,\r\n\t\t\t\t          timingFunction: 'linear',\r\n\t\t\t\t      })\r\n\t\t\t\t  \r\n\t\t\t\t      this.animation = animation\r\n\t\t\t\t  \r\n\t\t\t\t      animation.height(x).step()\r\n\t\t\t\t  \r\n\t\t\t\t      this.anData = animation.export()\r\n\t\t\t},\r\n\t\t\t// 保持消息体可见\r\n\t\t\tmsgGo(){\r\n\t\t\t\tconst query = uni.createSelectorQuery()\r\n\t\t\t\t// 延时100ms保证是最新的高度\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t// 获取消息体高度\r\n\t\t\t\t\tquery.select('#okk').boundingClientRect(data => {\r\n\t\t\t\t\t   // 如果超过scorll高度就滚动scorll\r\n\t\t\t\t\t   if(data.height-wh>0){\r\n\t\t\t\t\t\t   this.go=data.height-wh\r\n\t\t\t\t\t\t   \r\n\t\t\t\t\t   }\r\n\t\t\t\t\t   // 保证键盘第一次拉起时消息体能保持可见\r\n\t\t\t\t\t   var moveY=wh-data.height\r\n\t\t\t\t\t   // 超出页面则缩回空盒子\r\n\t\t\t\t\t   if(moveY-mgUpHeight<0){\r\n\t\t\t\t\t\t   // 小于0则视为0\r\n\t\t\t\t\t\t   if(moveY<0){\r\n\t\t\t\t\t\t\t   this.msgMove(0,200)\r\n\t\t\t\t\t\t   }else{\r\n\t\t\t\t\t\t\t   // 否则缩回盒子对应的高度\r\n\t\t\t\t\t\t\t  this.msgMove(moveY,200) \r\n\t\t\t\t\t\t   }\t\t\t\t\t   \r\n\t\t\t\t\t   }\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}).exec();\r\n\t\t\t\t},100)\r\n\t\t\t},\r\n\t\t\t// 回答问题的业务逻辑\r\n\t\t\tanswer(item){\r\n\t\t\t\tthis.msgLoad=false\r\n\t\t\t\tthis.msgList.push({\"msg\":item.title,\"my\":true})\r\n\t\t\t\tthis.msgGo()\r\n\t\t\t\t// 这里应该传入问题的id,模拟就用index代替了\r\n\t\t\t\tconsole.log(item.id)\r\n\t\t\t\t\r\n\t\t\t\t// loading\r\n\t\t\t\tthis.msgLoad=true\r\n\t\t\t\t// 这里连接服务器获取答案\r\n\t\t\t\tthis.apiGetQuestionDetail(item.id).then(data => {\r\n\t\t\t\t\tthis.msgLoad=false\r\n\t\t\t\t\tthis.msgList.push({my:false,msg:data.des,type:-1})\r\n\t\t\t\t\tthis.msgGo()\r\n\t\t\t\t})\r\n\t\t\t\t// 下面模拟请求\r\n\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t// \t// 取消loading\r\n\t\t\t\t// \tthis.msgLoad=false\r\n\t\t\t\t// \tthis.msgList.push({my:false,msg:\"娜娜还在学习中,没能明白您的问题,您点击下方提交反馈与问题,我们会尽快人工处理(无法回答模板)\",type:0,questionList:[\"如何注销用户\",\"我想了解业务流程\",\"手机号如何更换\"]})\r\n\t\t\t\t// \tthis.msgList.push({my:false,msg:\"单消息模板\",type:-1})\r\n\t\t\t\t// \tthis.msgList.push({my:false,msg:\"根据您的问题,已为您匹配了下列问题(多个答案模板)\",type:2,questionList:[\"如何注销用户\",\"我想了解业务流程\",\"手机号如何更换\"]})\r\n\t\t\t\t// \tthis.msgGo()\r\n\t\t\t\t// },2000)\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tsendMsg(){\r\n\t\t\t\t// 消息为空不做任何操作\r\n\t\t\t\tif(this.msg==\"\"){\r\n\t\t\t\t\t// \r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '输入的内容不能为空'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\t}\r\n\t\t\t\t// 显示消息 msg消息文本,my鉴别是谁发的消息(不能用俩个消息数组循环,否则消息不会穿插)\r\n\t\t\t\tthis.msgList.push({\"msg\":this.msg,\"my\":true})\t\t\t\t\r\n\t\t\t\t// 保证消息可见\r\n\t\t\t\tthis.msgGo()\r\n\t\t\t\t// 回答问题\r\n\t\t\t\tthis.msgKf(this.msg)\r\n\t\t\t\t// 清除消息\r\n\t\t\t\tthis.msg=\"\"\r\n\t\t\t},\r\n\t\t\tmsgKf(x){\r\n\t\t\t\tlet data ;\r\n\t\t\t\tif(x.type){\r\n\t\t\t\t\tdata = x.title;\r\n\t\t\t\t\tthis.msgList.push({\"msg\":data,\"my\":true})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tdata= x;\r\n\t\t\t\t}\r\n\t\t\t\t// loading\r\n\t\t\t\tthis.msgLoad=true\r\n\t\t\t\t// 这里连接服务器获取答案\r\n\t\t\t\tthis.apiGetQuestion(data,1).then(data => {\r\n\t\t\t\t\tthis.list = data;\r\n\t\t\t\t\tthis.msgLoad=false\r\n\t\t\t\t\tif(x==''){\r\n\t\t\t\t\t\tthis.msgList.push({my:false,msg:\"您好，请发送课程对应关键词，告诉我您想要了解的课程。\",\r\n\t\t\t\t\t\ttype:1,questionList:this.list})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.msgList.push({my:false,msg:\"根据您的问题,已为您匹配了下列问题\",type:2,questionList:this.list})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.msgGo()\r\n\t\t\t\t})\r\n\t\t\t\t// 下面模拟请求\r\n\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t// \t// 取消loading\r\n\t\t\t\t// \tthis.msgLoad=false\r\n\t\t\t\t// \tthis.msgList.push({my:false,msg:\"娜娜还在学习中,没能明白您的问题,您点击下方提交反馈与问题,我们会尽快人工处理(无法回答模板)\",type:0,questionList:[\"如何注销用户\",\"我想了解业务流程\",\"手机号如何更换\"]})\r\n\t\t\t\t// \tthis.msgList.push({my:false,msg:\"单消息模板\",type:-1})\r\n\t\t\t\t// \tthis.msgList.push({my:false,msg:\"根据您的问题,已为您匹配了下列问题(多个答案模板)\",type:2,questionList:[\"如何注销用户\",\"我想了解业务流程\",\"手机号如何更换\"]})\r\n\t\t\t\t// \tthis.msgGo()\r\n\t\t\t\t// },2000)\r\n\t\t\t},\r\n\t\t\t// 不建议输入框聚焦时操作此动画\r\n\t\t\tckAdd(){\r\n\t\t\t\tif(!this.showTow){\r\n\t\t\t\t\tthis.upTowmn(-180,350)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.upTowmn(0,200)\r\n\t\t\t\t}\r\n\t\t\t\tthis.showTow=!this.showTow\r\n\t\t\t},\r\n\t\t\thide(){\r\n\t\t\t\tif(this.showTow){\r\n\t\t\t\t\tthis.upTowmn(0,200)\r\n\t\t\t\t}\r\n\t\t\t\tthis.showTow=!this.showTow\r\n\t\t\t},\r\n\t\t\thideKey(){\r\n\t\t\t\tuni.hideKeyboard()\r\n\t\t\t},\r\n\t\t\t// 拉起/收回附加栏\r\n\t\t\tupTowmn(x,t){\r\n\t\t\t\t\r\n\t\t\t\t var animation = uni.createAnimation({\r\n\t\t\t\t      duration: t,\r\n\t\t\t\t        timingFunction: 'ease',\r\n\t\t\t\t    })\r\n\t\t\t\t \t\t\t\t\r\n\t\t\t\t    this.animation = animation\r\n\t\t\t\t \t\t\t\t\r\n\t\t\t\t    animation.translateY(x).step()\r\n\t\t\t\t \t\t\t\t\r\n\t\t\t\t    this.animationData = animation.export()\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t/**\r\n\t\t\t * 获取问题\r\n\t\t\t * @param {Object} id\r\n\t\t\t */\r\n\t\t\tapiGetQuestion: function(key,type) {\r\n\t\t\t\treturn this.$http.get('/v1/doQuestion', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tkey,\r\n\t\t\t\t\t\ttype\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取问题答案\r\n\t\t\t * @param {Object} id\r\n\t\t\t */\r\n\t\t\tapiGetQuestionDetail: function(id) {\r\n\t\t\t\treturn this.$http.get('/v1/doQuestionDetail', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n.bottom-dh-char{\r\n\t \tbackground-color: #f9f9f9;\r\n\t \twidth: 750rpx;\r\n\t \theight: 110rpx;\r\n\t }\r\n.center-box{\r\n\twidth: 720rpx;\r\n\tpadding-left: 25rpx;\r\n}\r\n.hui-box{\r\n\twidth: 750rpx;\r\n\theight: 100%;\r\n\t\r\n}\r\n.dh-input{\r\n\twidth: 500rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 30rpx;\r\n\tpadding-left: 15rpx;\r\n\tbackground-color: #FFFFFF;\r\n}\r\n.box-normal{\r\n\twidth: 750rpx;\r\n\theight: 180px;\r\n\tbackground-color: #FFFFFF;\r\n}\r\n.tb-text view{\r\n\tfont-size: 65rpx;\r\n}\r\n.tb-text text{\r\n\tfont-size: 25rpx;\r\n\tcolor: #737373;\r\n}\r\n.chat-img{\r\n\tborder-radius: 50%;\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tbackground-color: #f7f7f7;\r\n}\r\n\r\n.padding-chat{\r\n\tpadding: 17rpx 20rpx;\r\n}\r\n.tb-nv{\r\n\twidth: 50rpx;\r\n\theight: 50rpx;\r\n}\r\n.contact-btn {\n  display: inline-block;\n  position: absolute;\n  width: 100%;\n  background: salmon;\n    opacity: 0;\n}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040518734\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}