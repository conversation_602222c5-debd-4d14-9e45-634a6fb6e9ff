{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/lp-input/lp-input.vue?bc62", "webpack:///D:/gst/gst-uniapp/components/lp-input/lp-input.vue?4b88", "webpack:///D:/gst/gst-uniapp/components/lp-input/lp-input.vue?1300", "webpack:///D:/gst/gst-uniapp/components/lp-input/lp-input.vue?eac9", "uni-app:///components/lp-input/lp-input.vue", "webpack:///D:/gst/gst-uniapp/components/lp-input/lp-input.vue?3947", "webpack:///D:/gst/gst-uniapp/components/lp-input/lp-input.vue?9d96"], "names": ["name", "data", "focus", "show", "text", "keyboardHeight", "created", "uni", "self", "methods", "setText", "getText", "onFocusHandler", "onBlurHandler", "onCloseHandler", "onChangeHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCcznB;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;MACA;MACAA;MACAA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAP;IACA;IACA;AACA;AACA;IACAQ;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/lp-input/lp-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./lp-input.vue?vue&type=template&id=4af1b324&scoped=true&\"\nvar renderjs\nimport script from \"./lp-input.vue?vue&type=script&lang=js&\"\nexport * from \"./lp-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./lp-input.vue?vue&type=style&index=0&id=4af1b324&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4af1b324\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/lp-input/lp-input.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-input.vue?vue&type=template&id=4af1b324&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-input.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"root-box\" :class=\"{show:show}\" :style=\"{bottom: keyboardHeight+'px'}\">\n\t\t<view class=\"input-box lp-flex\" :class=\"{show:show}\">\n\t\t\t<!-- :cursor-spacing=\"75\" -->\n\t\t\t<textarea class=\"input\" :maxlength=\"5000\" :show-confirm-bar=\"false\" :focus=\"focus\" :adjust-position=\"false\"\n\t\t\t\tv-model=\"text\" @focus=\"onFocusHandler\" @blur=\"onBlurHandler\"\n\t\t\t\t@input=\"onChangeHandler\" @confirm=\"onChangeHandler\">\n\t\t\t </textarea>\n\t\t\t<view class=\"close-box lp-flex-center\" @tap=\"onCloseHandler\">完成输入</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname: \"input\",\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfocus: false,\n\t\t\t\tshow: false,\n\t\t\t\ttext: '',\n\t\t\t\tkeyboardHeight: -190\n\t\t\t};\n\t\t},\n\t\tcreated() {\n\t\t\tlet self = this;\n\t\t\tuni.$on('lp-get-input', function(content) {\n\t\t\t\tif (!self.show) {\n\t\t\t\t\tself.text = content;\n\t\t\t\t}\n\t\t\t\tself.show = true;\n\t\t\t\tself.focus = true\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tsetText: function(v) {\n\t\t\t\tthis.text = v;\n\t\t\t},\n\t\t\tgetText: function() {\n\t\t\t\treturn this.text;\n\t\t\t},\n\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 得到焦点\n\t\t\t */\n\t\t\tonFocusHandler: function(e) {\n\t\t\t\tthis.focus = true;\n\t\t\t\tthis.keyboardHeight = e.detail.height;\n\t\t\t},\n\t\t\tonBlurHandler: function(e) {\n\t\t\t\tthis.focus = false;\n\t\t\t\tthis.keyboardHeight = 0;\n\t\t\t},\n\t\t\tonCloseHandler: function() {\n\t\t\t\tthis.keyboardHeight = -190;\n\t\t\t\tthis.focus = false\n\t\t\t\tthis.show = false;\n\t\t\t\tuni.$emit('lp-input-completed', this.text);\n\t\t\t},\n\t\t\t/**\n\t\t\t * 笔译或者口译有变化时调用\n\t\t\t */\n\t\t\tonChangeHandler: function() {},\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.root-box {\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\tbackground: #fff;\n\t\ttransition: all 0.3s ease;\n\t\ttransform: translateY(100%);\n\n\t\t.input-box {\n\t\t\tpadding: 20rpx;\n\t\t\tborder-top: solid 1px #ddd;\n\t\t\theight: 150rpx;\n\t\t\t-moz-box-shadow: 0px -1px 5px #ddd;\n\t\t\t-webkit-box-shadow: 0px -1px 5px #ddd;\n\t\t\tbox-shadow: 0px -1px 5px #ddd;\n\n\t\t\t.close-box {\n\t\t\t\tbackground: #28b28b;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t\tpadding: 20rpx;\n\t\t\t\t//width: 50rpx;\n\t\t\t}\n\n\t\t\t.input {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\theight: 128rpx;\n\t\t\t\tpadding: 10rpx;\n\t\t\t\t/* \n\t\t\t\tborder: solid 1px #eee;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tbox-shadow: inset 0px 0px 3px 1px #eee; */\n\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tborder: solid 1px $uni-color-primary;\n\t\t\t}\n\t\t}\n\n\t\t.show {}\n\t}\n\n\t.show {\n\t\ttransform: translateY(0);\n\t}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-input.vue?vue&type=style&index=0&id=4af1b324&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-input.vue?vue&type=style&index=0&id=4af1b324&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689564695\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}