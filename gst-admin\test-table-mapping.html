<!DOCTYPE html>
<html>
<head>
    <title>表名映射测试</title>
</head>
<body>
    <h1>表名映射测试</h1>
    <div id="results"></div>

    <script type="module">
        // 测试表名映射函数
        function mapTableNameToType(tableName) {
            const mapping = {
                'courses_classify': 'categories',
                'courses': 'courses',
                'courses_item': 'courses', // 课程项目表映射到课程
                'course_units': 'course_units',
                'course_chapters': 'course_units', // 如果有章节表也映射到单元
                'course_items': 'course_units', // 课程项目也可以映射到单元
                'courses_units': 'course_units', // 课程单元表的其他可能命名
                'lesson': 'course_units', // 课时表
                'lessons': 'course_units', // 课时表复数
                'course_lesson': 'course_units', // 课程课时表
                'course_lessons': 'course_units' // 课程课时表复数
            }

            return mapping[tableName] || tableName
        }

        // 测试用例
        const testCases = [
            { tableName: 'courses_item', expectedType: 'courses' },
            { tableName: 'courses_classify', expectedType: 'categories' },
            { tableName: 'course_units', expectedType: 'course_units' },
            { tableName: 'courses', expectedType: 'courses' },
            { tableName: 'lessons', expectedType: 'course_units' }
        ]

        const resultsDiv = document.getElementById('results')
        let html = '<h2>测试结果:</h2><ul>'

        testCases.forEach(testCase => {
            const actualType = mapTableNameToType(testCase.tableName)
            const isCorrect = actualType === testCase.expectedType
            const status = isCorrect ? '✅' : '❌'
            
            html += `<li>${status} ${testCase.tableName} → ${actualType} (期望: ${testCase.expectedType})</li>`
        })

        html += '</ul>'
        resultsDiv.innerHTML = html

        console.log('表名映射测试完成')
    </script>
</body>
</html>
