const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Course = sequelize.define('Course', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [2, 200]
      }
    },
    description: {
      type: DataTypes.TEXT
    },
    content: {
      type: DataTypes.TEXT('long'),
      comment: '课程内容'
    },
    level: {
      type: DataTypes.ENUM('N5', 'N4', 'N3', 'N2', 'N1'),
      allowNull: false
    },
    categoryId: {
      type: DataTypes.INTEGER,
      field: 'category_id',
      references: {
        model: 'categories',
        key: 'id'
      },
      comment: '课程分类ID'
    },
    category: {
      type: DataTypes.ENUM('grammar', 'vocabulary', 'listening', 'speaking', 'reading', 'writing'),
      allowNull: false,
      comment: '课程类型（保留字段）'
    },
    duration: {
      type: DataTypes.INTEGER,
      defaultValue: 60,
      comment: '课程时长(分钟)',
      validate: {
        min: 1
      }
    },
    difficulty: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        min: 1,
        max: 5
      },
      comment: '难度等级1-5'
    },
    orderNum: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'order_num',
      comment: '排序序号'
    },
    status: {
      type: DataTypes.ENUM('draft', 'published', 'archived'),
      defaultValue: 'draft',
      allowNull: false
    },
    coverImage: {
      type: DataTypes.STRING(255),
      field: 'cover_image',
      validate: {
        isUrl: true
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00,
      comment: '课程价格',
      validate: {
        min: 0
      }
    },
    videoUrl: {
      type: DataTypes.STRING(255),
      field: 'video_url',
      validate: {
        isUrl: true
      }
    },
    audioUrl: {
      type: DataTypes.STRING(255),
      field: 'audio_url',
      validate: {
        isUrl: true
      }
    },
    attachments: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: '附件列表'
    },
    viewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'view_count',
      validate: {
        min: 0
      }
    },
    likeCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'like_count',
      validate: {
        min: 0
      }
    },
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    prerequisites: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: '前置课程要求'
    },
    objectives: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: '学习目标'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      field: 'created_by',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'courses',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    indexes: [
      { fields: ['title'] },
      { fields: ['level'] },
      { fields: ['category'] },
      { fields: ['status'] },
      { fields: ['created_by'] },
      { fields: ['order_num'] }
    ]
  });

  // 实例方法
  Course.prototype.isPublished = function() {
    return this.status === 'published';
  };

  Course.prototype.publish = async function() {
    this.status = 'published';
    await this.save();
  };

  Course.prototype.archive = async function() {
    this.status = 'archived';
    await this.save();
  };

  Course.prototype.incrementView = async function() {
    await this.increment('viewCount');
  };

  Course.prototype.incrementLike = async function() {
    await this.increment('likeCount');
  };

  Course.prototype.getDifficultyText = function() {
    const difficultyMap = {
      1: '入门',
      2: '初级',
      3: '中级',
      4: '高级',
      5: '专家'
    };
    return difficultyMap[this.difficulty] || '未知';
  };

  Course.prototype.getCategoryText = function() {
    const categoryMap = {
      grammar: '语法',
      vocabulary: '词汇',
      listening: '听力',
      speaking: '口语',
      reading: '阅读',
      writing: '写作'
    };
    return categoryMap[this.category] || '其他';
  };

  // 关联定义
  Course.associate = function(models) {
    // 课程属于分类
    Course.belongsTo(models.Category, {
      foreignKey: 'categoryId',
      as: 'courseCategory'
    });

    // 课程由用户创建
    Course.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });

    // 课程有多个单元
    Course.hasMany(models.CourseUnit, {
      foreignKey: 'courseId',
      as: 'units'
    });

    // 课程属于多个小组
    Course.belongsToMany(models.StudyGroup, {
      through: models.GroupCourse,
      foreignKey: 'courseId',
      as: 'groups'
    });

    // 课程的小组记录
    Course.hasMany(models.GroupCourse, {
      foreignKey: 'courseId',
      as: 'groupRecords'
    });

    // 课程的学习记录
    Course.hasMany(models.LearningRecord, {
      foreignKey: 'courseId',
      as: 'learningRecords'
    });

    // 课程的作业
    Course.hasMany(models.Assignment, {
      foreignKey: 'courseId',
      as: 'assignments'
    });
  };

  return Course;
};
