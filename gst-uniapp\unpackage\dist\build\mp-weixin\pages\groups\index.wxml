<gui-page vue-id="4e83e0b6-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" class="data-v-12a9f9ce vue-ref" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1 clean-groups-page data-v-12a9f9ce" slot="gBody"><block wx:if="{{hasGroupPermission}}"><view class="page-content data-v-12a9f9ce"><view class="page-header data-v-12a9f9ce"><view class="header-content data-v-12a9f9ce"><view class="title-section data-v-12a9f9ce"><text class="page-title data-v-12a9f9ce">🎓 GST派遣日语培训班</text><text class="page-subtitle data-v-12a9f9ce">与同伴一起进步，共同成长</text></view><view class="stats-section data-v-12a9f9ce"><view class="stat-item data-v-12a9f9ce"><text class="stat-number data-v-12a9f9ce">{{$root.g0}}</text><text class="stat-label data-v-12a9f9ce">个班</text></view></view></view></view><view class="split-layout data-v-12a9f9ce"><view class="left-panel data-v-12a9f9ce"><view class="panel-header data-v-12a9f9ce"><text class="panel-title data-v-12a9f9ce">学习内容</text><text class="panel-subtitle data-v-12a9f9ce">{{$root.g1+1+"项"}}</text></view><scroll-view class="group-list data-v-12a9f9ce" scroll-y="true"><view data-event-opts="{{[['tap',[['selectConceptTutorial']]]]}}" class="{{['concept-tutorial-item','data-v-12a9f9ce',(selectedGroupId==='concept')?'active':'']}}" bindtap="__e"><view class="concept-content data-v-12a9f9ce"><view class="concept-icon data-v-12a9f9ce">📖</view><text class="concept-title data-v-12a9f9ce">新标日本语教程</text><view class="concept-badge data-v-12a9f9ce">公共</view></view></view><block wx:for="{{groupList}}" wx:for-item="group" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectGroupForDetail',['$0',index],[[['groupList','',index]]]]]]]}}" class="{{['group-item','data-v-12a9f9ce',(selectedGroupId===group.id)?'active':'']}}" bindtap="__e"><view class="simple-group-content data-v-12a9f9ce"><text class="simple-group-name data-v-12a9f9ce">{{group.name}}</text><view class="{{['simple-status-dot','data-v-12a9f9ce',group.status]}}"></view></view></view></block></scroll-view></view><view class="right-panel data-v-12a9f9ce"><block wx:if="{{selectedGroupId==='concept'}}"><view class="concept-detail-content data-v-12a9f9ce"><view class="concept-detail-header data-v-12a9f9ce"><view class="concept-bg data-v-12a9f9ce"><view class="concept-overlay data-v-12a9f9ce"><view class="concept-detail-icon data-v-12a9f9ce">📖</view><view class="concept-detail-info data-v-12a9f9ce"><text class="concept-detail-title data-v-12a9f9ce">{{conceptTutorial.title}}</text><text class="concept-detail-subtitle data-v-12a9f9ce">{{conceptTutorial.description}}</text></view></view></view></view><view class="concept-categories data-v-12a9f9ce"><view class="categories-title data-v-12a9f9ce">学习内容</view><view class="simple-category-list data-v-12a9f9ce"><block wx:for="{{currentCategoryList}}" wx:for-item="category" wx:for-index="categoryIndex" wx:key="categoryIndex"><view class="simple-category-item data-v-12a9f9ce"><view data-event-opts="{{[['tap',[['toggleCategory',[categoryIndex]]]]]}}" class="category-header data-v-12a9f9ce" bindtap="__e"><view class="category-left data-v-12a9f9ce"><view class="category-icon data-v-12a9f9ce">{{category.icon}}</view><text class="category-name data-v-12a9f9ce">{{category.title}}</text></view><view class="{{['category-toggle','data-v-12a9f9ce',(category.expanded)?'expanded':'']}}"><text class="toggle-icon data-v-12a9f9ce">{{category.expanded?'▼':'▶'}}</text></view></view><block wx:if="{{category.expanded}}"><view class="lessons-list data-v-12a9f9ce"><block wx:for="{{category.li}}" wx:for-item="lesson" wx:for-index="lessonIndex" wx:key="lessonIndex"><view data-event-opts="{{[['tap',[['enterLesson',['$0','$1'],[[['currentCategoryList','',categoryIndex]],[['currentCategoryList','',categoryIndex],['li','',lessonIndex]]]]]]]}}" class="lesson-item data-v-12a9f9ce" bindtap="__e"><view class="lesson-number data-v-12a9f9ce">{{lessonIndex+1}}</view><view class="lesson-content data-v-12a9f9ce"><text class="lesson-title data-v-12a9f9ce">{{lesson.title}}</text><text class="lesson-subtitle data-v-12a9f9ce">{{lesson.subtitle}}</text></view><view class="{{['lesson-status','data-v-12a9f9ce',lesson.completed?'completed':'pending']}}"><text class="status-icon data-v-12a9f9ce">{{lesson.completed?'✓':'○'}}</text></view></view></block></view></block></view></block></view></view></view></block><block wx:else><block wx:if="{{selectedGroup}}"><view class="detail-content data-v-12a9f9ce"><view class="group-header data-v-12a9f9ce"><view class="group-info data-v-12a9f9ce"><view class="group-avatar data-v-12a9f9ce"><image src="{{selectedGroup.icon}}" mode="aspectFit" class="data-v-12a9f9ce"></image></view><view class="group-details data-v-12a9f9ce"><text class="group-name data-v-12a9f9ce">{{selectedGroup.name}}</text><text class="group-desc data-v-12a9f9ce">{{selectedGroup.description}}</text><view class="group-stats data-v-12a9f9ce"></view></view></view></view><view class="group-categories data-v-12a9f9ce"><view class="categories-title data-v-12a9f9ce">学习内容</view><view class="simple-category-list data-v-12a9f9ce"><block wx:for="{{currentCategoryList}}" wx:for-item="category" wx:for-index="categoryIndex" wx:key="categoryIndex"><view class="simple-category-item data-v-12a9f9ce"><view data-event-opts="{{[['tap',[['toggleCategory',[categoryIndex]]]]]}}" class="category-header data-v-12a9f9ce" bindtap="__e"><view class="category-left data-v-12a9f9ce"><view class="category-icon data-v-12a9f9ce">{{category.icon}}</view><text class="category-name data-v-12a9f9ce">{{category.title}}</text></view><view class="{{['category-toggle','data-v-12a9f9ce',(category.expanded)?'expanded':'']}}"><text class="toggle-icon data-v-12a9f9ce">{{category.expanded?'▼':'▶'}}</text></view></view><block wx:if="{{category.expanded}}"><view class="lessons-list data-v-12a9f9ce"><block wx:for="{{category.li}}" wx:for-item="lesson" wx:for-index="lessonIndex" wx:key="lessonIndex"><view data-event-opts="{{[['tap',[['enterLesson',['$0','$1'],[[['currentCategoryList','',categoryIndex]],[['currentCategoryList','',categoryIndex],['li','',lessonIndex]]]]]]]}}" class="lesson-item data-v-12a9f9ce" bindtap="__e"><view class="lesson-number data-v-12a9f9ce">{{lessonIndex+1}}</view><view class="lesson-content data-v-12a9f9ce"><text class="lesson-title data-v-12a9f9ce">{{lesson.title}}</text></view><view class="{{['lesson-status','data-v-12a9f9ce',lesson.completed?'completed':'pending']}}"><text class="status-icon data-v-12a9f9ce">{{lesson.completed?'✓':'○'}}</text></view></view></block></view></block></view></block></view></view></view></block><block wx:else><view class="empty-detail data-v-12a9f9ce"><view class="empty-icon data-v-12a9f9ce">👈</view><text class="empty-title data-v-12a9f9ce">选择一个小组</text><text class="empty-desc data-v-12a9f9ce">点击左侧小组查看详细信息</text></view></block></block></view></view></view></block><block wx:else><view class="no-permission-page data-v-12a9f9ce"><view class="permission-container data-v-12a9f9ce"><view class="permission-icon data-v-12a9f9ce">🔒</view><text class="permission-title data-v-12a9f9ce">访问受限</text><text class="permission-desc data-v-12a9f9ce">您暂时没有访问学习小组的权限</text><text class="permission-hint data-v-12a9f9ce">请联系管理员开通权限或使用授权账号登录</text><view class="permission-actions data-v-12a9f9ce"><button data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="btn-login data-v-12a9f9ce" bindtap="__e">重新登录</button><button data-event-opts="{{[['tap',[['contactAdmin',['$event']]]]]}}" class="btn-contact data-v-12a9f9ce" bindtap="__e">联系管理员</button></view></view></view></block></view></gui-page>