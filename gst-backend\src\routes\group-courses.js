const express = require('express');
const router = express.Router();

// 模拟小组课程数据
let groupCourses = [
  {
    id: 1,
    groupId: 1,
    courseId: 1,
    orderNum: 1,
    status: 'active',
    isRequired: true,
    startDate: '2024-01-15',
    endDate: '2024-03-15',
    createdAt: new Date(),
    updatedAt: new Date(),
    course: {
      id: 1,
      title: 'N5基础日语',
      level: 'N5',
      category: 'grammar',
      price: 199,
      picture: '',
      status: 'published'
    }
  },
  {
    id: 2,
    groupId: 1,
    courseId: 2,
    orderNum: 2,
    status: 'pending',
    isRequired: true,
    startDate: '2024-02-01',
    endDate: '2024-04-01',
    createdAt: new Date(),
    updatedAt: new Date(),
    course: {
      id: 2,
      title: 'N5词汇训练',
      level: 'N5',
      category: 'vocabulary',
      price: 159,
      picture: '',
      status: 'published'
    }
  }
];

let nextId = 3;

// 获取小组的课程列表
router.get('/:groupId/courses', (req, res) => {
  try {
    const { groupId } = req.params;
    
    const courses = groupCourses
      .filter(gc => gc.groupId === parseInt(groupId))
      .map(gc => ({
        id: gc.course.id,
        title: gc.course.title,
        level: gc.course.level,
        category: gc.course.category,
        price: gc.course.price,
        picture: gc.course.picture,
        orderNum: gc.orderNum,
        status: gc.status,
        isRequired: gc.isRequired,
        startDate: gc.startDate,
        endDate: gc.endDate,
        groupCourseId: gc.id
      }));
    
    res.json({
      success: true,
      data: {
        courses,
        total: courses.length
      }
    });
  } catch (error) {
    console.error('获取小组课程失败:', error);
    res.status(500).json({
      success: false,
      message: '获取小组课程失败'
    });
  }
});

// 为小组添加课程
router.post('/:groupId/courses', (req, res) => {
  try {
    const { groupId } = req.params;
    const { courseIds, isRequired = true, startDate, endDate } = req.body;
    
    if (!Array.isArray(courseIds) || courseIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '课程ID列表不能为空'
      });
    }
    
    const addedCourses = [];
    
    courseIds.forEach((courseId, index) => {
      // 检查是否已经存在
      const exists = groupCourses.find(gc => 
        gc.groupId === parseInt(groupId) && gc.courseId === parseInt(courseId)
      );
      
      if (!exists) {
        const newGroupCourse = {
          id: nextId++,
          groupId: parseInt(groupId),
          courseId: parseInt(courseId),
          orderNum: groupCourses.filter(gc => gc.groupId === parseInt(groupId)).length + index + 1,
          status: 'pending',
          isRequired,
          startDate,
          endDate,
          createdAt: new Date(),
          updatedAt: new Date(),
          course: {
            id: courseId,
            title: `课程 ${courseId}`,
            level: 'N5',
            category: 'grammar',
            price: 199,
            picture: '',
            status: 'published'
          }
        };
        
        groupCourses.push(newGroupCourse);
        addedCourses.push(newGroupCourse);
      }
    });
    
    res.status(201).json({
      success: true,
      data: {
        addedCourses: addedCourses.length,
        courses: addedCourses
      },
      message: `成功添加 ${addedCourses.length} 个课程`
    });
  } catch (error) {
    console.error('添加小组课程失败:', error);
    res.status(500).json({
      success: false,
      message: '添加小组课程失败'
    });
  }
});

// 更新小组课程信息
router.put('/:groupId/courses/:courseId', (req, res) => {
  try {
    const { groupId, courseId } = req.params;
    const { orderNum, status, isRequired, startDate, endDate } = req.body;
    
    const groupCourseIndex = groupCourses.findIndex(gc => 
      gc.groupId === parseInt(groupId) && gc.courseId === parseInt(courseId)
    );
    
    if (groupCourseIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '小组课程不存在'
      });
    }
    
    // 更新课程信息
    const updatedGroupCourse = {
      ...groupCourses[groupCourseIndex],
      ...(orderNum !== undefined && { orderNum }),
      ...(status && { status }),
      ...(isRequired !== undefined && { isRequired }),
      ...(startDate !== undefined && { startDate }),
      ...(endDate !== undefined && { endDate }),
      updatedAt: new Date()
    };
    
    groupCourses[groupCourseIndex] = updatedGroupCourse;
    
    res.json({
      success: true,
      data: { groupCourse: updatedGroupCourse },
      message: '小组课程更新成功'
    });
  } catch (error) {
    console.error('更新小组课程失败:', error);
    res.status(500).json({
      success: false,
      message: '更新小组课程失败'
    });
  }
});

// 从小组中移除课程
router.delete('/:groupId/courses/:courseId', (req, res) => {
  try {
    const { groupId, courseId } = req.params;
    
    const groupCourseIndex = groupCourses.findIndex(gc => 
      gc.groupId === parseInt(groupId) && gc.courseId === parseInt(courseId)
    );
    
    if (groupCourseIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '小组课程不存在'
      });
    }
    
    groupCourses.splice(groupCourseIndex, 1);
    
    res.json({
      success: true,
      message: '课程移除成功'
    });
  } catch (error) {
    console.error('移除小组课程失败:', error);
    res.status(500).json({
      success: false,
      message: '移除小组课程失败'
    });
  }
});

// 批量移除小组课程
router.delete('/:groupId/courses/batch', (req, res) => {
  try {
    const { groupId } = req.params;
    const { courseIds } = req.body;
    
    if (!Array.isArray(courseIds) || courseIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '课程ID列表不能为空'
      });
    }
    
    let removedCount = 0;
    
    courseIds.forEach(courseId => {
      const groupCourseIndex = groupCourses.findIndex(gc => 
        gc.groupId === parseInt(groupId) && gc.courseId === parseInt(courseId)
      );
      
      if (groupCourseIndex !== -1) {
        groupCourses.splice(groupCourseIndex, 1);
        removedCount++;
      }
    });
    
    res.json({
      success: true,
      data: { removedCount },
      message: `成功移除 ${removedCount} 个课程`
    });
  } catch (error) {
    console.error('批量移除小组课程失败:', error);
    res.status(500).json({
      success: false,
      message: '批量移除小组课程失败'
    });
  }
});

// 批量更新小组课程排序
router.put('/:groupId/courses/batch/sort', (req, res) => {
  try {
    const { groupId } = req.params;
    const { courses } = req.body;
    
    if (!Array.isArray(courses)) {
      return res.status(400).json({
        success: false,
        message: '课程数据格式错误'
      });
    }
    
    let updatedCount = 0;
    
    courses.forEach(({ courseId, orderNum }) => {
      const groupCourseIndex = groupCourses.findIndex(gc => 
        gc.groupId === parseInt(groupId) && gc.courseId === parseInt(courseId)
      );
      
      if (groupCourseIndex !== -1) {
        groupCourses[groupCourseIndex].orderNum = orderNum;
        groupCourses[groupCourseIndex].updatedAt = new Date();
        updatedCount++;
      }
    });
    
    res.json({
      success: true,
      data: { updatedCount },
      message: `成功更新 ${updatedCount} 个课程的排序`
    });
  } catch (error) {
    console.error('批量更新排序失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新排序失败'
    });
  }
});

module.exports = router;
