2025-07-28 06:34:18 [ERROR]: SequelizeConnectionError: �û� "postgres" Password ��֤ʧ��
    at Client._connectionCallback (D:\gst-backend\node_modules\sequelize\lib\dialects\postgres\connection-manager.js:145:24)
    at Client._handleErrorWhileConnecting (D:\gst-backend\node_modules\pg\lib\client.js:336:19)
    at Client._handleErrorMessage (D:\gst-backend\node_modules\pg\lib\client.js:356:19)
    at Connection.emit (node:events:518:28)
    at D:\gst-backend\node_modules\pg\lib\connection.js:116:12
    at Parser.parse (D:\gst-backend\node_modules\pg-protocol\dist\parser.js:36:17)
    at Socket.<anonymous> (D:\gst-backend\node_modules\pg-protocol\dist\index.js:11:42)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
2025-07-28 08:14:48 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:16:49 [ERROR]: SequelizeEagerLoadingError: User is associated to GroupMember using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at GroupMember._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at GroupMember._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at GroupMember._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at GroupMember.findAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1124:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async D:\gst-backend\src\routes\groups-simple.js:260:19
2025-07-28 08:29:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.insert (D:\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:308:21)
    at async model.save (D:\gst-backend\node_modules\sequelize\lib\model.js:2490:35)
    at async Course.create (D:\gst-backend\node_modules\sequelize\lib\model.js:1362:12)
    at async D:\gst-backend\src\routes\courses-simple.js:134:18
2025-07-28 08:29:42 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:30:14 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:32:01 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:32:46 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:33:32 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:33:59 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:34:17 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:34:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:34:59 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:45:33 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:46:22 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:47:28 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:47:33 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 09:11:08 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:11:50 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:12:02 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:40:26 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:41:01 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:42:38 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:42:50 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:43:02 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:43:51 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:44:06 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:44:34 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:52:39 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:52:53 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:55:53 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:56:04 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:56:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:56:33 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 10:10:27 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:10:42 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:18:47 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:19:09 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:19:27 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:19:46 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:29:23 [ERROR]: 数据导入失败: import_menus
2025-07-28 10:31:20 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:31:57 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:10 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:24 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:42 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:56 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:40:50 [ERROR]: 数据导入失败: import_menus
2025-07-28 10:48:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:52:37 [ERROR]: 文件导入失败: import_menus
2025-07-28 10:54:16 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:54:42 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:54:58 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:55:15 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:59:19 [ERROR]: 文件导入失败: import_menus
2025-07-28 11:04:21 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:04:38 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:22:49 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:46:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:46:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:47:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:47:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:53:56 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:54:16 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 12:14:36 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 12:37:45 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 12:58:30 [ERROR]: Error: listen EADDRINUSE: address already in use 0.0.0.0:8005
    at Server.setupListenHandle [as _listen2] (node:net:1937:16)
    at listenInCluster (node:net:1994:12)
    at node:net:2203:7
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
2025-07-28 12:59:04 [ERROR]: Error: listen EADDRINUSE: address already in use 0.0.0.0:8005
    at Server.setupListenHandle [as _listen2] (node:net:1937:16)
    at listenInCluster (node:net:1994:12)
    at node:net:2203:7
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
2025-07-28 13:15:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 17:31:11 [ERROR]: TypeError: Cannot read properties of undefined (reading 'or')
    at D:\gst\gst-backend\src\routes\groups-simple.js:28:41
    at D:\gst\gst-backend\src\utils\asyncHandler.js:7:21
    at Layer.handle [as handle_request] (D:\gst\gst-backend\node_modules\express\lib\router\layer.js:95:5)
    at next (D:\gst\gst-backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (D:\gst\gst-backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (D:\gst\gst-backend\node_modules\express\lib\router\layer.js:95:5)
    at D:\gst\gst-backend\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (D:\gst\gst-backend\node_modules\express\lib\router\index.js:346:12)
    at next (D:\gst\gst-backend\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (D:\gst\gst-backend\node_modules\express\lib\router\index.js:175:3)
2025-07-28 19:54:03 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Banner.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async D:\gst\gst-backend\src\routes\home.js:173:21
2025-07-28 20:01:12 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async CourseUnit.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async CourseUnit.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst\gst-backend\src\routes\courseUnits.js:21:34
2025-07-28 22:52:14 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async CourseUnit.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async CourseUnit.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst\gst-backend\src\routes\courseUnits.js:21:34
2025-07-28 23:19:10 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Course.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async Course.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
2025-07-28 23:19:10 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Category.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async D:\gst\gst-backend\src\routes\categories.js:13:24
2025-07-28 23:19:28 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Category.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async D:\gst\gst-backend\src\routes\categories.js:13:24
2025-07-28 23:32:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Course.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async Course.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
2025-07-29 05:23:55 [ERROR]: TypeError: Cannot read properties of undefined (reading 'or')
    at Banner.getActiveBanners (D:\gst\gst-backend\src\models\Banner.js:136:23)
    at D:\gst\gst-backend\src\routes\home.js:12:34
    at D:\gst\gst-backend\src\utils\asyncHandler.js:7:21
    at Layer.handle [as handle_request] (D:\gst\gst-backend\node_modules\express\lib\router\layer.js:95:5)
    at next (D:\gst\gst-backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (D:\gst\gst-backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (D:\gst\gst-backend\node_modules\express\lib\router\layer.js:95:5)
    at D:\gst\gst-backend\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (D:\gst\gst-backend\node_modules\express\lib\router\index.js:346:12)
    at next (D:\gst\gst-backend\node_modules\express\lib\router\index.js:280:10)
2025-07-29 05:27:04 [ERROR]: TypeError: Cannot read properties of undefined (reading 'or')
    at Banner.getActiveBanners (D:\gst\gst-backend\src\models\Banner.js:136:23)
    at D:\gst\gst-backend\src\routes\home.js:12:34
    at D:\gst\gst-backend\src\utils\asyncHandler.js:7:21
    at Layer.handle [as handle_request] (D:\gst\gst-backend\node_modules\express\lib\router\layer.js:95:5)
    at next (D:\gst\gst-backend\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (D:\gst\gst-backend\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (D:\gst\gst-backend\node_modules\express\lib\router\layer.js:95:5)
    at D:\gst\gst-backend\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (D:\gst\gst-backend\node_modules\express\lib\router\index.js:346:12)
    at next (D:\gst\gst-backend\node_modules\express\lib\router\index.js:280:10)
2025-07-29 05:54:08 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Course.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async Course.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst\gst-backend\src\routes\courses-simple.js:35:36
2025-07-29 05:56:45 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async Course.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async Course.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst\gst-backend\src\routes\courses-simple.js:35:36
2025-07-29 05:57:37 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async StudyGroup.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async StudyGroup.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst\gst-backend\src\routes\groups-simple.js:34:35
2025-07-29 05:57:43 [ERROR]: Error
    at Database.<anonymous> (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.select (D:\gst\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async StudyGroup.findAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1140:21)
    at async Promise.all (index 1)
    at async StudyGroup.findAndCountAll (D:\gst\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst\gst-backend\src\routes\groups-simple.js:34:35
