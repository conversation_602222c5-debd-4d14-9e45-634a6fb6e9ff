<template>
  <div class="dev-tools" v-if="isDev">
    <!-- 开发工具按钮 -->
    <div class="dev-tools-trigger" @click="togglePanel">
      <el-icon :size="20">
        <Tools />
      </el-icon>
    </div>

    <!-- 开发工具面板 -->
    <el-drawer
      v-model="panelVisible"
      title="开发者工具"
      direction="rtl"
      size="400px"
    >
      <div class="dev-tools-content">
        <!-- 性能监控 -->
        <el-collapse v-model="activeNames">
          <el-collapse-item title="性能监控" name="performance">
            <div class="performance-section">
              <div class="metric-item" v-for="(value, key) in performanceMetrics" :key="key">
                <span class="metric-label">{{ getMetricLabel(key) }}:</span>
                <span class="metric-value">{{ formatMetricValue(key, value) }}</span>
              </div>
              
              <div class="performance-actions">
                <el-button size="small" @click="refreshPerformance">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button size="small" @click="generateReport">
                  <el-icon><Document /></el-icon>
                  生成报告
                </el-button>
              </div>
            </div>
          </el-collapse-item>

          <!-- 缓存管理 -->
          <el-collapse-item title="缓存管理" name="cache">
            <div class="cache-section">
              <div class="cache-stats">
                <div class="stat-item" v-for="(stat, type) in cacheStats" :key="type">
                  <div class="stat-label">{{ type }}:</div>
                  <div class="stat-value">{{ stat.count }} 项</div>
                </div>
              </div>
              
              <div class="cache-actions">
                <el-button size="small" @click="refreshCache">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button size="small" type="warning" @click="clearAllCache">
                  <el-icon><Delete /></el-icon>
                  清空缓存
                </el-button>
              </div>
              
              <div class="cache-details" v-if="showCacheDetails">
                <el-tabs v-model="activeCacheTab">
                  <el-tab-pane 
                    v-for="(stat, type) in cacheStats" 
                    :key="type"
                    :label="`${type} (${stat.count})`"
                    :name="type"
                  >
                    <div class="cache-keys">
                      <div 
                        class="cache-key-item" 
                        v-for="key in stat.keys" 
                        :key="key"
                      >
                        <span class="key-name">{{ key }}</span>
                        <el-button
                          size="small"
                          link
                          @click="deleteCacheKey(key, type)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              
              <el-button
                size="small"
                link
                @click="showCacheDetails = !showCacheDetails"
              >
                {{ showCacheDetails ? '隐藏' : '显示' }}详情
              </el-button>
            </div>
          </el-collapse-item>

          <!-- 测试工具 -->
          <el-collapse-item title="测试工具" name="test">
            <div class="test-section">
              <div class="test-actions">
                <el-button size="small" @click="runFunctionalTests" :loading="testRunning">
                  <el-icon><CircleCheck /></el-icon>
                  功能测试
                </el-button>
                <el-button size="small" @click="runBenchmarkTests" :loading="benchmarkRunning">
                  <el-icon><Stopwatch /></el-icon>
                  性能测试
                </el-button>
              </div>
              
              <div class="test-results" v-if="testResults.length > 0">
                <h4>测试结果:</h4>
                <div 
                  class="test-result-item" 
                  v-for="result in testResults" 
                  :key="result.name"
                  :class="result.status"
                >
                  <el-icon>
                    <CircleCheck v-if="result.status === 'passed'" />
                    <CircleClose v-else />
                  </el-icon>
                  <span class="test-name">{{ result.name }}</span>
                  <span class="test-duration">{{ result.duration?.toFixed(2) }}ms</span>
                </div>
              </div>
            </div>
          </el-collapse-item>

          <!-- 系统信息 -->
          <el-collapse-item title="系统信息" name="system">
            <div class="system-section">
              <div class="system-info">
                <div class="info-item">
                  <span class="info-label">浏览器:</span>
                  <span class="info-value">{{ systemInfo.browser }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">版本:</span>
                  <span class="info-value">{{ systemInfo.version }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">屏幕:</span>
                  <span class="info-value">{{ systemInfo.screen }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">内存:</span>
                  <span class="info-value">{{ systemInfo.memory }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">连接:</span>
                  <span class="info-value">{{ systemInfo.connection }}</span>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 检查是否为开发环境
const isDev = computed(() => import.meta.env.DEV)

// 响应式数据
const panelVisible = ref(false)
const activeNames = ref(['performance'])
const activeCacheTab = ref('memory')
const showCacheDetails = ref(false)
const testRunning = ref(false)
const benchmarkRunning = ref(false)
const testResults = ref([])

const performanceMetrics = reactive({
  pageLoadTime: 0,
  memoryUsage: 0,
  requestCount: 0,
  cacheHitRate: 0
})

const cacheStats = reactive({
  memory: { count: 0, keys: [] },
  localStorage: { count: 0, keys: [] },
  sessionStorage: { count: 0, keys: [] }
})

const systemInfo = reactive({
  browser: '',
  version: '',
  screen: '',
  memory: '',
  connection: ''
})

// 切换面板显示
const togglePanel = () => {
  panelVisible.value = !panelVisible.value
  if (panelVisible.value) {
    refreshAll()
  }
}

// 刷新所有数据
const refreshAll = () => {
  refreshPerformance()
  refreshCache()
  refreshSystemInfo()
}

// 刷新性能数据
const refreshPerformance = async () => {
  try {
    const { generateReport } = await import('@/utils/performance')
    const report = generateReport()
    
    performanceMetrics.pageLoadTime = report.summary?.totalLoadTime || 0
    performanceMetrics.memoryUsage = report.memoryUsage?.usage || 0
    performanceMetrics.requestCount = report.metrics?.request_success?.length || 0
    
    const cacheHits = report.metrics?.request_cache_hit?.length || 0
    const totalRequests = (report.metrics?.request_success?.length || 0) + cacheHits
    performanceMetrics.cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests * 100) : 0
  } catch (error) {
    console.error('刷新性能数据失败:', error)
  }
}

// 刷新缓存数据
const refreshCache = async () => {
  try {
    const { getCacheStats } = await import('@/utils/cache')
    const stats = getCacheStats()
    Object.assign(cacheStats, stats)
  } catch (error) {
    console.error('刷新缓存数据失败:', error)
  }
}

// 刷新系统信息
const refreshSystemInfo = () => {
  systemInfo.browser = navigator.userAgent.split(' ').pop()
  systemInfo.version = navigator.appVersion
  systemInfo.screen = `${screen.width}x${screen.height}`
  
  if ('memory' in performance) {
    const memory = performance.memory
    systemInfo.memory = `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB / ${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`
  } else {
    systemInfo.memory = '不支持'
  }
  
  if ('connection' in navigator) {
    const conn = navigator.connection
    systemInfo.connection = `${conn.effectiveType} (${conn.downlink}Mbps)`
  } else {
    systemInfo.connection = '未知'
  }
}

// 生成性能报告
const generateReport = async () => {
  try {
    const { sendReport } = await import('@/utils/performance')
    const report = sendReport()
    
    // 下载报告
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${new Date().toISOString().slice(0, 19)}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('性能报告已下载')
  } catch (error) {
    console.error('生成报告失败:', error)
    ElMessage.error('生成报告失败')
  }
}

// 清空所有缓存
const clearAllCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有缓存吗？', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const { clearCache } = await import('@/utils/cache')
    clearCache('memory')
    clearCache('localStorage')
    clearCache('sessionStorage')
    
    refreshCache()
    ElMessage.success('缓存已清空')
  } catch {
    // 用户取消
  }
}

// 删除缓存键
const deleteCacheKey = async (key, storage) => {
  try {
    const { deleteCache } = await import('@/utils/cache')
    deleteCache(key, storage)
    refreshCache()
    ElMessage.success('缓存项已删除')
  } catch (error) {
    console.error('删除缓存失败:', error)
    ElMessage.error('删除缓存失败')
  }
}

// 运行功能测试
const runFunctionalTests = async () => {
  testRunning.value = true
  try {
    const { functionalTests } = await import('@/utils/test')
    const results = await functionalTests.runAll()
    testResults.value = results
    ElMessage.success('功能测试完成')
  } catch (error) {
    console.error('功能测试失败:', error)
    ElMessage.error('功能测试失败')
  } finally {
    testRunning.value = false
  }
}

// 运行性能测试
const runBenchmarkTests = async () => {
  benchmarkRunning.value = true
  try {
    const { benchmarkTests } = await import('@/utils/test')
    await benchmarkTests.testComponentRender()
    await benchmarkTests.testDataProcessing()
    ElMessage.success('性能测试完成，请查看控制台')
  } catch (error) {
    console.error('性能测试失败:', error)
    ElMessage.error('性能测试失败')
  } finally {
    benchmarkRunning.value = false
  }
}

// 格式化指标标签
const getMetricLabel = (key) => {
  const labels = {
    pageLoadTime: '页面加载时间',
    memoryUsage: '内存使用率',
    requestCount: '请求次数',
    cacheHitRate: '缓存命中率'
  }
  return labels[key] || key
}

// 格式化指标值
const formatMetricValue = (key, value) => {
  switch (key) {
    case 'pageLoadTime':
      return `${value.toFixed(2)}ms`
    case 'memoryUsage':
      return `${value.toFixed(1)}%`
    case 'requestCount':
      return `${value} 次`
    case 'cacheHitRate':
      return `${value.toFixed(1)}%`
    default:
      return value
  }
}

// 组件挂载时初始化
onMounted(() => {
  if (isDev.value) {
    refreshSystemInfo()
  }
})
</script>

<style lang="scss" scoped>
.dev-tools-trigger {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
  
  .el-icon {
    color: white;
  }
}

.dev-tools-content {
  .metric-item,
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-base);
    
    &:last-child {
      border-bottom: none;
    }
    
    .metric-label,
    .info-label {
      font-weight: 500;
      color: var(--text-secondary);
    }
    
    .metric-value,
    .info-value {
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .performance-actions,
  .cache-actions,
  .test-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-base);
  }
  
  .cache-stats {
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-xs) 0;
      
      .stat-label {
        font-weight: 500;
        color: var(--text-secondary);
      }
      
      .stat-value {
        font-weight: 600;
        color: var(--primary-color);
      }
    }
  }
  
  .cache-keys {
    max-height: 200px;
    overflow-y: auto;
    
    .cache-key-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-xs) 0;
      border-bottom: 1px solid var(--border-light);
      
      .key-name {
        font-size: var(--font-size-small);
        color: var(--text-regular);
        word-break: break-all;
      }
    }
  }
  
  .test-results {
    margin-top: var(--spacing-md);
    
    h4 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--font-size-base);
      color: var(--text-primary);
    }
    
    .test-result-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-xs) 0;
      
      &.passed {
        color: var(--success-color);
      }
      
      &.failed {
        color: var(--danger-color);
      }
      
      .test-name {
        flex: 1;
        font-size: var(--font-size-small);
      }
      
      .test-duration {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
      }
    }
  }
}
</style>
