<template>
  <div class="page-container">
    <div class="page-header">
      <h1>作业管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          type="primary"
          @click="showCreateDialog = true"
          v-if="authStore.hasRole(['admin', 'teacher'])"
        >
          <el-icon><Plus /></el-icon>
          布置作业
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item label="小组">
          <el-select v-model="filters.groupId" placeholder="选择小组" clearable @change="loadAssignments">
            <el-option
              v-for="group in groups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadAssignments">
            <el-option label="进行中" value="active" />
            <el-option label="已截止" value="expired" />
            <el-option label="已完成" value="completed" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="filters.type" placeholder="选择类型" clearable @change="loadAssignments">
            <el-option label="练习题" value="exercise" />
            <el-option label="作文" value="essay" />
            <el-option label="口语" value="speaking" />
            <el-option label="听力" value="listening" />
            <el-option label="项目" value="project" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="搜索作业标题"
            clearable
            @input="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 作业卡片列表 -->
    <div class="assignments-grid">
      <!-- 加载骨架屏 -->
      <Skeleton v-if="loading" type="cards" :cards="6" />

      <!-- 作业卡片 -->
      <el-card
        v-else
        v-for="assignment in assignments"
        :key="assignment.id"
        class="assignment-card"
        shadow="hover"
      >
        <template #header>
          <div class="card-header">
            <div class="assignment-title">
              <h3>{{ assignment.title }}</h3>
              <div class="assignment-meta">
                <el-tag :type="getTypeType(assignment.type)" size="small">
                  {{ getTypeText(assignment.type) }}
                </el-tag>
                <el-tag :type="getStatusType(assignment.status)" size="small">
                  {{ getStatusText(assignment.status) }}
                </el-tag>
              </div>
            </div>
            <div class="assignment-actions">
              <el-dropdown @command="handleCommand">
                <el-button link size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`view-${assignment.id}`">
                      <el-icon><View /></el-icon>
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="`submissions-${assignment.id}`"
                      v-if="authStore.hasRole(['admin', 'teacher'])"
                    >
                      <el-icon><Document /></el-icon>
                      查看提交
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="`edit-${assignment.id}`"
                      v-if="canEdit(assignment)"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="`submit-${assignment.id}`"
                      v-if="authStore.isStudent && assignment.status === 'active'"
                    >
                      <el-icon><Upload /></el-icon>
                      提交作业
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="`delete-${assignment.id}`"
                      divided
                      v-if="canDelete(assignment)"
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>

        <div class="assignment-content">
          <p class="assignment-description">{{ assignment.description || '暂无描述' }}</p>

          <div class="assignment-info">
            <div class="info-item">
              <el-icon><UserFilled /></el-icon>
              <span>{{ assignment.group?.name || '未指定小组' }}</span>
            </div>
            <div class="info-item">
              <el-icon><Avatar /></el-icon>
              <span>{{ assignment.teacher?.realName || assignment.teacher?.username || '未知' }}</span>
            </div>
            <div class="info-item">
              <el-icon><Calendar /></el-icon>
              <span>截止：{{ formatDateTime(assignment.dueDate) }}</span>
            </div>
            <div class="info-item" v-if="assignment.maxScore">
              <el-icon><Trophy /></el-icon>
              <span>满分：{{ assignment.maxScore }}分</span>
            </div>
          </div>

          <div class="assignment-progress" v-if="authStore.hasRole(['admin', 'teacher'])">
            <div class="progress-label">
              <span>提交进度</span>
              <span>{{ assignment.submittedCount || 0 }}/{{ assignment.totalStudents || 0 }}</span>
            </div>
            <el-progress
              :percentage="getSubmissionProgress(assignment)"
              :stroke-width="6"
              :show-text="false"
            />
          </div>

          <div class="assignment-status" v-if="authStore.isStudent">
            <div class="status-item">
              <span class="status-label">我的状态：</span>
              <el-tag :type="getMyStatusType(assignment.mySubmission?.status)" size="small">
                {{ getMyStatusText(assignment.mySubmission?.status) }}
              </el-tag>
            </div>
            <div class="status-item" v-if="assignment.mySubmission?.score !== undefined">
              <span class="status-label">我的得分：</span>
              <span class="score-text">{{ assignment.mySubmission.score }}/{{ assignment.maxScore }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty
      v-if="!loading && assignments.length === 0"
      description="暂无作业数据"
      :image-size="120"
    >
      <el-button
        type="primary"
        @click="showCreateDialog = true"
        v-if="authStore.hasRole(['admin', 'teacher'])"
      >
        布置第一个作业
      </el-button>
    </el-empty>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[12, 24, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadAssignments"
        @current-change="loadAssignments"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <AssignmentForm
      v-model="showCreateDialog"
      :assignment="selectedAssignment"
      @success="handleFormSuccess"
    />

    <!-- 详情对话框 -->
    <AssignmentDetail
      v-model="showDetailDialog"
      :assignment="selectedAssignment"
      @edit="editAssignment"
    />

    <!-- 提交列表对话框 -->
    <SubmissionList
      v-model="showSubmissionsDialog"
      :assignment="selectedAssignment"
    />

    <!-- 学生提交对话框 -->
    <SubmissionForm
      v-model="showSubmissionDialog"
      :assignment="selectedAssignment"
      @success="handleSubmissionSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import AssignmentForm from './components/AssignmentForm.vue'
import AssignmentDetail from './components/AssignmentDetail.vue'
import SubmissionList from './components/SubmissionList.vue'
import SubmissionForm from './components/SubmissionForm.vue'
import Skeleton from '@/components/Skeleton/index.vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const assignments = ref([])
const groups = ref([])
const total = ref(0)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showSubmissionsDialog = ref(false)
const showSubmissionDialog = ref(false)
const selectedAssignment = ref(null)

// 筛选器
const filters = reactive({
  groupId: '',
  status: '',
  type: '',
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 12
})

// 搜索防抖
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    pagination.page = 1
    loadAssignments()
  }, 500)
}

// 加载作业数据
const loadAssignments = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...filters
    }

    const response = await get('/api/assignments', params, { showLoading: false })
    if (response.success) {
      assignments.value = response.data.assignments || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载作业数据失败:', error)
    // 使用模拟数据
    assignments.value = generateMockAssignments()
    total.value = assignments.value.length
  } finally {
    loading.value = false
  }
}

// 加载小组数据
const loadGroups = async () => {
  try {
    const response = await get('/api/groups', { size: 100 })
    if (response.success) {
      groups.value = response.data.groups || []
    }
  } catch (error) {
    console.error('加载小组数据失败:', error)
    // 使用模拟数据
    groups.value = [
      { id: 1, name: 'N5基础入门班' },
      { id: 2, name: 'N4进阶学习班' },
      { id: 3, name: 'N3中级提升班' }
    ]
  }
}

// 生成模拟数据
const generateMockAssignments = () => {
  return [
    {
      id: 1,
      title: '第三课语法练习',
      description: '完成第三课的语法练习题，重点掌握动词变位',
      type: 'exercise',
      status: 'active',
      dueDate: new Date('2024-08-05T23:59:59'),
      maxScore: 100,
      group: { id: 1, name: 'N5基础入门班' },
      teacher: { realName: '张老师', username: 'teacher1' },
      submittedCount: 15,
      totalStudents: 25,
      mySubmission: authStore.isStudent ? { status: 'submitted', score: 85 } : null,
      createdAt: new Date('2024-07-25')
    },
    {
      id: 2,
      title: '日语作文：我的一天',
      description: '用日语写一篇关于自己一天生活的作文，不少于200字',
      type: 'essay',
      status: 'active',
      dueDate: new Date('2024-08-10T23:59:59'),
      maxScore: 100,
      group: { id: 2, name: 'N4进阶学习班' },
      teacher: { realName: '李老师', username: 'teacher2' },
      submittedCount: 8,
      totalStudents: 22,
      mySubmission: authStore.isStudent ? { status: 'pending' } : null,
      createdAt: new Date('2024-07-28')
    },
    {
      id: 3,
      title: '听力理解练习',
      description: '完成听力材料的理解练习，包含对话和短文',
      type: 'listening',
      status: 'expired',
      dueDate: new Date('2024-07-20T23:59:59'),
      maxScore: 50,
      group: { id: 3, name: 'N3中级提升班' },
      teacher: { realName: '王老师', username: 'teacher3' },
      submittedCount: 18,
      totalStudents: 20,
      mySubmission: authStore.isStudent ? { status: 'graded', score: 42 } : null,
      createdAt: new Date('2024-07-15')
    }
  ]
}

// 刷新数据
const refreshData = () => {
  loadAssignments()
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const assignment = assignments.value.find(a => a.id === parseInt(id))

  switch (action) {
    case 'view':
      selectedAssignment.value = assignment
      showDetailDialog.value = true
      break
    case 'submissions':
      selectedAssignment.value = assignment
      showSubmissionsDialog.value = true
      break
    case 'edit':
      selectedAssignment.value = assignment
      showCreateDialog.value = true
      break
    case 'submit':
      selectedAssignment.value = assignment
      showSubmissionDialog.value = true
      break
    case 'delete':
      handleDeleteAssignment(assignment)
      break
  }
}

// 编辑作业
const editAssignment = (assignment) => {
  selectedAssignment.value = assignment
  showCreateDialog.value = true
}

// 删除作业
const handleDeleteAssignment = async (assignment) => {
  try {
    await ElMessageBox.confirm(`确定要删除作业"${assignment.title}"吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用API
    ElMessage.success('删除成功')
    loadAssignments()
  } catch {
    // 用户取消
  }
}

// 权限检查
const canEdit = (assignment) => {
  if (authStore.isAdmin) return true
  if (authStore.isTeacher && assignment.teacher?.id === authStore.user.id) return true
  return false
}

const canDelete = (assignment) => {
  if (authStore.isAdmin) return true
  if (authStore.isTeacher && assignment.teacher?.id === authStore.user.id && assignment.status === 'draft') return true
  return false
}

// 获取提交进度
const getSubmissionProgress = (assignment) => {
  if (!assignment.totalStudents) return 0
  return Math.round((assignment.submittedCount || 0) / assignment.totalStudents * 100)
}

// 表单成功回调
const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedAssignment.value = null
  loadAssignments()
}

// 提交成功回调
const handleSubmissionSuccess = () => {
  showSubmissionDialog.value = false
  selectedAssignment.value = null
  loadAssignments()
}

// 获取类型样式
const getTypeType = (type) => {
  const types = {
    exercise: 'primary',
    essay: 'success',
    speaking: 'warning',
    listening: 'info',
    project: 'danger'
  }
  return types[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const texts = {
    exercise: '练习题',
    essay: '作文',
    speaking: '口语',
    listening: '听力',
    project: '项目'
  }
  return texts[type] || '其他'
}

// 获取状态样式
const getStatusType = (status) => {
  const types = {
    active: 'success',
    expired: 'danger',
    completed: 'info',
    draft: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    expired: '已截止',
    completed: '已完成',
    draft: '草稿'
  }
  return texts[status] || '未知'
}

// 获取我的提交状态样式
const getMyStatusType = (status) => {
  const types = {
    pending: 'info',
    submitted: 'warning',
    graded: 'success',
    late: 'danger'
  }
  return types[status] || 'info'
}

// 获取我的提交状态文本
const getMyStatusText = (status) => {
  const texts = {
    pending: '未提交',
    submitted: '已提交',
    graded: '已批改',
    late: '迟交'
  }
  return texts[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadGroups()
  loadAssignments()
})
</script>

<style lang="scss" scoped>
.filter-section {
  background: var(--bg-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  margin-bottom: var(--spacing-lg);
}

.assignments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.assignment-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-light);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .assignment-title {
      flex: 1;

      h3 {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-medium);
        font-weight: 600;
        color: var(--text-primary);
      }

      .assignment-meta {
        display: flex;
        gap: var(--spacing-xs);
      }
    }

    .assignment-actions {
      margin-left: var(--spacing-sm);
    }
  }

  .assignment-content {
    .assignment-description {
      color: var(--text-secondary);
      font-size: var(--font-size-small);
      line-height: 1.5;
      margin: 0 0 var(--spacing-md) 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .assignment-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-md);

      .info-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-small);
        color: var(--text-regular);

        .el-icon {
          color: var(--primary-color);
        }
      }
    }

    .assignment-progress {
      margin-bottom: var(--spacing-md);

      .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
        font-size: var(--font-size-small);
        color: var(--text-regular);
      }
    }

    .assignment-status {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);

      .status-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-small);

        .status-label {
          color: var(--text-secondary);
        }

        .score-text {
          font-weight: 600;
          color: var(--primary-color);
        }
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

@media (max-width: 768px) {
  .assignments-grid {
    grid-template-columns: 1fr;
  }

  .filter-section {
    .el-form {
      flex-direction: column;

      .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
      }
    }
  }

  .assignment-info {
    grid-template-columns: 1fr !important;
  }
}
</style>
