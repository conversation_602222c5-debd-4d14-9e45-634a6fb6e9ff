<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-content">
      <div class="error-icon">
        <el-icon :size="64" color="#f56c6c">
          <WarningFilled />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h2>页面出现错误</h2>
        <p class="error-message">{{ errorMessage }}</p>
        
        <div class="error-details" v-if="showDetails">
          <el-collapse>
            <el-collapse-item title="错误详情" name="details">
              <pre class="error-stack">{{ errorStack }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
        
        <div class="error-actions">
          <el-button @click="reload" type="primary">
            <el-icon><Refresh /></el-icon>
            重新加载
          </el-button>
          <el-button @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button @click="toggleDetails" link>
            {{ showDetails ? '隐藏' : '显示' }}错误详情
          </el-button>
        </div>
        
        <div class="error-tips">
          <h4>可能的解决方案：</h4>
          <ul>
            <li>刷新页面重试</li>
            <li>检查网络连接</li>
            <li>清除浏览器缓存</li>
            <li>如果问题持续存在，请联系技术支持</li>
          </ul>
        </div>
      </div>
    </div>
    
    <slot v-else />
  </div>
</template>

<script setup>
import { ref, onErrorCaptured, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const hasError = ref(false)
const errorMessage = ref('')
const errorStack = ref('')
const showDetails = ref(false)

// 捕获错误
onErrorCaptured((error, instance, info) => {
  console.error('ErrorBoundary caught an error:', error, info)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorStack.value = error.stack || '无堆栈信息'
  
  // 发送错误报告（可选）
  reportError(error, info)
  
  // 阻止错误继续传播
  return false
})

// 重新加载
const reload = () => {
  hasError.value = false
  errorMessage.value = ''
  errorStack.value = ''
  showDetails.value = false
  
  nextTick(() => {
    window.location.reload()
  })
}

// 返回首页
const goHome = () => {
  hasError.value = false
  errorMessage.value = ''
  errorStack.value = ''
  showDetails.value = false
  
  router.push('/dashboard')
  ElMessage.success('已返回首页')
}

// 切换详情显示
const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

// 错误报告（可选实现）
const reportError = (error, info) => {
  // 这里可以实现错误上报逻辑
  // 例如发送到错误监控服务
  const errorReport = {
    message: error.message,
    stack: error.stack,
    info: info,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  }
  
  // 发送错误报告到服务器
  // fetch('/api/error-report', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(errorReport)
  // }).catch(console.error)
  
  console.log('Error Report:', errorReport)
}

// 暴露重置方法
const reset = () => {
  hasError.value = false
  errorMessage.value = ''
  errorStack.value = ''
  showDetails.value = false
}

defineExpose({
  reset
})
</script>

<style lang="scss" scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
  text-align: center;
}

.error-icon {
  margin-bottom: var(--spacing-lg);
}

.error-info {
  max-width: 600px;
  width: 100%;
  
  h2 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-large);
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .error-message {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

.error-details {
  margin-bottom: var(--spacing-lg);
  text-align: left;
  
  .error-stack {
    background: var(--bg-color-page);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-small);
    color: var(--text-regular);
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
  }
}

.error-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.error-tips {
  text-align: left;
  background: var(--bg-color-page);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  border-left: 4px solid var(--primary-color);
  
  h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
  }
  
  ul {
    margin: 0;
    padding-left: var(--spacing-lg);
    
    li {
      margin-bottom: var(--spacing-xs);
      font-size: var(--font-size-small);
      color: var(--text-regular);
      line-height: 1.5;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .error-content {
    padding: var(--spacing-lg);
    min-height: 300px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 200px;
    }
  }
}
</style>
