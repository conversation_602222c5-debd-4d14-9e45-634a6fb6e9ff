2025-07-28 06:34:18 [ERROR]: SequelizeConnectionError: �û� "postgres" Password ��֤ʧ��
    at Client._connectionCallback (D:\gst-backend\node_modules\sequelize\lib\dialects\postgres\connection-manager.js:145:24)
    at Client._handleErrorWhileConnecting (D:\gst-backend\node_modules\pg\lib\client.js:336:19)
    at Client._handleErrorMessage (D:\gst-backend\node_modules\pg\lib\client.js:356:19)
    at Connection.emit (node:events:518:28)
    at D:\gst-backend\node_modules\pg\lib\connection.js:116:12
    at Parser.parse (D:\gst-backend\node_modules\pg-protocol\dist\parser.js:36:17)
    at Socket.<anonymous> (D:\gst-backend\node_modules\pg-protocol\dist\index.js:11:42)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
2025-07-28 06:50:59 [INFO]: ✅ 数据库连接成功
2025-07-28 06:50:59 [INFO]: ✅ 数据库模型同步完成
2025-07-28 06:50:59 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 06:50:59 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 06:50:59 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 06:50:59 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 06:50:59 [INFO]: ⏰ 启动时间: 2025/7/28 06:50:59
2025-07-28 06:50:59 [INFO]: 🌍 运行环境: development
2025-07-28 07:14:08 [INFO]: ✅ 数据库连接成功
2025-07-28 07:14:09 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:14:09 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:14:09 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:14:09 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:14:09 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:14:09 [INFO]: ⏰ 启动时间: 2025/7/28 07:14:09
2025-07-28 07:14:09 [INFO]: 🌍 运行环境: development
2025-07-28 07:14:18 [INFO]: ✅ 数据库连接成功
2025-07-28 07:14:19 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:14:19 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:14:19 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:14:19 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:14:19 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:14:19 [INFO]: ⏰ 启动时间: 2025/7/28 07:14:19
2025-07-28 07:14:19 [INFO]: 🌍 运行环境: development
2025-07-28 07:14:44 [INFO]: ✅ 数据库连接成功
2025-07-28 07:14:45 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:14:45 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:14:45 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:14:45 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:14:45 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:14:45 [INFO]: ⏰ 启动时间: 2025/7/28 07:14:45
2025-07-28 07:14:45 [INFO]: 🌍 运行环境: development
2025-07-28 07:15:03 [INFO]: ✅ 数据库连接成功
2025-07-28 07:15:04 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:15:04 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:15:04 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:15:04 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:15:04 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:15:04 [INFO]: ⏰ 启动时间: 2025/7/28 07:15:04
2025-07-28 07:15:04 [INFO]: 🌍 运行环境: development
2025-07-28 07:15:21 [INFO]: ✅ 数据库连接成功
2025-07-28 07:15:23 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:15:23 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:15:23 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:15:23 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:15:23 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:15:23 [INFO]: ⏰ 启动时间: 2025/7/28 07:15:23
2025-07-28 07:15:23 [INFO]: 🌍 运行环境: development
2025-07-28 07:20:25 [INFO]: ✅ 数据库连接成功
2025-07-28 07:20:26 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:20:26 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:20:26 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:20:26 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:20:26 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:20:26 [INFO]: ⏰ 启动时间: 2025/7/28 07:20:26
2025-07-28 07:20:26 [INFO]: 🌍 运行环境: development
2025-07-28 07:32:45 [INFO]: ✅ 数据库连接成功
2025-07-28 07:32:47 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:32:47 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:32:47 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:32:47 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:32:47 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:32:47 [INFO]: ⏰ 启动时间: 2025/7/28 07:32:47
2025-07-28 07:32:47 [INFO]: 🌍 运行环境: development
2025-07-28 07:33:11 [INFO]: ✅ 数据库连接成功
2025-07-28 07:33:13 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:33:13 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:33:13 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:33:13 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:33:13 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:33:13 [INFO]: ⏰ 启动时间: 2025/7/28 07:33:13
2025-07-28 07:33:13 [INFO]: 🌍 运行环境: development
2025-07-28 07:33:37 [INFO]: ✅ 数据库连接成功
2025-07-28 07:33:38 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:33:38 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:33:38 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:33:38 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:33:38 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:33:38 [INFO]: ⏰ 启动时间: 2025/7/28 07:33:38
2025-07-28 07:33:38 [INFO]: 🌍 运行环境: development
2025-07-28 07:56:48 [INFO]: ✅ 数据库连接成功
2025-07-28 07:56:49 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:56:49 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:56:49 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:56:49 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:56:49 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:56:49 [INFO]: ⏰ 启动时间: 2025/7/28 07:56:49
2025-07-28 07:56:49 [INFO]: 🌍 运行环境: development
2025-07-28 07:57:29 [INFO]: ✅ 数据库连接成功
2025-07-28 07:57:31 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:57:31 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:57:31 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:57:31 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:57:31 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:57:31 [INFO]: ⏰ 启动时间: 2025/7/28 07:57:31
2025-07-28 07:57:31 [INFO]: 🌍 运行环境: development
2025-07-28 07:57:49 [INFO]: ✅ 数据库连接成功
2025-07-28 07:57:51 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:57:51 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:57:51 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:57:51 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:57:51 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:57:51 [INFO]: ⏰ 启动时间: 2025/7/28 07:57:51
2025-07-28 07:57:51 [INFO]: 🌍 运行环境: development
2025-07-28 07:58:15 [INFO]: ✅ 数据库连接成功
2025-07-28 07:58:17 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:58:17 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:58:17 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:58:17 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:58:17 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:58:17 [INFO]: ⏰ 启动时间: 2025/7/28 07:58:17
2025-07-28 07:58:17 [INFO]: 🌍 运行环境: development
2025-07-28 07:58:36 [INFO]: ✅ 数据库连接成功
2025-07-28 07:58:39 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:58:39 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:58:39 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:58:39 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:58:39 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:58:39 [INFO]: ⏰ 启动时间: 2025/7/28 07:58:39
2025-07-28 07:58:39 [INFO]: 🌍 运行环境: development
2025-07-28 07:59:02 [INFO]: ✅ 数据库连接成功
2025-07-28 07:59:04 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:59:04 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:59:04 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:59:04 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:59:04 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:59:04 [INFO]: ⏰ 启动时间: 2025/7/28 07:59:04
2025-07-28 07:59:04 [INFO]: 🌍 运行环境: development
2025-07-28 07:59:20 [INFO]: ✅ 数据库连接成功
2025-07-28 07:59:22 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:59:22 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:59:22 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:59:22 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:59:22 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:59:22 [INFO]: ⏰ 启动时间: 2025/7/28 07:59:22
2025-07-28 07:59:22 [INFO]: 🌍 运行环境: development
2025-07-28 07:59:40 [INFO]: ✅ 数据库连接成功
2025-07-28 07:59:42 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:59:42 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:59:42 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:59:42 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:59:42 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:59:42 [INFO]: ⏰ 启动时间: 2025/7/28 07:59:42
2025-07-28 07:59:42 [INFO]: 🌍 运行环境: development
2025-07-28 07:59:56 [INFO]: ✅ 数据库连接成功
2025-07-28 07:59:58 [INFO]: ✅ 数据库模型同步完成
2025-07-28 07:59:58 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 07:59:58 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 07:59:58 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 07:59:58 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 07:59:58 [INFO]: ⏰ 启动时间: 2025/7/28 07:59:58
2025-07-28 07:59:58 [INFO]: 🌍 运行环境: development
2025-07-28 08:00:16 [INFO]: ✅ 数据库连接成功
2025-07-28 08:00:18 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:00:18 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:00:18 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:00:18 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:00:18 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:00:18 [INFO]: ⏰ 启动时间: 2025/7/28 08:00:18
2025-07-28 08:00:18 [INFO]: 🌍 运行环境: development
2025-07-28 08:00:33 [INFO]: ✅ 数据库连接成功
2025-07-28 08:00:35 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:00:35 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:00:35 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:00:35 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:00:35 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:00:35 [INFO]: ⏰ 启动时间: 2025/7/28 08:00:35
2025-07-28 08:00:35 [INFO]: 🌍 运行环境: development
2025-07-28 08:03:38 [INFO]: ✅ 数据库连接成功
2025-07-28 08:03:40 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:03:40 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:03:40 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:03:40 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:03:40 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:03:40 [INFO]: ⏰ 启动时间: 2025/7/28 08:03:40
2025-07-28 08:03:40 [INFO]: 🌍 运行环境: development
2025-07-28 08:05:00 [INFO]: ✅ 数据库连接成功
2025-07-28 08:05:02 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:05:02 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:05:02 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:05:02 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:05:02 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:05:02 [INFO]: ⏰ 启动时间: 2025/7/28 08:05:02
2025-07-28 08:05:02 [INFO]: 🌍 运行环境: development
2025-07-28 08:05:27 [INFO]: ✅ 数据库连接成功
2025-07-28 08:05:29 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:05:29 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:05:29 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:05:29 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:05:29 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:05:29 [INFO]: ⏰ 启动时间: 2025/7/28 08:05:29
2025-07-28 08:05:29 [INFO]: 🌍 运行环境: development
2025-07-28 08:14:04 [INFO]: 用户登录成功: admin (1)
2025-07-28 08:14:48 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:16:33 [INFO]: 小组创建成功: 第一组 (1) by admin
2025-07-28 08:16:49 [ERROR]: SequelizeEagerLoadingError: User is associated to GroupMember using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at GroupMember._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at GroupMember._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at GroupMember._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at GroupMember.findAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1124:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async D:\gst-backend\src\routes\groups-simple.js:260:19
2025-07-28 08:29:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.insert (D:\gst-backend\node_modules\sequelize\lib\dialects\abstract\query-interface.js:308:21)
    at async model.save (D:\gst-backend\node_modules\sequelize\lib\model.js:2490:35)
    at async Course.create (D:\gst-backend\node_modules\sequelize\lib\model.js:1362:12)
    at async D:\gst-backend\src\routes\courses-simple.js:134:18
2025-07-28 08:29:42 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:30:13 [INFO]: 作业创建成功: 121 (1) by admin
2025-07-28 08:30:14 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:32:01 [INFO]: ✅ 数据库连接成功
2025-07-28 08:32:01 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:32:46 [INFO]: ✅ 数据库连接成功
2025-07-28 08:32:46 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:33:32 [INFO]: ✅ 数据库连接成功
2025-07-28 08:33:32 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:33:59 [INFO]: ✅ 数据库连接成功
2025-07-28 08:33:59 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:34:17 [INFO]: ✅ 数据库连接成功
2025-07-28 08:34:17 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:34:35 [INFO]: ✅ 数据库连接成功
2025-07-28 08:34:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:34:59 [INFO]: ✅ 数据库连接成功
2025-07-28 08:34:59 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 08:42:46 [INFO]: ✅ 数据库连接成功
2025-07-28 08:42:46 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:42:46 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:42:46 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:42:46 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:42:46 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:42:46 [INFO]: ⏰ 启动时间: 2025/7/28 08:42:46
2025-07-28 08:42:46 [INFO]: 🌍 运行环境: development
2025-07-28 08:45:33 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:46:20 [INFO]: 作业创建成功: 第一颗 (1) by admin
2025-07-28 08:46:22 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:47:28 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:47:33 [ERROR]: SequelizeEagerLoadingError: StudyGroup is associated to Assignment using an alias. You must use the 'as' keyword to specify the alias within your include statement.
    at Assignment._getIncludedAssociation (D:\gst-backend\node_modules\sequelize\lib\model.js:576:13)
    at Assignment._validateIncludedElement (D:\gst-backend\node_modules\sequelize\lib\model.js:502:53)
    at D:\gst-backend\node_modules\sequelize\lib\model.js:421:37
    at Array.map (<anonymous>)
    at Assignment._validateIncludedElements (D:\gst-backend\node_modules\sequelize\lib\model.js:417:39)
    at Assignment.aggregate (D:\gst-backend\node_modules\sequelize\lib\model.js:1252:12)
    at Assignment.count (D:\gst-backend\node_modules\sequelize\lib\model.js:1306:31)
    at async Promise.all (index 0)
    at async Assignment.findAndCountAll (D:\gst-backend\node_modules\sequelize\lib\model.js:1322:27)
    at async D:\gst-backend\src\routes\assignments-simple.js:40:40
2025-07-28 08:49:06 [INFO]: ✅ 数据库连接成功
2025-07-28 08:49:08 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:49:08 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:49:08 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:49:08 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:49:08 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:49:08 [INFO]: ⏰ 启动时间: 2025/7/28 08:49:08
2025-07-28 08:49:08 [INFO]: 🌍 运行环境: development
2025-07-28 08:49:42 [INFO]: ✅ 数据库连接成功
2025-07-28 08:49:44 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:49:44 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:49:44 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:49:44 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:49:44 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:49:44 [INFO]: ⏰ 启动时间: 2025/7/28 08:49:44
2025-07-28 08:49:44 [INFO]: 🌍 运行环境: development
2025-07-28 08:50:45 [INFO]: ✅ 数据库连接成功
2025-07-28 08:50:47 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:50:47 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:50:47 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:50:47 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:50:47 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:50:47 [INFO]: ⏰ 启动时间: 2025/7/28 08:50:47
2025-07-28 08:50:47 [INFO]: 🌍 运行环境: development
2025-07-28 08:59:27 [INFO]: ✅ 数据库连接成功
2025-07-28 08:59:29 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:59:29 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:59:29 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:59:29 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:59:29 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:59:29 [INFO]: ⏰ 启动时间: 2025/7/28 08:59:29
2025-07-28 08:59:29 [INFO]: 🌍 运行环境: development
2025-07-28 08:59:52 [INFO]: ✅ 数据库连接成功
2025-07-28 08:59:54 [INFO]: ✅ 数据库模型同步完成
2025-07-28 08:59:54 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 08:59:54 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 08:59:54 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 08:59:54 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 08:59:54 [INFO]: ⏰ 启动时间: 2025/7/28 08:59:54
2025-07-28 08:59:54 [INFO]: 🌍 运行环境: development
2025-07-28 09:00:29 [INFO]: ✅ 数据库连接成功
2025-07-28 09:00:31 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:00:31 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:00:31 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:00:31 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:00:31 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:00:31 [INFO]: ⏰ 启动时间: 2025/7/28 09:00:31
2025-07-28 09:00:31 [INFO]: 🌍 运行环境: development
2025-07-28 09:01:04 [INFO]: ✅ 数据库连接成功
2025-07-28 09:01:06 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:01:06 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:01:06 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:01:06 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:01:06 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:01:06 [INFO]: ⏰ 启动时间: 2025/7/28 09:01:06
2025-07-28 09:01:06 [INFO]: 🌍 运行环境: development
2025-07-28 09:01:17 [INFO]: ✅ 数据库连接成功
2025-07-28 09:01:20 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:01:20 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:01:20 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:01:20 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:01:20 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:01:20 [INFO]: ⏰ 启动时间: 2025/7/28 09:01:20
2025-07-28 09:01:20 [INFO]: 🌍 运行环境: development
2025-07-28 09:02:05 [INFO]: ✅ 数据库连接成功
2025-07-28 09:02:08 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:02:08 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:02:08 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:02:08 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:02:08 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:02:08 [INFO]: ⏰ 启动时间: 2025/7/28 09:02:08
2025-07-28 09:02:08 [INFO]: 🌍 运行环境: development
2025-07-28 09:02:21 [INFO]: ✅ 数据库连接成功
2025-07-28 09:02:23 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:02:23 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:02:23 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:02:23 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:02:23 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:02:23 [INFO]: ⏰ 启动时间: 2025/7/28 09:02:23
2025-07-28 09:02:23 [INFO]: 🌍 运行环境: development
2025-07-28 09:02:49 [INFO]: ✅ 数据库连接成功
2025-07-28 09:02:51 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:02:51 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:02:51 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:02:51 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:02:51 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:02:51 [INFO]: ⏰ 启动时间: 2025/7/28 09:02:51
2025-07-28 09:02:51 [INFO]: 🌍 运行环境: development
2025-07-28 09:04:45 [INFO]: ✅ 数据库连接成功
2025-07-28 09:04:45 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:04:45 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:04:45 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:04:45 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:04:45 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:04:46 [INFO]: ⏰ 启动时间: 2025/7/28 09:04:45
2025-07-28 09:04:46 [INFO]: 🌍 运行环境: development
2025-07-28 09:11:08 [INFO]: ✅ 数据库连接成功
2025-07-28 09:11:08 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:11:50 [INFO]: ✅ 数据库连接成功
2025-07-28 09:11:50 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:12:02 [INFO]: ✅ 数据库连接成功
2025-07-28 09:12:02 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:20:22 [INFO]: ✅ 数据库连接成功
2025-07-28 09:20:22 [INFO]: ✅ 数据库模型同步完成
2025-07-28 09:20:22 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 09:20:22 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 09:20:22 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 09:20:22 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 09:20:22 [INFO]: ⏰ 启动时间: 2025/7/28 09:20:22
2025-07-28 09:20:22 [INFO]: 🌍 运行环境: development
2025-07-28 09:40:26 [INFO]: ✅ 数据库连接成功
2025-07-28 09:40:26 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:41:01 [INFO]: ✅ 数据库连接成功
2025-07-28 09:41:01 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:42:38 [INFO]: ✅ 数据库连接成功
2025-07-28 09:42:38 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:42:50 [INFO]: ✅ 数据库连接成功
2025-07-28 09:42:50 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:43:02 [INFO]: ✅ 数据库连接成功
2025-07-28 09:43:02 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:43:51 [INFO]: ✅ 数据库连接成功
2025-07-28 09:43:51 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:44:06 [INFO]: ✅ 数据库连接成功
2025-07-28 09:44:06 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:44:34 [INFO]: ✅ 数据库连接成功
2025-07-28 09:44:34 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:52:39 [INFO]: ✅ 数据库连接成功
2025-07-28 09:52:39 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:52:53 [INFO]: ✅ 数据库连接成功
2025-07-28 09:52:53 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:55:53 [INFO]: ✅ 数据库连接成功
2025-07-28 09:55:53 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:56:04 [INFO]: ✅ 数据库连接成功
2025-07-28 09:56:04 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:56:19 [INFO]: ✅ 数据库连接成功
2025-07-28 09:56:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 09:56:33 [INFO]: ✅ 数据库连接成功
2025-07-28 09:56:33 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:17:7)
2025-07-28 10:07:24 [INFO]: ✅ 数据库连接成功
2025-07-28 10:07:24 [INFO]: ✅ 数据库模型同步完成
2025-07-28 10:07:24 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 10:07:24 [INFO]: 📡 服务地址: http://localhost:8005
2025-07-28 10:07:24 [INFO]: 🔍 健康检查: http://localhost:8005/health
2025-07-28 10:07:24 [INFO]: 📚 API文档: http://localhost:8005/api
2025-07-28 10:07:24 [INFO]: ⏰ 启动时间: 2025/7/28 10:07:24
2025-07-28 10:07:24 [INFO]: 🌍 运行环境: development
2025-07-28 10:10:27 [INFO]: ✅ 数据库连接成功
2025-07-28 10:10:27 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:10:42 [INFO]: ✅ 数据库连接成功
2025-07-28 10:10:42 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:16:05 [INFO]: ✅ 数据库连接成功
2025-07-28 10:16:05 [INFO]: ✅ 数据库模型同步完成
2025-07-28 10:16:05 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 10:16:05 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 10:16:05 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 10:16:05 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 10:16:05 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 10:16:05 [INFO]: ⏰ 启动时间: 2025/7/28 10:16:05
2025-07-28 10:16:05 [INFO]: 🌍 运行环境: development
2025-07-28 10:16:05 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 10:18:47 [INFO]: ✅ 数据库连接成功
2025-07-28 10:18:47 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:19:09 [INFO]: ✅ 数据库连接成功
2025-07-28 10:19:09 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:19:27 [INFO]: ✅ 数据库连接成功
2025-07-28 10:19:27 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:19:46 [INFO]: ✅ 数据库连接成功
2025-07-28 10:19:46 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:21:43 [INFO]: ✅ 数据库连接成功
2025-07-28 10:21:43 [INFO]: ✅ 数据库模型同步完成
2025-07-28 10:21:43 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 10:21:43 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 10:21:43 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 10:21:43 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 10:21:43 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 10:21:43 [INFO]: ⏰ 启动时间: 2025/7/28 10:21:43
2025-07-28 10:21:43 [INFO]: 🌍 运行环境: development
2025-07-28 10:21:43 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 10:21:43 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 10:21:43 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 10:26:56 [INFO]: 用户登录成功: admin (1)
2025-07-28 10:29:23 [ERROR]: 数据导入失败: import_menus
2025-07-28 10:31:20 [INFO]: ✅ 数据库连接成功
2025-07-28 10:31:20 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:31:57 [INFO]: ✅ 数据库连接成功
2025-07-28 10:31:57 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:10 [INFO]: ✅ 数据库连接成功
2025-07-28 10:32:10 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:24 [INFO]: ✅ 数据库连接成功
2025-07-28 10:32:24 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:42 [INFO]: ✅ 数据库连接成功
2025-07-28 10:32:42 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:32:56 [INFO]: ✅ 数据库连接成功
2025-07-28 10:32:56 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:37:38 [INFO]: ✅ 数据库连接成功
2025-07-28 10:37:38 [INFO]: ✅ 数据库模型同步完成
2025-07-28 10:37:38 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 10:37:38 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 10:37:38 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 10:37:38 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 10:37:38 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 10:37:38 [INFO]: ⏰ 启动时间: 2025/7/28 10:37:38
2025-07-28 10:37:38 [INFO]: 🌍 运行环境: development
2025-07-28 10:37:38 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 10:37:38 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 10:37:38 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 10:40:50 [ERROR]: 数据导入失败: import_menus
2025-07-28 10:48:19 [INFO]: ✅ 数据库连接成功
2025-07-28 10:48:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:49:29 [INFO]: ✅ 数据库连接成功
2025-07-28 10:49:29 [INFO]: ✅ 数据库模型同步完成
2025-07-28 10:49:29 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 10:49:29 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 10:49:29 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 10:49:29 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 10:49:29 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 10:49:29 [INFO]: ⏰ 启动时间: 2025/7/28 10:49:29
2025-07-28 10:49:29 [INFO]: 🌍 运行环境: development
2025-07-28 10:49:29 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 10:49:29 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 10:49:29 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 10:52:37 [ERROR]: 文件导入失败: import_menus
2025-07-28 10:54:16 [INFO]: ✅ 数据库连接成功
2025-07-28 10:54:16 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:54:42 [INFO]: ✅ 数据库连接成功
2025-07-28 10:54:42 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:54:58 [INFO]: ✅ 数据库连接成功
2025-07-28 10:54:58 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:55:15 [INFO]: ✅ 数据库连接成功
2025-07-28 10:55:15 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 10:56:31 [INFO]: ✅ 数据库连接成功
2025-07-28 10:56:31 [INFO]: ✅ 数据库模型同步完成
2025-07-28 10:56:31 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 10:56:31 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 10:56:31 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 10:56:31 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 10:56:31 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 10:56:31 [INFO]: ⏰ 启动时间: 2025/7/28 10:56:31
2025-07-28 10:56:31 [INFO]: 🌍 运行环境: development
2025-07-28 10:56:31 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 10:56:31 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 10:56:31 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 10:59:19 [ERROR]: 文件导入失败: import_menus
2025-07-28 11:02:07 [INFO]: 作业创建成功: 121 (1) by admin
2025-07-28 11:04:21 [INFO]: ✅ 数据库连接成功
2025-07-28 11:04:21 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:04:38 [INFO]: ✅ 数据库连接成功
2025-07-28 11:04:38 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:22:49 [INFO]: ✅ 数据库连接成功
2025-07-28 11:22:49 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:25:21 [INFO]: ✅ 数据库连接成功
2025-07-28 11:25:21 [INFO]: ✅ 数据库模型同步完成
2025-07-28 11:25:21 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 11:25:21 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 11:25:21 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 11:25:21 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 11:25:21 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 11:25:21 [INFO]: ⏰ 启动时间: 2025/7/28 11:25:21
2025-07-28 11:25:21 [INFO]: 🌍 运行环境: development
2025-07-28 11:25:21 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 11:25:21 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 11:25:21 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 11:38:10 [INFO]: 文件导入成功: import_menus
2025-07-28 11:46:35 [INFO]: ✅ 数据库连接成功
2025-07-28 11:46:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:46:35 [INFO]: ✅ 数据库连接成功
2025-07-28 11:46:35 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:47:19 [INFO]: ✅ 数据库连接成功
2025-07-28 11:47:19 [INFO]: ✅ 数据库连接成功
2025-07-28 11:47:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:47:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:53:56 [INFO]: ✅ 数据库连接成功
2025-07-28 11:53:56 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 11:54:16 [INFO]: ✅ 数据库连接成功
2025-07-28 11:54:16 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 12:14:36 [INFO]: ✅ 数据库连接成功
2025-07-28 12:14:36 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 12:37:45 [INFO]: ✅ 数据库连接成功
2025-07-28 12:37:45 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 12:58:26 [INFO]: ✅ 数据库连接成功
2025-07-28 12:58:26 [INFO]: ✅ 数据库连接成功
2025-07-28 12:58:27 [INFO]: ✅ 数据库模型同步完成
2025-07-28 12:58:27 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 12:58:27 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 12:58:27 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 12:58:27 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 12:58:27 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 12:58:27 [INFO]: ⏰ 启动时间: 2025/7/28 12:58:27
2025-07-28 12:58:27 [INFO]: 🌍 运行环境: development
2025-07-28 12:58:27 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 12:58:27 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 12:58:27 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 12:58:30 [INFO]: ✅ 数据库模型同步完成
2025-07-28 12:59:00 [INFO]: ✅ 数据库连接成功
2025-07-28 12:59:04 [INFO]: ✅ 数据库模型同步完成
2025-07-28 13:02:47 [INFO]: ✅ 数据库连接成功
2025-07-28 13:02:50 [INFO]: ✅ 数据库模型同步完成
2025-07-28 13:02:50 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 13:02:50 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 13:02:50 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 13:02:50 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 13:02:50 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 13:02:50 [INFO]: ⏰ 启动时间: 2025/7/28 13:02:50
2025-07-28 13:02:50 [INFO]: 🌍 运行环境: development
2025-07-28 13:02:50 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 13:02:50 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 13:02:50 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 13:12:55 [INFO]: 文件导入成功: import_menus
2025-07-28 13:13:11 [INFO]: 文件导入成功: import_courses
2025-07-28 13:13:27 [INFO]: 文件导入成功: import_course_items
2025-07-28 13:13:45 [INFO]: 收到SIGINT信号，正在优雅关闭服务器...
2025-07-28 13:13:45 [INFO]: 数据库连接已关闭
2025-07-28 13:15:19 [INFO]: ✅ 数据库连接成功
2025-07-28 13:15:19 [ERROR]: Error
    at Database.<anonymous> (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:185:27)
    at D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:50
    at new Promise (<anonymous>)
    at Query.run (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query.js:183:12)
    at D:\gst-backend\node_modules\sequelize\lib\sequelize.js:315:28
    at async SQLiteQueryInterface.changeColumn (D:\gst-backend\node_modules\sequelize\lib\dialects\sqlite\query-interface.js:43:7)
    at async User.sync (D:\gst-backend\node_modules\sequelize\lib\model.js:984:11)
    at async Sequelize.sync (D:\gst-backend\node_modules\sequelize\lib\sequelize.js:377:9)
    at async startServer (D:\gst-backend\server.js:34:7)
2025-07-28 13:28:35 [INFO]: ✅ 数据库连接成功
2025-07-28 13:28:35 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 13:28:35 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 13:28:35 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 13:28:35 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 13:28:35 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 13:28:35 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 13:28:35 [INFO]: ⏰ 启动时间: 2025/7/28 13:28:35
2025-07-28 13:28:35 [INFO]: 🌍 运行环境: development
2025-07-28 13:28:35 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 13:28:35 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 13:28:35 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 13:36:18 [INFO]: 文件导入成功: import_menus
2025-07-28 13:36:38 [INFO]: 文件导入成功: import_courses
2025-07-28 13:36:55 [INFO]: 文件导入成功: import_course_items
2025-07-28 13:38:34 [INFO]: ✅ 数据库连接成功
2025-07-28 13:38:34 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 13:38:34 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 13:38:34 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 13:38:34 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 13:38:34 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 13:38:34 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 13:38:34 [INFO]: ⏰ 启动时间: 2025/7/28 13:38:34
2025-07-28 13:38:34 [INFO]: 🌍 运行环境: development
2025-07-28 13:38:34 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 13:38:34 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 13:38:34 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 13:38:49 [INFO]: ✅ 数据库连接成功
2025-07-28 13:38:49 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 13:38:49 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 13:38:49 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 13:38:49 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 13:38:49 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 13:38:49 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 13:38:49 [INFO]: ⏰ 启动时间: 2025/7/28 13:38:49
2025-07-28 13:38:49 [INFO]: 🌍 运行环境: development
2025-07-28 13:38:49 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 13:38:49 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 13:38:49 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 13:39:39 [INFO]: ✅ 数据库连接成功
2025-07-28 13:39:39 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 13:39:39 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 13:39:39 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 13:39:39 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 13:39:39 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 13:39:39 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 13:39:39 [INFO]: ⏰ 启动时间: 2025/7/28 13:39:39
2025-07-28 13:39:39 [INFO]: 🌍 运行环境: development
2025-07-28 13:39:40 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 13:39:40 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 13:39:40 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 13:39:53 [INFO]: ✅ 数据库连接成功
2025-07-28 13:39:53 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 13:39:53 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 13:39:53 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 13:39:53 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 13:39:53 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 13:39:53 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 13:39:53 [INFO]: ⏰ 启动时间: 2025/7/28 13:39:53
2025-07-28 13:39:53 [INFO]: 🌍 运行环境: development
2025-07-28 13:39:53 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 13:39:53 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 13:39:53 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:41:24 [INFO]: ✅ 数据库连接成功
2025-07-28 15:41:24 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 15:41:24 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 15:41:24 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 15:41:24 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 15:41:24 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 15:41:24 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 15:41:24 [INFO]: ⏰ 启动时间: 2025/7/28 15:41:24
2025-07-28 15:41:24 [INFO]: 🌍 运行环境: development
2025-07-28 15:41:24 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 15:41:24 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 15:41:24 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:43:11 [INFO]: ✅ 数据库连接成功
2025-07-28 15:43:11 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 15:43:11 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 15:43:11 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 15:43:11 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 15:43:11 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 15:43:11 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 15:43:11 [INFO]: ⏰ 启动时间: 2025/7/28 15:43:11
2025-07-28 15:43:11 [INFO]: 🌍 运行环境: development
2025-07-28 15:43:11 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 15:43:11 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 15:43:11 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:43:26 [INFO]: ✅ 数据库连接成功
2025-07-28 15:43:27 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 15:43:27 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 15:43:27 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 15:43:27 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 15:43:27 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 15:43:27 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 15:43:27 [INFO]: ⏰ 启动时间: 2025/7/28 15:43:27
2025-07-28 15:43:27 [INFO]: 🌍 运行环境: development
2025-07-28 15:43:27 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 15:43:27 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 15:43:27 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:44:19 [INFO]: ✅ 数据库连接成功
2025-07-28 15:44:20 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 15:44:20 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 15:44:20 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 15:44:20 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 15:44:20 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 15:44:20 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 15:44:20 [INFO]: ⏰ 启动时间: 2025/7/28 15:44:20
2025-07-28 15:44:20 [INFO]: 🌍 运行环境: development
2025-07-28 15:44:20 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 15:44:20 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 15:44:20 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:50:42 [WARN]: 登录失败: admin - 用户不存在或已被禁用
2025-07-28 15:52:10 [WARN]: 登录失败: admin - 用户不存在或已被禁用
2025-07-28 15:55:23 [INFO]: ✅ 数据库连接成功
2025-07-28 15:55:23 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 15:55:23 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 15:55:23 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 15:55:23 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 15:55:23 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 15:55:23 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 15:55:23 [INFO]: ⏰ 启动时间: 2025/7/28 15:55:23
2025-07-28 15:55:23 [INFO]: 🌍 运行环境: development
2025-07-28 15:55:23 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 15:55:23 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 15:55:23 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:55:40 [WARN]: 登录失败: admin - 用户不存在或已被禁用
2025-07-28 15:59:38 [INFO]: ✅ 数据库连接成功
2025-07-28 15:59:38 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 15:59:38 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 15:59:38 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 15:59:38 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 15:59:38 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 15:59:38 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 15:59:38 [INFO]: ⏰ 启动时间: 2025/7/28 15:59:38
2025-07-28 15:59:38 [INFO]: 🌍 运行环境: development
2025-07-28 15:59:38 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 15:59:38 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 15:59:38 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 15:59:48 [WARN]: 登录失败: admin - 用户不存在或已被禁用
2025-07-28 16:00:33 [INFO]: ✅ 数据库连接成功
2025-07-28 16:00:33 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 16:00:33 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:00:33 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:00:33 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:00:33 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:00:33 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:00:33 [INFO]: ⏰ 启动时间: 2025/7/28 16:00:33
2025-07-28 16:00:33 [INFO]: 🌍 运行环境: development
2025-07-28 16:00:33 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:00:33 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:00:33 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:02:34 [INFO]: ✅ 数据库连接成功
2025-07-28 16:02:34 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 16:02:34 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:02:34 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:02:34 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:02:34 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:02:34 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:02:34 [INFO]: ⏰ 启动时间: 2025/7/28 16:02:34
2025-07-28 16:02:34 [INFO]: 🌍 运行环境: development
2025-07-28 16:02:34 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:02:34 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:02:34 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:03:45 [INFO]: ✅ 数据库连接成功
2025-07-28 16:03:46 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 16:03:46 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:03:46 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:03:46 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:03:46 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:03:46 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:03:46 [INFO]: ⏰ 启动时间: 2025/7/28 16:03:46
2025-07-28 16:03:46 [INFO]: 🌍 运行环境: development
2025-07-28 16:03:46 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:03:46 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:03:46 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:04:30 [INFO]: ✅ 数据库连接成功
2025-07-28 16:04:30 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 16:04:30 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:04:30 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:04:30 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:04:30 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:04:30 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:04:30 [INFO]: ⏰ 启动时间: 2025/7/28 16:04:30
2025-07-28 16:04:30 [INFO]: 🌍 运行环境: development
2025-07-28 16:04:30 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:04:30 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:04:30 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:05:53 [INFO]: ✅ 数据库连接成功
2025-07-28 16:05:53 [INFO]: ✅ 数据库模型同步完成 (强制重建)
2025-07-28 16:05:53 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:05:53 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:05:53 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:05:53 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:05:53 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:05:53 [INFO]: ⏰ 启动时间: 2025/7/28 16:05:53
2025-07-28 16:05:53 [INFO]: 🌍 运行环境: development
2025-07-28 16:05:53 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:05:53 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:05:53 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:06:04 [WARN]: 登录失败: admin - 用户不存在或已被禁用
2025-07-28 16:06:32 [INFO]: ✅ 数据库连接成功
2025-07-28 16:06:35 [INFO]: ✅ 数据库模型同步完成 (保留数据)
2025-07-28 16:06:35 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:06:35 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:06:35 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:06:35 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:06:35 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:06:35 [INFO]: ⏰ 启动时间: 2025/7/28 16:06:35
2025-07-28 16:06:35 [INFO]: 🌍 运行环境: development
2025-07-28 16:06:35 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:06:35 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:06:35 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:07:05 [INFO]: ✅ 数据库连接成功
2025-07-28 16:07:09 [INFO]: ✅ 数据库模型同步完成 (保留数据)
2025-07-28 16:07:09 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:07:09 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:07:09 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:07:09 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:07:09 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:07:09 [INFO]: ⏰ 启动时间: 2025/7/28 16:07:09
2025-07-28 16:07:09 [INFO]: 🌍 运行环境: development
2025-07-28 16:07:09 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:07:09 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:07:09 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:07:37 [WARN]: 登录失败: admin - 用户不存在或已被禁用
2025-07-28 16:08:06 [INFO]: ✅ 数据库连接成功
2025-07-28 16:08:06 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:08:06 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:08:06 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:08:06 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:08:06 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:08:06 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:08:06 [INFO]: ⏰ 启动时间: 2025/7/28 16:08:06
2025-07-28 16:08:06 [INFO]: 🌍 运行环境: development
2025-07-28 16:08:06 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:08:06 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:08:06 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:08:41 [INFO]: ✅ 数据库连接成功
2025-07-28 16:08:41 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:08:41 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:08:41 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:08:41 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:08:41 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:08:41 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:08:41 [INFO]: ⏰ 启动时间: 2025/7/28 16:08:41
2025-07-28 16:08:41 [INFO]: 🌍 运行环境: development
2025-07-28 16:08:41 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:08:41 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:08:41 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:09:39 [INFO]: 用户登录成功: admin (1)
2025-07-28 16:22:00 [INFO]: ✅ 数据库连接成功
2025-07-28 16:22:00 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:22:00 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:22:00 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:22:00 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:22:00 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:22:00 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:22:00 [INFO]: ⏰ 启动时间: 2025/7/28 16:22:00
2025-07-28 16:22:00 [INFO]: 🌍 运行环境: development
2025-07-28 16:22:00 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:22:00 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:22:00 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:22:18 [INFO]: ✅ 数据库连接成功
2025-07-28 16:22:18 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:22:18 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:22:18 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:22:18 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:22:18 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:22:18 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:22:18 [INFO]: ⏰ 启动时间: 2025/7/28 16:22:18
2025-07-28 16:22:18 [INFO]: 🌍 运行环境: development
2025-07-28 16:22:18 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:22:18 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:22:18 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:22:31 [INFO]: ✅ 数据库连接成功
2025-07-28 16:22:31 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:22:31 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:22:31 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:22:31 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:22:31 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:22:31 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:22:31 [INFO]: ⏰ 启动时间: 2025/7/28 16:22:31
2025-07-28 16:22:31 [INFO]: 🌍 运行环境: development
2025-07-28 16:22:31 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:22:31 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:22:31 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:22:54 [INFO]: ✅ 数据库连接成功
2025-07-28 16:22:54 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:22:54 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:22:54 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:22:54 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:22:54 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:22:54 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:22:54 [INFO]: ⏰ 启动时间: 2025/7/28 16:22:54
2025-07-28 16:22:54 [INFO]: 🌍 运行环境: development
2025-07-28 16:22:54 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:22:54 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:22:54 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:24:34 [INFO]: ✅ 数据库连接成功
2025-07-28 16:24:34 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:24:34 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:24:34 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:24:34 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:24:34 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:24:34 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:24:34 [INFO]: ⏰ 启动时间: 2025/7/28 16:24:34
2025-07-28 16:24:34 [INFO]: 🌍 运行环境: development
2025-07-28 16:24:34 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:24:34 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:24:34 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:29:15 [INFO]: ✅ 数据库连接成功
2025-07-28 16:29:15 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:29:15 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:29:15 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:29:15 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:29:15 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:29:15 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:29:16 [INFO]: ⏰ 启动时间: 2025/7/28 16:29:15
2025-07-28 16:29:16 [INFO]: 🌍 运行环境: development
2025-07-28 16:29:16 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:29:16 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:29:16 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:29:30 [INFO]: ✅ 数据库连接成功
2025-07-28 16:29:30 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:29:30 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:29:30 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:29:30 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:29:30 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:29:30 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:29:30 [INFO]: ⏰ 启动时间: 2025/7/28 16:29:30
2025-07-28 16:29:30 [INFO]: 🌍 运行环境: development
2025-07-28 16:29:30 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:29:30 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:29:30 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:30:28 [INFO]: ✅ 数据库连接成功
2025-07-28 16:30:28 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:30:28 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:30:28 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:30:28 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:30:28 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:30:28 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:30:28 [INFO]: ⏰ 启动时间: 2025/7/28 16:30:28
2025-07-28 16:30:28 [INFO]: 🌍 运行环境: development
2025-07-28 16:30:28 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:30:28 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:30:28 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:31:29 [INFO]: ✅ 数据库连接成功
2025-07-28 16:31:29 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:31:29 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:31:29 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:31:29 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:31:29 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:31:29 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:31:29 [INFO]: ⏰ 启动时间: 2025/7/28 16:31:29
2025-07-28 16:31:29 [INFO]: 🌍 运行环境: development
2025-07-28 16:31:29 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:31:29 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:31:29 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:32:09 [INFO]: ✅ 数据库连接成功
2025-07-28 16:32:09 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:32:09 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:32:09 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:32:09 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:32:09 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:32:09 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:32:09 [INFO]: ⏰ 启动时间: 2025/7/28 16:32:09
2025-07-28 16:32:09 [INFO]: 🌍 运行环境: development
2025-07-28 16:32:09 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:32:09 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:32:09 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:32:50 [INFO]: ✅ 数据库连接成功
2025-07-28 16:32:50 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:32:50 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:32:50 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:32:50 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:32:50 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:32:50 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:32:50 [INFO]: ⏰ 启动时间: 2025/7/28 16:32:50
2025-07-28 16:32:50 [INFO]: 🌍 运行环境: development
2025-07-28 16:32:50 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:32:50 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:32:50 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:33:31 [INFO]: ✅ 数据库连接成功
2025-07-28 16:33:31 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:33:31 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:33:31 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:33:31 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:33:31 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:33:31 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:33:31 [INFO]: ⏰ 启动时间: 2025/7/28 16:33:31
2025-07-28 16:33:31 [INFO]: 🌍 运行环境: development
2025-07-28 16:33:31 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:33:31 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:33:31 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:35:52 [INFO]: ✅ 数据库连接成功
2025-07-28 16:35:52 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:35:52 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:35:52 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:35:52 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:35:52 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:35:52 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:35:52 [INFO]: ⏰ 启动时间: 2025/7/28 16:35:52
2025-07-28 16:35:52 [INFO]: 🌍 运行环境: development
2025-07-28 16:35:52 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:35:52 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:35:52 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:36:07 [INFO]: ✅ 数据库连接成功
2025-07-28 16:36:07 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:36:07 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:36:07 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:36:07 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:36:07 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:36:07 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:36:07 [INFO]: ⏰ 启动时间: 2025/7/28 16:36:07
2025-07-28 16:36:07 [INFO]: 🌍 运行环境: development
2025-07-28 16:36:07 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:36:07 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:36:07 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:36:26 [INFO]: ✅ 数据库连接成功
2025-07-28 16:36:26 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:36:26 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:36:26 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:36:26 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:36:26 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:36:26 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:36:26 [INFO]: ⏰ 启动时间: 2025/7/28 16:36:26
2025-07-28 16:36:26 [INFO]: 🌍 运行环境: development
2025-07-28 16:36:26 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:36:26 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:36:26 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:36:47 [INFO]: ✅ 数据库连接成功
2025-07-28 16:36:47 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:36:47 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:36:47 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:36:47 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:36:47 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:36:47 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:36:47 [INFO]: ⏰ 启动时间: 2025/7/28 16:36:47
2025-07-28 16:36:47 [INFO]: 🌍 运行环境: development
2025-07-28 16:36:47 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:36:47 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:36:47 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:37:25 [INFO]: ✅ 数据库连接成功
2025-07-28 16:37:25 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:37:25 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:37:25 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:37:25 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:37:25 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:37:25 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:37:25 [INFO]: ⏰ 启动时间: 2025/7/28 16:37:25
2025-07-28 16:37:25 [INFO]: 🌍 运行环境: development
2025-07-28 16:37:25 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:37:25 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:37:25 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:38:03 [INFO]: ✅ 数据库连接成功
2025-07-28 16:38:03 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:38:03 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:38:03 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:38:03 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:38:03 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:38:03 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:38:03 [INFO]: ⏰ 启动时间: 2025/7/28 16:38:03
2025-07-28 16:38:03 [INFO]: 🌍 运行环境: development
2025-07-28 16:38:03 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:38:03 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:38:03 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:39:28 [INFO]: ✅ 数据库连接成功
2025-07-28 16:39:28 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:39:28 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:39:28 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:39:28 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:39:28 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:39:28 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:39:28 [INFO]: ⏰ 启动时间: 2025/7/28 16:39:28
2025-07-28 16:39:28 [INFO]: 🌍 运行环境: development
2025-07-28 16:39:28 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:39:28 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:39:28 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:39:52 [INFO]: ✅ 数据库连接成功
2025-07-28 16:39:52 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:39:52 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:39:52 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:39:52 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:39:52 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:39:52 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:39:52 [INFO]: ⏰ 启动时间: 2025/7/28 16:39:52
2025-07-28 16:39:52 [INFO]: 🌍 运行环境: development
2025-07-28 16:39:52 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:39:52 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:39:52 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:40:16 [INFO]: ✅ 数据库连接成功
2025-07-28 16:40:16 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:40:16 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:40:16 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:40:16 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:40:16 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:40:16 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:40:16 [INFO]: ⏰ 启动时间: 2025/7/28 16:40:16
2025-07-28 16:40:16 [INFO]: 🌍 运行环境: development
2025-07-28 16:40:16 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:40:16 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:40:16 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:42:21 [INFO]: ✅ 数据库连接成功
2025-07-28 16:42:21 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:42:21 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:42:21 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:42:21 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:42:21 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:42:21 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:42:21 [INFO]: ⏰ 启动时间: 2025/7/28 16:42:21
2025-07-28 16:42:21 [INFO]: 🌍 运行环境: development
2025-07-28 16:42:21 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:42:21 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:42:21 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:43:02 [INFO]: ✅ 数据库连接成功
2025-07-28 16:43:02 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:43:02 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:43:02 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:43:02 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:43:02 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:43:02 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:43:02 [INFO]: ⏰ 启动时间: 2025/7/28 16:43:02
2025-07-28 16:43:02 [INFO]: 🌍 运行环境: development
2025-07-28 16:43:02 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:43:02 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:43:02 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:43:56 [INFO]: ✅ 数据库连接成功
2025-07-28 16:43:56 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:43:56 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:43:56 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:43:56 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:43:56 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:43:56 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:43:56 [INFO]: ⏰ 启动时间: 2025/7/28 16:43:56
2025-07-28 16:43:56 [INFO]: 🌍 运行环境: development
2025-07-28 16:43:56 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:43:56 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:43:56 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:44:12 [INFO]: ✅ 数据库连接成功
2025-07-28 16:44:12 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:44:12 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:44:12 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:44:12 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:44:12 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:44:12 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:44:12 [INFO]: ⏰ 启动时间: 2025/7/28 16:44:12
2025-07-28 16:44:12 [INFO]: 🌍 运行环境: development
2025-07-28 16:44:12 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:44:12 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:44:12 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:44:28 [INFO]: ✅ 数据库连接成功
2025-07-28 16:44:28 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:44:28 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:44:28 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:44:28 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:44:28 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:44:28 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:44:28 [INFO]: ⏰ 启动时间: 2025/7/28 16:44:28
2025-07-28 16:44:28 [INFO]: 🌍 运行环境: development
2025-07-28 16:44:28 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:44:28 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:44:28 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:45:10 [INFO]: ✅ 数据库连接成功
2025-07-28 16:45:10 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:45:10 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:45:10 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:45:10 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:45:10 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:45:10 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:45:10 [INFO]: ⏰ 启动时间: 2025/7/28 16:45:10
2025-07-28 16:45:10 [INFO]: 🌍 运行环境: development
2025-07-28 16:45:10 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:45:10 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:45:10 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:47:57 [INFO]: ✅ 数据库连接成功
2025-07-28 16:47:57 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:47:57 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:47:57 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:47:57 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:47:57 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:47:57 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:47:57 [INFO]: ⏰ 启动时间: 2025/7/28 16:47:57
2025-07-28 16:47:57 [INFO]: 🌍 运行环境: development
2025-07-28 16:47:57 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:47:57 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:47:57 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:51:49 [INFO]: ✅ 数据库连接成功
2025-07-28 16:51:49 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:51:49 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:51:49 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:51:49 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:51:49 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:51:49 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:51:49 [INFO]: ⏰ 启动时间: 2025/7/28 16:51:49
2025-07-28 16:51:49 [INFO]: 🌍 运行环境: development
2025-07-28 16:51:49 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:51:49 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:51:49 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:52:36 [INFO]: ✅ 数据库连接成功
2025-07-28 16:52:36 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:52:36 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:52:36 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:52:36 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:52:36 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:52:36 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:52:36 [INFO]: ⏰ 启动时间: 2025/7/28 16:52:36
2025-07-28 16:52:36 [INFO]: 🌍 运行环境: development
2025-07-28 16:52:36 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:52:36 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:52:36 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:52:52 [INFO]: ✅ 数据库连接成功
2025-07-28 16:52:52 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:52:52 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:52:52 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:52:52 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:52:52 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:52:52 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:52:52 [INFO]: ⏰ 启动时间: 2025/7/28 16:52:52
2025-07-28 16:52:52 [INFO]: 🌍 运行环境: development
2025-07-28 16:52:52 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:52:52 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:52:52 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:53:58 [INFO]: ✅ 数据库连接成功
2025-07-28 16:53:58 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:53:58 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:53:58 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:53:58 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:53:58 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:53:58 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:53:58 [INFO]: ⏰ 启动时间: 2025/7/28 16:53:58
2025-07-28 16:53:58 [INFO]: 🌍 运行环境: development
2025-07-28 16:53:58 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:53:58 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:53:58 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:56:47 [INFO]: ✅ 数据库连接成功
2025-07-28 16:56:47 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:56:47 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:56:47 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:56:47 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:56:47 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:56:47 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:56:47 [INFO]: ⏰ 启动时间: 2025/7/28 16:56:47
2025-07-28 16:56:47 [INFO]: 🌍 运行环境: development
2025-07-28 16:56:47 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:56:47 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:56:47 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:57:04 [INFO]: ✅ 数据库连接成功
2025-07-28 16:57:04 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:57:04 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:57:04 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:57:04 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:57:04 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:57:04 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:57:04 [INFO]: ⏰ 启动时间: 2025/7/28 16:57:04
2025-07-28 16:57:04 [INFO]: 🌍 运行环境: development
2025-07-28 16:57:04 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:57:04 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:57:04 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 16:57:45 [INFO]: ✅ 数据库连接成功
2025-07-28 16:57:45 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 16:57:45 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 16:57:45 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 16:57:45 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 16:57:45 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 16:57:45 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 16:57:45 [INFO]: ⏰ 启动时间: 2025/7/28 16:57:45
2025-07-28 16:57:45 [INFO]: 🌍 运行环境: development
2025-07-28 16:57:45 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 16:57:45 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 16:57:45 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 17:00:24 [INFO]: ✅ 数据库连接成功
2025-07-28 17:00:24 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 17:00:24 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 17:00:24 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 17:00:24 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 17:00:24 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 17:00:24 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 17:00:24 [INFO]: ⏰ 启动时间: 2025/7/28 17:00:24
2025-07-28 17:00:24 [INFO]: 🌍 运行环境: development
2025-07-28 17:00:24 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 17:00:24 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 17:00:24 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 17:00:38 [INFO]: ✅ 数据库连接成功
2025-07-28 17:00:38 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 17:00:38 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 17:00:38 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 17:00:38 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 17:00:38 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 17:00:38 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 17:00:38 [INFO]: ⏰ 启动时间: 2025/7/28 17:00:38
2025-07-28 17:00:38 [INFO]: 🌍 运行环境: development
2025-07-28 17:00:38 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 17:00:38 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 17:00:38 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 17:01:11 [INFO]: ✅ 数据库连接成功
2025-07-28 17:01:11 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 17:01:11 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 17:01:11 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 17:01:11 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 17:01:11 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 17:01:11 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 17:01:11 [INFO]: ⏰ 启动时间: 2025/7/28 17:01:11
2025-07-28 17:01:11 [INFO]: 🌍 运行环境: development
2025-07-28 17:01:11 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 17:01:11 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 17:01:11 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 17:01:29 [INFO]: ✅ 数据库连接成功
2025-07-28 17:01:29 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 17:01:29 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 17:01:29 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 17:01:29 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 17:01:29 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 17:01:29 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 17:01:29 [INFO]: ⏰ 启动时间: 2025/7/28 17:01:29
2025-07-28 17:01:29 [INFO]: 🌍 运行环境: development
2025-07-28 17:01:29 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 17:01:29 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 17:01:29 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 17:01:48 [INFO]: ✅ 数据库连接成功
2025-07-28 17:01:48 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 17:01:48 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 17:01:48 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 17:01:48 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 17:01:48 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 17:01:48 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 17:01:48 [INFO]: ⏰ 启动时间: 2025/7/28 17:01:48
2025-07-28 17:01:48 [INFO]: 🌍 运行环境: development
2025-07-28 17:01:48 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 17:01:48 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 17:01:48 [INFO]: 📱 支持端口: 3005, 8080, 8081
2025-07-28 17:02:07 [INFO]: ✅ 数据库连接成功
2025-07-28 17:02:07 [INFO]: ✅ 跳过数据库同步，使用现有数据库结构
2025-07-28 17:02:07 [INFO]: 🚀 GST日语培训班API服务器启动成功！
2025-07-28 17:02:07 [INFO]: 📡 本地访问: http://localhost:8005
2025-07-28 17:02:07 [INFO]: 🌐 局域网访问: http://*************:8005
2025-07-28 17:02:07 [INFO]: 🔍 健康检查: http://*************:8005/health
2025-07-28 17:02:07 [INFO]: 📚 API文档: http://*************:8005/api
2025-07-28 17:02:08 [INFO]: ⏰ 启动时间: 2025/7/28 17:02:07
2025-07-28 17:02:08 [INFO]: 🌍 运行环境: development
2025-07-28 17:02:08 [INFO]: 🔗 允许跨域访问: 已启用CORS
2025-07-28 17:02:08 [INFO]: 🌐 支持访问域名: localhost, *************, 局域网IP
2025-07-28 17:02:08 [INFO]: 📱 支持端口: 3005, 8080, 8081
