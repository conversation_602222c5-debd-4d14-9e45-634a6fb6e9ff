const { sequelize } = require('../src/models');

async function redistributeCourses() {
  try {
    console.log('🔄 开始重新分配课程到分类...');

    // 1. 获取所有分类
    const [categories] = await sequelize.query(`
      SELECT id, name, pid FROM categories WHERE status = 'active' ORDER BY id
    `);

    console.log(`📋 找到 ${categories.length} 个分类:`);
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (ID: ${cat.id}, 父ID: ${cat.pid})`);
    });

    // 2. 获取所有课程
    const [courses] = await sequelize.query(`
      SELECT id, title, category_id FROM courses LIMIT 20
    `);

    console.log(`\n📋 课程示例 (前20个):`);
    courses.forEach(course => {
      console.log(`  - ${course.title} (当前分类ID: ${course.category_id})`);
    });

    // 3. 根据课程标题关键词重新分配分类
    const categoryKeywords = {
      236: ['考级', 'N1', 'N2', 'N3', 'N4', 'N5', '等级', '考试'], // 日语考级
      238: ['升学', '高考', '考研', '入学'], // 升学考试
      240: ['翻译', '口译', '笔译'], // 日语翻译
      241: ['留学', '出国', '签证'], // 留学日语
      242: ['考证', '证书', '资格'], // 日语考证
      243: ['文化', '传统', '习俗', '节日'], // 日本文化
      244: ['文学', '小说', '诗歌', '作家'], // 日本文学
      245: ['动漫', '漫画', '游戏', '娱乐'], // 动漫娱乐
      246: ['时事', '新闻', '热点', '社会'], // 时事热点
      247: ['商务', '职场', '工作', '商业'], // 商务日语 (如果存在)
      248: ['旅游', '旅行', '观光'], // 旅游日语 (如果存在)
      249: ['生活', '日常', '会话', '对话'] // 生活日语 (如果存在)
    };

    // 4. 重新分配课程
    let redistributedCount = 0;
    const [allCourses] = await sequelize.query(`SELECT id, title FROM courses`);

    for (const course of allCourses) {
      let newCategoryId = 235; // 默认分类：日语学习
      
      // 根据标题关键词匹配分类
      for (const [categoryId, keywords] of Object.entries(categoryKeywords)) {
        const catId = parseInt(categoryId);
        
        // 检查分类是否存在
        const categoryExists = categories.find(cat => cat.id === catId);
        if (!categoryExists) continue;

        // 检查标题是否包含关键词
        const titleLower = course.title.toLowerCase();
        const hasKeyword = keywords.some(keyword => 
          titleLower.includes(keyword.toLowerCase()) || 
          course.title.includes(keyword)
        );

        if (hasKeyword) {
          newCategoryId = catId;
          break;
        }
      }

      // 特殊规则：根据标题模式分配
      if (course.title.includes('汽车')) {
        // 汽车相关课程可能属于专业日语
        newCategoryId = 235; // 保持在日语学习
      } else if (course.title.includes('外教')) {
        // 外教课程可能属于口语练习
        newCategoryId = 235; // 保持在日语学习
      }

      // 更新课程分类
      await sequelize.query(`
        UPDATE courses SET category_id = ? WHERE id = ?
      `, { replacements: [newCategoryId, course.id] });

      if (newCategoryId !== 235) {
        redistributedCount++;
        const categoryName = categories.find(cat => cat.id === newCategoryId)?.name || '未知';
        console.log(`✅ 课程 "${course.title}" 重新分配到 "${categoryName}" (ID: ${newCategoryId})`);
      }
    }

    console.log(`\n📊 重新分配统计:`);
    console.log(`  - 总课程数: ${allCourses.length}`);
    console.log(`  - 重新分配的课程: ${redistributedCount}`);
    console.log(`  - 保持原分类的课程: ${allCourses.length - redistributedCount}`);

    // 5. 显示新的分类分布
    console.log('\n📋 新的分类分布:');
    const [newDistribution] = await sequelize.query(`
      SELECT 
        c.id,
        c.name,
        COUNT(co.id) as course_count
      FROM categories c
      LEFT JOIN courses co ON c.id = co.category_id
      GROUP BY c.id, c.name
      HAVING course_count > 0
      ORDER BY course_count DESC
    `);

    newDistribution.forEach(cat => {
      console.log(`  - ${cat.name} (ID: ${cat.id}): ${cat.course_count} 个课程`);
    });

    // 6. 清理孤立的课程单元
    console.log('\n📋 清理孤立的课程单元...');
    
    const [orphanUnits] = await sequelize.query(`
      SELECT cu.id, cu.title, cu.course_id
      FROM course_units cu
      LEFT JOIN courses c ON cu.course_id = c.id
      WHERE c.id IS NULL
    `);

    if (orphanUnits.length > 0) {
      console.log(`发现 ${orphanUnits.length} 个孤立的课程单元，将被删除:`);
      orphanUnits.forEach(unit => {
        console.log(`  - "${unit.title}" (课程ID: ${unit.course_id})`);
      });

      // 删除孤立的课程单元
      await sequelize.query(`
        DELETE FROM course_units 
        WHERE course_id NOT IN (SELECT id FROM courses)
      `);

      console.log(`✅ 已删除 ${orphanUnits.length} 个孤立的课程单元`);
    } else {
      console.log('✅ 没有发现孤立的课程单元');
    }

    // 7. 最终统计
    const [finalStats] = await sequelize.query(`
      SELECT 
        COUNT(*) as total_courses,
        COUNT(category_id) as courses_with_category
      FROM courses
    `);

    const [finalUnitStats] = await sequelize.query(`
      SELECT COUNT(*) as total_units FROM course_units
    `);

    console.log('\n📊 最终统计:');
    console.log(`  - 总课程数: ${finalStats[0].total_courses}`);
    console.log(`  - 有分类的课程: ${finalStats[0].courses_with_category}`);
    console.log(`  - 总课程单元数: ${finalUnitStats[0].total_units}`);

    console.log('🎉 课程重新分配完成！');

  } catch (error) {
    console.error('❌ 课程重新分配失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  redistributeCourses().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = redistributeCourses;
