<template>
  <el-dialog
    v-model="dialogVisible"
    title="课程详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="course-detail" v-if="course">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>课程标题</label>
            <span>{{ course.title }}</span>
          </div>
          <div class="info-item">
            <label>分类</label>
            <el-tag :type="getCategoryType(course.category)">{{ getCategoryText(course.category) }}</el-tag>
          </div>
          <div class="info-item">
            <label>等级</label>
            <el-tag :type="getLevelType(course.level)">{{ course.level }}</el-tag>
          </div>
          <div class="info-item">
            <label>时长</label>
            <span>{{ course.duration || 0 }}分钟</span>
          </div>
          <div class="info-item">
            <label>状态</label>
            <el-tag :type="getStatusType(course.status)">{{ getStatusText(course.status) }}</el-tag>
          </div>
          <div class="info-item">
            <label>作者</label>
            <span>{{ course.author?.realName || course.author?.username || '未知' }}</span>
          </div>
          <div class="info-item">
            <label>浏览量</label>
            <span>{{ course.viewCount || 0 }}</span>
          </div>
          <div class="info-item">
            <label>创建时间</label>
            <span>{{ formatDateTime(course.createdAt) }}</span>
          </div>
        </div>
      </div>

      <!-- 课程描述 -->
      <div class="detail-section" v-if="course.description">
        <h3 class="section-title">课程描述</h3>
        <p class="description-text">{{ course.description }}</p>
      </div>

      <!-- 课程内容 -->
      <div class="detail-section" v-if="course.content">
        <h3 class="section-title">课程内容</h3>
        <div class="content-text" v-html="course.content"></div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="editCourse"
          v-if="canEdit"
        >
          编辑课程
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  course: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit'])

const authStore = useAuthStore()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 权限检查
const canEdit = computed(() => {
  if (!props.course) return false
  if (authStore.isAdmin) return true
  if (authStore.isTeacher && props.course.author?.id === authStore.user.id) return true
  return false
})

// 编辑课程
const editCourse = () => {
  emit('edit', props.course)
  dialogVisible.value = false
}

// 获取分类类型
const getCategoryType = (category) => {
  const types = {
    grammar: 'primary',
    vocabulary: 'success',
    listening: 'warning',
    speaking: 'danger',
    reading: 'info',
    writing: ''
  }
  return types[category] || ''
}

// 获取分类文本
const getCategoryText = (category) => {
  const texts = {
    grammar: '语法',
    vocabulary: '词汇',
    listening: '听力',
    speaking: '口语',
    reading: '阅读',
    writing: '写作'
  }
  return texts[category] || '其他'
}

// 获取等级类型
const getLevelType = (level) => {
  const types = {
    N5: 'success',
    N4: 'primary',
    N3: 'warning',
    N2: 'danger',
    N1: 'info'
  }
  return types[level] || 'info'
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    draft: 'info',
    published: 'success',
    archived: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    archived: '已下架'
  }
  return texts[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.course-detail {
  .detail-section {
    margin-bottom: var(--spacing-xl);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-medium);
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    
    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      
      label {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
        font-weight: 500;
      }
      
      span {
        font-size: var(--font-size-base);
        color: var(--text-primary);
      }
    }
  }
  
  .description-text {
    margin: 0;
    line-height: 1.6;
    color: var(--text-regular);
    background: var(--bg-color-page);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-base);
    border-left: 4px solid var(--primary-color);
  }
  
  .content-text {
    line-height: 1.6;
    color: var(--text-regular);
    background: var(--bg-color-page);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-base);
    border-left: 4px solid var(--success-color);
    white-space: pre-wrap;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}
</style>
