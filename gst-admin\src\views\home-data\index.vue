<template>
  <div class="home-data-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>首页数据配置</span>
          <el-button type="primary" @click="saveConfig" :loading="saving">
            保存配置
          </el-button>
        </div>
      </template>

      <el-form :model="config" label-width="150px" v-loading="loading">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>显示设置</h3>
            <el-form-item label="显示轮播图">
              <el-switch v-model="config.showBanners" />
            </el-form-item>
            <el-form-item label="显示分类">
              <el-switch v-model="config.showCategories" />
            </el-form-item>
            <el-form-item label="显示推荐课程">
              <el-switch v-model="config.showRecommendedCourses" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <h3>数量设置</h3>
            <el-form-item label="轮播图数量">
              <el-input-number v-model="config.maxBanners" :min="1" :max="10" />
            </el-form-item>
            <el-form-item label="第一行分类数量">
              <el-input-number v-model="config.maxCategoriesRow1" :min="1" :max="8" />
            </el-form-item>
            <el-form-item label="第二行分类数量">
              <el-input-number v-model="config.maxCategoriesRow2" :min="1" :max="8" />
            </el-form-item>
            <el-form-item label="推荐课程数量">
              <el-input-number v-model="config.maxRecommendedCourses" :min="1" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <h3>分类设置</h3>
            <el-form-item label="分类显示模式">
              <el-radio-group v-model="config.categoryDisplayMode">
                <el-radio value="top_level_only">只显示一级分类</el-radio>
                <el-radio value="mixed">混合显示</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <h3>课程排序</h3>
            <el-form-item label="排序字段">
              <el-select v-model="config.coursesSortBy">
                <el-option label="创建时间" value="created_at" />
                <el-option label="评分" value="rating" />
                <el-option label="学生数量" value="student_count" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序方式">
              <el-radio-group v-model="config.coursesSortOrder">
                <el-radio value="DESC">降序</el-radio>
                <el-radio value="ASC">升序</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>预览效果</span>
          <el-button @click="refreshPreview" :loading="previewLoading">
            刷新预览
          </el-button>
        </div>
      </template>

      <div v-loading="previewLoading">
        <div v-if="preview.banners && preview.banners.length > 0">
          <h4>轮播图 ({{ preview.banners.length }}个)</h4>
          <el-row :gutter="10">
            <el-col :span="6" v-for="banner in preview.banners" :key="banner.id">
              <el-card :body-style="{ padding: '10px' }">
                <img :src="banner.image" style="width: 100%; height: 80px; object-fit: cover;" />
                <div style="padding: 5px 0;">
                  <span>{{ banner.title }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div v-if="preview.categories && preview.categories.length > 0" style="margin-top: 20px;">
          <h4>分类 ({{ preview.categories.length }}个)</h4>
          <el-row :gutter="10">
            <el-col :span="3" v-for="category in preview.categories" :key="category.id">
              <el-card :body-style="{ padding: '10px', textAlign: 'center' }">
                <div>{{ category.name }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div v-if="preview.courses && preview.courses.length > 0" style="margin-top: 20px;">
          <h4>推荐课程 ({{ preview.courses.length }}个)</h4>
          <el-row :gutter="10">
            <el-col :span="6" v-for="course in preview.courses" :key="course.id">
              <el-card :body-style="{ padding: '10px' }">
                <img :src="course.picture || '/static/imgs/placeholder.svg'" style="width: 100%; height: 80px; object-fit: cover;" />
                <div style="padding: 5px 0;">
                  <div>{{ course.title }}</div>
                  <div style="color: #999; font-size: 12px;">
                    ¥{{ course.Cost || 0 }} | {{ course.level }} | {{ course.studentCount || 0 }}人
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { apiRequest } from '@/utils/request'

export default {
  name: 'HomeDataConfig',
  data() {
    return {
      loading: false,
      saving: false,
      previewLoading: false,
      config: {
        showBanners: true,
        showCategories: true,
        showRecommendedCourses: true,
        maxBanners: 5,
        maxCategoriesRow1: 4,
        maxCategoriesRow2: 4,
        maxRecommendedCourses: 4,
        categoryDisplayMode: 'top_level_only',
        coursesSortBy: 'created_at',
        coursesSortOrder: 'DESC'
      },
      preview: {
        banners: [],
        categories: [],
        courses: []
      }
    }
  },
  mounted() {
    this.loadConfig()
  },
  methods: {
    async loadConfig() {
      this.loading = true
      try {
        const response = await apiRequest('/home-data', { method: 'GET' })
        if (response.success) {
          this.config = { ...this.config, ...response.data.config }
          this.preview = response.data.preview
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载配置失败')
      } finally {
        this.loading = false
      }
    },

    async saveConfig() {
      this.saving = true
      try {
        const response = await apiRequest('/home-data', {
          method: 'PUT',
          body: JSON.stringify(this.config)
        })
        if (response.success) {
          this.$message.success('配置保存成功')
          this.refreshPreview()
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      } finally {
        this.saving = false
      }
    },

    async refreshPreview() {
      this.previewLoading = true
      try {
        const response = await apiRequest('/home-data/preview', { method: 'GET' })
        if (response.success) {
          // 转换数据格式以适应预览显示
          this.preview = {
            banners: response.data.banner || [],
            categories: [...(response.data.course1 || []), ...(response.data.course2 || [])],
            courses: response.data.new || []
          }
        }
      } catch (error) {
        console.error('刷新预览失败:', error)
        this.$message.error('刷新预览失败')
      } finally {
        this.previewLoading = false
      }
    }
  }
}
</script>

<style scoped>
.home-data-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-bottom: 20px;
}

h3 {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
  margin-bottom: 20px;
}

h4 {
  color: #666;
  margin-bottom: 10px;
}
</style>
