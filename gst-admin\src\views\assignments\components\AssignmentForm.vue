<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑作业' : '布置作业'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="large"
    >
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="作业标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入作业标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="作业类型" prop="type">
            <el-select v-model="form.type" placeholder="选择类型" style="width: 100%">
              <el-option label="练习题" value="exercise" />
              <el-option label="作文" value="essay" />
              <el-option label="口语" value="speaking" />
              <el-option label="听力" value="listening" />
              <el-option label="项目" value="project" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="目标小组" prop="groupId">
            <el-select v-model="form.groupId" placeholder="选择小组" style="width: 100%">
              <el-option 
                v-for="group in groups" 
                :key="group.id" 
                :label="group.name" 
                :value="group.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止时间" prop="dueDate">
            <el-date-picker
              v-model="form.dueDate"
              type="datetime"
              placeholder="选择截止时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="满分" prop="maxScore">
            <el-input-number
              v-model="form.maxScore"
              :min="1"
              :max="1000"
              :step="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" v-if="isEdit">
            <el-select v-model="form.status" placeholder="选择状态" style="width: 100%">
              <el-option label="草稿" value="draft" />
              <el-option label="进行中" value="active" />
              <el-option label="已截止" value="expired" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="作业描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入作业描述和要求"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="详细内容">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="8"
          placeholder="请输入作业的详细内容和要求"
        />
      </el-form-item>

      <el-form-item label="附件">
        <div class="file-upload">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :on-remove="handleFileRemove"
            :before-upload="beforeFileUpload"
            multiple
            :limit="5"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              上传附件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传图片、文档、音频等文件，单个文件不超过10MB，最多5个文件
              </div>
            </template>
          </el-upload>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveDraft" :loading="submitting" v-if="!isEdit">
          保存草稿
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '发布作业' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put } from '@/utils/request'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  assignment: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const authStore = useAuthStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const groups = ref([])
const fileList = ref([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.assignment?.id)

// 上传配置
const uploadUrl = computed(() => '/api/upload/assignment')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

// 表单数据
const form = reactive({
  title: '',
  type: 'exercise',
  groupId: '',
  dueDate: '',
  maxScore: 100,
  status: 'draft',
  description: '',
  content: '',
  attachments: []
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入作业标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择作业类型', trigger: 'change' }
  ],
  groupId: [
    { required: true, message: '请选择目标小组', trigger: 'change' }
  ],
  dueDate: [
    { required: true, message: '请选择截止时间', trigger: 'change' }
  ],
  maxScore: [
    { required: true, message: '请输入满分', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '满分范围在 1 到 1000 之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入作业描述', trigger: 'blur' },
    { max: 1000, message: '描述不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 加载小组数据
const loadGroups = async () => {
  try {
    const response = await get('/api/groups', { size: 100 })
    if (response.success) {
      groups.value = response.data.groups || []
    }
  } catch (error) {
    console.error('加载小组数据失败:', error)
    // 使用模拟数据
    groups.value = [
      { id: 1, name: 'N5基础入门班' },
      { id: 2, name: 'N4进阶学习班' },
      { id: 3, name: 'N3中级提升班' }
    ]
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    title: '',
    type: 'exercise',
    groupId: '',
    dueDate: '',
    maxScore: 100,
    status: 'draft',
    description: '',
    content: '',
    attachments: []
  })
  
  fileList.value = []
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 填充表单数据
const fillForm = (assignment) => {
  if (assignment) {
    Object.assign(form, {
      title: assignment.title || '',
      type: assignment.type || 'exercise',
      groupId: assignment.group?.id || assignment.groupId || '',
      dueDate: assignment.dueDate || '',
      maxScore: assignment.maxScore || 100,
      status: assignment.status || 'draft',
      description: assignment.description || '',
      content: assignment.content || '',
      attachments: assignment.attachments || []
    })
    
    fileList.value = (assignment.attachments || []).map(file => ({
      name: file.name,
      url: file.url,
      uid: file.id
    }))
  }
}

// 文件上传成功
const handleFileSuccess = (response, file) => {
  if (response.success) {
    form.attachments.push({
      id: response.data.id,
      name: response.data.name,
      url: response.data.url,
      size: response.data.size
    })
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleFileError = (error) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败，请重试')
}

// 文件移除
const handleFileRemove = (file) => {
  const index = form.attachments.findIndex(item => item.id === file.uid)
  if (index > -1) {
    form.attachments.splice(index, 1)
  }
}

// 文件上传前检查
const beforeFileUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 保存草稿
const handleSaveDraft = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    const data = { ...form, status: 'draft' }
    const response = await post('/api/assignments', data, { showSuccess: true })
    
    if (response.success) {
      ElMessage.success('草稿保存成功')
      emit('success')
    }
  } catch (error) {
    console.error('保存草稿失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    const data = { ...form }
    if (!isEdit.value) {
      data.status = 'active'
    }
    
    let response
    if (isEdit.value) {
      response = await put(`/api/assignments/${props.assignment.id}`, data, { showSuccess: true })
    } else {
      response = await post('/api/assignments', data, { showSuccess: true })
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '发布成功')
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    if (props.assignment) {
      fillForm(props.assignment)
    } else {
      resetForm()
    }
  }
})

// 组件挂载时加载小组数据
onMounted(() => {
  loadGroups()
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.file-upload {
  width: 100%;
  
  :deep(.el-upload__tip) {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-small);
    color: var(--text-secondary);
    line-height: 1.4;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
