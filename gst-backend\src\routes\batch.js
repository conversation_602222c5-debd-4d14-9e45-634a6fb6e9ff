const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { Course, CourseUnit, Category } = require('../models');
const { auth, requireTeacher } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');
const AlicloudVodService = require('../services/alicloudVod');

const router = express.Router();
const vodService = new AlicloudVodService();

// 配置multer用于批量文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/batch');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 保持原始文件名，添加时间戳避免冲突
    const timestamp = Date.now();
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    cb(null, `${name}_${timestamp}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB限制
    files: 50 // 最多50个文件
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedTypes = /\.(mp3|wav|m4a|aac|ogg|mp4|avi|mov|wmv|flv|webm)$/i;
    if (allowedTypes.test(file.originalname)) {
      cb(null, true);
    } else {
      cb(new Error('只支持音频和视频文件'), false);
    }
  }
});

// 批量上传音频文件并创建课程
router.post('/upload-audio-courses', auth, requireTeacher, upload.array('audioFiles', 50), asyncHandler(async (req, res) => {
  const { categoryId, level = 'N5', courseType = 'listening' } = req.body;
  const files = req.files;

  if (!files || files.length === 0) {
    return res.status(400).json({
      success: false,
      message: '没有上传任何文件'
    });
  }

  logger.info(`开始批量处理 ${files.length} 个音频文件`);

  const results = {
    total: files.length,
    success: 0,
    failed: 0,
    courses: [],
    errors: []
  };

  try {
    // 处理每个音频文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
      const courseName = path.basename(originalName, path.extname(originalName));

      try {
        logger.info(`处理第 ${i + 1} 个文件: ${originalName}`);

        // 1. 获取阿里云上传凭证
        const uploadAuthResult = await vodService.getUploadAuth(
          courseName,
          originalName,
          file.size
        );

        if (!uploadAuthResult.success) {
          throw new Error(`获取上传凭证失败: ${uploadAuthResult.message}`);
        }

        // 2. 创建课程
        const course = await Course.create({
          title: courseName,
          description: `基于音频文件 ${originalName} 创建的课程`,
          categoryId: categoryId ? parseInt(categoryId) : null,
          level: level,
          category: courseType,
          duration: 30, // 默认30分钟
          status: 'draft', // 默认为草稿状态
          createdBy: req.user.id
        });

        // 3. 创建课程单元
        const courseUnit = await CourseUnit.create({
          courseId: course.id,
          title: `${courseName} - 音频课程`,
          content: `基于音频文件 ${originalName} 的课程内容`,
          type: 'audio',
          duration: 30,
          sortOrder: 1,
          createdBy: req.user.id
        });

        // 4. 记录成功结果
        results.courses.push({
          courseId: course.id,
          unitId: courseUnit.id,
          title: courseName,
          fileName: originalName,
          filePath: file.path,
          vodInfo: {
            videoId: uploadAuthResult.data.videoId,
            uploadAddress: uploadAuthResult.data.uploadAddress,
            uploadAuth: uploadAuthResult.data.uploadAuth
          }
        });

        results.success++;
        logger.info(`成功处理文件 ${originalName}, 课程ID: ${course.id}`);

      } catch (error) {
        logger.error(`处理文件 ${originalName} 失败:`, error);
        results.errors.push({
          fileName: originalName,
          error: error.message
        });
        results.failed++;

        // 清理已上传的文件
        try {
          fs.unlinkSync(file.path);
        } catch (cleanupError) {
          logger.error(`清理文件失败: ${cleanupError.message}`);
        }
      }
    }

    // 返回处理结果
    res.json({
      success: true,
      data: results,
      message: `批量处理完成: 成功 ${results.success} 个，失败 ${results.failed} 个`
    });

  } catch (error) {
    logger.error('批量处理过程中发生错误:', error);
    
    // 清理所有上传的文件
    files.forEach(file => {
      try {
        fs.unlinkSync(file.path);
      } catch (cleanupError) {
        logger.error(`清理文件失败: ${cleanupError.message}`);
      }
    });

    res.status(500).json({
      success: false,
      message: '批量处理失败',
      error: error.message
    });
  }
}));

// 获取批量上传的进度状态
router.get('/upload-status/:batchId', auth, asyncHandler(async (req, res) => {
  const { batchId } = req.params;
  
  // 这里可以实现基于Redis或数据库的进度跟踪
  // 暂时返回模拟数据
  res.json({
    success: true,
    data: {
      batchId: batchId,
      status: 'processing', // processing, completed, failed
      progress: 75, // 百分比
      total: 40,
      completed: 30,
      failed: 0,
      message: '正在处理中...'
    }
  });
}));

// 批量删除课程和相关文件
router.delete('/courses', auth, requireTeacher, asyncHandler(async (req, res) => {
  const { courseIds } = req.body;

  if (!courseIds || !Array.isArray(courseIds)) {
    return res.status(400).json({
      success: false,
      message: '请提供要删除的课程ID数组'
    });
  }

  const results = {
    total: courseIds.length,
    success: 0,
    failed: 0,
    errors: []
  };

  try {
    for (const courseId of courseIds) {
      try {
        // 获取课程单元信息，用于删除阿里云视频
        const units = await CourseUnit.findAll({
          where: { courseId: courseId }
        });

        // 删除阿里云视频
        for (const unit of units) {
          if (unit.videoId) {
            await vodService.deleteVideo(unit.videoId);
          }
          if (unit.audioId) {
            await vodService.deleteVideo(unit.audioId);
          }
        }

        // 删除课程单元
        await CourseUnit.destroy({
          where: { courseId: courseId }
        });

        // 删除课程
        await Course.destroy({
          where: { id: courseId }
        });

        results.success++;

      } catch (error) {
        logger.error(`删除课程 ${courseId} 失败:`, error);
        results.errors.push({
          courseId: courseId,
          error: error.message
        });
        results.failed++;
      }
    }

    res.json({
      success: true,
      data: results,
      message: `批量删除完成: 成功 ${results.success} 个，失败 ${results.failed} 个`
    });

  } catch (error) {
    logger.error('批量删除过程中发生错误:', error);
    res.status(500).json({
      success: false,
      message: '批量删除失败',
      error: error.message
    });
  }
}));

module.exports = router;
