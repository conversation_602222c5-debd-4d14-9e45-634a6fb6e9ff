{"version": 3, "sources": [null, "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?f537", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?1bb5", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?44ad", "uni-app:///components/test-tabbar.vue", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?2800", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?3dac"], "names": ["name", "data", "currentTab", "computed", "hasGroupPermission", "console", "mounted", "uni", "animation", "methods", "getCurrentTab", "goHome", "url", "success", "goSearch", "goStudy", "goGroups", "goUser", "checkMemberPermission"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAylB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiC7mB;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;MACA;QACAC;QACA;MACA;IACA;EACA;EACAC;IACAD;;IAEA;IACAE;MACAC;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;QACAL;MACA;IACA;IAEAM;MAAA;MACAN;MACAE;QACAK;QACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAT;MACAE;QACAK;QACAC;UACA;QACA;MACA;IACA;IACAE;MAAA;MACAV;MACAE;QACAK;QACAC;UACA;QACA;MACA;IACA;IACAG;MAAA;MACAX;MACAE;QACAK;QACAC;UACA;QACA;MACA;IACA;IAEAI;MAAA;MACAZ;MACAE;QACAK;QACAC;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAA83B,CAAgB,k4BAAG,EAAC,C;;;;;;;;;;;ACAl5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/test-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./test-tabbar.vue?vue&type=template&id=235d9b8e&scoped=true&\"\nvar renderjs\nimport script from \"./test-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./test-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test-tabbar.vue?vue&type=style&index=0&id=235d9b8e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"235d9b8e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/test-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=template&id=235d9b8e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"test-tabbar\">\n\t\t<view class=\"tabs\">\n\t\t\t<view class=\"tab\" :class=\"{ 'active': currentTab === 'pages/index/index' }\" @click=\"goHome\">\n\t\t\t\t<image class=\"tab-icon\" :src=\"currentTab === 'pages/index/index' ? '/static/tab-home-current.png' : '/static/tab-home.png'\" mode=\"aspectFit\" />\n\t\t\t\t<text class=\"tab-text\" :class=\"{ 'active': currentTab === 'pages/index/index' }\">首页</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab\" :class=\"{ 'active': currentTab === 'pages/category/list-page' }\" @click=\"goSearch\">\n\t\t\t\t<image class=\"tab-icon\" :src=\"currentTab === 'pages/category/list-page' ? '/static/tab-search-current.png' : '/static/tab-search.png'\" mode=\"aspectFit\" />\n\t\t\t\t<text class=\"tab-text\" :class=\"{ 'active': currentTab === 'pages/category/list-page' }\">找课</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab\" :class=\"{ 'active': currentTab === 'pages/study/study' }\" @click=\"goStudy\">\n\t\t\t\t<image class=\"tab-icon\" :src=\"currentTab === 'pages/study/study' ? '/static/tab-cate-current.png' : '/static/tab-cate.png'\" mode=\"aspectFit\" />\n\t\t\t\t<text class=\"tab-text\" :class=\"{ 'active': currentTab === 'pages/study/study' }\">学习</text>\n\t\t\t</view>\n\t\t\t<!-- 小组菜单 - 仅会员可见 -->\n\t\t\t<view v-if=\"hasGroupPermission\" class=\"tab\" :class=\"{ 'active': currentTab === 'pages/groups/index' }\" @click=\"goGroups\">\n\t\t\t\t<image class=\"tab-icon\" :src=\"currentTab === 'pages/groups/index' ? '/static/tab-cate-current.png' : '/static/tab-cate.png'\" mode=\"aspectFit\" />\n\t\t\t\t<text class=\"tab-text\" :class=\"{ 'active': currentTab === 'pages/groups/index' }\">小组</text>\n\t\t\t\t<view class=\"member-badge\">\n\t\t\t\t\t<text class=\"member-text\">VIP</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"tab\" :class=\"{ 'active': currentTab === 'pages/user/user' }\" @click=\"goUser\">\n\t\t\t\t<image class=\"tab-icon\" :src=\"currentTab === 'pages/user/user' ? '/static/tab-my-current.png' : '/static/tab-my.png'\" mode=\"aspectFit\" />\n\t\t\t\t<text class=\"tab-text\" :class=\"{ 'active': currentTab === 'pages/user/user' }\">我的</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'TestTabbar',\n\tdata() {\n\t\treturn {\n\t\t\tcurrentTab: ''\n\t\t};\n\t},\n\tcomputed: {\n\t\t// 检查用户是否有小组权限\n\t\thasGroupPermission() {\n\t\t\ttry {\n\t\t\t\tconst userInfo = this.$store.state.user.userInfo;\n\t\t\t\tconst userMember = this.$store.state.user.member;\n\t\t\t\tconst hasLogin = this.$store.getters.hasLogin;\n\n\t\t\t\t// 检查用户是否登录\n\t\t\t\tconst userLoggedIn = hasLogin || (userInfo && userInfo.id);\n\t\t\t\tif (!userLoggedIn) return false;\n\n\t\t\t\t// 检查会员权限\n\t\t\t\treturn this.checkMemberPermission(userMember);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.log('权限检查出错:', error);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t},\n\tmounted() {\n\t\tconsole.log('TestTabbar mounted');\n\n\t\t// 隐藏原生tabBar\n\t\tuni.hideTabBar({\n\t\t\tanimation: false\n\t\t});\n\n\t\t// 获取当前页面路径\n\t\tthis.getCurrentTab();\n\t},\n\tmethods: {\n\t\t// 获取当前页面路径\n\t\tgetCurrentTab() {\n\t\t\tconst pages = getCurrentPages();\n\t\t\tif (pages.length > 0) {\n\t\t\t\tconst currentPage = pages[pages.length - 1];\n\t\t\t\tthis.currentTab = currentPage.route;\n\t\t\t\tconsole.log('当前页面:', this.currentTab);\n\t\t\t}\n\t\t},\n\n\t\tgoHome() {\n\t\t\tconsole.log('点击首页');\n\t\t\tuni.reLaunch({\n\t\t\t\turl: '/pages/index/index',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.currentTab = 'pages/index/index';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgoSearch() {\n\t\t\tconsole.log('点击找课');\n\t\t\tuni.reLaunch({\n\t\t\t\turl: '/pages/category/list-page',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.currentTab = 'pages/category/list-page';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgoStudy() {\n\t\t\tconsole.log('点击学习');\n\t\t\tuni.reLaunch({\n\t\t\t\turl: '/pages/study/study',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.currentTab = 'pages/study/study';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgoGroups() {\n\t\t\tconsole.log('点击小组');\n\t\t\tuni.reLaunch({\n\t\t\t\turl: '/pages/groups/index',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.currentTab = 'pages/groups/index';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tgoUser() {\n\t\t\tconsole.log('点击我的');\n\t\t\tuni.reLaunch({\n\t\t\t\turl: '/pages/user/user',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.currentTab = 'pages/user/user';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 检查会员权限\n\t\tcheckMemberPermission(member) {\n\t\t\tif (!member) return false;\n\n\t\t\t// 检查会员等级\n\t\t\tif (member.level && ['premium', 'vip', 'svip'].includes(member.level.toLowerCase())) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// 检查会员到期时间\n\t\t\tif (member.expireDate) {\n\t\t\t\tconst expireDate = new Date(member.expireDate);\n\t\t\t\tconst now = new Date();\n\t\t\t\treturn expireDate > now;\n\t\t\t}\n\n\t\t\t// 检查会员状态\n\t\t\tif (member.status === 'active' || member.status === 1) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\treturn false;\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.test-tabbar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 120rpx;\n\tbackground: #ffffff;\n\tborder-top: 1rpx solid #e5e5e5;\n\tz-index: 99999;\n\tbox-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.tabs {\n\tdisplay: flex;\n\twidth: 100%;\n\theight: 100%;\n}\n\n.tab {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 8rpx 0;\n\tposition: relative;\n\ttransition: background-color 0.3s ease;\n}\n\n.tab:active {\n\tbackground: rgba(0, 0, 0, 0.05);\n}\n\n.tab-icon {\n\twidth: 44rpx;\n\theight: 44rpx;\n\tmargin-bottom: 4rpx;\n}\n\n.tab-text {\n\tfont-size: 20rpx;\n\tcolor: #C0C4CC;\n\ttransition: color 0.3s ease;\n}\n\n.tab-text.active {\n\tcolor: #2094CE;\n\tfont-weight: 600;\n}\n\n.tab.active .tab-icon {\n\ttransform: scale(1.1);\n}\n\n.member-badge {\n\tposition: absolute;\n\ttop: 2rpx;\n\tright: 20rpx;\n\tbackground: linear-gradient(135deg, #ff6b6b, #ffa500);\n\tborder-radius: 20rpx;\n\tpadding: 2rpx 8rpx;\n\ttransform: scale(0.8);\n}\n\n.member-text {\n\tfont-size: 16rpx;\n\tcolor: #fff;\n\tfont-weight: 600;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=style&index=0&id=235d9b8e&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=style&index=0&id=235d9b8e&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754048464508\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}