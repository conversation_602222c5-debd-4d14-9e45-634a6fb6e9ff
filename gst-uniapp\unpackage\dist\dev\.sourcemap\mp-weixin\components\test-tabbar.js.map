{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?8a16", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?f537", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?1bb5", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?44ad", "uni-app:///components/test-tabbar.vue", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?2800", "webpack:///D:/gst/gst-uniapp/components/test-tabbar.vue?3dac"], "names": ["name", "methods", "goHome", "console", "uni", "url", "goSearch", "goStudy", "goUser"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAylB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCa7mB;EACAA;EACAC;IACAC;MACAC;MACAC;QAAAC;MAAA;IACA;IACAC;MACAH;MACAC;QAAAC;MAAA;IACA;IACAE;MACAJ;MACAC;QAAAC;MAAA;IACA;IACAG;MACAL;MACAC;QAAAC;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA83B,CAAgB,k4BAAG,EAAC,C;;;;;;;;;;;ACAl5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/test-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./test-tabbar.vue?vue&type=template&id=235d9b8e&scoped=true&\"\nvar renderjs\nimport script from \"./test-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./test-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test-tabbar.vue?vue&type=style&index=0&id=235d9b8e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"235d9b8e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/test-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=template&id=235d9b8e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"test-tabbar\">\n\t\t<text style=\"color: white; font-size: 32rpx;\">测试底部菜单</text>\n\t\t<view class=\"tabs\">\n\t\t\t<view class=\"tab\" @click=\"goHome\">首页</view>\n\t\t\t<view class=\"tab\" @click=\"goSearch\">找课</view>\n\t\t\t<view class=\"tab\" @click=\"goStudy\">学习</view>\n\t\t\t<view class=\"tab\" @click=\"goUser\">我的</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'TestTabbar',\n\tmethods: {\n\t\tgoHome() {\n\t\t\tconsole.log('点击首页');\n\t\t\tuni.reLaunch({ url: '/pages/index/index' });\n\t\t},\n\t\tgoSearch() {\n\t\t\tconsole.log('点击找课');\n\t\t\tuni.reLaunch({ url: '/pages/category/list-page' });\n\t\t},\n\t\tgoStudy() {\n\t\t\tconsole.log('点击学习');\n\t\t\tuni.reLaunch({ url: '/pages/study/study' });\n\t\t},\n\t\tgoUser() {\n\t\t\tconsole.log('点击我的');\n\t\t\tuni.reLaunch({ url: '/pages/user/user' });\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.test-tabbar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 120rpx;\n\tbackground: red;\n\tz-index: 99999;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.tabs {\n\tdisplay: flex;\n\twidth: 100%;\n}\n\n.tab {\n\tflex: 1;\n\ttext-align: center;\n\tcolor: white;\n\tfont-size: 28rpx;\n\tpadding: 20rpx 0;\n\tbackground: rgba(0, 0, 0, 0.3);\n\tmargin: 0 2rpx;\n}\n\n.tab:active {\n\tbackground: rgba(0, 0, 0, 0.5);\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=style&index=0&id=235d9b8e&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-tabbar.vue?vue&type=style&index=0&id=235d9b8e&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041398810\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}