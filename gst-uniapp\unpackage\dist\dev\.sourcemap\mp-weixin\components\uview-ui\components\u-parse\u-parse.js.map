{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-parse/u-parse.vue?02f2", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-parse/u-parse.vue?4683", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-parse/u-parse.vue?1be2", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-parse/u-parse.vue?433f", "uni-app:///components/uview-ui/components/u-parse/u-parse.vue", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-parse/u-parse.vue?47fe", "webpack:///D:/gst/gst-uniapp/components/uview-ui/components/u-parse/u-parse.vue?de92"], "names": ["fs", "<PERSON><PERSON><PERSON>", "val", "name", "data", "showAm", "nodes", "components", "trees", "props", "html", "autopause", "type", "default", "autoscroll", "autosetTitle", "compress", "loadingImg", "useCache", "domain", "lazyLoad", "selectable", "tagStyle", "showWithAnimation", "useAnchor", "watch", "created", "newSrc", "info", "filePath", "encoding", "success", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "cache", "uni", "title", "cs", "f", "select", "height", "getText", "replace", "in", "navigateTo", "d", "selector", "scrollTop", "duration", "obj", "getVideoContext"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsBtpB;EAEAA;EAEAC;AACA;AACA;AACA;EACA;IACAC;EAAA;EACA;AACA;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAC;EACAC;IACA;MAQAC;MAEAC;IACA;EACA;EAEAC;IACAC;EACA;EAEAC;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;IACAC;MACAH;MACAC;IACA;IAEAG;IACAC;IACAC;IAEAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAf;MACA;IACA;EACA;EACAgB;IACA;IACA;IACA;MACA;QACA;MAAA;IACA;IACA;MAAA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAA;QACA;MACA;MAEA;MACA;MACA;QACA;UAAAC;QACA;QAEAC;QACA7B;UACA6B;UACAzB;UACA0B;UACAC;YAAA;UAAA;QACA;MAYA;IACA;EACA;EACAC;IAKA;IAMA;EAIA;EACAC;IAIA;MASA,iDACAjC;QACA6B;MACA;IAEA;IACAK;EACA;EACAC;IACA;IACAC;MAAA;MAsLA;MACA;MACA;MACA;MACA;QACA;QACA,oBACA9B,4BACA;UACAA;UACA+B;QACA;MACA;MACA;MACA,uDACA;MACA,sDACAC;QACAC;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAC;cACAA;cACAC;YACA;UACA;QACA;QACA;MACA;MAEA;MACAP;MACA;QAKAI,qCACAI;UACA;UACA;UAEA;YACA;YACAR;UACA;UACAS;QAEA;MAEA;MACA;IAEA;IACA;IACAC;MAAA;MACA;MAQA;QACA,2GACAC,4BACA,qCACA;UACA;UACA,oHACA;UACA;UACA;UACA,2DACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MAUA;MAEAC;MAEA,oHACA;MACA,oHACAC;MACAA;QACA;QACA;QACA,uEACAX;UACAY;UACAC;QACA;QACAC;MACA;IAEA;IACA;IACAC;MAEA,wCAEA;QACA;MAAA;IAEA;EA4FA;AACA;AAAA,2B;;;;;;;;;;;;;ACnmBA;AAAA;AAAA;AAAA;AAAi6B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;ACAr7B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-parse/u-parse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-parse.vue?vue&type=template&id=43fb93aa&\"\nvar renderjs\nimport script from \"./u-parse.vue?vue&type=script&lang=js&\"\nexport * from \"./u-parse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-parse.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-parse/u-parse.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=template&id=43fb93aa&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.nodes.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<slot v-if=\"!nodes.length\" />\n\t\t<!--#ifdef APP-PLUS-NVUE-->\n\t\t<web-view id=\"_top\" ref=\"web\" :style=\"'margin-top:-2px;height:'+height+'px'\" @onPostMessage=\"_message\" />\n\t\t<!--#endif-->\n\t\t<!--#ifndef APP-PLUS-NVUE-->\n\t\t<view id=\"_top\" :style=\"showAm+(selectable?';user-select:text;-webkit-user-select:text':'')\">\n\t\t\t<!--#ifdef H5 || MP-360-->\n\t\t\t<div :id=\"'rtf'+uid\"></div>\n\t\t\t<!--#endif-->\n\t\t\t<!--#ifndef H5 || MP-360-->\n\t\t\t<trees :nodes=\"nodes\" :lazyLoad=\"lazyLoad\" :loading=\"loadingImg\" />\n\t\t\t<!--#endif-->\n\t\t</view>\n\t\t<!--#endif-->\n\t</view>\n</template>\n\n<script>\n\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\n\timport trees from './libs/trees';\n\tvar cache = {},\n\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\n\t\tfs = uni.getFileSystemManager ? uni.getFileSystemManager() : null,\n\t\t// #endif\n\t\tParser = require('./libs/MpHtmlParser.js');\n\tvar dom;\n\t// 计算 cache 的 key\n\tfunction hash(str) {\n\t\tfor (var i = str.length, val = 5381; i--;)\n\t\t\tval += (val << 5) + str.charCodeAt(i);\n\t\treturn val;\n\t}\n\t// #endif\n\t// #ifdef H5 || APP-PLUS-NVUE || MP-360\n\tvar {\n\t\twindowWidth,\n\t\tplatform\n\t} = uni.getSystemInfoSync(),\n\t\tcfg = require('./libs/config.js');\n\t// #endif\n\t// #ifdef APP-PLUS-NVUE\n\tvar weexDom = weex.requireModule('dom');\n\t// #endif\n\t/**\n\t * Parser 富文本组件\n\t * @tutorial https://github.com/jin-yufeng/Parser\n\t * @property {String} html 富文本数据\n\t * @property {Boolean} autopause 是否在播放一个视频时自动暂停其他视频\n\t * @property {Boolean} autoscroll 是否自动给所有表格添加一个滚动层\n\t * @property {Boolean} autosetTitle 是否自动将 title 标签中的内容设置到页面标题\n\t * @property {Number} compress 压缩等级\n\t * @property {String} domain 图片、视频等链接的主域名\n\t * @property {Boolean} lazyLoad 是否开启图片懒加载\n\t * @property {String} loadingImg 图片加载完成前的占位图\n\t * @property {Boolean} selectable 是否开启长按复制\n\t * @property {Object} tagStyle 标签的默认样式\n\t * @property {Boolean} showWithAnimation 是否使用渐显动画\n\t * @property {Boolean} useAnchor 是否使用锚点\n\t * @property {Boolean} useCache 是否缓存解析结果\n\t * @event {Function} parse 解析完成事件\n\t * @event {Function} load dom 加载完成事件\n\t * @event {Function} ready 所有图片加载完毕事件\n\t * @event {Function} error 错误事件\n\t * @event {Function} imgtap 图片点击事件\n\t * @event {Function} linkpress 链接点击事件\n\t * <AUTHOR>\n\t * @version 20201014\n\t * @listens MIT\n\t */\n\texport default {\n\t\tname: 'parser',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// #ifdef H5 || MP-360\n\t\t\t\tuid: this._uid,\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t\theight: 1,\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\t\tshowAm: '',\n\t\t\t\t// #endif\n\t\t\t\tnodes: []\n\t\t\t}\n\t\t},\n\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\n\t\tcomponents: {\n\t\t\ttrees\n\t\t},\n\t\t// #endif\n\t\tprops: {\n\t\t\thtml: String,\n\t\t\tautopause: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tautoscroll: Boolean,\n\t\t\tautosetTitle: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\n\t\t\tcompress: Number,\n\t\t\tloadingImg: String,\n\t\t\tuseCache: Boolean,\n\t\t\t// #endif\n\t\t\tdomain: String,\n\t\t\tlazyLoad: Boolean,\n\t\t\tselectable: Boolean,\n\t\t\ttagStyle: Object,\n\t\t\tshowWithAnimation: Boolean,\n\t\t\tuseAnchor: Boolean\n\t\t},\n\t\twatch: {\n\t\t\thtml(html) {\n\t\t\t\tthis.setContent(html);\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 图片数组\n\t\t\tthis.imgList = [];\n\t\t\tthis.imgList.each = function(f) {\n\t\t\t\tfor (var i = 0, len = this.length; i < len; i++)\n\t\t\t\t\tthis.setItem(i, f(this[i], i, this));\n\t\t\t}\n\t\t\tthis.imgList.setItem = function(i, src) {\n\t\t\t\tif (i == void 0 || !src) return;\n\t\t\t\t// #ifndef MP-ALIPAY || APP-PLUS\n\t\t\t\t// 去重\n\t\t\t\tif (src.indexOf('http') == 0 && this.includes(src)) {\n\t\t\t\t\tvar newSrc = src.split('://')[0];\n\t\t\t\t\tfor (var j = newSrc.length, c; c = src[j]; j++) {\n\t\t\t\t\t\tif (c == '/' && src[j - 1] != '/' && src[j + 1] != '/') break;\n\t\t\t\t\t\tnewSrc += Math.random() > 0.5 ? c.toUpperCase() : c;\n\t\t\t\t\t}\n\t\t\t\t\tnewSrc += src.substr(j);\n\t\t\t\t\treturn this[i] = newSrc;\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\tthis[i] = src;\n\t\t\t\t// 暂存 data src\n\t\t\t\tif (src.includes('data:image')) {\n\t\t\t\t\tvar filePath, info = src.match(/data:image\\/(\\S+?);(\\S+?),(.+)/);\n\t\t\t\t\tif (!info) return;\n\t\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\n\t\t\t\t\tfilePath = `${wx.env.USER_DATA_PATH}/${Date.now()}.${info[1]}`;\n\t\t\t\t\tfs && fs.writeFile({\n\t\t\t\t\t\tfilePath,\n\t\t\t\t\t\tdata: info[3],\n\t\t\t\t\t\tencoding: info[2],\n\t\t\t\t\t\tsuccess: () => this[i] = filePath\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tfilePath = `_doc/parser_tmp/${Date.now()}.${info[1]}`;\n\t\t\t\t\tvar bitmap = new plus.nativeObj.Bitmap();\n\t\t\t\t\tbitmap.loadBase64Data(src, () => {\n\t\t\t\t\t\tbitmap.save(filePath, {}, () => {\n\t\t\t\t\t\t\tbitmap.clear()\n\t\t\t\t\t\t\tthis[i] = filePath;\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t// #ifdef H5 || MP-360\n\t\t\tthis.document = document.getElementById('rtf' + this._uid);\n\t\t\t// #endif\n\t\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\n\t\t\tif (dom) this.document = new dom(this);\n\t\t\t// #endif\n\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\tthis.document = this.$refs.web;\n\t\t\tsetTimeout(() => {\n\t\t\t\t// #endif\n\t\t\t\tif (this.html) this.setContent(this.html);\n\t\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t}, 30)\n\t\t\t// #endif\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// #ifdef H5 || MP-360\n\t\t\tif (this._observer) this._observer.disconnect();\n\t\t\t// #endif\n\t\t\tthis.imgList.each(src => {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif (src && src.includes('_doc')) {\n\t\t\t\t\tplus.io.resolveLocalFileSystemURL(src, entry => {\n\t\t\t\t\t\tentry.remove();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\n\t\t\t\tif (src && src.includes(uni.env.USER_DATA_PATH))\n\t\t\t\t\tfs && fs.unlink({\n\t\t\t\t\t\tfilePath: src\n\t\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t})\n\t\t\tclearInterval(this._timer);\n\t\t},\n\t\tmethods: {\n\t\t\t// 设置富文本内容\n\t\t\tsetContent(html, append) {\n\t\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t\tif (!html)\n\t\t\t\t\treturn this.height = 1;\n\t\t\t\tif (append)\n\t\t\t\t\tthis.$refs.web.evalJs(\"var b=document.createElement('div');b.innerHTML='\" + html.replace(/'/g, \"\\\\'\") +\n\t\t\t\t\t\t\"';document.getElementById('parser').appendChild(b)\");\n\t\t\t\telse {\n\t\t\t\t\thtml =\n\t\t\t\t\t\t'<meta charset=\"utf-8\" /><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\"><style>html,body{width:100%;height:100%;overflow:hidden}body{margin:0}</style><base href=\"' +\n\t\t\t\t\t\tthis.domain + '\"><div id=\"parser\"' + (this.selectable ? '>' : ' style=\"user-select:none\">') + this._handleHtml(html).replace(/\\n/g, '\\\\n') +\n\t\t\t\t\t\t'</div><script>\"use strict\";function e(e){if(window.__dcloud_weex_postMessage||window.__dcloud_weex_){var t={data:[e]};window.__dcloud_weex_postMessage?window.__dcloud_weex_postMessage(t):window.__dcloud_weex_.postMessage(JSON.stringify(t))}}document.body.onclick=function(){e({action:\"click\"})},' +\n\t\t\t\t\t\t(this.showWithAnimation ? 'document.body.style.animation=\"_show .5s\",' : '') +\n\t\t\t\t\t\t'setTimeout(function(){e({action:\"load\",text:document.body.innerText,height:document.getElementById(\"parser\").scrollHeight})},50);\\x3c/script>';\n\t\t\t\t\tif (platform == 'android') html = html.replace(/%/g, '%25');\n\t\t\t\t\tthis.$refs.web.evalJs(\"document.write('\" + html.replace(/'/g, \"\\\\'\") + \"');document.close()\");\n\t\t\t\t}\n\t\t\t\tthis.$refs.web.evalJs(\n\t\t\t\t\t'var t=document.getElementsByTagName(\"title\");t.length&&e({action:\"getTitle\",title:t[0].innerText});for(var o,n=document.getElementsByTagName(\"style\"),r=1;o=n[r++];)o.innerHTML=o.innerHTML.replace(/body/g,\"#parser\");for(var a,c=document.getElementsByTagName(\"img\"),s=[],i=0==c.length,d=0,l=0,g=0;a=c[l];l++)parseInt(a.style.width||a.getAttribute(\"width\"))>' +\n\t\t\t\t\twindowWidth + '&&(a.style.height=\"auto\"),a.onload=function(){++d==c.length&&(i=!0)},a.onerror=function(){++d==c.length&&(i=!0),' + (cfg.errorImg ? 'this.src=\"' + cfg.errorImg + '\",' : '') +\n\t\t\t\t\t'e({action:\"error\",source:\"img\",target:this})},a.hasAttribute(\"ignore\")||\"A\"==a.parentElement.nodeName||(a.i=g++,s.push(a.getAttribute(\"original-src\")||a.src||a.getAttribute(\"data-src\")),a.onclick=function(){e({action:\"preview\",img:{i:this.i,src:this.src}})});e({action:\"getImgList\",imgList:s});for(var u,m=document.getElementsByTagName(\"a\"),f=0;u=m[f];f++)u.onclick=function(){var t,o=this.getAttribute(\"href\");if(\"#\"==o[0]){var n=document.getElementById(o.substr(1));n&&(t=n.offsetTop)}return e({action:\"linkpress\",href:o,offset:t}),!1};for(var h,y=document.getElementsByTagName(\"video\"),v=0;h=y[v];v++)h.style.maxWidth=\"100%\",h.onerror=function(){e({action:\"error\",source:\"video\",target:this})}' +\n\t\t\t\t\t(this.autopause ? ',h.onplay=function(){for(var e,t=0;e=y[t];t++)e!=this&&e.pause()}' : '') +\n\t\t\t\t\t';for(var _,p=document.getElementsByTagName(\"audio\"),w=0;_=p[w];w++)_.onerror=function(){e({action:\"error\",source:\"audio\",target:this})};' +\n\t\t\t\t\t(this.autoscroll ? 'for(var T,E=document.getElementsByTagName(\"table\"),B=0;T=E[B];B++){var N=document.createElement(\"div\");N.style.overflow=\"scroll\",T.parentNode.replaceChild(N,T),N.appendChild(T)}' : '') +\n\t\t\t\t\t'var x=document.getElementById(\"parser\");clearInterval(window.timer),window.timer=setInterval(function(){i&&clearInterval(window.timer),e({action:\"ready\",ready:i,height:x.scrollHeight})},350)'\n\t\t\t\t)\n\t\t\t\tthis.nodes = [1];\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5 || MP-360\n\t\t\t\tif (!html) {\n\t\t\t\t\tif (this.rtf && !append) this.rtf.parentNode.removeChild(this.rtf);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tvar div = document.createElement('div');\n\t\t\t\tif (!append) {\n\t\t\t\t\tif (this.rtf) this.rtf.parentNode.removeChild(this.rtf);\n\t\t\t\t\tthis.rtf = div;\n\t\t\t\t} else {\n\t\t\t\t\tif (!this.rtf) this.rtf = div;\n\t\t\t\t\telse this.rtf.appendChild(div);\n\t\t\t\t}\n\t\t\t\tdiv.innerHTML = this._handleHtml(html, append);\n\t\t\t\tfor (var styles = this.rtf.getElementsByTagName('style'), i = 0, style; style = styles[i++];) {\n\t\t\t\t\tstyle.innerHTML = style.innerHTML.replace(/body/g, '#rtf' + this._uid);\n\t\t\t\t\tstyle.setAttribute('scoped', 'true');\n\t\t\t\t}\n\t\t\t\t// 懒加载\n\t\t\t\tif (!this._observer && this.lazyLoad && IntersectionObserver) {\n\t\t\t\t\tthis._observer = new IntersectionObserver(changes => {\n\t\t\t\t\t\tfor (let item, i = 0; item = changes[i++];) {\n\t\t\t\t\t\t\tif (item.isIntersecting) {\n\t\t\t\t\t\t\t\titem.target.src = item.target.getAttribute('data-src');\n\t\t\t\t\t\t\t\titem.target.removeAttribute('data-src');\n\t\t\t\t\t\t\t\tthis._observer.unobserve(item.target);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}, {\n\t\t\t\t\t\trootMargin: '500px 0px 500px 0px'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tvar _ts = this;\n\t\t\t\t// 获取标题\n\t\t\t\tvar title = this.rtf.getElementsByTagName('title');\n\t\t\t\tif (title.length && this.autosetTitle)\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: title[0].innerText\n\t\t\t\t\t})\n\t\t\t\t// 图片处理\n\t\t\t\tthis.imgList.length = 0;\n\t\t\t\tvar imgs = this.rtf.getElementsByTagName('img');\n\t\t\t\tfor (let i = 0, j = 0, img; img = imgs[i]; i++) {\n\t\t\t\t\tif (parseInt(img.style.width || img.getAttribute('width')) > windowWidth)\n\t\t\t\t\t\timg.style.height = 'auto';\n\t\t\t\t\tvar src = img.getAttribute('src');\n\t\t\t\t\tif (this.domain && src) {\n\t\t\t\t\t\tif (src[0] == '/') {\n\t\t\t\t\t\t\tif (src[1] == '/')\n\t\t\t\t\t\t\t\timg.src = (this.domain.includes('://') ? this.domain.split('://')[0] : '') + ':' + src;\n\t\t\t\t\t\t\telse img.src = this.domain + src;\n\t\t\t\t\t\t} else if (!src.includes('://')) img.src = this.domain + '/' + src;\n\t\t\t\t\t}\n\t\t\t\t\tif (!img.hasAttribute('ignore') && img.parentElement.nodeName != 'A') {\n\t\t\t\t\t\timg.i = j++;\n\t\t\t\t\t\t_ts.imgList.push(img.getAttribute('original-src') || img.src || img.getAttribute('data-src'));\n\t\t\t\t\t\timg.onclick = function() {\n\t\t\t\t\t\t\tvar preview = true;\n\t\t\t\t\t\t\tthis.ignore = () => preview = false;\n\t\t\t\t\t\t\t_ts.$emit('imgtap', this);\n\t\t\t\t\t\t\tif (preview) {\n\t\t\t\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\t\t\t\tcurrent: this.i,\n\t\t\t\t\t\t\t\t\turls: _ts.imgList\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\timg.onerror = function() {\n\t\t\t\t\t\tif (cfg.errorImg)\n\t\t\t\t\t\t\t_ts.imgList[this.i] = this.src = cfg.errorImg;\n\t\t\t\t\t\t_ts.$emit('error', {\n\t\t\t\t\t\t\tsource: 'img',\n\t\t\t\t\t\t\ttarget: this\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tif (_ts.lazyLoad && this._observer && img.src && img.i != 0) {\n\t\t\t\t\t\timg.setAttribute('data-src', img.src);\n\t\t\t\t\t\timg.removeAttribute('src');\n\t\t\t\t\t\tthis._observer.observe(img);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 链接处理\n\t\t\t\tvar links = this.rtf.getElementsByTagName('a');\n\t\t\t\tfor (var link of links) {\n\t\t\t\t\tlink.onclick = function() {\n\t\t\t\t\t\tvar jump = true,\n\t\t\t\t\t\t\thref = this.getAttribute('href');\n\t\t\t\t\t\t_ts.$emit('linkpress', {\n\t\t\t\t\t\t\thref,\n\t\t\t\t\t\t\tignore: () => jump = false\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (jump && href) {\n\t\t\t\t\t\t\tif (href[0] == '#') {\n\t\t\t\t\t\t\t\tif (_ts.useAnchor) {\n\t\t\t\t\t\t\t\t\t_ts.navigateTo({\n\t\t\t\t\t\t\t\t\t\tid: href.substr(1)\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (href.indexOf('http') == 0 || href.indexOf('//') == 0)\n\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: href\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 视频处理\n\t\t\t\tvar videos = this.rtf.getElementsByTagName('video');\n\t\t\t\t_ts.videoContexts = videos;\n\t\t\t\tfor (let video, i = 0; video = videos[i++];) {\n\t\t\t\t\tvideo.style.maxWidth = '100%';\n\t\t\t\t\tvideo.onerror = function() {\n\t\t\t\t\t\t_ts.$emit('error', {\n\t\t\t\t\t\t\tsource: 'video',\n\t\t\t\t\t\t\ttarget: this\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tvideo.onplay = function() {\n\t\t\t\t\t\tif (_ts.autopause)\n\t\t\t\t\t\t\tfor (let item, i = 0; item = _ts.videoContexts[i++];)\n\t\t\t\t\t\t\t\tif (item != this) item.pause();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 音频处理\n\t\t\t\tvar audios = this.rtf.getElementsByTagName('audio');\n\t\t\t\tfor (var audio of audios)\n\t\t\t\t\taudio.onerror = function() {\n\t\t\t\t\t\t_ts.$emit('error', {\n\t\t\t\t\t\t\tsource: 'audio',\n\t\t\t\t\t\t\ttarget: this\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t// 表格处理\n\t\t\t\tif (this.autoscroll) {\n\t\t\t\t\tvar tables = this.rtf.getElementsByTagName('table');\n\t\t\t\t\tfor (var table of tables) {\n\t\t\t\t\t\tlet div = document.createElement('div');\n\t\t\t\t\t\tdiv.style.overflow = 'scroll';\n\t\t\t\t\t\ttable.parentNode.replaceChild(div, table);\n\t\t\t\t\t\tdiv.appendChild(table);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!append) this.document.appendChild(this.rtf);\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.nodes = [1];\n\t\t\t\t\tthis.$emit('load');\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => this.showAm = '', 500);\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\t\t// #ifndef H5 || MP-360\n\t\t\t\tvar nodes;\n\t\t\t\tif (!html) return this.nodes = [];\n\t\t\t\tvar parser = new Parser(html, this);\n\t\t\t\t// 缓存读取\n\t\t\t\tif (this.useCache) {\n\t\t\t\t\tvar hashVal = hash(html);\n\t\t\t\t\tif (cache[hashVal])\n\t\t\t\t\t\tnodes = cache[hashVal];\n\t\t\t\t\telse {\n\t\t\t\t\t\tnodes = parser.parse();\n\t\t\t\t\t\tcache[hashVal] = nodes;\n\t\t\t\t\t}\n\t\t\t\t} else nodes = parser.parse();\n\t\t\t\tthis.$emit('parse', nodes);\n\t\t\t\tif (append) this.nodes = this.nodes.concat(nodes);\n\t\t\t\telse this.nodes = nodes;\n\t\t\t\tif (nodes.length && nodes.title && this.autosetTitle)\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: nodes.title\n\t\t\t\t\t})\n\t\t\t\tif (this.imgList) this.imgList.length = 0;\n\t\t\t\tthis.videoContexts = [];\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t(function f(cs) {\n\t\t\t\t\t\tfor (var i = cs.length; i--;) {\n\t\t\t\t\t\t\tif (cs[i].top) {\n\t\t\t\t\t\t\t\tcs[i].controls = [];\n\t\t\t\t\t\t\t\tcs[i].init();\n\t\t\t\t\t\t\t\tf(cs[i].$children);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})(this.$children)\n\t\t\t\t\tthis.$emit('load');\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\tvar height;\n\t\t\t\tclearInterval(this._timer);\n\t\t\t\tthis._timer = setInterval(() => {\n\t\t\t\t\t// #ifdef H5 || MP-360\n\t\t\t\t\tthis.rect = this.rtf.getBoundingClientRect();\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef H5 || MP-360\n\t\t\t\t\tuni.createSelectorQuery().in(this)\n\t\t\t\t\t\t.select('#_top').boundingClientRect().exec(res => {\n\t\t\t\t\t\t\tif (!res) return;\n\t\t\t\t\t\t\tthis.rect = res[0];\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\tif (this.rect.height == height) {\n\t\t\t\t\t\t\t\tthis.$emit('ready', this.rect)\n\t\t\t\t\t\t\t\tclearInterval(this._timer);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\theight = this.rect.height;\n\t\t\t\t\t\t\t// #ifndef H5 || MP-360\n\t\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t}, 350);\n\t\t\t\tif (this.showWithAnimation && !append) this.showAm = 'animation:_show .5s';\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 获取文本内容\n\t\t\tgetText(ns = this.nodes) {\n\t\t\t\tvar txt = '';\n\t\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t\ttxt = this._text;\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5 || MP-360\n\t\t\t\ttxt = this.rtf.innerText;\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\n\t\t\t\tfor (var i = 0, n; n = ns[i++];) {\n\t\t\t\t\tif (n.type == 'text') txt += n.text.replace(/&nbsp;/g, '\\u00A0').replace(/&lt;/g, '<').replace(/&gt;/g, '>')\n\t\t\t\t\t\t.replace(/&amp;/g, '&');\n\t\t\t\t\telse if (n.type == 'br') txt += '\\n';\n\t\t\t\t\telse {\n\t\t\t\t\t\t// 块级标签前后加换行\n\t\t\t\t\t\tvar block = n.name == 'p' || n.name == 'div' || n.name == 'tr' || n.name == 'li' || (n.name[0] == 'h' && n.name[1] >\n\t\t\t\t\t\t\t'0' && n.name[1] < '7');\n\t\t\t\t\t\tif (block && txt && txt[txt.length - 1] != '\\n') txt += '\\n';\n\t\t\t\t\t\tif (n.children) txt += this.getText(n.children);\n\t\t\t\t\t\tif (block && txt[txt.length - 1] != '\\n') txt += '\\n';\n\t\t\t\t\t\telse if (n.name == 'td' || n.name == 'th') txt += '\\t';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\treturn txt;\n\t\t\t},\n\t\t\t// 锚点跳转\n\t\t\tin (obj) {\n\t\t\t\tif (obj.page && obj.selector && obj.scrollTop) this._in = obj;\n\t\t\t},\n\t\t\tnavigateTo(obj) {\n\t\t\t\tif (!this.useAnchor) return obj.fail && obj.fail('Anchor is disabled');\n\t\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t\tif (!obj.id)\n\t\t\t\t\tweexDom.scrollToElement(this.$refs.web);\n\t\t\t\telse\n\t\t\t\t\tthis.$refs.web.evalJs('var pos=document.getElementById(\"' + obj.id +\n\t\t\t\t\t\t'\");if(pos)post({action:\"linkpress\",href:\"#\",offset:pos.offsetTop+' + (obj.offset || 0) + '})');\n\t\t\t\tobj.success && obj.success();\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\t\tvar d = ' ';\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\n\t\t\t\td = '>>>';\n\t\t\t\t// #endif\n\t\t\t\tvar selector = uni.createSelectorQuery().in(this._in ? this._in.page : this).select((this._in ? this._in.selector :\n\t\t\t\t\t'#_top') + (obj.id ? `${d}#${obj.id},${this._in?this._in.selector:'#_top'}${d}.${obj.id}` : '')).boundingClientRect();\n\t\t\t\tif (this._in) selector.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect();\n\t\t\t\telse selector.selectViewport().scrollOffset();\n\t\t\t\tselector.exec(res => {\n\t\t\t\t\tif (!res[0]) return obj.fail && obj.fail('Label not found')\n\t\t\t\t\tvar scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + (obj.offset || 0);\n\t\t\t\t\tif (this._in) this._in.page[this._in.scrollTop] = scrollTop;\n\t\t\t\t\telse uni.pageScrollTo({\n\t\t\t\t\t\tscrollTop,\n\t\t\t\t\t\tduration: 300\n\t\t\t\t\t})\n\t\t\t\t\tobj.success && obj.success();\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 获取视频对象\n\t\t\tgetVideoContext(id) {\n\t\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\t\tif (!id) return this.videoContexts;\n\t\t\t\telse\n\t\t\t\t\tfor (var i = this.videoContexts.length; i--;)\n\t\t\t\t\t\tif (this.videoContexts[i].id == id) return this.videoContexts[i];\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// #ifdef H5 || APP-PLUS-NVUE || MP-360\n\t\t\t_handleHtml(html, append) {\n\t\t\t\tif (!append) {\n\t\t\t\t\t// 处理 tag-style 和 userAgentStyles\n\t\t\t\t\tvar style = '<style>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}';\n\t\t\t\t\tfor (var item in cfg.userAgentStyles)\n\t\t\t\t\t\tstyle += `${item}{${cfg.userAgentStyles[item]}}`;\n\t\t\t\t\tfor (item in this.tagStyle)\n\t\t\t\t\t\tstyle += `${item}{${this.tagStyle[item]}}`;\n\t\t\t\t\tstyle += '</style>';\n\t\t\t\t\thtml = style + html;\n\t\t\t\t}\n\t\t\t\t// 处理 rpx\n\t\t\t\tif (html.includes('rpx'))\n\t\t\t\t\thtml = html.replace(/[0-9.]+\\s*rpx/g, $ => (parseFloat($) * windowWidth / 750) + 'px');\n\t\t\t\treturn html;\n\t\t\t},\n\t\t\t// #endif\n\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t_message(e) {\n\t\t\t\t// 接收 web-view 消息\n\t\t\t\tvar d = e.detail.data[0];\n\t\t\t\tswitch (d.action) {\n\t\t\t\t\tcase 'load':\n\t\t\t\t\t\tthis.$emit('load');\n\t\t\t\t\t\tthis.height = d.height;\n\t\t\t\t\t\tthis._text = d.text;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'getTitle':\n\t\t\t\t\t\tif (this.autosetTitle)\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\t\t\ttitle: d.title\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'getImgList':\n\t\t\t\t\t\tthis.imgList.length = 0;\n\t\t\t\t\t\tfor (var i = d.imgList.length; i--;)\n\t\t\t\t\t\t\tthis.imgList.setItem(i, d.imgList[i]);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'preview':\n\t\t\t\t\t\tvar preview = true;\n\t\t\t\t\t\td.img.ignore = () => preview = false;\n\t\t\t\t\t\tthis.$emit('imgtap', d.img);\n\t\t\t\t\t\tif (preview)\n\t\t\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\t\t\tcurrent: d.img.i,\n\t\t\t\t\t\t\t\turls: this.imgList\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'linkpress':\n\t\t\t\t\t\tvar jump = true,\n\t\t\t\t\t\t\thref = d.href;\n\t\t\t\t\t\tthis.$emit('linkpress', {\n\t\t\t\t\t\t\thref,\n\t\t\t\t\t\t\tignore: () => jump = false\n\t\t\t\t\t\t})\n\t\t\t\t\t\tif (jump && href) {\n\t\t\t\t\t\t\tif (href[0] == '#') {\n\t\t\t\t\t\t\t\tif (this.useAnchor)\n\t\t\t\t\t\t\t\t\tweexDom.scrollToElement(this.$refs.web, {\n\t\t\t\t\t\t\t\t\t\toffset: d.offset\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (href.includes('://'))\n\t\t\t\t\t\t\t\tplus.runtime.openWeb(href);\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: href\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'error':\n\t\t\t\t\t\tif (d.source == 'img' && cfg.errorImg)\n\t\t\t\t\t\t\tthis.imgList.setItem(d.target.i, cfg.errorImg);\n\t\t\t\t\t\tthis.$emit('error', {\n\t\t\t\t\t\t\tsource: d.source,\n\t\t\t\t\t\t\ttarget: d.target\n\t\t\t\t\t\t})\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'ready':\n\t\t\t\t\t\tthis.height = d.height;\n\t\t\t\t\t\tif (d.ready) uni.createSelectorQuery().in(this).select('#_top').boundingClientRect().exec(res => {\n\t\t\t\t\t\t\tthis.rect = res[0];\n\t\t\t\t\t\t\tthis.$emit('ready', res[0]);\n\t\t\t\t\t\t})\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'click':\n\t\t\t\t\t\tthis.$emit('click');\n\t\t\t\t\t\tthis.$emit('tap');\n\t\t\t\t}\n\t\t\t},\n\t\t\t// #endif\n\t\t}\n\t}\n</script>\n\n<style>\n\t@keyframes _show {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t/* #ifdef MP-WEIXIN */\n\t:host {\n\t\tdisplay: block;\n\t\toverflow: scroll;\n\t\t-webkit-overflow-scrolling: touch;\n\t}\n\n\t/* #endif */\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040519731\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}