﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>操作日志</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportLogs" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出日志
        </el-button>
        <el-button @click="clearLogs" type="danger">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="操作用户">
          <el-input v-model="filters.userName" placeholder="输入用户名" clearable @change="loadLogs" />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="filters.action" placeholder="选择操作类型" clearable @change="loadLogs">
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="创建" value="create" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
            <el-option label="查看" value="view" />
          </el-select>
        </el-form-item>
        <el-form-item label="模块">
          <el-select v-model="filters.module" placeholder="选择模块" clearable @change="loadLogs">
            <el-option label="用户管理" value="user" />
            <el-option label="课程管理" value="course" />
            <el-option label="小组管理" value="group" />
            <el-option label="系统设置" value="system" />
            <el-option label="权限管理" value="permission" />
          </el-select>
        </el-form-item>
        <el-form-item label="日志级别">
          <el-select v-model="filters.level" placeholder="选择日志级别" clearable @change="loadLogs">
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
            <el-option label="调试" value="debug" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="loadLogs"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志列表 -->
    <el-card>
      <el-table :data="logs" v-loading="loading" stripe>
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="log-detail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="请求ID">{{ row.requestId }}</el-descriptions-item>
                <el-descriptions-item label="IP地址">{{ row.ipAddress }}</el-descriptions-item>
                <el-descriptions-item label="用户代理">{{ row.userAgent }}</el-descriptions-item>
                <el-descriptions-item label="请求方法">{{ row.method }}</el-descriptions-item>
                <el-descriptions-item label="请求URL" :span="2">{{ row.url }}</el-descriptions-item>
                <el-descriptions-item label="请求参数" :span="2">
                  <pre class="json-content">{{ formatJson(row.params) }}</pre>
                </el-descriptions-item>
                <el-descriptions-item label="响应数据" :span="2">
                  <pre class="json-content">{{ formatJson(row.response) }}</pre>
                </el-descriptions-item>
                <el-descriptions-item label="错误信息" :span="2" v-if="row.error">
                  <pre class="error-content">{{ row.error }}</pre>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="级别" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getLevelColor(row.level)" size="small">
              {{ getLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="操作用户" width="120" />
        <el-table-column prop="action" label="操作类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getActionColor(row.action)" size="small">
              {{ getActionText(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="模块" width="100" />
        <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ipAddress" label="IP地址" width="120" />
        <el-table-column prop="createdAt" label="操作时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="80" align="center">
          <template #default="{ row }">
            <span v-if="row.duration">{{ row.duration }}ms</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadLogs"
          @current-change="loadLogs"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Download,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const logs = ref([])
const total = ref(0)

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 筛选器
const filters = reactive({
  userName: '',
  action: '',
  module: '',
  level: '',
  dateRange: null
})

// 加载日志数据
const loadLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size,
      ...filters
    }

    const response = await get('/api/logs', params)
    if (response.success) {
      logs.value = response.data.logs || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载日志数据失败:', error)
    ElMessage.error('加载日志数据失败')

    // 使用模拟数据
    logs.value = generateMockLogs()
    total.value = logs.value.length
  } finally {
    loading.value = false
  }
}

// 生成模拟日志数据
const generateMockLogs = () => {
  return [
    {
      id: 1,
      level: 'info',
      userName: '管理员',
      action: 'login',
      module: 'user',
      description: '用户登录系统',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      method: 'POST',
      url: '/api/auth/login',
      params: { username: 'admin' },
      response: { success: true, message: '登录成功' },
      duration: 120,
      requestId: 'req_123456789',
      createdAt: new Date()
    },
    {
      id: 2,
      level: 'info',
      userName: '教师A',
      action: 'create',
      module: 'course',
      description: '创建新课程：N5基础日语',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      method: 'POST',
      url: '/api/courses',
      params: { title: 'N5基础日语', level: 'N5' },
      response: { success: true, id: 123 },
      duration: 250,
      requestId: 'req_123456790',
      createdAt: new Date(Date.now() - 60 * 60 * 1000)
    },
    {
      id: 3,
      level: 'warning',
      userName: '学生B',
      action: 'view',
      module: 'course',
      description: '访问未授权课程',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      method: 'GET',
      url: '/api/courses/456',
      params: {},
      response: { success: false, message: '权限不足' },
      duration: 50,
      requestId: 'req_123456791',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 4,
      level: 'error',
      userName: '系统',
      action: 'delete',
      module: 'system',
      description: '删除用户失败',
      ipAddress: '127.0.0.1',
      userAgent: 'System',
      method: 'DELETE',
      url: '/api/users/789',
      params: { id: 789 },
      response: { success: false, message: '数据库连接失败' },
      error: 'Database connection timeout after 30 seconds',
      duration: 30000,
      requestId: 'req_123456792',
      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000)
    }
  ]
}

// 导出日志
const exportLogs = async () => {
  exporting.value = true
  try {
    // 这里实现日志导出逻辑
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('日志导出成功')
  } catch (error) {
    ElMessage.error('日志导出失败')
  } finally {
    exporting.value = false
  }
}

// 清空日志
const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有日志吗？此操作不可恢复！', '确认清空', {
      type: 'warning'
    })

    await del('/api/logs/clear')
    ElMessage.success('日志清空成功')
    await loadLogs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error)
      ElMessage.error('清空日志失败')
    }
  }
}

// 工具函数
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const formatJson = (obj) => {
  if (!obj) return ''
  return JSON.stringify(obj, null, 2)
}

const getLevelColor = (level) => {
  const colors = {
    debug: 'info',
    info: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return colors[level] || ''
}

const getLevelText = (level) => {
  const texts = {
    debug: '调试',
    info: '信息',
    warning: '警告',
    error: '错误'
  }
  return texts[level] || level
}

const getActionColor = (action) => {
  const colors = {
    login: 'success',
    logout: 'info',
    create: 'primary',
    update: 'warning',
    delete: 'danger',
    view: 'info'
  }
  return colors[action] || ''
}

const getActionText = (action) => {
  const texts = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    view: '查看'
  }
  return texts[action] || action
}

const refreshData = () => {
  loadLogs()
}

// 组件挂载时加载数据
onMounted(() => {
  loadLogs()
})
</script>

<style lang="scss" scoped>
.log-detail {
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  margin: 8px 0;

  .json-content {
    background: var(--el-fill-color-light);
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .error-content {
    background: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

// 表格样式优化
.el-table {
  .el-table__expand-icon {
    color: var(--el-color-primary);
  }
}
</style>
