<template>
  <div class="home-container">
    <!-- 轮播图区域 -->
    <el-card class="banner-card" v-if="homeData.banner?.length">
      <template #header>
        <div class="card-header">
          <span>首页轮播图</span>
          <el-tag type="info">{{ homeData.banner.length }} 张</el-tag>
        </div>
      </template>
      <el-carousel height="200px" indicator-position="outside">
        <el-carousel-item v-for="banner in homeData.banner" :key="banner.id">
          <div class="banner-item" @click="handleBannerClick(banner)">
            <img :src="banner.image" :alt="banner.title" />
            <div class="banner-overlay">
              <h3>{{ banner.title }}</h3>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </el-card>

    <!-- 分类展示区域 -->
    <div class="categories-section">
      <!-- 第一分类（首页主要分类） -->
      <el-card class="category-card">
        <template #header>
          <div class="card-header">
            <span>首页主要分类</span>
            <el-tag type="primary">{{ homeData.course1?.length || 0 }} 个</el-tag>
          </div>
        </template>
        <div class="category-grid">
          <div 
            v-for="category in homeData.course1" 
            :key="category.id"
            class="category-item"
            @click="handleCategoryClick(category)"
          >
            <div class="category-icon">
              <img :src="category.icon" :alt="category.name" />
            </div>
            <div class="category-name">{{ category.name }}</div>
          </div>
        </div>
      </el-card>

      <!-- 第二分类（所有分类） -->
      <el-card class="category-card">
        <template #header>
          <div class="card-header">
            <span>所有分类</span>
            <el-tag type="success">{{ homeData.course2?.length || 0 }} 个</el-tag>
          </div>
        </template>
        <div class="category-grid">
          <div 
            v-for="category in homeData.course2" 
            :key="category.id"
            class="category-item"
            @click="handleCategoryClick(category)"
          >
            <div class="category-icon">
              <img :src="category.icon" :alt="category.name" />
            </div>
            <div class="category-name">{{ category.name }}</div>
            <div class="category-badges">
              <el-tag v-if="category.show_in_home" size="small" type="primary">首页</el-tag>
              <el-tag v-if="category.show_in_group" size="small" type="success">小组</el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能入口区域 -->
    <div class="functions-section">
      <!-- 顶部功能入口 -->
      <el-card class="function-card">
        <template #header>
          <div class="card-header">
            <span>顶部功能入口</span>
            <el-tag type="warning">{{ homeData.top_post?.length || 0 }} 个</el-tag>
          </div>
        </template>
        <div class="function-grid">
          <div 
            v-for="item in homeData.top_post" 
            :key="item.id"
            class="function-item"
            @click="handleFunctionClick(item)"
          >
            <div class="function-icon">
              <img :src="item.thumb || '/images/default-function.png'" :alt="item.title" />
            </div>
            <div class="function-name">{{ item.title }}</div>
          </div>
        </div>
      </el-card>

      <!-- 功能展示区 -->
      <el-card class="function-card">
        <template #header>
          <div class="card-header">
            <span>功能展示区</span>
            <el-tag type="info">{{ homeData.game_show_footer?.length || 0 }} 个</el-tag>
          </div>
        </template>
        <div class="function-grid">
          <div 
            v-for="item in homeData.game_show_footer" 
            :key="item.id"
            class="function-item"
            @click="handleFunctionClick(item)"
          >
            <div class="function-icon">
              <img :src="item.thumb || '/images/default-function.png'" :alt="item.title" />
            </div>
            <div class="function-name">{{ item.title }}</div>
          </div>
        </div>
      </el-card>

      <!-- 底部推荐 -->
      <el-card class="function-card">
        <template #header>
          <div class="card-header">
            <span>底部推荐</span>
            <el-tag type="danger">{{ homeData.game_foot_post?.length || 0 }} 个</el-tag>
          </div>
        </template>
        <div class="recommend-list">
          <div 
            v-for="item in homeData.game_foot_post" 
            :key="item.id"
            class="recommend-item"
            @click="handleFunctionClick(item)"
          >
            <div class="recommend-title">{{ item.title }}</div>
            <div class="recommend-type">{{ item.type }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据统计 -->
    <el-card class="stats-card">
      <template #header>
        <div class="card-header">
          <span>数据统计</span>
          <el-button @click="refreshData" size="small">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="轮播图" :value="homeData.banner?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="主要分类" :value="homeData.course1?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="所有分类" :value="homeData.course2?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="功能入口" :value="(homeData.top_post?.length || 0) + (homeData.game_show_footer?.length || 0)" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get } from '@/utils/request'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import router from '@/router'

// 响应式数据
const loading = ref(false)
const homeData = reactive({
  banner: [],
  course1: [],
  course2: [],
  top_post: [],
  game_show_footer: [],
  game_foot_post: []
})

// 加载首页数据
const loadHomeData = async () => {
  loading.value = true
  try {
    const response = await get('/api/home/<USER>')
    console.log('首页数据:', response)
    
    if (response.code === 0 && response.status) {
      Object.assign(homeData, response.data)
    } else {
      ElMessage.error(response.message || '加载首页数据失败')
    }
  } catch (error) {
    console.error('加载首页数据失败:', error)
    ElMessage.error('加载首页数据失败')
  } finally {
    loading.value = false
  }
}

// 处理轮播图点击
const handleBannerClick = (banner) => {
  console.log('点击轮播图:', banner)
  if (banner.link) {
    if (banner.jump_type === 1) {
      // 内部页面跳转
      router.push(banner.link)
    } else {
      // 外部链接
      window.open(banner.link, '_blank')
    }
  }
}

// 处理分类点击
const handleCategoryClick = (category) => {
  console.log('点击分类:', category)
  if (category.jump_type === 4) {
    // 跳转到分类页面
    router.push(`/courses?categoryId=${category.jump_pid}`)
  }
}

// 处理功能点击
const handleFunctionClick = (item) => {
  console.log('点击功能:', item)
  if (item.link) {
    if (item.jump_type === 1) {
      // 内部页面跳转
      router.push(item.link)
    } else {
      // 外部链接
      window.open(item.link, '_blank')
    }
  }
}

// 刷新数据
const refreshData = () => {
  loadHomeData()
}

// 组件挂载时加载数据
onMounted(() => {
  loadHomeData()
})
</script>

<style lang="scss" scoped>
.home-container {
  padding: 20px;
}

.banner-card, .category-card, .function-card, .stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 轮播图样式
.banner-item {
  position: relative;
  height: 200px;
  cursor: pointer;
  overflow: hidden;
  border-radius: 8px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .banner-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: white;
    padding: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
    }
  }
}

// 分类网格样式
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
}

.category-item {
  text-align: center;
  cursor: pointer;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--el-fill-color-light);
    transform: translateY(-2px);
  }
  
  .category-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }
  
  .category-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .category-badges {
    display: flex;
    justify-content: center;
    gap: 4px;
  }
}

// 功能网格样式
.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 15px;
}

.function-item {
  text-align: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--el-fill-color-light);
  }
  
  .function-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto 8px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
  
  .function-name {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

// 推荐列表样式
.recommend-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recommend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--el-fill-color-light);
  }
  
  .recommend-title {
    font-weight: 500;
  }
  
  .recommend-type {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
  }
}

.categories-section, .functions-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .categories-section, .functions-section {
    grid-template-columns: 1fr;
  }
  
  .category-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .function-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
}
</style>
