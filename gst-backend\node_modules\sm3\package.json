{"name": "sm3", "version": "1.0.3", "description": "国密SM3密码杂凑算法的JavaScript实现", "main": "sm3.js", "repository": {"type": "git", "url": "https://github.com/jiaxingzheng/JavaScript-SM3.git"}, "keywords": ["sm3", "javascript"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jiaxingzheng/JavaScript-SM3/issues"}, "homepage": "https://github.com/jiaxingzheng/JavaScript-SM3", "scripts": {"test": "node test.js", "build": "babel sm3.js --out-file sm3-babel.js"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "babel-preset-stage-1": "^6.24.1", "babel-preset-stage-2": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}}