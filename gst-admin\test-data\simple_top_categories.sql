/*
 最简单的顶级分类测试数据
 只包含顶级分类，避免外键约束问题
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for courses_classify
-- ----------------------------
DROP TABLE IF EXISTS `courses_classify`;
CREATE TABLE `courses_classify`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` tinyint(4) NOT NULL DEFAULT 1,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  `pid` int(11) NOT NULL DEFAULT 0,
  `level` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of courses_classify (只有顶级分类，pid=0)
-- ----------------------------
INSERT INTO `courses_classify` VALUES (1, '日语学习', '基础日语学习课程分类', 1, 1, 0, 1);
INSERT INTO `courses_classify` VALUES (2, '日语考级', 'JLPT考级相关课程', 2, 1, 0, 1);
INSERT INTO `courses_classify` VALUES (3, '实用口语', '日常生活口语练习', 3, 1, 0, 1);

SET FOREIGN_KEY_CHECKS = 1;
