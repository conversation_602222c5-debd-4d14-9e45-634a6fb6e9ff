{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/foreign/index.vue?f189", "webpack:///D:/gst/gst-uniapp/pages/foreign/index.vue?e03f", "webpack:///D:/gst/gst-uniapp/pages/foreign/index.vue?8c8c", "webpack:///D:/gst/gst-uniapp/pages/foreign/index.vue?1943", "uni-app:///pages/foreign/index.vue", "webpack:///D:/gst/gst-uniapp/pages/foreign/index.vue?196a", "webpack:///D:/gst/gst-uniapp/pages/foreign/index.vue?11e3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "mainHeight", "scrollTop", "loadMoreTimer", "navItems", "id", "title", "icon_url", "type", "styles", "name", "content", "currentIndex", "newsList", "page", "swiper<PERSON><PERSON>rent", "swiper<PERSON><PERSON><PERSON>", "carouselList", "superInList", "rmfwBgUrl", "otherList", "courseList", "more", "link", "onLoad", "grace<PERSON>s", "ref", "withShareTicket", "menus", "onShareAppMessage", "imageUrl", "path", "onShareTimeline", "methods", "loadData", "that", "loadData2", "uni", "url", "header", "success", "console", "swiper<PERSON><PERSON>e", "navchange", "getNews", "params", "scroll", "touchstart", "touchmove", "touchend", "reload", "loadmorefun", "clearTimeout", "newstap", "navTo", "toDetail", "getUrl", "appId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoHtnB;AACA;AACA;AAAA,eACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACAJ;QACAK;QACAC;QACAH;QACAC;MACA,GACA;QACAJ;QACAK;QACAC;QACAH;QACAC;MACA;QACAJ;QACAK;QACAC;QACAH;QACAC;MACA,EACA;MACA;MACAG;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAR;MACAE;MACAO;QACAb;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAY;MACAC;MACAC;MACAC;QACAhB;QACAiB;QACAf;MACA;IACA;EACA;EACAgB;IAAA;IACA;IACAC;MACAC;QACA;QACAD;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;QACA;MACA;IACA;;IACA/B;MACAiC;MACAC;IACA;EAEA;EACAC;IACA;MACAvB;MAAA;MACAwB;MAAA;MACAC;IACA;EACA;EACA;EACAC;IACA;MACA1B;MAAA;MACAwB;MAAA;MACAC;IACA;EACA;;EACAE;IACAC;MACA;MACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACAC;QACAC;QAAA;QACAvC;UACAM;QACA;QACAkC;UACA;QACA;;QACAC;UACAC;UACA;UACA;;UAEA;;UAEA;;UAEA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;UACArC;UACAM;UACAL;QACA;MACA;QACA;UACA;UACA;QACA;UACA;UACAgC;UACA;YACA;YACA;YACA;YACA;YACA;YACA;cACA;YACA;UACA;UACA;UAAA,KACA;YACA;YACA;YACA;YACA;cACA;YACA;YACA;UACA;UACA;QACA;MAEA;IACA;IACAK;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAZ;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA;IACAa;MACAb;MACA;QACA;QACA;QACA;MACA;MACA;QACA;UACA;YACAJ;cACAC;YACA;YACA;UACA;YACAG;YACAJ;cACAC;YACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;YAAA;YACA;YACA;QAAA;QAEA;MACA;MACA;QACA;UACA;YAAA;YACAD;cACAC;YACA;YACA;UACA;YAAA;YACAD;cACAC;YACA;YACA;UACA;YAAA;YACA;YACA;YACA;YACA;QAAA;QAGA;MACA;MACA;QACA;MACA;IACA;IACA;IACAiB;MACA;MACA;MACAlB;QACAC;MACA;IACA;IACAkB;MACAf;MACAJ;QACAoB;QACA1B;QACAS;UACA;QAAA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/dA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/foreign/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/foreign/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=eaab4ea4&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/foreign/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=eaab4ea4&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.superInList.length\n  var g1 = _vm.otherList.length\n  var g2 = _vm.courseList.length\n  var g3 = g2 > 0 ? _vm.courseList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\r\n\t\t\t<!-- 头部轮播 -->\r\n\t\t\t<view class=\"carousel-section\">\r\n\t\t\t\t<!-- 标题栏和状态栏占位符 -->\r\n\t\t\t\t<!-- <view class=\"titleNview-placing\"></view> -->\r\n\t\t\t\t<!-- 背景色区域 -->\r\n\t\t\t\t<!-- <view class=\"titleNview-background\" :style=\"{backgroundColor:titleNViewBackground}\"></view> -->\r\n\t\t\t\t<swiper class=\"carousel\" circular @change=\"swiperChange\" autoplay=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in carouselList\" :key=\"index\" class=\"carousel-item\" \r\n\t\t\t\t\t\t@tap=\"navTo(item,'swiper')\">\r\n\t\t\t\t\t\t<image :src=\"item.thumb\" />\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<!-- 自定义swiper指示器 -->\r\n\t\t\t\t<view class=\"swiper-dots\">\r\n\t\t\t\t\t<text class=\"num\">{{swiperCurrent+1}}</text>\r\n\t\t\t\t\t<text class=\"sign\">/</text>\r\n\t\t\t\t\t<text class=\"num\">{{swiperLength}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--分类-->\r\n\t\t\t<view class=\"flexbox hbclass\" v-if=\"superInList.length > 0\">\r\n\t\t\t    <view   class=\"flex1\" v-for=\"(item,index) in superInList\" :key=\"index\" @tap=\"navTo(item,'link')\">\r\n\t\t\t        <image class=\"icon120\" mode=\"aspectFit\" :src=\"item.thumb\"></image>\r\n\t\t\t        <view class=\"txt26 nowrap\">{{item.title}}</view>\r\n\t\t\t    </view>\r\n\t\t\t</view>\r\n\t\t\t<!--更多服务-->\r\n\t\t\t<view class=\"bg-box hbclass\" v-if=\"otherList.length > 0\">\r\n\t\t\t    <view class=\"hot-service\">\r\n\t\t\t        <image ariaHidden=\"true\" class=\"bg-img\" lazyLoad=\"true\" mode=\"aspectFill\" :src=\"rmfwBgUrl\"></image>\r\n\t\t\t        <view ariaRole=\"heading\" class=\"hot-service-title\">\r\n\t\t\t            <view class=\"hot-service-title-h3\">更多服务</view>\r\n\t\t\t        </view>\r\n\t\t\t        <view class=\"hot-service-content\">\r\n\t\t\t            <swiper bind:change=\"handleChange\" class=\"swiper-container-row\"  previousMargin=\"6rpx\">\r\n\t\t\t                <swiper-item class=\"swiper-item\" data-index=\"index\" v-for=\"(item,index) in otherList\" :key=\"index\">\r\n\t\t\t                    <view  bind:tap=\"handleTap\" class=\"srv-item\"  v-for=\"(item,index) in otherList\" :key=\"index\" @tap=\"navTo(item,'link')\">\r\n\t\t\t                        <image class=\"srv-item-icon\" :src=\"item.picture\"></image>\r\n\t\t\t                        <text class=\"srv-item-title nowrap\">{{item.name}}</text>\r\n\t\t\t                    </view>\r\n\t\t\t                </swiper-item>\r\n\t\t\t            </swiper>\r\n\t\t\t           <!-- <view class=\"indicator\" wx:if=\"{{list[1]}}\">\r\n\t\t\t                <view style=\"margin: 0 auto\">\r\n\t\t\t                    <view :class=\"indicator-child {{current==index?'active':''}}\" :style=\"indicatorStyle\" v-for=\"(item,index) in superInList\" :key=\"index\"></view>\r\n\t\t\t                </view>\r\n\t\t\t            </view> -->\r\n\t\t\t        </view>\r\n\t\t\t    </view>\r\n\t\t\t</view>\r\n\t\t\t<!--更多课程-->\r\n\t\t\t<view class=\"bg-box hbclass\" v-if=\"courseList.length > 0\">\r\n\t\t\t    <view class=\"hot-service\">\r\n\t\t\t        <image ariaHidden=\"true\" class=\"bg-img\" lazyLoad=\"true\" mode=\"aspectFill\" :src=\"rmfwBgUrl\"></image>\r\n\t\t\t        <view ariaRole=\"heading\" class=\"hot-service-title\">\r\n\t\t\t            <view class=\"hot-service-title-h3\">更多课程</view>\r\n\t\t\t        </view>\r\n\t\t\t        <view class=\"hot-service-content\">\r\n\t\t\t            <swiper bind:change=\"handleChange\" :class=\"courseList.length>4?'swiper-container':'swiper-container-row'\"  previousMargin=\"6rpx\">\r\n\t\t\t                <swiper-item class=\"swiper-item\" data-index=\"index\" v-for=\"(item,index) in courseList\" :key=\"index\">\r\n\t\t\t                    <view  bind:tap=\"handleTap\" class=\"srv-item\" style=\"width: 25%;\" v-for=\"(item1,index1) in courseList\" :key=\"index1\" @tap=\"navTo(item1,'link')\">\r\n\t\t\t                        <image class=\"srv-item-icon1\" :src=\"item1.icon\" style=\"width: 60rpx;height: 60rpx;border-radius: 60rpx;\"></image>\r\n\t\t\t                        <text class=\"srv-item-title nowrap\">{{item1.name}}</text>\r\n\t\t\t                    </view>\r\n\t\t\t                </swiper-item>\r\n\t\t\t            </swiper>\r\n\t\t\t           <!-- <view class=\"indicator\" wx:if=\"{{list[1]}}\">\r\n\t\t\t                <view style=\"margin: 0 auto\">\r\n\t\t\t                    <view :class=\"indicator-child {{current==index?'active':''}}\" :style=\"indicatorStyle\" v-for=\"(item,index) in superInList\" :key=\"index\"></view>\r\n\t\t\t                </view>\r\n\t\t\t            </view> -->\r\n\t\t\t        </view>\r\n\t\t\t    </view>\r\n\t\t\t</view>\r\n\t\t\t<!--推荐阅读-->\r\n\t\t\t<view class=\"everyone-doing bg hbclass\" >\r\n\t\t\t    <view class=\"service-main\">\r\n\t\t\t        <view ariaLabel=\"大家都在办\" ariaRole=\"text\" class=\"listbox service-list\">\r\n\t\t\t            <view ariaLabel=\"换一换\" ariaRole=\"button\" bind:tap=\"handleTitleTap\" class=\"titlebox\">\r\n\t\t\t                <view class=\"h2title viewtitle\">推荐阅读</view>\r\n\t\t\t                <view ariaLabel=\"换一换\" ariaRole=\"button\" bindtap=\"handleRefresh\" class=\"service-hot-title\">\r\n\t\t\t                    <!-- <image class=\"refresh-icon\" src=\"https://fingertip-static.gdbs.gov.cn/static/yueshengshi/5c3e7e2e7c05cd222057ad3a0d89661d.png\"></image> -->\r\n\t\t\t                    <!-- <view @tap=\"navTo(more,'link')\">查看更多</view> -->\r\n\t\t\t                </view>\r\n\t\t\t            </view>\r\n\t\t\t            <view class=\"content service-hot-list\">\r\n\t\t\t                <view class=\"list-box\">\r\n\t\t\t                \t<view class=\"item-box lp-flex-column\" v-for=\"(item1, index1) in newsList\" :key=\"index1\"\r\n\t\t\t                \t@tap=\"navTo(item1,'link')\"\r\n\t\t\t                \t\t>\r\n\t\t\t                \t\t<view class=\"top-box lp-flex\">\r\n\t\t\t                \t\t\t        \r\n\t\t\t                \t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t                \t\t\t\t<image class=\"cover\" :src=\"item1.thumb\"></image>\r\n\t\t\t                \t\t\t\t<!-- <view class=\"button\"  >{{item1.status_text}}</view> -->\r\n\t\t\t                \t\t\t</view>\r\n\t\t\t                \t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t                \t\t\t\t<view class=\"title\">{{item1.title}}</view>\r\n\t\t\t                \t\t\t\t<!-- <view class=\"des\">{{item1.des}}</view> -->\r\n\t\t\t                \t\t\t\t<view class=\"end\"><text style=\"text-align: right;float: right;\">更多</text></view>\r\n\t\t\t                \t\t\t</view>\r\n\t\t\t                \t\t</view>\r\n\t\t\t                \t</view>\r\n\t\t\t                </view>\r\n\t\t\t            </view>\r\n\t\t\t        </view>\r\n\t\t\t    </view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n<script>\r\n\tvar graceJs = require('@/GraceUI5/js/grace.js');\r\n\t// 模拟 api 请求数据，格式见 article.js\r\n\tvar artciles = require('@/GraceUI5/demoData/article.js');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 页面加载\r\n\t\t\t\tpageLoading: true,\r\n\t\t\t\t// 主体高度\r\n\t\t\t\tmainHeight: 200,\r\n\t\t\t\t// 滚动区域滚动距离\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\t// 加载更多延迟\r\n\t\t\t\tloadMoreTimer: null,\r\n\t\t\t\t// 分类\r\n\t\t\t\tnavItems: [{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '赛事公告',\r\n\t\t\t\t\t\ticon_url:'news',\r\n\t\t\t\t\t\ttype:0,\r\n\t\t\t\t\t\tstyles:1\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\tname: '参赛须知',\r\n\t\t\t\t\t\tcontent:'news',\r\n\t\t\t\t\t\ttype:8,\r\n\t\t\t\t\t\tstyles:2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\tname: '大赛平台',\r\n\t\t\t\t\t\tcontent:'news',\r\n\t\t\t\t\t\ttype:9,\r\n\t\t\t\t\t\tstyles:2\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\tname: '常见问题',\r\n\t\t\t\t\t\tcontent:'news',\r\n\t\t\t\t\t\ttype:10,\r\n\t\t\t\t\t\tstyles:3\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 当前展示的分类索引\r\n\t\t\t\tcurrentIndex: 0,\r\n\t\t\t\t// 新闻列表数据， 分类切换重新获取第一页\r\n\t\t\t\tnewsList: [],\r\n\t\t\t\t// 页码\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tswiperCurrent: 0,\r\n\t\t\t\tswiperLength: 0,\r\n\t\t\t\tcarouselList: [],\r\n\t\t\t\tstyles:1,\r\n\t\t\t\tcontent:'<p>1212121</p>',\r\n\t\t\t\tsuperInList: [{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '报名参赛',\r\n\t\t\t\t\t\ticon_url:'/static/chaoqicuiban.png',\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\ttitle: '写作赛题',\r\n\t\t\t\t\t\ticon_url:'/static/dingdanguanli.png',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\ttitle: '参赛须知',\r\n\t\t\t\t\t\ticon_url:'/static/kaohepingguguanli.png',\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\ttitle: '最新公告',\r\n\t\t\t\t\t\ticon_url:'/static/tongzhizhongxin.png',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t {\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\ttitle: '常见问题',\r\n\t\t\t\t\t\ticon_url:'/static/yanguankaohe.png',\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\trmfwBgUrl: \"https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20221105124531.png\",\r\n\t\t\t\totherList:[],\r\n\t\t\t\tcourseList:[],\r\n\t\t\t\tmore:{\r\n\t\t\t\t\ttitle:'联普日语社区',\r\n\t\t\t\t\tlink:'https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MzI2Mjc4ODk4NA==#wechat_redirect',\r\n\t\t\t\t\ttype:'web'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function() {\r\n\t\t\t// 01. 获取页面主体高度\r\n\t\t\tgraceJs.getRefs('guiPage', this, 0, (ref) => {\r\n\t\t\t\tref.getDomSize('guiPageBody', (e) => {\r\n\t\t\t\t\t// 主体高度 = 页面高度 - 自定义区域高度\r\n\t\t\t\t\tgraceJs.select('#myheader', (e2) => {\r\n\t\t\t\t\t\t// 如果自定义区域有 margin 尺寸获取不到请加上这个值，可以利用 uni.upx2px() 转换\r\n\t\t\t\t\t\t// this.mainHeight = e.height - e2.height;\r\n\t\t\t\t\t\tthis.pageLoading = false;\r\n\t\t\t\t\t\tthis.loadData()\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t//this.loadData2()\r\n\t\t\t\t\t\t// 第一次加载数据\r\n\t\t\t\t\t\t//this.getNews();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\twx.showShareMenu({\r\n\t\t\t\t\twithShareTicket:true,\r\n\t\t\t\t\tmenus:[\"shareAppMessage\",\"shareTimeline\"]\r\n\t\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonShareAppMessage() {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\ttitle: \"人民中国杯日语国际写作大赛\",//标题\r\n\t\t\t\t\t\timageUrl: \"static/logo.png\",//封面\r\n\t\t\t\t\t\tpath: \"/pages/index/index\"//此处链接为要分享的页面链接\t\r\n\t\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t// 分享到朋友圈\r\n\t\t\tonShareTimeline() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: \"人民中国杯日语国际写作大赛\",//标题\r\n\t\t\t\t\timageUrl: \"static/logo.png\",//封面\r\n\t\t\t\t\tpath: \"/pages/index/index\"//此处链接为要分享的页面链接\t\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\tmethods: {\r\n\t\t\tloadData(source) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthis.$http.get(\"v1/foreign/index\").then(res => {\r\n\t\t\t\t\tthat.carouselList = res.data.data.banner;\r\n\t\t\t\t\tthat.swiperLength = res.data.data.banner.length;\r\n\t\t\t\t\tthat.superInList = res.data.data.home_menu\r\n\t\t\t\t\tthat.newsList = res.data.data.game_foot_post\r\n\t\t\t\t\tthat.otherList = res.data.data.project\r\n\t\t\t\t\tthat.courseList = res.data.data.projectType;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tloadData2(){\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: 'https://practice.jpworld.cn/api/v1/project_list', //仅为示例，并非真实接口地址。\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t},\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'custom-header': 'hello' //自定义请求头信息\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t\t// let allList = res.data.data.list.data;\r\n\t\t\t\t\t\t// allList.forEach(item => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t//        console.log(item)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.courseList = res.data.data.list.data;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//轮播图切换修改背景色\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tconst index = e.detail.current;\r\n\t\t\t\tthis.swiperCurrent = index;\r\n\t\t\t},\r\n\t\t\tnavchange: function(index) {\r\n\t\t\t\t// 刷新当前分类对应的数据\r\n\t\t\t\tif (this.currentIndex != index) {\r\n\t\t\t\t\tthis.styles = this.navItems[index].styles;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.currentIndex = index;\r\n\t\t\t\t\tthis.getNews();\r\n\t\t\t\t\t// 重置加载组件状态\r\n\t\t\t\t\tthis.$refs.loadmorecom.stoploadmore();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 新闻加载函数\r\n\t\t\t// isReload 代表下拉刷新\r\n\t\t\tgetNews: function(isReload) {\r\n\t\t\t\tif (this.page <= 1) {\r\n\t\t\t\t\tthis.newsList = [];\r\n\t\t\t\t\tif (!isReload) {\r\n\t\t\t\t\t\tthis.pageLoading = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tlet item = this.navItems[this.currentIndex]\r\n\t\t\t\tlet url = 'v1/news'\r\n\t\t\t\t// if(item.content=='news'){\r\n\t\t\t\t// \turl = 'v1/news'\r\n\t\t\t\t// }else{\r\n\t\t\t\t// \turl = 'v1/web-zbs'\r\n\t\t\t\t// }\r\n\t\t\t\tthis.$http.get(url, {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\ttype: 0,\r\n\t\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\t\tstyles:1\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif(this.styles==2){\r\n\t\t\t\t\t\tthis.content = res.data.data.content\r\n\t\t\t\t\t\tthis.pageLoading = false;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tvar demoArr = graceJs.arrayConcat(res.data.data.data);\r\n\t\t\t\t\t\tconsole.log(demoArr)\r\n\t\t\t\t\t\tif (this.page >= 2) {\r\n\t\t\t\t\t\t\tthis.newsList = this.newsList.concat(demoArr);\r\n\t\t\t\t\t\t\t// 加载完成后停止加载动画\r\n\t\t\t\t\t\t\tthis.$refs.loadmorecom.stoploadmore();\r\n\t\t\t\t\t\t\t// 假定第3页加载了全部数据，通知组件不再加载更多\r\n\t\t\t\t\t\t\t// 实际开发由接口返回值来决定\r\n\t\t\t\t\t\t\tif (this.page >= 3) {\r\n\t\t\t\t\t\t\t\tthis.$refs.loadmorecom.nomore();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 第一页 有可能是第一次加载或者刷新\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tthis.newsList = [];\r\n\t\t\t\t\t\t\tthis.newsList = demoArr;\r\n\t\t\t\t\t\t\t// 刷新\r\n\t\t\t\t\t\t\tif (isReload) {\r\n\t\t\t\t\t\t\t\tthis.$refs.refreshcom.endReload();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.pageLoading = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.page++;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tscroll: function(e) {\r\n\t\t\t\tthis.scrollTop = e.detail.scrollTop;\r\n\t\t\t},\r\n\t\t\t// 下拉刷新相关事件绑定\r\n\t\t\ttouchstart: function(e) {\r\n\t\t\t\tif (this.scrollTop > 0) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.refreshcom.touchstart(e);\r\n\t\t\t},\r\n\t\t\ttouchmove: function(e) {\r\n\t\t\t\tif (this.scrollTop > 0) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.refreshcom.touchmove(e);\r\n\t\t\t},\r\n\t\t\ttouchend: function(e) {\r\n\t\t\t\tif (this.scrollTop > 0) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.refreshcom.touchend(e);\r\n\t\t\t},\r\n\t\t\t// 刷新事件\r\n\t\t\treload: function() {\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.getNews(true);\r\n\t\t\t\t// 刷新时重置加载组件状态\r\n\t\t\t\tthis.$refs.loadmorecom.stoploadmore();\r\n\t\t\t},\r\n\t\t\t// 加载更多事件\r\n\t\t\tloadmorefun: function() {\r\n\t\t\t\t// 获取加载组件状态看一下是否还能继续加载\r\n\t\t\t\t// 保证触底只执行一次加载\r\n\t\t\t\tif (this.loadMoreTimer != null) {\r\n\t\t\t\t\tclearTimeout(this.loadMoreTimer);\r\n\t\t\t\t}\r\n\t\t\t\tthis.loadMoreTimer = setTimeout(() => {\r\n\t\t\t\t\tvar status = this.$refs.loadmorecom.loadMoreStatus;\r\n\t\t\t\t\tif (status != 0) {\r\n\t\t\t\t\t\treturn null;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$refs.loadmorecom.loading();\r\n\t\t\t\t\t// 此处开启加载动画执行加载数据的函数\r\n\t\t\t\t\tthis.getNews();\r\n\t\t\t\t}, 80);\r\n\t\t\t},\r\n\t\t\t// 新闻点击\r\n\t\t\tnewstap: function(e) {\r\n\t\t\t\t// 获取新闻 id\r\n\t\t\t\tvar newsId = e;\r\n\t\t\t\tconsole.log(newsId);\r\n\t\t\t\t// 打开新闻详情页面\r\n\t\t\t\t//uni.navigateTo()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 统一跳转接口,拦截未登录路由\r\n\t\t\t * navigator标签现在默认没有转场动画，所以用view\r\n\t\t\t */\r\n\t\t\t// 跳转\r\n\t\t\tnavTo(item,type){\r\n\t\t\t\tconsole.log(item,type)\r\n\t\t\t\tif(type=='info'){\r\n\t\t\t\t\t// 作品详情跳转\r\n\t\t\t\t\tthis.comJs.navToInfo(item.id)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif(type=='link'){\r\n\t\t\t\t\tswitch(item.type){\r\n\t\t\t\t\t\tcase \"web\":\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl:\"/pages/webView/webView?data=\"+encodeURIComponent(JSON.stringify(item))\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"mini_app\":\r\n\t\t\t\t\t\t\tconsole.log(item,type)\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: item.link\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"popu\":\r\n\t\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\t\tthis.moduleTitle = item.title;\r\n\t\t\t\t\t\t\tthis.moduleContent = item.description;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"other_mini\":\t\t// 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\t\tthis.getUrl(item.app_id,item.link) \r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(type=='swiper'){\r\n\t\t\t\t\tswitch(item.type){\r\n\t\t\t\t\t\tcase \"web\":\t\t\t// 项目外部跳转，需要使用web-view跳转外部H5页面\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl:\"/pages/webView/webView?data=\"+encodeURIComponent(JSON.stringify(item))\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"mini_app\":\t// 项目内部跳转\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl:item.link\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"popu\":\t\t// 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\t\tthis.moduleTitle = item.title;\r\n\t\t\t\t\t\t\tthis.moduleContent = item.description;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(type=='other'){\r\n\t\t\t\t\tthis.getUrl('wx9fbee055617c932a','/pages/index/index')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 详情\r\n\t\t\ttoDetail: function(e) {\r\n\t\t\t\t// 获取 id\r\n\t\t\t\tvar id = e.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/article/article?id=${id}`\r\n\t\t\t\t})  \r\n\t\t\t},\r\n\t\t\tgetUrl(appId,path){\r\n\t\t\t\t console.log(appId)\r\n\t\t\t      uni.navigateToMiniProgram({\r\n\t\t\t            appId: appId,\r\n\t\t\t            path: path,\r\n\t\t\t            success(res) {\r\n\t\t\t                              // 打开成功\r\n\t\t\t            }\r\n\t\t\t    })\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.header {\r\n\t\tpadding: 15rpx 30rpx;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t/* 头部 轮播图 */\r\n\t.carousel-section {\r\n\t\tposition: relative;\r\n\t\tpadding-top: 10px;\r\n\r\n\t\t.titleNview-placing {\r\n\t\t\theight: var(--status-bar-height);\r\n\t\t\tpadding-top: 44px;\r\n\t\t\tbox-sizing: content-box;\r\n\t\t}\r\n\r\n\t\t.titleNview-background {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 426upx;\r\n\t\t\ttransition: .4s;\r\n\t\t}\r\n\t}\r\n\r\n\t.carousel {\r\n\t\twidth: 100%;\r\n\t\theight: 350upx;\r\n\r\n\t\t.carousel-item {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tpadding: 0 28upx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tborder-radius: 10upx;\r\n\t\t}\r\n\t}\r\n\r\n\t.swiper-dots {\r\n\t\tdisplay: flex;\r\n\t\tposition: absolute;\r\n\t\tleft: 60upx;\r\n\t\tbottom: 15upx;\r\n\t\twidth: 72upx;\r\n\t\theight: 36upx;\r\n\t\tbackground-image: url(data:image/png;base64,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);\r\n\t\tbackground-size: 100% 100%;\r\n\r\n\t\t.num {\r\n\t\t\twidth: 36upx;\r\n\t\t\theight: 36upx;\r\n\t\t\tborder-radius: 50px;\r\n\t\t\tfont-size: 24upx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 36upx;\r\n\t\t}\r\n\r\n\t\t.sign {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 50%;\r\n\t\t\tline-height: 36upx;\r\n\t\t\tfont-size: 12upx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 分类 */\r\n\t.cate-section {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 30upx 22upx;\r\n\t\tbackground: #fff;\r\n\r\n\t\t.cate-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t}\r\n\r\n\t\t/* 原图标颜色太深,不想改图了,所以加了透明度 */\r\n\t\timage {\r\n\t\t\twidth: 88upx;\r\n\t\t\theight: 88upx;\r\n\t\t\tmargin-bottom: 14upx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\topacity: .7;\r\n\t\t\tbox-shadow: 4upx 4upx 20upx rgba(250, 67, 106, 0.3);\r\n\t\t}\r\n\t}\r\n\r\n\t.ad-1 {\r\n\t\twidth: 100%;\r\n\t\theight: 210upx;\r\n\t\tpadding: 10upx 0;\r\n\t\tbackground: #fff;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\t.rich-text{\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\t.content {\r\n\t\tpadding: 15px;\r\n\t}\r\n\t.flexbox {\r\n\t    margin: 36rpx 16rpx 6rpx;\r\n\t    padding-bottom: 12rpx;\r\n\t}\r\n\t\r\n\t.flex1,.flexbox {\r\n\t    display: flex;\r\n\t}\r\n\t\r\n\t.flex1 {\r\n\t    align-items: center;\r\n\t    flex-direction: column;\r\n\t    justify-content: center;\r\n\t    width: 25%;\r\n\t}\r\n\t\r\n\t.icon120 {\r\n\t    height: 100rpx;\r\n\t    width: 100rpx;\r\n\t}\r\n\t\r\n\t.nowrap {\r\n\t    overflow: hidden;\r\n\t    text-overflow: ellipsis;\r\n\t    white-space: nowrap;\r\n\t}\r\n\t\r\n\t.txt26 {\r\n\t    color: #000;\r\n\t    display: block;\r\n\t    font-size: 28rpx;\r\n\t    height: 36rpx;\r\n\t    line-height: 36rpx;\r\n\t    margin-top: 6rpx;\r\n\t    text-align: center;\r\n\t    width: 130rpx;\r\n\t}\r\n\t.bg-box {\r\n\t    align-items: center;\r\n\t    display: flex;\r\n\t    justify-content: center;\r\n\t    width: 100%;\r\n\t}\r\n\t\r\n\t.hot-service {\r\n\t    background: #fff;\r\n\t    border-radius: 8rpx;\r\n\t    box-sizing: border-box;\r\n\t    margin: 18rpx;\r\n\t    overflow: hidden;\r\n\t    width: 670rpx;\r\n\t}\r\n\t\r\n\t.bg-img {\r\n\t    height: 88rpx;\r\n\t    position: absolute;\r\n\t    width: 670rpx;\r\n\t    z-index: 1;\r\n\t}\r\n\t\r\n\t.hot-service-content {\r\n\t    padding: 6rpx 6rpx 0;\r\n\t}\r\n\t\r\n\t.hot-service-title {\r\n\t    background-position: 50%;\r\n\t    background-size: cover;\r\n\t    border-radius: 8rpx 8rpx 0 0;\r\n\t    display: block;\r\n\t    height: 88rpx;\r\n\t    position: relative;\r\n\t    width: 670rpx;\r\n\t    z-index: 2;\r\n\t}\r\n\t\r\n\t.hot-service-title-h3 {\r\n\t    color: #2e3f56;\r\n\t    font-size: 32rpx;\r\n\t    font-weight: 700;\r\n\t    line-height: 88rpx;\r\n\t    margin-left: 30rpx;\r\n\t}\r\n\t\r\n\t.swiper-container {\r\n\t    height: 300rpx;\r\n\t}\r\n\t\r\n\t.swiper-container .swiper-item {\r\n\t    display: flex;\r\n\t    flex-wrap: wrap;\r\n\t}\r\n\t\r\n\t.swiper-container-row {\r\n\t    height: 166rpx;\r\n\t}\r\n\t\r\n\t.swiper-container-row .swiper-item {\r\n\t    display: flex;\r\n\t}\r\n\t\r\n\t.srv-col {\r\n\t    box-sizing: border-box;\r\n\t    flex: 1;\r\n\t    width: 160rpx;\r\n\t}\r\n\t\r\n\t.srv-item {\r\n\t    align-items: center;\r\n\t    display: flex;\r\n\t    flex-direction: column;\r\n\t    height: 144rpx;\r\n\t    justify-content: center;\r\n\t    text-align: center;\r\n\t    width: 33%;\r\n\t}\r\n\t\r\n\t.srv-item:nth-child(4n) {\r\n\t    margin-right: 0rpx;\r\n\t}\r\n\t\r\n\t.srv-item-icon {\r\n\t    height: 80rpx;\r\n\t    margin-bottom: 12rpx;\r\n\t    margin-top: 6rpx;\r\n\t    width: 80rpx;\r\n\t\tborder-radius: 80rpx;\r\n\t}\r\n\t\r\n\t.srv-item-icon1 {\r\n\t    height: 50rpx;\r\n\t    margin-bottom: 12rpx;\r\n\t    margin-top: 6rpx;\r\n\t    width: 50rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\t\r\n\t.srv-item-title {\r\n\t    box-sizing: border-box;\r\n\t    color: #000;\r\n\t    display: block;\r\n\t    font-size: 28rpx;\r\n\t    height: 36rpx;\r\n\t    line-height: 36rpx;\r\n\t    overflow: hidden;\r\n\t    text-align: center;\r\n\t    text-overflow: ellipsis;\r\n\t    white-space: nowrap;\r\n\t    width: 100%;\r\n\t}\r\n\t\r\n\t.indicator {\r\n\t    display: flex;\r\n\t    height: 8rpx;\r\n\t    margin-bottom: 30rpx;\r\n\t    margin-top: 12rpx;\r\n\t    width: 670rpx;\r\n\t}\r\n\t\r\n\t.indicator-child {\r\n\t    background: rgba(56,136,255,.5);\r\n\t    border-radius: 4rpx;\r\n\t    float: left;\r\n\t    height: 8rpx;\r\n\t    margin-right: 10rpx;\r\n\t    transition: all .3s ease;\r\n\t    width: 8rpx;\r\n\t}\r\n\t\r\n\t.active {\r\n\t    background-color: #3888ff;\r\n\t    width: 50rpx;\r\n\t}\r\n\t.bg {\r\n\t    width: 100%;\r\n\t}\r\n\t\r\n\t.service-main {\r\n\t    margin: 0 auto;\r\n\t}\r\n\t\r\n\t.h2title {\r\n\t    color: #000;\r\n\t    display: block;\r\n\t    font-size: 40rpx;\r\n\t    height: 60rpx;\r\n\t    line-height: 60rpx;\r\n\t}\r\n\t\r\n\t.listbox {\r\n\t    background: #fff;\r\n\t    border: 1rpx solid #ebebeb;\r\n\t    border-radius: 8rpx;\r\n\t    margin: 40rpx 40rpx 0;\r\n\t    padding-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.titlebox {\r\n\t    align-items: center;\r\n\t    color: #3888ff;\r\n\t    display: flex;\r\n\t    height: 60rpx;\r\n\t    justify-content: space-between;\r\n\t    padding-bottom: 18rpx;\r\n\t    padding-top: 36rpx;\r\n\t}\r\n\t\r\n\t.service-list {\r\n\t    background-color: initial!important;\r\n\t    border: 1rpx solid transparent!important;\r\n\t    box-shadow: none!important;\r\n\t    margin-top: 0!important;\r\n\t}\r\n\t\r\n\t.service-list-title {\r\n\t    padding-left: 0rpx!important;\r\n\t}\r\n\t\r\n\t.viewtitle {\r\n\t    font-weight: 700;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-title {\r\n\t    align-items: center;\r\n\t    color: #3888ff;\r\n\t    display: inline-flex;\r\n\t    font-size: 30rpx;\r\n\t    height: 40rpx;\r\n\t    justify-content: space-between;\r\n\t    line-height: 40rpx;\r\n\t    width: 133rpx;\r\n\t}\r\n\t\r\n\t.content.service-hot-list {\r\n\t    background-color: #fff;\r\n\t    border-radius: 8rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-title .refresh-icon {\r\n\t    height: 27rpx;\r\n\t    width: 30rpx;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-list .service-hot-item {\r\n\t    align-items: center;\r\n\t    box-shadow: inset 0 -1rpx 0 0 #ebebeb;\r\n\t    display: flex;\r\n\t    margin: 0 40rpx;\r\n\t    padding: 36rpx 0;\r\n\t    position: relative;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-list .service-hot-item .title {\r\n\t    color: #000;\r\n\t    font-family: PingFangSC-Regular;\r\n\t    font-size: 30rpx;\r\n\t    line-height: 40rpx;\r\n\t    max-width: 540rpx;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-list .service-hot-item .tag {\r\n\t    background: rgba(66,147,244,.1);\r\n\t    border-radius: 4rpx;\r\n\t    color: #4293f4;\r\n\t    display: inline-block;\r\n\t    font-family: PingFangSC-Regular;\r\n\t    font-size: 26rpx;\r\n\t    font-weight: 700;\r\n\t    height: 36rpx;\r\n\t    line-height: 36rpx;\r\n\t    margin-left: 12rpx;\r\n\t    padding: 0 12rpx;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-list .service-hot-item .arrow {\r\n\t    height: 24rpx;\r\n\t    position: absolute;\r\n\t    right: 0;\r\n\t    width: 14rpx;\r\n\t}\r\n\t\r\n\t.service-main .service-hot-list .service-hot-item:last-child {\r\n\t    box-shadow: none;\r\n\t}\r\n\t\r\n\t.twoNoWrap {\r\n\t    -webkit-line-clamp: 2;\r\n\t    -webkit-box-orient: vertical;\r\n\t    display: -webkit-box;\r\n\t}\r\n\t\r\n\t.nowrap,.twoNoWrap {\r\n\t    overflow: hidden;\r\n\t    text-overflow: ellipsis;\r\n\t}\r\n\t\r\n\t.nowrap {\r\n\t    white-space: nowrap;\r\n\t}\r\n\t.item {\r\n\t    border-bottom: 1rpx solid #ebebeb;\r\n\t    box-sizing: border-box;\r\n\t    display: flex;\r\n\t    height: 160rpx;\r\n\t    margin: 0 auto;\r\n\t    width: 610rpx;\r\n\t}\r\n\t\r\n\t.pop-box .item {\r\n\t    padding-left: 0rpx;\r\n\t    width: 670rpx;\r\n\t}\r\n\t\r\n\t.item-icon {\r\n\t    height: 150rpx;\r\n\t    margin-right: 30rpx;\r\n\t    // margin-top: 39rpx;\r\n\t    vertical-align: middle;\r\n\t    width: 150rpx;\r\n\t}\r\n\t\r\n\t.item-text {\r\n\t    display: flex;\r\n\t    flex-direction: column;\r\n\t}\r\n\t\r\n\t.item-title {\r\n\t    color: #000;\r\n\t    font-size: 34rpx;\r\n\t    height: 48rpx;\r\n\t    line-height: 48rpx;\r\n\t    margin-bottom: 6rpx;\r\n\t    margin-top: 36rpx;\r\n\t}\r\n\t\r\n\t.item-title .nowrap {\r\n\t    display: inline-block;\r\n\t    font-weight: 700;\r\n\t    margin-right: 10rpx;\r\n\t    max-width: 500rpx;\r\n\t    vertical-align: middle;\r\n\t}\r\n\t\r\n\t.item-desc {\r\n\t    color: rgba(0,0,0,.3);\r\n\t    font-size: 24rpx;\r\n\t    height: 34rpx;\r\n\t    line-height: 34rpx;\r\n\t    margin-bottom: 20rpx;\r\n\t    width: 456rpx;\r\n\t}\r\n\t\r\n\t.item-title .topic-tip {\r\n\t    background: rgba(69,154,255,.1);\r\n\t    border-radius: 4rpx;\r\n\t    color: #3888ff;\r\n\t    display: inline-block;\r\n\t    font-size: 26rpx;\r\n\t    font-weight: 700;\r\n\t    height: 36rpx;\r\n\t    line-height: 36rpx;\r\n\t    text-align: center;\r\n\t    width: 50rpx;\r\n\t}\r\n\t\r\n\t.nowrap {\r\n\t    overflow: hidden;\r\n\t    text-overflow: ellipsis;\r\n\t    white-space: nowrap;\r\n\t}\r\n\t\r\n\t.service-banner__list {\r\n\t    padding-left: 0;\r\n\t}\r\n\t\r\n\t.content__desc {\r\n\t    color: rgba(0,0,0,.3)!important;\r\n\t    font-size: 24rpx!important;\r\n\t}\r\n\t\r\n\t.pop-item {\r\n\t    margin: 0;\r\n\t}\r\n\t\r\n\t.pop-item:last-child {\r\n\t    border-bottom: none;\r\n\t}\r\n\t.list-box {\r\n\t\tpadding-bottom: 20rpx;\r\n\t\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding:10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\t border-bottom: 1rpx solid #ebebeb;\r\n\t        \r\n\t\t\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.cover :after{\r\n\t\t\t\t\t    background-color: red;\r\n\t\t\t\t\t    border-radius: 10rpx;\r\n\t\t\t\t\t    color: #fff;\r\n\t\t\t\t\t    content: \"hot\";\r\n\t\t\t\t\t    font-size: 25rpx;\r\n\t\t\t\t\t    line-height: 1;\r\n\t\t\t\t\t    padding: 2rpx 6rpx;\r\n\t\t\t\t\t    position: absolute;\r\n\t\t\t\t\t    left: 5rpx;\r\n\t\t\t\t\t    top: 5rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.button{\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.button{\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t  \r\n\t\t\t\t\r\n\t\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.des{\r\n\t\t\t\t\t\tfont-size:22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.price{\r\n\t\t\t\t\t\tfont-size:24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.end{\r\n\t\t\t\t\t\tfont-size:24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t:last-child{\r\n\t\t   border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n\t.lp-flex {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\t\r\n\t.lp-flex-column {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040521827\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}