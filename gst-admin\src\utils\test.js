/**
 * 简单的测试工具
 */

class TestRunner {
  constructor() {
    this.tests = []
    this.results = []
  }

  // 添加测试用例
  test(name, fn) {
    this.tests.push({ name, fn })
  }

  // 运行所有测试
  async run() {
    console.log(`🧪 开始运行 ${this.tests.length} 个测试...`)
    this.results = []

    for (const test of this.tests) {
      try {
        const startTime = performance.now()
        await test.fn()
        const endTime = performance.now()
        
        this.results.push({
          name: test.name,
          status: 'passed',
          duration: endTime - startTime
        })
        
        console.log(`✅ ${test.name} - 通过 (${(endTime - startTime).toFixed(2)}ms)`)
      } catch (error) {
        this.results.push({
          name: test.name,
          status: 'failed',
          error: error.message,
          duration: 0
        })
        
        console.error(`❌ ${test.name} - 失败: ${error.message}`)
      }
    }

    this.printSummary()
    return this.results
  }

  // 打印测试摘要
  printSummary() {
    const passed = this.results.filter(r => r.status === 'passed').length
    const failed = this.results.filter(r => r.status === 'failed').length
    const total = this.results.length
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log('\n📊 测试摘要:')
    console.log(`总计: ${total}`)
    console.log(`通过: ${passed}`)
    console.log(`失败: ${failed}`)
    console.log(`总耗时: ${totalTime.toFixed(2)}ms`)
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.results
        .filter(r => r.status === 'failed')
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`))
    }
  }

  // 清除所有测试
  clear() {
    this.tests = []
    this.results = []
  }
}

// 断言工具
export const assert = {
  // 相等断言
  equal(actual, expected, message = '') {
    if (actual !== expected) {
      throw new Error(`断言失败: ${message || `期望 ${expected}, 实际 ${actual}`}`)
    }
  },

  // 深度相等断言
  deepEqual(actual, expected, message = '') {
    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
      throw new Error(`断言失败: ${message || `期望 ${JSON.stringify(expected)}, 实际 ${JSON.stringify(actual)}`}`)
    }
  },

  // 真值断言
  ok(value, message = '') {
    if (!value) {
      throw new Error(`断言失败: ${message || `期望真值, 实际 ${value}`}`)
    }
  },

  // 抛出异常断言
  async throws(fn, message = '') {
    try {
      await fn()
      throw new Error(`断言失败: ${message || '期望抛出异常，但没有抛出'}`)
    } catch (error) {
      if (error.message.includes('断言失败')) {
        throw error
      }
      // 正常抛出异常，断言通过
    }
  },

  // 包含断言
  includes(array, item, message = '') {
    if (!array.includes(item)) {
      throw new Error(`断言失败: ${message || `期望数组包含 ${item}`}`)
    }
  },

  // 类型断言
  type(value, expectedType, message = '') {
    const actualType = typeof value
    if (actualType !== expectedType) {
      throw new Error(`断言失败: ${message || `期望类型 ${expectedType}, 实际类型 ${actualType}`}`)
    }
  }
}

// 创建全局测试实例
const testRunner = new TestRunner()

// 导出测试函数
export const test = (name, fn) => testRunner.test(name, fn)
export const runTests = () => testRunner.run()
export const clearTests = () => testRunner.clear()

// 功能测试套件
export const functionalTests = {
  // 测试缓存功能
  async testCache() {
    const { setCache, getCache, deleteCache, hasCache } = await import('./cache')
    
    test('缓存设置和获取', () => {
      setCache('test_key', 'test_value', 1000)
      assert.equal(getCache('test_key'), 'test_value')
    })

    test('缓存过期', async () => {
      setCache('expire_key', 'expire_value', 10) // 10ms过期
      await new Promise(resolve => setTimeout(resolve, 20))
      assert.equal(getCache('expire_key'), null)
    })

    test('缓存删除', () => {
      setCache('delete_key', 'delete_value')
      assert.ok(hasCache('delete_key'))
      deleteCache('delete_key')
      assert.ok(!hasCache('delete_key'))
    })
  },

  // 测试请求功能
  async testRequest() {
    const { get } = await import('./request')
    
    test('GET请求缓存', async () => {
      // 模拟请求，实际环境中会调用真实API
      try {
        const response = await get('/api/test', {}, { cache: true, showError: false })
        assert.type(response, 'object')
      } catch (error) {
        // 在没有后端的情况下，这是预期的
        assert.ok(true)
      }
    })
  },

  // 测试性能监控
  async testPerformance() {
    const { startTiming, endTiming, recordMetric, getMetrics } = await import('./performance')
    
    test('性能计时', () => {
      startTiming('test_timing')
      // 模拟一些工作
      for (let i = 0; i < 1000; i++) {
        Math.random()
      }
      endTiming('test_timing')
      
      const metrics = getMetrics('custom_timing')
      assert.ok(metrics.length > 0)
    })

    test('自定义指标记录', () => {
      recordMetric('test_metric', { value: 123, type: 'test' })
      const metrics = getMetrics('test_metric')
      assert.ok(metrics.length > 0)
      assert.equal(metrics[0].value, 123)
    })
  },

  // 运行所有功能测试
  async runAll() {
    console.log('🚀 开始功能测试...')
    
    await this.testCache()
    await this.testRequest()
    await this.testPerformance()
    
    return runTests()
  }
}

// 性能基准测试
export const benchmarkTests = {
  // 测试组件渲染性能
  async testComponentRender() {
    const iterations = 100
    const startTime = performance.now()
    
    // 模拟组件渲染
    for (let i = 0; i < iterations; i++) {
      const div = document.createElement('div')
      div.innerHTML = `<span>测试内容 ${i}</span>`
      document.body.appendChild(div)
      document.body.removeChild(div)
    }
    
    const endTime = performance.now()
    const avgTime = (endTime - startTime) / iterations
    
    console.log(`📊 组件渲染基准测试:`)
    console.log(`  - 迭代次数: ${iterations}`)
    console.log(`  - 总耗时: ${(endTime - startTime).toFixed(2)}ms`)
    console.log(`  - 平均耗时: ${avgTime.toFixed(2)}ms`)
    
    return { iterations, totalTime: endTime - startTime, avgTime }
  },

  // 测试数据处理性能
  async testDataProcessing() {
    const dataSize = 10000
    const testData = Array.from({ length: dataSize }, (_, i) => ({
      id: i,
      name: `用户${i}`,
      email: `user${i}@test.com`,
      score: Math.random() * 100
    }))

    const startTime = performance.now()
    
    // 数据过滤和排序
    const filtered = testData
      .filter(item => item.score > 50)
      .sort((a, b) => b.score - a.score)
      .slice(0, 100)
    
    const endTime = performance.now()
    
    console.log(`📊 数据处理基准测试:`)
    console.log(`  - 数据量: ${dataSize}`)
    console.log(`  - 处理耗时: ${(endTime - startTime).toFixed(2)}ms`)
    console.log(`  - 结果数量: ${filtered.length}`)
    
    return { dataSize, processTime: endTime - startTime, resultCount: filtered.length }
  }
}

export default testRunner
