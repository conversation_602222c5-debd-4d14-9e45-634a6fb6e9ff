/// <reference types="node" />
import { IncomingMessage, IncomingHttpHeaders } from 'http';
import { Readable } from 'stream';
declare type TeaDict = {
    [key: string]: string;
};
declare type TeaObject = {
    [key: string]: any;
};
export declare class BytesReadable extends Readable {
    value: Buffer;
    constructor(value: string | Buffer);
    _read(): void;
}
export declare class Request {
    protocol: string;
    port: number;
    method: string;
    pathname: string;
    query: TeaDict;
    headers: TeaDict;
    body: Readable;
    constructor();
}
export declare class Response {
    statusCode: number;
    statusMessage: string;
    headers: TeaDict;
    body: IncomingMessage;
    constructor(httpResponse: IncomingMessage);
    convertHeaders(headers: IncomingHttpHeaders): TeaDict;
    readBytes(): Promise<Buffer>;
}
export declare function doAction(request: Request, runtime?: TeaObject): Promise<Response>;
declare class ResponseError extends Error {
    code: string;
    statusCode: number;
    data: any;
    description: string;
    accessDeniedDetail: any;
    constructor(map: any);
}
export declare function newError(data: any): ResponseError;
export declare function toMap(value?: any): any;
export declare class Model {
    [key: string]: any;
    constructor(map?: TeaObject);
    toMap(): TeaObject;
}
export declare function cast<T>(obj: any, t: T): T;
export declare function sleep(ms: number): Promise<void>;
export declare function allowRetry(retry: TeaObject, retryTimes: number, startTime: number): boolean;
export declare function getBackoffTime(backoff: TeaObject, retryTimes: number): number;
export declare function newUnretryableError(request: Request): Error;
export declare function retryError(request: Request, response: Response): Error;
export declare function isRetryable(err: Error): boolean;
export {};
