{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/pages/article/detail/original.vue?88d1", "webpack:///D:/gst/gst-uniapp/pages/article/detail/original.vue?7f2a", "webpack:///D:/gst/gst-uniapp/pages/article/detail/original.vue?b834", "webpack:///D:/gst/gst-uniapp/pages/article/detail/original.vue?368b", "uni-app:///pages/article/detail/original.vue", "webpack:///D:/gst/gst-uniapp/pages/article/detail/original.vue?aa06", "webpack:///D:/gst/gst-uniapp/pages/article/detail/original.vue?1922", "uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/article/detail/index.vue?3961", "webpack:///D:/gst/gst-uniapp/pages/article/detail/index.vue?0cff", "webpack:///D:/gst/gst-uniapp/pages/article/detail/index.vue?55aa", "webpack:///D:/gst/gst-uniapp/pages/article/detail/index.vue?9b58", "uni-app:///pages/article/detail/index.vue", "webpack:///D:/gst/gst-uniapp/pages/article/detail/index.vue?5f8d", "webpack:///D:/gst/gst-uniapp/pages/article/detail/index.vue?ebcc"], "names": ["components", "lpTran", "lpAnalyse", "lpAudioPlayer", "props", "article_id", "type", "default", "data", "article", "custom_y", "onReady", "uni", "computed", "articleAudio", "src", "isOriginal", "contents", "methods", "ready", "self", "do<PERSON>lock", "title", "created_at", "setTimeout", "goShare", "url", "sendArticleUpdate", "onLikeHandler", "icon", "onSetClipboardDataHandler", "console", "success", "onScrollHandler", "onTranDirtyHandler", "apiGetDetail", "params", "id", "apiDoClock", "apiDoCollect", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "lpOriginal", "statusBarHeight", "currentIndex", "created", "content", "onLoad", "keepScreenOn", "onUnload", "onNavChangeHandler", "onSwiperChangeHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAonB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4IxoB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAA;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACAT;UACAU;QACA;QACA;QACA;UACAC;QACA;QACA;QACAC;UACA;QACA;MACA;IACA;IAEAC;MACAb;QACAc;MACA;IACA;IAEAC;MACAf;IACA;IACA;IACA;IACA;IACA;IACA;IACAgB;MAAA;MACA;MACA;QACA;UACAhB;YACAiB;YACAP;UACA;UACA;UACA;QACA;MACA;IACA;IACAQ;MACAC;MACAnB;QACAJ;QACAwB;UACApB;YACAiB;YACAP;UACA;QACA;MACA;IACA;IACAW;MACA;IAAA,CACA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACAD;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAE;MACA;QACAF;QACA/B;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpTA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAkC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuBroB;;;;;;;;;;;;;;;;;;;;;;;eACA;EACA5C;IACA6C;EACA;EACAzC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAsC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACApC;QACAU;QACA2B;QACAjB;UACA;YACApB;cACAc;YACA;UACA;YACA;AACA;AACA;UAFA;QAIA;MACA;IACA;EACA;EACAwB;IACAnB;IACAnB;MACAuC;IACA;EACA;EACAC;IACArB;IACAnB;MACAuC;IACA;EACA;EACAjC;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAmC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAgsC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAptC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/article/detail/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./original.vue?vue&type=template&id=04522d66&scoped=true&\"\nvar renderjs\nimport script from \"./original.vue?vue&type=script&lang=js&\"\nexport * from \"./original.vue?vue&type=script&lang=js&\"\nimport style0 from \"./original.vue?vue&type=style&index=0&id=04522d66&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"04522d66\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article/detail/original.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./original.vue?vue&type=template&id=04522d66&scoped=true&\"", "var components\ntry {\n  components = {\n    lpInput: function () {\n      return import(\n        /* webpackChunkName: \"components/lp-input/lp-input\" */ \"@/components/lp-input/lp-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.article && (_vm.type == \"original\" || _vm.article.is_trans)\n      ? _vm.article.trans_dir.substr(_vm.isOriginal ? 0 : 2, 1)\n      : null\n  var l0 =\n    _vm.article &&\n    (_vm.type == \"original\" || _vm.article.is_trans) &&\n    _vm.article.type != 2\n      ? _vm.__map(_vm.contents, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var f0 = _vm._f(\"formatRichText\")(item.content)\n          return {\n            $orig: $orig,\n            f0: f0,\n          }\n        })\n      : null\n  var g1 =\n    _vm.article && (_vm.type == \"original\" || _vm.article.is_trans)\n      ? !_vm.isOriginal && _vm.article.comment.length\n      : null\n  var g2 =\n    _vm.article && (_vm.type == \"original\" || _vm.article.is_trans)\n      ? !_vm.isOriginal &&\n        _vm.article.trans.length &&\n        _vm.article.trans[0].comments.length\n      : null\n  var l1 =\n    _vm.article && (_vm.type == \"original\" || _vm.article.is_trans) && g2\n      ? _vm.__map(_vm.article.trans[0].comments, function (comment, index) {\n          var $orig = _vm.__get_orig(comment)\n          var f1 = _vm._f(\"formatRichText\")(comment.content)\n          return {\n            $orig: $orig,\n            f1: f1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./original.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./original.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"root lp-flex-column\" v-if=\"article\" style=\"height: 100%;\">\n\t\t<scroll-view enable-flex=\"true\" v-if=\"type=='original' || article.is_trans\" class=\"root-box\" scroll-y=\"true\"\n\t\t\t:scroll-top=\"custom_y\" @scroll=\"onScrollHandler\">\n\t\t\t<!-- 文章内容 -->\n\t\t\t<view class=\"panel main-box\">\n\t\t\t\t<view class=\"head head-box\">\n\t\t\t\t\t<view class=\"left-box\">\n\t\t\t\t\t\t<view class=\"title-icon\"></view>\n\t\t\t\t\t\t<text class=\"publish-date\">{{article.publish_date}}</text>\n\t\t\t\t\t\t<text class=\"lang\">{{article.trans_dir.substr(isOriginal ? 0 : 2,1)}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right-box\">\n\t\t\t\t\t\t<text class=\"gui-icons\"\n\t\t\t\t\t\t\t@tap=\"onLikeHandler\">{{article.is_collect ? '&#xe605;' : '&#xe6ad;'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"body\">\n\t\t\t\t\t<!-- 取第个内容的标题 -->\n\t\t\t\t\t<view class=\"title-box\">{{contents[0].title}}</view>\n\t\t\t\t\t<view class=\"cover-box\">\n\t\t\t\t\t\t<image class=\"cover\" mode=\"aspectFill\" :src=\"article.picture\"></image>\n\t\t\t\t\t</view>\n\t\t\t\n\t\t\t\t\t\t<view class=\"content-box\">\n\t\t\t\t\t\t\t<view class=\"rich-box\" v-if=\"article.type != 2\">\n\t\t\t\t\t\t\t\t<rich-text v-for=\"(item ,index) in contents\" :key=\"item.id\"\n\t\t\t\t\t\t\t\t\t@longpress=\"onSetClipboardDataHandler(item.content)\"\n\t\t\t\t\t\t\t\t\t:nodes=\"item.content | formatRichText\"></rich-text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- <view v-else>\n\t\t\t\t\t\t\t\t<lp-audio-player class=\"source-player\" ref=\"sourcePlayer\" :audio=\"articleAudio\">\n\t\t\t\t\t\t\t\t</lp-audio-player>\n\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\n\n\t\t\t\t\t<view class=\"content-foot-box\">\n\t\t\t\t\t\t<!-- <text>主编：{{article.editor_text}}</text>\n\t\t\t\t\t\t<text>审校：{{article.reviser_text}}</text> -->\n\t\t\t\t\t\t<text><text style=\"width: 120rpx;\">文章来源：</text>{{article.source ? article.source : '--'}}</text>\n\t\t\t\t\t\t<text class=\"declaration\"><text style=\"width: 120rpx;\">声明：</text>本材料为独家材料，版权归《{{article.source ? article.source : '--'}}》所有，仅供培训班内学员学习使用，禁止转载传播，一经发现将追究法律责任。</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 打卡 -->\n\t\t\t<!-- <view class=\"panel sign-in-box\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"title-icon\"></view>\n\t\t\t\t\t<text>打卡</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"body\">-->\n\t\t\t\t\t<!-- 会员查看 -->\n\t\t\t\t<!--\t<lp-auth-content :permission=\"article.is_read\"\n\t\t\t\t\t\t:auth_url=\"'/pages/course/detail/detail?course_id='+article.course_id\">\n\t\t\t\t\t\t<view class=\"btn-box\">\n\t\t\t\t\t\t\t<view v-if=\"!article.is_clock\" class=\"btn\" @tap=\"doClock()\">马上打卡</view>\n\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t<view class=\"btn done\" @tap=\"goShare()\">已打卡</view>\n\t\t\t\t\t\t\t\t<text class=\"sign-in-time\">{{article.clock_time.created_at}}</text>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</lp-auth-content>\n\t\t\t\t</view>\n\t\t\t</view> -->\n\n\t\t\t<!-- 翻译  -->\n\t\t\t<view class=\"panel tran-box\" v-if=\"isOriginal\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"title-icon\"></view>\n\t\t\t\t\t<view class=\"title-box\">\n\t\t\t\t\t\t<text>翻译</text>\n\t\t\t\t\t\t<!-- <text class=\"des\">（您可以同时提交文本和音频）</text> -->\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"body\">\n\n\t\t\t\t\t\t<lp-tran :article=\"article\" :type=\"type\" @get-input=\"onGetInputHandler\"\n\t\t\t\t\t\t\t@dirty=\"onTranDirtyHandler\"></lp-tran>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 名师解析  -->\n\t\t\t<view class=\"panel analyse-box\" v-if=\"!isOriginal && article.comment.length\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"title-icon\"></view>\n\t\t\t\t\t<view class=\"title-box\">\n\t\t\t\t\t\t<text>译文解析</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"body\">\n\n\t\t\t\t\t\t<lp-analyse v-for=\"(item ,index) in article.comment\" :key=\"item.id\" :comment=\"item\">\n\t\t\t\t\t\t</lp-analyse>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 名师点评  -->\n\t\t\t<view class=\"panel comment-box\" v-if=\"!isOriginal && article.trans.length && article.trans[0].comments.length\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"title-icon\"></view>\n\t\t\t\t\t<view class=\"title-box\">\n\t\t\t\t\t\t<text>老师点评</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\n\t\t\t\t<view class=\"body\">\n\t\t\t\t\t\t<view class=\"content-box\">\n\t\t\t\t\t\t\t<template v-for=\"(comment,index) in article.trans[0].comments\">\n\t\t\t\t\t\t\t\t<view v-if=\"comment.author\" style=\"font-size: 32rpx;font-weight: bold;\">{{comment.author.name}}点评：</view>\n\t\t\t\t\t\t\t\t<rich-text :key=\"comment.id\"\n\t\t\t\t\t\t\t\t\t@longpress=\"onSetClipboardDataHandler(comment.content)\"\n\t\t\t\t\t\t\t\t\t:nodes=\"comment.content | formatRichText\"></rich-text>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\n\t\t<view class=\"nopublish lp-flex-column lp-flex-center\" v-else>\n\t\t\t<text class=\"gui-icons\" style=\"font-size: 100rpx;\">&#xe687;</text>\n\t\t\t<text>译文即将公布!</text>\n\t\t</view>\n\n\t\t<lp-input ref=\"input\" v-if=\"isOriginal\"></lp-input>\n\t</view>\n\t<view class=\"nopublish lp-flex-column lp-flex-center\" v-else>\n\t\t<text class=\"gui-icons\">数据正在玩命加载中...</text>\n\t</view>\n</template>\n\n<script>\n\timport lpTran from './tran.vue';\n\timport lpAnalyse from './analyse.vue';\n\timport lpAudioPlayer from '@/components/audio-player/audio-player.vue';\n\timport util from '@/common/js/util.js';\n\n\tlet uploader;\n\n\texport default {\n\t\tcomponents: {\n\t\t\tlpTran,\n\t\t\tlpAnalyse,\n\t\t\tlpAudioPlayer\n\t\t},\n\t\tprops: {\n\t\t\tarticle_id: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 1\n\t\t\t},\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'original'\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tarticle: null,\n\t\t\t\tcustom_y: 0,\n\t\t\t}\n\t\t},\n\t\tonReady() {\n\t\t\tthis.ready();\n\t\t\tuni.$on('article_update', (article) => {\n\t\t\t\tthis.article = article;\n\t\t\t});\n\t\t},\n\t\tcomputed: {\n\t\t\tarticleAudio: function() {\n\t\t\t\treturn {\n\t\t\t\t\tsrc: this.contents ? this.contents[0].file : ''\n\t\t\t\t}\n\t\t\t},\n\t\t\tisOriginal: function() {\n\t\t\t\treturn this.type == 'original';\n\t\t\t},\n\t\t\t// 当前内容\n\t\t\tcontents: function() {\n\t\t\t\treturn this.article ? this.article[this.type] : null;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tready: function() {\n\t\t\t\tlet self = this;\n\t\t\t\tif(this.isOriginal){\n\t\t\t\t\tthis.apiGetDetail(this.article_id).then(data => {\n\t\t\t\t\t\tthis.article = data;\n\t\t\t\t\t\tself.sendArticleUpdate();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * 打卡\n\t\t\t */\n\t\t\tdoClock: function() {\n\t\t\t\tthis.apiDoClock(this.article_id).then((data) => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '打卡成功'\n\t\t\t\t\t});\n\t\t\t\t\tthis.article.is_clock = 1;\n\t\t\t\t\tthis.article.clock_time = {\n\t\t\t\t\t\tcreated_at: data.created_at\n\t\t\t\t\t};\n\t\t\t\t\tthis.sendArticleUpdate();\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.goShare();\n\t\t\t\t\t}, 1500)\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tgoShare: function() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/article/sign-in-share/sign-in-share?article_id=' + this.article_id\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tsendArticleUpdate: function() {\n\t\t\t\tuni.$emit('article_update', this.article);\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tonLikeHandler: function() {\n\t\t\t\tconst is_collect = this.article.is_collect;\n\t\t\t\tthis.apiDoCollect(this.article.id, is_collect ? 2 : 1).then(data => {\n\t\t\t\t\tif (data.status) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: is_collect ? '取消收藏' : '收藏成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.article.is_collect = !is_collect;\n\t\t\t\t\t\tthis.sendArticleUpdate();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tonSetClipboardDataHandler: function(text) {\r\n\t\t\t\tconsole.log(text);\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: util.html.getPureContent(text),\n\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '已复制'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tonScrollHandler: function(event) {\n\t\t\t\t//this.custom_y = event.detail.scrollTop;\n\t\t\t},\n\t\t\tonTranDirtyHandler: function() {\n\t\t\t\tthis.custom_y = 99999;\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 获取文章详情\n\t\t\t * @param {Object} id\n\t\t\t */\n\t\t\tapiGetDetail: function(id) {\n\t\t\t\treturn this.$http.get('/v1/article_detail', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tid\n\t\t\t\t\t}\n\t\t\t\t}).then((res) => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t});\n\t\t\t},\n\t\t\t/**\n\t\t\t * 打卡\n\t\t\t * @param {Object} id 文章ID\n\t\t\t */\n\t\t\tapiDoClock: function(id) {\n\t\t\t\treturn this.$http.post('/v1/doClock', {\n\t\t\t\t\tid\n\t\t\t\t}).then((res) => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t});\n\t\t\t},\n\t\t\t/**\n\t\t\t * 收藏\n\t\t\t * @param {string} id 文章ID\n\t\t\t * @param {int} type 1 收藏 2取消收藏\n\t\t\t */\n\t\t\tapiDoCollect: function(id, type) {\n\t\t\t\treturn this.$http.post('/v1/doCollect', {\n\t\t\t\t\tid,\n\t\t\t\t\ttype\n\t\t\t\t}).then((res) => {\n\t\t\t\t\treturn Promise.resolve(res.data);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.panel {\n\t\tmargin-bottom: 50rpx;\n\n\t\t.head {\n\t\t\tdisplay: flex;\n\t\t\tfont-size: 32rpx;\n\t\t\tmargin: 30rpx;\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t.body {\n\t\t\tpadding: 30rpx;\n\t\t}\n\n\t\t.title-icon {\n\t\t\twidth: 10rpx;\n\t\t\tbackground-color: $uni-color-primary;\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\n\t\t.des {\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 100;\n\t\t\tcolor: $uni-text-color-grey;\n\t\t}\n\n\t}\n\n\t.root{\n\t\tdisplay: flex;\n\t}\n\t.root-box {\n\t\theight: 1000rpx;\n\t\tflex:1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\n\t\t/* 文章内容 */\n\t\t.main-box {\n\t\t\t.head-box {\n\t\t\t\t.left-box {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t.lang {\n\t\t\t\t\t\tbackground: #aaa;\n\t\t\t\t\t\tpadding: 5rpx 15rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tborder-radius: 10rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tfont-weight: unset;\n\t\t\t\t\t\tmargin: 0 10rpx;\n\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.right-box {\n\t\t\t\t\t.gui-icons {\n\t\t\t\t\t\tcolor: $uni-color-error;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.title-box {\n\t\t\t\tfont-size: 40rpx;\n\t\t\t}\n\n\t\t\t.cover-box {\n\t\t\t\tmargin: 30rpx 0;\n\t\t\t\theight: 600rpx;\n\n\t\t\t\t.cover {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.content-box {\n\t\t\t\t.rich-box {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.content-foot-box {\n\t\t\t\tborder-top: solid 1px #eee;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #aaa;\n\t\t\t\tline-height: 40rpx;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.declaration{\n\t\t\t\tcolor: $uni-color-error;\n\t\t\t}\n\t\t}\n\n\t\t/* 打卡 */\n\t\t.sign-in-box {\n\t\t\t.sign-in-time {\n\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tline-height: 50rpx;\n\t\t\t}\n\n\t\t\t.btn-box {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.btn {\n\t\t\t\t\tborder: solid 1px $uni-color-error;\n\t\t\t\t\tbackground-color: $uni-color-error;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t}\n\n\t\t\t\t.done {\n\t\t\t\t\tbackground-color: unset;\n\t\t\t\t\tcolor: $uni-color-error;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/* 翻译 */\n\t\t.tran-box {\n\t\t\t.title-box {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-end;\n\t\t\t}\n\t\t}\n\t\t\n\t\t/* 点评 */\n\t\t.comment-box{\n\t\t\t.content-box{\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tborder: solid 1px #eee;\n\t\t\t\tpadding: 30rpx;\n\t\t\t\tbackground: #f5f5f5;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\t}\n\n\t.nopublish {\n\t\theight: 400rpx;\n\t\ttext-align: center;\n\t\tcolor: #c5c5c5;\n\t\tfont-size: 28rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./original.vue?vue&type=style&index=0&id=04522d66&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./original.vue?vue&type=style&index=0&id=04522d66&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689563383\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/article/detail/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=b90080f4&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=b90080f4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b90080f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article/detail/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=b90080f4&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" class=\"root-box\">\r\n\t\t<!-- 页面主体 -->\r\n\t\t<view slot=\"gBody\" class=\"gui-margin content-box\">\r\n\t\t\t<view class=\"gui-align-items-center gui-rows gui-nowrap gui-flex \">\r\n\t\t\t\t<view class=\"gui-header-content nav-box\">\r\n\t\t\t\t\t<view class=\"nav-btn\" :class=\"{active:currentIndex==0}\" @tap=\"onNavChangeHandler(0)\">原文</view>\r\n\t\t\t\t\t<view class=\"nav-btn\" :class=\"{active:currentIndex==1}\" @tap=\"onNavChangeHandler(1)\">译文</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<swiper class=\"tab-card-body swiper\" :current=\"currentIndex\" @change=\"onSwiperChangeHandler\">\r\n\t\t\t\t<!-- 轮播项目数量对应 上面的选项标签 -->\r\n\t\t\t\t<swiper-item class=\"tab-card-item\">\r\n\t\t\t\t\t<lp-original :article_id=\"article_id\"></lp-original>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item class=\"tab-card-item\">\r\n\t\t\t\t\t<lp-original :article_id=\"article_id\" type=\"translation\"></lp-original>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n<script>\r\n\timport lpOriginal from './original.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tlpOriginal,\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tarticle_id: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 4\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 0,\r\n\t\t\t\tcurrentIndex: 0,\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.statusBarHeight = this.$store.state.statusBar;\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tconsole.log('onLoad');\r\n\t\t\tuni.setKeepScreenOn({\r\n\t\t\t\tkeepScreenOn: true\r\n\t\t\t});\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\tconsole.log('onUnload');\r\n\t\t\tuni.setKeepScreenOn({\r\n\t\t\t\tkeepScreenOn: false\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// action\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// handler\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t/**\r\n\t\t\t * 内容列表改变\r\n\t\t\t */\r\n\t\t\tonNavChangeHandler: function(index) {\r\n\t\t\t\tthis.currentIndex = index;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 内容列表改变\r\n\t\t\t */\r\n\t\t\tonSwiperChangeHandler: function(e) {\r\n\t\t\t\tvar index = e.detail.current;\r\n\t\t\t\tthis.currentIndex = index;\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.root-box {\r\n\t\tdisplay: flex;\r\n\t\tmargin: 0rpx;\r\n\r\n\t\t.head-box {\r\n\t\t\t.nav-box {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t.nav-btn {\r\n\t\t\t\t\tborder: solid 1px $uni-color-primary;\r\n\t\t\t\t\tborder-radius: 40rpx 0 0 40rpx;\r\n\t\t\t\t\tpadding: 8rpx 30rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.nav-btn:last-child {\r\n\t\t\t\t\tborder-radius: 0 40rpx 40rpx 0\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.active {\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.content-box {\r\n\t\t\tflex: 1;\r\n\t\t\tborder-top: solid 1px #eee;\r\n\t\t\tmargin: 0;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.gui-header-content {\r\n\t\twidth: 100%;\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tmargin:0;\r\n\t}\r\n\r\n\t.nav-box {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tborder-bottom: solid 1px #eeeeee;\r\n\r\n\t\t.nav-btn {\r\n\t\t\tborder: solid 1px $uni-color-primary;\r\n\t\t\tborder-radius: 40rpx 0 0 40rpx;\r\n\t\t\tpadding: 8rpx 30rpx;\r\n\t\t\tfont-size: 35rpx;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t}\r\n\r\n\t\t.nav-btn:last-child {\r\n\t\t\tborder-radius: 0 40rpx 40rpx 0\r\n\t\t}\r\n\r\n\t\t.active {\r\n\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.gui-align-items-center {\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.gui-rows {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.gui-nowrap {\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: nowrap;\r\n\t}\r\n\r\n\t.gui-flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.gui-padding {\r\n\t\tpadding-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t}\r\n\r\n\t/* 左右内间距 */\r\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b90080f4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b90080f4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689564169\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}