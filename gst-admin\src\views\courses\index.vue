<template>
  <div class="page-container">
    <div class="page-header">
      <h1>课程管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          v-if="authStore.hasRole(['admin', 'teacher'])"
        >
          <el-icon><Plus /></el-icon>
          添加课程
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item label="分类">
          <el-select v-model="filters.category" placeholder="选择分类" clearable @change="loadCourses">
            <el-option label="语法" value="grammar" />
            <el-option label="词汇" value="vocabulary" />
            <el-option label="听力" value="listening" />
            <el-option label="口语" value="speaking" />
            <el-option label="阅读" value="reading" />
            <el-option label="写作" value="writing" />
          </el-select>
        </el-form-item>
        <el-form-item label="等级">
          <el-select v-model="filters.level" placeholder="选择等级" clearable @change="loadCourses">
            <el-option label="N5" value="N5" />
            <el-option label="N4" value="N4" />
            <el-option label="N3" value="N3" />
            <el-option label="N2" value="N2" />
            <el-option label="N1" value="N1" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadCourses">
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已下架" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="搜索课程标题"
            clearable
            @input="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 课程表格 -->
    <div class="table-section">
      <el-table
        :data="courses"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column type="index" label="#" width="60" />
        
        <el-table-column prop="title" label="课程标题" min-width="200">
          <template #default="{ row }">
            <div class="course-title">
              <div class="title-text">{{ row.title }}</div>
              <div class="title-meta">
                <el-tag size="small" :type="getCategoryType(row.category)">
                  {{ getCategoryText(row.category) }}
                </el-tag>
                <el-tag size="small" :type="getLevelType(row.level)">
                  {{ row.level }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column prop="author" label="作者" width="120">
          <template #default="{ row }">
            {{ row.author?.realName || row.author?.username || '未知' }}
          </template>
        </el-table-column>

        <el-table-column prop="duration" label="时长" width="80">
          <template #default="{ row }">
            {{ row.duration || 0 }}分钟
          </template>
        </el-table-column>

        <el-table-column prop="viewCount" label="浏览量" width="100" sortable="custom">
          <template #default="{ row }">
            {{ row.viewCount || 0 }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button type="text" size="small" @click="viewCourse(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="editCourse(row)"
                v-if="canEdit(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="toggleStatus(row)"
                v-if="authStore.hasRole(['admin', 'teacher'])"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'published' ? '下架' : '发布' }}
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="deleteCourse(row)"
                v-if="canDelete(row)"
                class="danger-button"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadCourses"
        @current-change="loadCourses"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <CourseForm
      v-model="showCreateDialog"
      :course="selectedCourse"
      @success="handleFormSuccess"
    />

    <!-- 详情对话框 -->
    <CourseDetail
      v-model="showDetailDialog"
      :course="selectedCourse"
      @edit="editCourse"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import CourseForm from './components/CourseForm.vue'
import CourseDetail from './components/CourseDetail.vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const courses = ref([])
const total = ref(0)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedCourse = ref(null)

// 筛选器
const filters = reactive({
  category: '',
  level: '',
  status: '',
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 排序
const sort = reactive({
  prop: '',
  order: ''
})

// 搜索防抖
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    pagination.page = 1
    loadCourses()
  }, 500)
}

// 加载课程数据
const loadCourses = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...filters,
      ...sort
    }
    
    const response = await get('/api/courses', params, { showLoading: false })
    if (response.success) {
      courses.value = response.data.courses || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载课程数据失败:', error)
    // 使用模拟数据
    courses.value = generateMockCourses()
    total.value = courses.value.length
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockCourses = () => {
  return [
    {
      id: 1,
      title: '日语五十音图入门',
      description: '从零开始学习日语五十音图，掌握基础发音',
      category: 'grammar',
      level: 'N5',
      duration: 45,
      viewCount: 1250,
      status: 'published',
      author: { realName: '张老师', username: 'teacher1' },
      createdAt: new Date('2024-01-15')
    },
    {
      id: 2,
      title: 'N4语法精讲',
      description: '详细讲解N4级别的重要语法点',
      category: 'grammar',
      level: 'N4',
      duration: 60,
      viewCount: 890,
      status: 'published',
      author: { realName: '李老师', username: 'teacher2' },
      createdAt: new Date('2024-02-01')
    },
    {
      id: 3,
      title: '日常会话练习',
      description: '通过实际对话场景练习口语表达',
      category: 'speaking',
      level: 'N3',
      duration: 30,
      viewCount: 567,
      status: 'draft',
      author: { realName: '王老师', username: 'teacher3' },
      createdAt: new Date('2024-03-10')
    }
  ]
}

// 刷新数据
const refreshData = () => {
  loadCourses()
}

// 查看课程
const viewCourse = (course) => {
  selectedCourse.value = course
  showDetailDialog.value = true
}

// 编辑课程
const editCourse = (course) => {
  selectedCourse.value = course
  showCreateDialog.value = true
}

// 切换状态
const toggleStatus = async (course) => {
  const newStatus = course.status === 'published' ? 'archived' : 'published'
  const action = newStatus === 'published' ? '发布' : '下架'
  
  try {
    await ElMessageBox.confirm(`确定要${action}课程"${course.title}"吗？`, `确认${action}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    // 这里应该调用API
    ElMessage.success(`${action}成功`)
    loadCourses()
  } catch {
    // 用户取消
  }
}

// 删除课程
const deleteCourse = async (course) => {
  try {
    await ElMessageBox.confirm(`确定要删除课程"${course.title}"吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API
    ElMessage.success('删除成功')
    loadCourses()
  } catch {
    // 用户取消
  }
}

// 处理排序
const handleSortChange = ({ prop, order }) => {
  sort.prop = prop
  sort.order = order
  loadCourses()
}

// 表单成功回调
const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedCourse.value = null
  loadCourses()
}

// 权限检查
const canEdit = (course) => {
  if (authStore.isAdmin) return true
  if (authStore.isTeacher && course.author?.id === authStore.user.id) return true
  return false
}

const canDelete = (course) => {
  if (authStore.isAdmin) return true
  if (authStore.isTeacher && course.author?.id === authStore.user.id && course.status === 'draft') return true
  return false
}

// 获取分类类型
const getCategoryType = (category) => {
  const types = {
    grammar: 'primary',
    vocabulary: 'success',
    listening: 'warning',
    speaking: 'danger',
    reading: 'info',
    writing: ''
  }
  return types[category] || ''
}

// 获取分类文本
const getCategoryText = (category) => {
  const texts = {
    grammar: '语法',
    vocabulary: '词汇',
    listening: '听力',
    speaking: '口语',
    reading: '阅读',
    writing: '写作'
  }
  return texts[category] || '其他'
}

// 获取等级类型
const getLevelType = (level) => {
  const types = {
    N5: 'success',
    N4: 'primary',
    N3: 'warning',
    N2: 'danger',
    N1: 'info'
  }
  return types[level] || 'info'
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    draft: 'info',
    published: 'success',
    archived: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    archived: '已下架'
  }
  return texts[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadCourses()
})
</script>

<style lang="scss" scoped>
.filter-section {
  background: var(--bg-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  margin-bottom: var(--spacing-lg);
}

.table-section {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  overflow: hidden;
}

.course-title {
  .title-text {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .title-meta {
    display: flex;
    gap: var(--spacing-xs);
  }
}

.table-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
  
  .danger-button {
    color: var(--danger-color);
    
    &:hover {
      background: var(--danger-color);
      color: white;
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

@media (max-width: 768px) {
  .filter-section {
    .el-form {
      flex-direction: column;
      
      .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
      }
    }
  }
  
  .table-actions {
    flex-direction: column;
    
    .el-button {
      justify-content: flex-start;
    }
  }
}
</style>
