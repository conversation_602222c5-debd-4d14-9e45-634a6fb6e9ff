{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseImg.vue?1f5a", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseImg.vue?a0eb", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseImg.vue?b086", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseImg.vue?576a", "uni-app:///components/gaoyia-parse/components/wxParseImg.vue"], "names": ["name", "data", "newStyleStr", "preview", "inject", "mounted", "props", "node", "type", "default", "methods", "wxParseImgTap", "parent", "wxParseImgLoad", "width", "height", "imageWidth", "padding", "mode", "wxAutoImageCal", "results"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;;;AAGzD;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsnB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCc1oB;EACAA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACAC;MACA;MACAA;IACA;IACA;IACAC;MACA;MACA;MACA;QAAAC;QAAAC;MAEA;MAEA;QAAAC;MACA;QAAAC;QAAAC;MACA;;MAEA;MACA;MAEA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;UACAP;QACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACAQ;QACAA;MACA;QACA;QACAA;QACAA;MACA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "components/gaoyia-parse/components/wxParseImg.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxParseImg.vue?vue&type=template&id=660b0b50&\"\nvar renderjs\nimport script from \"./wxParseImg.vue?vue&type=script&lang=js&\"\nexport * from \"./wxParseImg.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gaoyia-parse/components/wxParseImg.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseImg.vue?vue&type=template&id=660b0b50&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseImg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseImg.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<image\r\n\t\t:mode=\"node.attr.mode\"\r\n\t\t:lazy-load=\"node.attr.lazyLoad\"\r\n\t\t:class=\"node.classStr\"\r\n\t\t:style=\"newStyleStr || node.styleStr\"\r\n\t\t:data-src=\"node.attr.src\"\r\n\t\t:src=\"node.attr.src\"\r\n\t\t@tap=\"wxParseImgTap\"\r\n\t\t@load=\"wxParseImgLoad\"\r\n\t/>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: 'wxParseImg',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tnewStyleStr: '',\r\n\t\t\tpreview: true\r\n\t\t};\r\n\t},\r\n\tinject: ['parseWidth'],\r\n\tmounted() {},\r\n\tprops: {\r\n\t\tnode: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\twxParseImgTap(e) {\r\n\t\t\tif (!this.preview) return;\r\n\t\t\tconst { src } = e.currentTarget.dataset;\r\n\t\t\tif (!src) return;\r\n\t\t\tlet parent = this.$parent;\r\n\t\t\twhile (!parent.preview || typeof parent.preview !== 'function') {\r\n\t\t\t\t// TODO 遍历获取父节点执行方法\r\n\t\t\t\tparent = parent.$parent;\r\n\t\t\t}\r\n\t\t\tparent.preview(src, e);\r\n\t\t},\r\n\t\t// 图片视觉宽高计算函数区\r\n\t\twxParseImgLoad(e) {\r\n\t\t\tconst { src } = e.currentTarget.dataset;\r\n\t\t\tif (!src) return;\r\n\t\t\tlet { width, height } = e.mp.detail;\r\n\r\n\t\t\tconst recal = this.wxAutoImageCal(width, height);\r\n\r\n\t\t\tconst { imageheight, imageWidth } = recal;\r\n\t\t\tconst { padding, mode } = this.node.attr;//删除padding\r\n\t\t\t// const { mode } = this.node.attr;\r\n\r\n\t\t\tconst { styleStr } = this.node;\r\n\t\t\tconst imageHeightStyle = mode === 'widthFix' ? '' : `height: ${imageheight}px;`;\r\n\r\n\t\t\tthis.newStyleStr = `${styleStr}; ${imageHeightStyle}; width: ${imageWidth}px; padding: 0 ${+padding}px;`;//删除padding\r\n\t\t\t// this.newStyleStr = `${styleStr}; ${imageHeightStyle}; width: ${imageWidth}px;`;\r\n\t\t},\r\n\t\t// 计算视觉优先的图片宽高\r\n\t\twxAutoImageCal(originalWidth, originalHeight) {\r\n\t\t\t// 获取图片的原始长宽\r\n\t\t\tconst windowWidth = this.parseWidth.value;\r\n\t\t\tconst results = {};\r\n\r\n\t\t\tif (originalWidth < 60 || originalHeight < 60) {\r\n\t\t\t\tconst { src } = this.node.attr;\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\twhile (!parent.preview || typeof parent.preview !== 'function') {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t}\r\n\t\t\t\tparent.removeImageUrl(src);\r\n\t\t\t\tthis.preview = false;\r\n\t\t\t}\r\n\r\n\t\t\t// 判断按照那种方式进行缩放\r\n\t\t\tif (originalWidth > windowWidth) {\r\n\t\t\t\t// 在图片width大于手机屏幕width时候\r\n\t\t\t\tresults.imageWidth = windowWidth;\r\n\t\t\t\tresults.imageheight = windowWidth * (originalHeight / originalWidth);\r\n\t\t\t} else {\r\n\t\t\t\t// 否则展示原来的数据\r\n\t\t\t\tresults.imageWidth = originalWidth;\r\n\t\t\t\tresults.imageheight = originalHeight;\r\n\t\t\t}\r\n\t\t\treturn results;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n"], "sourceRoot": ""}