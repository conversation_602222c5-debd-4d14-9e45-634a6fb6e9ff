<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <div class="error-number">404</div>
      </div>
      <div class="error-info">
        <h1>页面不存在</h1>
        <p>抱歉，您访问的页面不存在或已被删除</p>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: var(--spacing-xl);
}

.error-image {
  margin-bottom: var(--spacing-xl);
  
  .error-number {
    font-size: 120px;
    font-weight: 700;
    line-height: 1;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

.error-info {
  h1 {
    font-size: var(--font-size-extra-large);
    font-weight: 600;
    margin: 0 0 var(--spacing-md) 0;
    color: white;
  }
  
  p {
    font-size: var(--font-size-medium);
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 var(--spacing-xl) 0;
    line-height: 1.6;
  }
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  
  .el-button {
    padding: 12px 24px;
    font-size: var(--font-size-base);
    border-radius: 8px;
    
    &.el-button--primary {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }
    
    &:not(.el-button--primary) {
      background: transparent;
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}

@media (max-width: 768px) {
  .error-content {
    padding: var(--spacing-lg);
  }
  
  .error-image .error-number {
    font-size: 80px;
  }
  
  .error-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
