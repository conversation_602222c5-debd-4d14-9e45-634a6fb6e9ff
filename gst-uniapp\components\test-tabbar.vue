<template>
	<view class="test-tabbar">
		<text style="color: white; font-size: 32rpx;">测试底部菜单</text>
		<view class="tabs">
			<view class="tab" @click="goHome">首页</view>
			<view class="tab" @click="goSearch">找课</view>
			<view class="tab" @click="goStudy">学习</view>
			<view class="tab" @click="goUser">我的</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TestTabbar',
	methods: {
		goHome() {
			console.log('点击首页');
			uni.reLaunch({ url: '/pages/index/index' });
		},
		goSearch() {
			console.log('点击找课');
			uni.reLaunch({ url: '/pages/category/list-page' });
		},
		goStudy() {
			console.log('点击学习');
			uni.reLaunch({ url: '/pages/study/study' });
		},
		goUser() {
			console.log('点击我的');
			uni.reLaunch({ url: '/pages/user/user' });
		}
	}
};
</script>

<style scoped>
.test-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background: red;
	z-index: 99999;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.tabs {
	display: flex;
	width: 100%;
}

.tab {
	flex: 1;
	text-align: center;
	color: white;
	font-size: 28rpx;
	padding: 20rpx 0;
	background: rgba(0, 0, 0, 0.3);
	margin: 0 2rpx;
}

.tab:active {
	background: rgba(0, 0, 0, 0.5);
}
</style>
