<template>
	<view class="test-tabbar">
		<view class="tabs">
			<view class="tab" :class="{ 'active': currentTab === 'pages/index/index' }" @click="goHome">
				<image class="tab-icon" :src="currentTab === 'pages/index/index' ? '/static/tab-home-current.png' : '/static/tab-home.png'" mode="aspectFit" />
				<text class="tab-text" :class="{ 'active': currentTab === 'pages/index/index' }">首页</text>
			</view>
			<view class="tab" :class="{ 'active': currentTab === 'pages/category/list-page' }" @click="goSearch">
				<image class="tab-icon" :src="currentTab === 'pages/category/list-page' ? '/static/tab-search-current.png' : '/static/tab-search.png'" mode="aspectFit" />
				<text class="tab-text" :class="{ 'active': currentTab === 'pages/category/list-page' }">找课</text>
			</view>
			<view class="tab" :class="{ 'active': currentTab === 'pages/study/study' }" @click="goStudy">
				<image class="tab-icon" :src="currentTab === 'pages/study/study' ? '/static/tab-cate-current.png' : '/static/tab-cate.png'" mode="aspectFit" />
				<text class="tab-text" :class="{ 'active': currentTab === 'pages/study/study' }">学习</text>
			</view>
			<!-- 小组菜单 - 仅会员可见 -->
			<view v-if="hasGroupPermission" class="tab" :class="{ 'active': currentTab === 'pages/groups/index' }" @click="goGroups">
				<image class="tab-icon" :src="currentTab === 'pages/groups/index' ? '/static/tab-cate-current.png' : '/static/tab-cate.png'" mode="aspectFit" />
				<text class="tab-text" :class="{ 'active': currentTab === 'pages/groups/index' }">小组</text>
				<view class="member-badge">
					<text class="member-text">VIP</text>
				</view>
			</view>

			<view class="tab" :class="{ 'active': currentTab === 'pages/user/user' }" @click="goUser">
				<image class="tab-icon" :src="currentTab === 'pages/user/user' ? '/static/tab-my-current.png' : '/static/tab-my.png'" mode="aspectFit" />
				<text class="tab-text" :class="{ 'active': currentTab === 'pages/user/user' }">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TestTabbar',
	data() {
		return {
			currentTab: ''
		};
	},
	computed: {
		// 检查用户是否有小组权限
		hasGroupPermission() {
			try {
				const userInfo = this.$store.state.user.userInfo;
				const userMember = this.$store.state.user.member;
				const hasLogin = this.$store.getters.hasLogin;

				// 检查用户是否登录
				const userLoggedIn = hasLogin || (userInfo && userInfo.id);
				if (!userLoggedIn) return false;

				// 检查会员权限
				return this.checkMemberPermission(userMember);
			} catch (error) {
				console.log('权限检查出错:', error);
				return false;
			}
		}
	},
	mounted() {
		console.log('TestTabbar mounted');

		// 隐藏原生tabBar
		uni.hideTabBar({
			animation: false
		});

		// 获取当前页面路径
		this.getCurrentTab();
	},
	methods: {
		// 获取当前页面路径
		getCurrentTab() {
			const pages = getCurrentPages();
			if (pages.length > 0) {
				const currentPage = pages[pages.length - 1];
				this.currentTab = currentPage.route;
				console.log('当前页面:', this.currentTab);
			}
		},

		goHome() {
			console.log('点击首页');
			uni.reLaunch({
				url: '/pages/index/index',
				success: () => {
					this.currentTab = 'pages/index/index';
				}
			});
		},
		goSearch() {
			console.log('点击找课');
			uni.reLaunch({
				url: '/pages/category/list-page',
				success: () => {
					this.currentTab = 'pages/category/list-page';
				}
			});
		},
		goStudy() {
			console.log('点击学习');
			uni.reLaunch({
				url: '/pages/study/study',
				success: () => {
					this.currentTab = 'pages/study/study';
				}
			});
		},
		goGroups() {
			console.log('点击小组');
			uni.reLaunch({
				url: '/pages/groups/index',
				success: () => {
					this.currentTab = 'pages/groups/index';
				}
			});
		},

		goUser() {
			console.log('点击我的');
			uni.reLaunch({
				url: '/pages/user/user',
				success: () => {
					this.currentTab = 'pages/user/user';
				}
			});
		},

		// 检查会员权限
		checkMemberPermission(member) {
			if (!member) return false;

			// 检查会员等级
			if (member.level && ['premium', 'vip', 'svip'].includes(member.level.toLowerCase())) {
				return true;
			}

			// 检查会员到期时间
			if (member.expireDate) {
				const expireDate = new Date(member.expireDate);
				const now = new Date();
				return expireDate > now;
			}

			// 检查会员状态
			if (member.status === 'active' || member.status === 1) {
				return true;
			}

			return false;
		}
	}
};
</script>

<style scoped>
.test-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background: #ffffff;
	border-top: 1rpx solid #e5e5e5;
	z-index: 99999;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tabs {
	display: flex;
	width: 100%;
	height: 100%;
}

.tab {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 8rpx 0;
	position: relative;
	transition: background-color 0.3s ease;
}

.tab:active {
	background: rgba(0, 0, 0, 0.05);
}

.tab-icon {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 4rpx;
}

.tab-text {
	font-size: 20rpx;
	color: #C0C4CC;
	transition: color 0.3s ease;
}

.tab-text.active {
	color: #2094CE;
	font-weight: 600;
}

.tab.active .tab-icon {
	transform: scale(1.1);
}

.member-badge {
	position: absolute;
	top: 2rpx;
	right: 20rpx;
	background: linear-gradient(135deg, #ff6b6b, #ffa500);
	border-radius: 20rpx;
	padding: 2rpx 8rpx;
	transform: scale(0.8);
}

.member-text {
	font-size: 16rpx;
	color: #fff;
	font-weight: 600;
}
</style>
