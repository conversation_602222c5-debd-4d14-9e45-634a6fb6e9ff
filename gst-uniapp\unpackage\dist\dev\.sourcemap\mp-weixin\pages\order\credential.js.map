{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/order/credential.vue?f704", "webpack:///D:/gst/gst-uniapp/pages/order/credential.vue?7ebd", "webpack:///D:/gst/gst-uniapp/pages/order/credential.vue?699d", "webpack:///D:/gst/gst-uniapp/pages/order/credential.vue?920a", "uni-app:///pages/order/credential.vue", "webpack:///D:/gst/gst-uniapp/pages/order/credential.vue?b021", "webpack:///D:/gst/gst-uniapp/pages/order/credential.vue?c5ca"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "sn_order", "order", "retry_num", "onLoad", "methods", "getOrderDetail", "uni", "console", "title", "success", "url", "setTimeout", "apiGetOrderDetail", "params", "order_num", "savePoster", "scope", "fail", "icon", "duration", "saveImageToPhotosAlbum", "filePath"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuB3nB;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACAC;MACA;QACAC;QACAD;QACA;QACA;UACA;YACAA;cACAE;cACAC;gBACAH;kBACAI;gBACA;cACA;YACA;UACA;YACAC;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAT;QAAA;QACAG;UACA;YAAA;YACA;UACA;YACAH;cAAA;cACAU;cACAP;gBACA;cACA;cACAQ;gBACAX;kBACAE;kBACAU;kBACAC;gBACA;gBACAR;kBACAL;oBAAA;oBACAG;sBACA;oBAAA;kBAEA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;MACA;MACA;MACA;MACAd;QACAI;QACAD;UACAF;UACA;UACAD;YACAe;YAAA;YACAZ;cACAH;gBACAE;gBACAU;cACA;YACA;YACAD;cACAX;gBACAE;gBACAU;cACA;YACA;UACA;QACA;QACAD;UACAV;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/credential.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/credential.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./credential.vue?vue&type=template&id=191a9631&scoped=true&\"\nvar renderjs\nimport script from \"./credential.vue?vue&type=script&lang=js&\"\nexport * from \"./credential.vue?vue&type=script&lang=js&\"\nimport style0 from \"./credential.vue?vue&type=style&index=0&id=191a9631&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"191a9631\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/credential.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./credential.vue?vue&type=template&id=191a9631&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./credential.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./credential.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" :customHeader=\"false\" class=\"root-box\">\r\n\t\t<!-- 自定义头部导航 -->\r\n\t\t<!-- <view slot=\"gHeader\">\r\n\t\t\t<lp-head>报名结果</lp-head>\r\n\t\t</view> -->\r\n\t\t<!-- 页面主体 -->\r\n\t\t<view slot=\"gBody\" class=\"root-box lp-flex-column\">\r\n\t\t\t<!-- <view class=\"content-box\">\r\n\t\t\t\t<view>感谢您对《人民中国》系列课程的信任与支持！</view>\r\n\t\t\t\t<view>开课在即，请您联系助教老师进入班级学习群。</view>\r\n\t\t\t\t<view>微信号“日语世界”：lp18902494162。</view>\r\n\t\t\t\t<view>咨询电话：18902494162</view>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- <image :src=\"order.credential\" style=\"text-align: center;\" mode=\"aspectFill\"></image> -->\r\n\t\t\t<view style=\"text-align: center;\">\r\n\t\t\t\t<image :src=\"order.credential\" mode=\"widthFix\" @longpress=\"savePoster\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsn_order: null,\r\n\t\t\t\torder: null,\r\n\t\t\t\tretry_num: 3,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.sn_order = options.sn_order;\r\n\t\t\tthis.getOrderDetail();\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// action\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tgetOrderDetail: function() {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tuni.showLoading();\r\n\t\t\t\tthis.apiGetOrderDetail(this.sn_order).then(data => {\r\n\t\t\t\t\tconsole.log(data);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.order = data;\r\n\t\t\t\t\tif (!data.status) {\r\n\t\t\t\t\t\tif (--this.retry_num <= 0) {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '查找报名状态失败，报名编号：' + this.sn_order + '请联系工作人员',\r\n\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pagesA/about-us/about-us'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tsetTimeout(this.getOrderDetail, 1000);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t/**\r\n\t\t\t * 获取订单详情\r\n\t\t\t * @param {Object} order_num 订单号\r\n\t\t\t */\r\n\t\t\tapiGetOrderDetail: function(order_num) {\r\n\t\t\t\treturn this.$http.get('/v1/queryOrder', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\torder_num\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 保存到相册\r\n\t\t\tsavePoster() {\r\n\t\t\t\tuni.getSetting({ //获取用户的当前设置\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.authSetting['scope.writePhotosAlbum']) { //验证用户是否授权可以访问相册\r\n\t\t\t\t\t\t\tthis.saveImageToPhotosAlbum();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.authorize({ //如果没有授权，向用户发起请求\r\n\t\t\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tthis.saveImageToPhotosAlbum();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"请打开保存相册权限，再点击保存相册分享\",\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.openSetting({ //调起客户端小程序设置界面,让用户开启访问相册\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: (res2) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// console.log(res2.authSetting)\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}, 3000);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsaveImageToPhotosAlbum() {\r\n\t\t\t\tvar _this = this;\r\n\t\t\t\t// uni.getImageInfo({\r\n\t\t\t\t// \tsrc: _this.order.credential,\r\n\t\t\t\t// \tsuccess: res => {\r\n\t\t\t\t\t\t//先下载到本地获取临时路径\r\n\t\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\t\turl: _this.order.credential,\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tconsole.log('downloadFile success, res is', res.tempFilePath)\r\n\t\t\t\t\t\t\t\t//将临时路径保存到相册，即可在相册中查看图片\r\n\t\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\t\tfilePath: res.tempFilePath, //不支持网络地址\r\n\t\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '保存图片到相册成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail:function(){\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '保存图片到相册失败',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.log('downloadFile fail, err is:', err)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// fail: err => {\r\n\t\t\t\t\t// \tconsole.log(err, 'err')\r\n\t\t\t\t\t// }\r\n\t\t\t\t// })\r\n\t\t\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.root-box {\r\n\t\tcolor: #666;\r\n\r\n\t\t.head {\r\n\t\t\tpadding: 100rpx 0;\r\n\r\n\t\t\t.gui-icons {\r\n\t\t\t\tfont-size: 200rpx;\r\n\t\t\t\tcolor: #28b28b;\r\n\t\t\t}\r\n\r\n\t\t\t.success-text {\r\n\t\t\t\tmargin: 20rpx 0;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.content-box {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tpadding: 60rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./credential.vue?vue&type=style&index=0&id=191a9631&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./credential.vue?vue&type=style&index=0&id=191a9631&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699114824\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}