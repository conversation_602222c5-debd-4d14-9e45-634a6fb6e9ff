import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus, { ElMessage } from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'normalize.css'

import App from './App.vue'
import router from './router'
import './styles/index.scss'

// 导入性能监控和缓存管理
import performanceMonitor from './utils/performance'
import cacheManager from './utils/cache'

const app = createApp(App)
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('全局错误:', error, info)

  // 记录错误到性能监控
  performanceMonitor.recordMetric('app_error', {
    message: error.message,
    stack: error.stack,
    info: info,
    timestamp: Date.now()
  })

  // 显示用户友好的错误消息
  if (error.message && !error.message.includes('ResizeObserver')) {
    ElMessage.error('应用出现错误，请刷新页面重试')
  }
}

// 全局未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未捕获的Promise错误:', event.reason)

  performanceMonitor.recordMetric('promise_error', {
    reason: event.reason?.message || event.reason,
    timestamp: Date.now()
  })

  // 阻止默认的错误处理
  event.preventDefault()
})

// 应用启动性能监控
performanceMonitor.startTiming('app_mount')

app.mount('#app')

performanceMonitor.endTiming('app_mount')

// 获取API基础URL
function getApiBaseURL() {
  // 如果是localhost访问，使用localhost
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:8005';
  }
  // 如果是IP访问，使用相同的IP
  return `http://${window.location.hostname}:8005`;
}

const apiBaseURL = getApiBaseURL();

console.log('🎉 GST日语培训班管理后台启动成功')
console.log(`📡 前端地址: ${window.location.origin}`)
console.log(`🔗 后端API: ${apiBaseURL}`)
console.log('📱 小程序端口: 当前UniApp项目端口')
console.log('🌐 局域网访问: 自动检测IP地址')

// 检查后端连接
fetch(`${apiBaseURL}/health`)
  .then(response => response.json())
  .then(data => {
    console.log('✅ 后端连接正常:', data)
    console.log(`🔗 API服务地址: ${apiBaseURL}`)
  })
  .catch(error => {
    console.error('❌ 后端连接失败:', error)
    console.log(`💡 请检查后端服务是否启动: npm run dev`)
    console.log(`🔗 尝试访问: ${apiBaseURL}/health`)
  })
