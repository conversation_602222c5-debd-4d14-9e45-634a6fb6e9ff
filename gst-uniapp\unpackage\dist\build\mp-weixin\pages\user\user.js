(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/user/user"],{"5ea7":function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("5788");o(e("3240"));var i=o(e("d0a1"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"8ff2":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e("8f59");var o=0,i=0,s={components:{listCell:function(){e.e("components/mix-list-cell").then(function(){return resolve(e("3154"))}.bind(null,e)).catch(e.oe)}},data:function(){return{ver:this.$store.state.ver,coverTransform:"translateY(0px)",coverTransition:"0s",moving:!1,user:null,is_login:!1}},onLoad:function(){},onShow:function(){this.user=this.$store.state.user,this.$store.state.user.token?(this.is_login=!0,this.$store.dispatch("refreshUserMember")):t.showModal({title:"请先登录",content:"登录后才能进行操作",success:function(n){n.confirm?t.navigateTo({url:"/pages/login/login?type=wx"}):n.cancel}})},methods:{navTo:function(n){t.navigateTo({url:n})},coverTouchstart:function(t){this.coverTransition="transform .1s linear",o=t.touches[0].clientY},coverTouchmove:function(t){i=t.touches[0].clientY;var n=i-o;n<0?this.moving=!1:(this.moving=!0,n>=80&&n<100&&(n=80),n>0&&n<=80&&(this.coverTransform="translateY(".concat(n,"px)")))},coverTouchend:function(){!1!==this.moving&&(this.moving=!1,this.coverTransition="transform 0.3s cubic-bezier(.21,1.93,.53,.64)",this.coverTransform="translateY(0px)")},login:function(){this.$store.state.user.token?t.navigateTo({url:"/pages/userinfo/userinfo"}):t.showModal({title:"请先登录",content:"登录后才能进行操作",success:function(n){n.confirm?(self.reload=!0,t.navigateTo({url:"/pages/login/login?type=wx"})):n.cancel&&t.navigateBack()}})},out:function(){var n=this;t.showModal({title:"退出登录",content:"请确认是否退出登录当前账号",success:function(e){e.confirm?(n.$store.commit("clearUserData"),t.showToast({title:"退出成功",icon:"success",duration:1500}),setTimeout((function(){n.reload()}),1500)):e.cancel}})},reload:function(){var t=getCurrentPages(),n=t[t.length-1];console.log(n),n.onLoad(n.options),n.onShow(),n.onReady()}}};n.default=s}).call(this,e("df3c")["default"])},9894:function(t,n,e){"use strict";e.r(n);var o=e("8ff2"),i=e.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(s);n["default"]=i.a},b63b:function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},i=[]},d0a1:function(t,n,e){"use strict";e.r(n);var o=e("b63b"),i=e("9894");for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(s);e("da1c");var r=e("828b"),c=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=c.exports},da1c:function(t,n,e){"use strict";var o=e("fe91"),i=e.n(o);i.a},fe91:function(t,n,e){}},[["5ea7","common/runtime","common/vendor"]]]);