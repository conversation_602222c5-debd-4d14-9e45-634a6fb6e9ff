{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/uniapp-zaudio/zaudio.vue?1ec3", "webpack:///D:/gst/gst-uniapp/components/uniapp-zaudio/zaudio.vue?0204", "webpack:///D:/gst/gst-uniapp/components/uniapp-zaudio/zaudio.vue?3330", "webpack:///D:/gst/gst-uniapp/components/uniapp-zaudio/zaudio.vue?c76a", "uni-app:///components/uniapp-zaudio/zaudio.vue", "webpack:///D:/gst/gst-uniapp/components/uniapp-zaudio/zaudio.vue?2ba3", "webpack:///D:/gst/gst-uniapp/components/uniapp-zaudio/zaudio.vue?beb2"], "names": ["props", "theme", "type", "default", "themeColor", "data", "playinfo", "audiolist", "paused", "renderIsPlay", "audio", "loading", "action", "computed", "renderData", "mounted", "renderinfo", "methods", "operate", "change", "stepPlay", "changeplay", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,+BAAsB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,+BAAsB;AACtC;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,gCAAuB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,+BAAsB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,+BAAsB;AACtC;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,gCAAuB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,mBAAO,CAAC,4BAAmB;AACjE,8CAA8C,mBAAO,CAAC,0BAAiB;AACvE;AACA;AACA,QAAQ,mBAAO,CAAC,gCAAuB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,gCAAuB;AACvC;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,iCAAwB;AACxC;AACA,8CAA8C,mBAAO,CAAC,0BAAiB;AACvE;AACA,sCAAsC,mBAAO,CAAC,4BAAmB;AACjE;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxIA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2GvnB;EACAA;IACAC;MACAC;MAAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;YACA;UACA;UAEA;QACA;UACA;YACA;UACA;UAEA;QACA;MACA;IACA;EACA;EAEAC;IAAA;IACA;MACA;MACA;QAAA;UAAAP;UAAAF;UAAAG;UAAAO;UAAAL;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAM;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5LA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uniapp-zaudio/zaudio.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./zaudio.vue?vue&type=template&id=abdab3e4&scoped=true&\"\nvar renderjs\nimport script from \"./zaudio.vue?vue&type=script&lang=js&\"\nexport * from \"./zaudio.vue?vue&type=script&lang=js&\"\nimport style0 from \"./zaudio.vue?vue&type=style&index=0&id=abdab3e4&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"abdab3e4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uniapp-zaudio/zaudio.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zaudio.vue?vue&type=template&id=abdab3e4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.audiolist.length\n  var m0 =\n    g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"current_value\") : null\n  var m1 =\n    g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"duration_value\") : null\n  var m2 = g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"paused\") : null\n  var m3 =\n    g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"coverImgUrl\") : null\n  var m4 =\n    g0 > 0 && _vm.theme == \"theme3\" && _vm.loading\n      ? require(\"./static/loading.png\")\n      : null\n  var m5 =\n    g0 > 0 && _vm.theme == \"theme3\" && !_vm.loading\n      ? _vm.renderData(\"paused\")\n      : null\n  var m6 =\n    g0 > 0 && _vm.theme == \"theme3\" && !_vm.loading && m5\n      ? require(\"./static/playbtn.png\")\n      : null\n  var m7 =\n    g0 > 0 && _vm.theme == \"theme3\" && !_vm.loading && !m5\n      ? require(\"./static/pausebtn.png\")\n      : null\n  var m8 = g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"title\") : null\n  var m9 = g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"singer\") : null\n  var m10 = g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"current\") : null\n  var m11 = g0 > 0 && _vm.theme == \"theme3\" ? _vm.renderData(\"duration\") : null\n  var m12 =\n    g0 > 0 && _vm.theme == \"theme2\" ? _vm.renderData(\"coverImgUrl\") : null\n  var m13 =\n    g0 > 0 && _vm.theme == \"theme2\" && _vm.loading\n      ? require(\"./static/loading.png\")\n      : null\n  var m14 =\n    g0 > 0 && _vm.theme == \"theme2\" && !_vm.loading\n      ? _vm.renderData(\"paused\")\n      : null\n  var m15 =\n    g0 > 0 && _vm.theme == \"theme2\" && !_vm.loading && m14\n      ? require(\"./static/playbtn.png\")\n      : null\n  var m16 =\n    g0 > 0 && _vm.theme == \"theme2\" && !_vm.loading && !m14\n      ? require(\"./static/pausebtn.png\")\n      : null\n  var m17 = g0 > 0 && _vm.theme == \"theme2\" ? _vm.renderData(\"title\") : null\n  var m18 = g0 > 0 && _vm.theme == \"theme2\" ? _vm.renderData(\"current\") : null\n  var m19 = g0 > 0 && _vm.theme == \"theme2\" ? _vm.renderData(\"duration\") : null\n  var m20 = g0 > 0 && _vm.theme == \"theme2\" ? _vm.renderData(\"singer\") : null\n  var m21 = g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"paused\") : null\n  var m22 =\n    g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"coverImgUrl\") : null\n  var m23 = g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"title\") : null\n  var m24 = g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"singer\") : null\n  var m25 = g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"current\") : null\n  var m26 =\n    g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"current_value\") : null\n  var m27 =\n    g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"duration_value\") : null\n  var m28 = g0 > 0 && _vm.theme == \"theme1\" ? _vm.renderData(\"duration\") : null\n  var m29 =\n    g0 > 0 && _vm.theme == \"theme1\" ? require(\"./static/prev.png\") : null\n  var m30 = g0 > 0 && _vm.theme == \"theme1\" ? require(\"./static/go.png\") : null\n  var m31 =\n    g0 > 0 && _vm.theme == \"theme1\" && _vm.loading\n      ? require(\"./static/loading2.png\")\n      : null\n  var m32 =\n    g0 > 0 && _vm.theme == \"theme1\" && !_vm.loading\n      ? _vm.renderData(\"paused\")\n      : null\n  var m33 =\n    g0 > 0 && _vm.theme == \"theme1\" && !_vm.loading && m32\n      ? require(\"./static/playbtn2.png\")\n      : null\n  var m34 =\n    g0 > 0 && _vm.theme == \"theme1\" && !_vm.loading && !m32\n      ? require(\"./static/pausebtn2.png\")\n      : null\n  var m35 = g0 > 0 && _vm.theme == \"theme1\" ? require(\"./static/go.png\") : null\n  var m36 =\n    g0 > 0 && _vm.theme == \"theme1\" ? require(\"./static/next.png\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        m31: m31,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        m35: m35,\n        m36: m36,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zaudio.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zaudio.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"imt-audio\" :class=\"[`${theme}`]\" v-if=\"audiolist.length > 0\">\r\n\t\t<template v-if=\"theme == 'theme3'\">\r\n\t\t\t<slider\r\n\t\t\t\tclass=\"audio-slider\"\r\n\t\t\t\t:activeColor=\"themeColor\"\r\n\t\t\t\tblock-size=\"0\"\r\n\t\t\t\t:value=\"renderData('current_value')\"\r\n\t\t\t\t:max=\"renderData('duration_value')\"\r\n\t\t\t\t@change=\"change\"\r\n\t\t\t\t:disabled=\"!renderIsPlay\"\r\n\t\t\t></slider>\r\n\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"audio-control-wrapper\">\r\n\t\t\t\t\t<image :src=\"renderData('coverImgUrl')\" mode=\"aspectFill\" class=\"cover\" :class=\"{ on: !renderData('paused') }\"></image>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<image :src=\"require('./static/loading.png')\" v-if=\"loading\" class=\"play loading\"></image>\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<image :src=\"require('./static/playbtn.png')\" alt=\"\" @click=\"operate\" class=\"play\" v-if=\"renderData('paused')\"></image>\r\n\t\t\t\t\t\t<image :src=\"require('./static/pausebtn.png')\" alt=\"\" @click=\"operate\" class=\"play\" v-else></image>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"audio-wrapper\">\r\n\t\t\t\t<view class=\"titlebox\">\r\n\t\t\t\t\t<view class=\"title\">{{ renderData('title') }}</view>\r\n\t\t\t\t\t<view class=\"singer\">{{ renderData('singer') }}</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"slidebox\">\r\n\t\t\t\t\t<view>{{ renderData('current') }}/ {{ renderData('duration') }}</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text @click=\"changeplay(-1)\">上一首</text>\r\n\t\t\t\t\t\t<text @click=\"changeplay(1)\">下一首</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\r\n\t\t<template v-if=\"theme == 'theme2'\">\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"audio-control-wrapper\" :style=\"{backgroundImage: `url(${renderData('coverImgUrl')})`}\">\r\n\t\t\t\t\t<image :src=\"require('./static/loading.png')\" v-if=\"loading\" class=\"play loading\"></image>\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<image :src=\"require('./static/playbtn.png')\" alt=\"\" @click=\"operate\" class=\"play\" v-if=\"renderData('paused')\"></image>\r\n\t\t\t\t\t\t<image :src=\"require('./static/pausebtn.png')\" alt=\"\" @click=\"operate\" class=\"play\" v-else></image>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t<text>{{ renderData('title') }}</text>\r\n\t\t\t\t\t\t<view class=\"audio-number\">{{ renderData('current') }}/{{ renderData('duration') }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"singer\">{{ renderData('singer') }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\r\n\t\t<template v-if=\"theme == 'theme1'\">\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"audio-control-wrapper\"><image :src=\"renderData('coverImgUrl')\" mode=\"aspectFill\" class=\"cover\" :class=\"{ on: !renderData('paused') }\"></image></view>\r\n\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"title\">{{ renderData('title') }}</view>\r\n\t\t\t\t\t<view class=\"singer\">{{ renderData('singer') }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"audio-wrapper\">\r\n\t\t\t\t<view class=\"audio-number\">{{ renderData('current') }}</view>\r\n\t\t\t\t<slider\r\n\t\t\t\t\tclass=\"audio-slider\"\r\n\t\t\t\t\t:activeColor=\"themeColor\"\r\n\t\t\t\t\tblock-size=\"16\"\r\n\t\t\t\t\t:value=\"renderData('current_value')\"\r\n\t\t\t\t\t:max=\"renderData('duration_value')\"\r\n\t\t\t\t\t@change=\"change\"\r\n\t\t\t\t\t:disabled=\"!renderIsPlay\"\r\n\t\t\t\t></slider>\r\n\t\t\t\t<view class=\"audio-number\">{{ renderData('duration') }}</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"audio-button-box\">\r\n\t\t\t\t<!-- 块退15s -->\r\n\t\t\t\t<image :src=\"require('./static/prev.png')\" class=\"prevbtn\" @click=\"stepPlay(-15)\" mode=\"widthFix\"></image>\r\n\t\t\t\t<!-- 上一首 -->\r\n\t\t\t\t<image :src=\"require('./static/go.png')\" class=\"prevplay\" @click=\"changeplay(-1)\" mode=\"widthFix\"></image>\r\n\t\t\t\t<div class=\"playbox\">\r\n\t\t\t\t\t<image :src=\"require('./static/loading2.png')\" v-if=\"loading\" class=\"play loading\"></image>\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<!-- 播放 -->\r\n\t\t\t\t\t\t<image :src=\"require('./static/playbtn2.png')\" alt=\"\" @click=\"operate\" class=\"play\" v-if=\"renderData('paused')\"></image>\r\n\t\t\t\t\t\t<!-- 暂停 -->\r\n\t\t\t\t\t\t<image :src=\"require('./static/pausebtn2.png')\" alt=\"\" @click=\"operate\" class=\"pause\" v-else></image>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- 下一首 -->\r\n\t\t\t\t<image :src=\"require('./static/go.png')\" class=\"nextplay\" @click=\"changeplay(1)\" mode=\"widthFix\"></image>\r\n\t\t\t\t<!-- 快进15s -->\r\n\t\t\t\t<image :src=\"require('./static/next.png')\" class=\"nextbtn\" @click=\"stepPlay(15)\" mode=\"widthFix\"></image>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\ttheme: {\r\n\t\t\ttype: String, // 主题 'theme1' or 'theme2'\r\n\t\t\tdefault: 'theme1'\r\n\t\t},\r\n\t\tthemeColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#42b983'\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tplayinfo: this.$zaudio.playinfo,\r\n\t\t\taudiolist: this.$zaudio.audiolist,\r\n\t\t\tpaused: this.$zaudio.paused,\r\n\t\t\trenderIsPlay: this.$zaudio.renderIsPlay,\r\n\t\t\taudio: this.$zaudio.renderinfo,\r\n\t\t\tloading: this.$zaudio.loading,\r\n\t\t\taction: Symbol('zaudio')\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\trenderData() {\r\n\t\t\treturn name => {\r\n\t\t\t\tif (!this.renderIsPlay) {\r\n\t\t\t\t\tif (name == 'paused') {\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn this.audio[name];\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (name == 'paused') {\r\n\t\t\t\t\t\treturn this.paused;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn this.playinfo[name];\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t}\r\n\t},\r\n\r\n\tmounted() {\r\n\t\tthis.$nextTick(() => {\t\t\t\r\n\t\t\tlet action = this.action;\r\n\t\t\tthis.$zaudio.syncStateOn(action, ({ audiolist, paused, playinfo, renderIsPlay, renderinfo, loading }) => {\r\n\t\t\t\tthis.audiolist = audiolist;\r\n\t\t\t\tthis.paused = paused;\r\n\t\t\t\tthis.playinfo = playinfo;\r\n\t\t\t\tthis.renderIsPlay = renderIsPlay;\r\n\t\t\t\tthis.audio = renderinfo;\r\n\t\t\t\tthis.loading = loading;\r\n\t\t\t}); \r\n\t\t\t \r\n \t\t});\r\n\t},\r\n\tmethods: {\r\n\t\t//播放or暂停\r\n\t\toperate() {\r\n\t\t\tthis.$zaudio.operate();\r\n\t\t},\r\n\t\t//进度拖到\r\n\t\tchange(event) {\r\n\t\t\tif (this.renderIsPlay) {\r\n\t\t\t\tthis.$zaudio.seek(event.detail.value);\r\n\t\t\t}\r\n\t\t},\r\n\t\t//快进\r\n\t\tstepPlay(value) {\r\n\t\t\tthis.$zaudio.stepPlay(value);\r\n\t\t},\r\n\t\t//切歌\r\n\t\tchangeplay(count) {\r\n\t\t\tthis.$zaudio.changeplay(count);\r\n\t\t}\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\t//组件卸载时卸载业务逻辑\r\n\t\tlet action = this.action;\r\n\t\tthis.$zaudio.syncStateOff(action)\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import './index.scss';\r\n//  #ifdef MP-WEIXIN\r\n.theme3 .audio-slider {\r\n\tmargin-top: -8px !important;\r\n}\r\n// #endif\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zaudio.vue?vue&type=style&index=0&id=abdab3e4&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zaudio.vue?vue&type=style&index=0&id=abdab3e4&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699116494\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}