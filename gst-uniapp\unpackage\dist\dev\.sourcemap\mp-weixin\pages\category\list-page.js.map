{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?435b", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?bfbe", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?d870", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?d663", "uni-app:///pages/category/list-page.vue", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?2216", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?1436"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty1", "data", "keyword", "activeIndex", "classifyList", "flid", "son_fls", "goodstype", "open", "cateId", "style", "title", "isLoading", "searchTimer", "defaultCover", "value", "onShareAppMessage", "uni", "url", "path", "onLoad", "console", "methods", "loadHotData", "type", "loading", "params", "page", "icon", "addTestData", "id", "name", "count", "class", "picture", "teacher", "student_count", "price", "is_free", "onSearchInput", "clearTimeout", "performSearch", "searchCourses", "limit", "handleImageError", "e", "postClassifylist", "postSonflData", "setTimeout", "navigate", "postSearchCourse", "navToDetailPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsG1nB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;MACA;QACAP;QACAQ;MACA;IACA;MACA,4BAEA;MACA;QACAR;QACAQ;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACAC;IACA;IACAJ;MACAN;IACA;IACA;EACA;EACAW;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBACA;gBACAR;gBACA;kBACAS;oBACAC;kBACA;gBACA;kBACAN;kBACA;kBACA;kBACA;kBACAJ;gBACA;kBACAI;kBACA;kBACAJ;;kBAEA;kBACA;kBAEAA;oBACAN;oBACAiB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACAR;MACA,qBACA;QAAAS;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MAEA,gBACA;QACAD;QACAE,QACA;UACAH;UACAnB;UACAuB;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAR;UACAnB;UACAuB;UACAC;UACAC;UACAE;QACA;MAEA,EACA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;MACA;MAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;QACA;MACA;;MAEA;MACApB;MACA;IACA;IAEA;IACAqB;MAAA;MACA;MACA;QACAhB;UACAxB;UACAyB;UACAgB;QACA;MACA;QACAtB;QACA;UACA;UACA;YACAU;YACAE;UACA;QACA;UACA;YACAF;YACAE;UACA;QACA;QACA;MACA;QACAZ;QACA;UACAU;UACAE;QACA;QACA;MACA;IACA;IAEA;IACAW;MACAvB;MACA;MACAwB;IACA;IACA;IACAC;MAAA;MACA;QAAAvC;MAAA;QACA;QACA;MACA;IACA;IACA;IACAwC;MAAA;MACA;QACAC;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAtB;UACAI;QACA;MACA;QACAT;QACA;MACA;IACA;IACA4B;MACA;MACA;QACAhC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACAgC;MACA7B;MACA;MACA;QACAnB;MACA;QACAA;QACA;UACAe;YACAN;YACAiB;UACA;UACA;QACA;MACA;MACAX;QACAC;MACA;IACA;IACAiC;MACA;MACAlC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5WA;AAAA;AAAA;AAAA;AAAyoC,CAAgB,2lCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/list-page.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/category/list-page.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list-page.vue?vue&type=template&id=3e425c46&scoped=true&\"\nvar renderjs\nimport script from \"./list-page.vue?vue&type=script&lang=js&\"\nexport * from \"./list-page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-page.vue?vue&type=style&index=0&id=3e425c46&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e425c46\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/list-page.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=template&id=3e425c46&scoped=true&\"", "var components\ntry {\n  components = {\n    uniCollapse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse/uni-collapse\" */ \"@/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue\"\n      )\n    },\n    uniCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item\" */ \"@/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l2 = !_vm.isLoading\n    ? _vm.__map(_vm.son_fls, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.class.length\n        var l1 = !(g0 === 0)\n          ? _vm.__map(item.class, function (course, courseIndex) {\n              var $orig = _vm.__get_orig(course)\n              var l0 = course.tags ? course.tags.slice(0, 2) : null\n              return {\n                $orig: $orig,\n                l0: l0,\n              }\n            })\n          : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          l1: l1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"course-container\">\r\n\t\t<!-- 搜索栏 -->\r\n\t\t<view class=\"search-header\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<text class=\"search-icon\">🔍</text>\r\n\t\t\t\t<input\r\n\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\tplaceholder=\"搜索课程...\"\r\n\t\t\t\t\tv-model=\"keyword\"\r\n\t\t\t\t\t@input=\"onSearchInput\"\r\n\t\t\t\t/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要内容区域 -->\r\n\t\t<view class=\"main-content\">\r\n\t\t\t<!-- 左侧分类导航 -->\r\n\t\t\t<scroll-view class=\"category-nav\" scroll-y=\"true\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"category-item\"\r\n\t\t\t\t\t:class=\"{ active: activeIndex === index }\"\r\n\t\t\t\t\tv-for=\"(item, index) in classifyList\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t@click=\"postSonflData(item.id, index)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"category-indicator\" v-if=\"activeIndex === index\"></view>\r\n\t\t\t\t\t<text class=\"category-name\">{{item.name}}</text>\r\n\t\t\t\t\t<view class=\"category-count\" v-if=\"item.count\">{{item.count}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\r\n\t\t\t<!-- 右侧课程列表 -->\r\n\t\t\t<scroll-view class=\"course-list\" scroll-y=\"true\">\r\n\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t<view class=\"loading-container\" v-if=\"isLoading\">\r\n\t\t\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 课程分组 -->\r\n\t\t\t\t<view class=\"course-groups\" v-else>\r\n\t\t\t\t\t<uni-collapse ref=\"collapse\" v-model=\"value\">\r\n\t\t\t\t\t\t<uni-collapse-item\r\n\t\t\t\t\t\t\t:title=\"item.name\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in son_fls\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t:open=\"open == 0 ? false : true\"\r\n\t\t\t\t\t\t\tclass=\"course-group\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t\t\t\t<view class=\"empty-state\" v-if=\"item.class.length === 0\">\r\n\t\t\t\t\t\t\t\t<text class=\"empty-icon\">📚</text>\r\n\t\t\t\t\t\t\t\t<text class=\"empty-text\">暂无课程</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 课程列表 -->\r\n\t\t\t\t\t\t\t<view class=\"course-grid\" v-else>\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\tclass=\"course-card\"\r\n\t\t\t\t\t\t\t\t\tv-for=\"(course, courseIndex) in item.class\"\r\n\t\t\t\t\t\t\t\t\t:key=\"courseIndex\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"navToDetailPage(course)\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<!-- 课程封面 -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"course-cover\">\r\n\t\t\t\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"cover-image\"\r\n\t\t\t\t\t\t\t\t\t\t\t:src=\"course.picture || defaultCover\"\r\n\t\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t\t\t\t@error=\"handleImageError\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"course-badge\" v-if=\"course.is_free\">免费</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"course-badge premium\" v-else-if=\"course.price\">¥{{course.price}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t<!-- 课程信息 -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"course-info\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"course-title\">{{course.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"course-meta\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"course-teacher\" v-if=\"course.teacher\">{{course.teacher}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"course-students\" v-if=\"course.student_count\">{{course.student_count}}人学习</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"course-tags\" v-if=\"course.tags\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"course-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"tag in course.tags.slice(0, 2)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t>{{tag}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</uni-collapse-item>\r\n\t\t\t\t\t</uni-collapse>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { postClassifylist, postSonflData } from '@/request/index'\r\n\timport {postSearchCourse} from '@/request/search'\r\n\timport empty1 from \"@/components/null\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tempty1\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tclassifyList: [],\r\n\t\t\t\tflid: '',\r\n\t\t\t\tson_fls: [],\r\n\t\t\t\tgoodstype: '',\r\n\t\t\t\topen: 0,\r\n\t\t\t\t// 添加缺失的响应式属性\r\n\t\t\t\tcateId: '',\r\n\t\t\t\tstyle: '',\r\n\t\t\t\ttitle: '',\r\n\t\t\t\t// 新增优化相关数据\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tsearchTimer: null,\r\n\t\t\t\tdefaultCover: '/static/imgs/default-course.png',\r\n\t\t\t\tvalue: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tlet path = getCurrentPages()\r\n\t\t\tlet path_share = path[0].$page.fullPath\r\n\t\t\tlet path_title = path[0].data.title\r\n\t\t\tlet userinfo = uni.getStorageSync('userinfo')\r\n\t\t\tlet base_set = uni.getStorageSync('base_set')\r\n\t\t\tif(userinfo.uid=='' || !userinfo.uid){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'../login/login'\r\n\t\t\t\t})\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: '请先登录后再分享给好友',\r\n\t\t\t\t\tpath: ''\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tif (res.from === 'button') {\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: base_set.title,\r\n\t\t\t\t\tpath: `${path_share}?pid=${userinfo.uid}`\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.cateId = options.id;\r\n\t\t\tthis.style = options.style;\r\n\t\t\tthis.title = options.title;\r\n\t\t\tconsole.log(this.title);\r\n\t\t\t// 动态设置标题\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.title\r\n\t\t\t});\r\n\t\t\tthis.loadHotData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync loadHotData(type = 'add', loading) {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tuni.showLoading();\r\n\t\t\t\tthis.$http.get(\"v1/course/getCate\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tpage: this.page\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthis.classifyList = res.data.data\r\n\t\t\t\t\tthis.postSonflData(this.classifyList[0].id, 0)\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}).catch(error => {\r\n\t\t\t\t\tconsole.error('加载分类失败:', error);\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\t// 添加测试数据确保页面有内容\r\n\t\t\t\t\tthis.addTestData();\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络请求失败，显示测试数据',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 添加测试数据\r\n\t\t\taddTestData() {\r\n\t\t\t\tconsole.log('添加分类测试数据...');\r\n\t\t\t\tthis.classifyList = [\r\n\t\t\t\t\t{ id: 1, name: '基础日语', count: 15 },\r\n\t\t\t\t\t{ id: 2, name: '进阶日语', count: 12 },\r\n\t\t\t\t\t{ id: 3, name: '商务日语', count: 8 },\r\n\t\t\t\t\t{ id: 4, name: '考试辅导', count: 10 }\r\n\t\t\t\t];\r\n\r\n\t\t\t\tthis.son_fls = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '基础日语课程',\r\n\t\t\t\t\t\tclass: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\t\t\ttitle: '五十音图入门',\r\n\t\t\t\t\t\t\t\tpicture: 'https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/course1.jpg',\r\n\t\t\t\t\t\t\t\tteacher: '田中老师',\r\n\t\t\t\t\t\t\t\tstudent_count: 1200,\r\n\t\t\t\t\t\t\t\tprice: 99,\r\n\t\t\t\t\t\t\t\tis_free: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\t\t\ttitle: '基础语法精讲',\r\n\t\t\t\t\t\t\t\tpicture: 'https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/course2.jpg',\r\n\t\t\t\t\t\t\t\tteacher: '佐藤老师',\r\n\t\t\t\t\t\t\t\tstudent_count: 800,\r\n\t\t\t\t\t\t\t\tis_free: true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t];\r\n\r\n\t\t\t\tthis.activeIndex = 0;\r\n\t\t\t},\r\n\r\n\t\t\t// 搜索输入处理\r\n\t\t\tonSearchInput() {\r\n\t\t\t\t// 防抖处理\r\n\t\t\t\tif (this.searchTimer) {\r\n\t\t\t\t\tclearTimeout(this.searchTimer);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.searchTimer = setTimeout(() => {\r\n\t\t\t\t\tthis.performSearch();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\r\n\t\t\t// 执行搜索\r\n\t\t\tperformSearch() {\r\n\t\t\t\tif (!this.keyword.trim()) {\r\n\t\t\t\t\t// 如果搜索关键词为空，重新加载当前分类数据\r\n\t\t\t\t\tif (this.classifyList.length > 0) {\r\n\t\t\t\t\t\tthis.postSonflData(this.classifyList[this.activeIndex].id, this.activeIndex);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 实现搜索功能\r\n\t\t\t\tconsole.log('搜索关键词:', this.keyword);\r\n\t\t\t\tthis.searchCourses(this.keyword);\r\n\t\t\t},\r\n\r\n\t\t\t// 搜索课程\r\n\t\t\tsearchCourses(keyword) {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.$http.get(\"v1/course/search\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tkeyword: keyword,\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tlimit: 20\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log('搜索结果:', res);\r\n\t\t\t\t\tif (res.data.code === 0) {\r\n\t\t\t\t\t\t// 将搜索结果转换为分类格式显示\r\n\t\t\t\t\t\tthis.son_fls = [{\r\n\t\t\t\t\t\t\tname: `\"${keyword}\" 的搜索结果`,\r\n\t\t\t\t\t\t\tclass: res.data.data.list || []\r\n\t\t\t\t\t\t}];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.son_fls = [{\r\n\t\t\t\t\t\t\tname: `\"${keyword}\" 的搜索结果`,\r\n\t\t\t\t\t\t\tclass: []\r\n\t\t\t\t\t\t}];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t}).catch(error => {\r\n\t\t\t\t\tconsole.error('搜索失败:', error);\r\n\t\t\t\t\tthis.son_fls = [{\r\n\t\t\t\t\t\tname: `\"${keyword}\" 的搜索结果`,\r\n\t\t\t\t\t\tclass: []\r\n\t\t\t\t\t}];\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 图片加载错误处理\r\n\t\t\thandleImageError(e) {\r\n\t\t\t\tconsole.warn('图片加载失败:', e);\r\n\t\t\t\t// 可以设置默认图片\r\n\t\t\t\te.target.src = this.defaultCover;\r\n\t\t\t},\r\n\t\t\t// 获取分类列表\r\n\t\t\tpostClassifylist(goodstype) {\r\n\t\t\t\tpostClassifylist({goodstype: goodstype}).then(res => {\r\n\t\t\t\t\tthis.classifyList = res.data.data\r\n\t\t\t\t\tthis.postSonflData(this.classifyList[0].id, 0)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取子分类列表\r\n\t\t\tpostSonflData(id, key) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthis.$refs.collapse.resize();\r\n\t\t\t\t\t},500)\r\n\t\t\t\t});\r\n\t\t\t\tthis.activeIndex = key\r\n\t\t\t\tthis.flid = this.classifyList[key].id\r\n\t\t\t\t// postSonflData({goodstype: this.goodstype, flid: id}).then(res => {\r\n\t\t\t\t// \tthis.son_fls = res.data.data\r\n\t\t\t\t// })\r\n\t\t\t\tthis.$http.get(\"v1/course/getSubClass\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid: id\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthis.son_fls = res.data.data.class\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavigate(id) {\r\n\t\t\t\t// console.log(id)\r\n\t\t\t\tif(this.goodstype == 'course') {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/course-list/course-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/commodity-list/commodity-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpostSearchCourse(e) {\r\n\t\t\t\tconsole.log(123)\r\n\t\t\t\tvar keyword\r\n\t\t\t\tif(e == 'hot') {\r\n\t\t\t\t\tkeyword = e\r\n\t\t\t\t} else {\r\n\t\t\t\t\tkeyword = this.keyword\r\n\t\t\t\t\tif(keyword == '') {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '搜索内容不能为空',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course-list/course-list?keyword=${keyword}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavToDetailPage(item){\r\n\t\t\t\tlet id = item.id\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t/* 新增的现代化样式 */\r\n\t.course-container {\r\n\t\theight: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.search-header {\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tpadding: 20rpx;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n\r\n\t.search-box {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 15rpx 25rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.search-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.search-input {\r\n\t\tflex: 1;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.main-content {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.category-nav {\r\n\t\twidth: 200rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-right: 1rpx solid #e9ecef;\r\n\t}\r\n\r\n\t.category-item {\r\n\t\tposition: relative;\r\n\t\tpadding: 30rpx 20rpx;\r\n\t\tborder-bottom: 1rpx solid #e9ecef;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&.active {\r\n\t\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.category-indicator {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\twidth: 6rpx;\r\n\t\theight: 40rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 0 3rpx 3rpx 0;\r\n\t}\r\n\r\n\t.category-name {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.3;\r\n\t}\r\n\r\n\t.category-count {\r\n\t\tfont-size: 20rpx;\r\n\t\topacity: 0.8;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t.course-list {\r\n\t\tflex: 1;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.loading-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 100rpx 0;\r\n\t}\r\n\r\n\t.loading-spinner {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 4rpx solid #f3f3f3;\r\n\t\tborder-top: 4rpx solid #667eea;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.loading-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\t0% { transform: rotate(0deg); }\r\n\t\t100% { transform: rotate(360deg); }\r\n\t}\r\n\r\n\t.course-groups {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.course-group {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.empty-state {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 80rpx 0;\r\n\t}\r\n\r\n\t.empty-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.empty-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.course-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));\r\n\t\tgap: 20rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.course-card {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:hover {\r\n\t\t\ttransform: translateY(-5rpx);\r\n\t\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);\r\n\t\t}\r\n\t}\r\n\r\n\t.course-cover {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 180rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.cover-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tobject-fit: cover;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.course-card:hover .cover-image {\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.course-badge {\r\n\t\tposition: absolute;\r\n\t\ttop: 15rpx;\r\n\t\tright: 15rpx;\r\n\t\tbackground: #28a745;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 500;\r\n\r\n\t\t&.premium {\r\n\t\t\tbackground: #ff6b6b;\r\n\t\t}\r\n\t}\r\n\r\n\t.course-info {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.course-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tline-height: 1.4;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.course-meta {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.course-teacher {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.course-students {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.course-tags {\r\n\t\tdisplay: flex;\r\n\t\tgap: 10rpx;\r\n\t}\r\n\r\n\t.course-tag {\r\n\t\tbackground: #f8f9fa;\r\n\t\tcolor: #666;\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\r\n\t// search框\r\n\t.search {\r\n\t\theight: 90upx;\r\n\t\tbackground-color: #4b89ff;\r\n\t\tpadding: 0 20upx;\r\n\t\tpadding-top: 30upx;\r\n\r\n\t\t&-input {\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 31upx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-top: 10upx;\r\n\t\t\t}\r\n\t\t\timage {\r\n\t\t\t\twidth: 25upx;\r\n\t\t\t\theight: 14upx;\r\n\t\t\t\tmargin: 25upx 20upx 0 10upx;\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\twidth: 710upx;\r\n\t\t\t\theight: 63upx;\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 63upx;\r\n\t\t\t\tfont-size: 24upx;\r\n\t\t\t\tpadding-left: 20upx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\t.goods-search {\r\n\t\t\t\twidth: 28upx;\r\n\t\t\t\theight: 28upx;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 10upx;\r\n\t\t\t\ttop: -5upx;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t\r\n\t.scroll {\r\n\t\theight: calc(100vh - 5upx);\r\n\t\t// background-color: pink;\r\n\t\tdisplay: flex;\r\n\t\t&-left {\r\n\t\t\tflex: 2;\r\n\t\t\t// background-color: red;\r\n\t\t\tview {\r\n\t\t\t\theight: 120upx;\r\n\t\t\t\tbackground-color: #eee;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t//flex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t// border-bottom: 2upx solid #ddd;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tletter-spacing: 2upx;\r\n\t\t\t}\r\n\t\t\t.active {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&-right {\r\n\t\t\tflex: 5;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 0 10upx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t.item {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 220upx;\r\n\t\t\t\theight: 60upx;\r\n\t\t\t\tbackground-color: #eee;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 60upx;\r\n\t\t\t\tborder-radius: 60upx;\r\n\t\t\t\tmargin: 20upx 12upx 0;\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 30upx;\r\n\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\tletter-spacing: 4upx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.list-box {\r\n\t\t// padding-bottom: 20rpx;\r\n\t\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\t\r\n\t\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 150rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-large-box {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, .5) !important;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\t//color: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.des {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\r\n\t\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: blue;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t:last-child {\r\n\t\t\t// border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=style&index=0&id=3e425c46&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=style&index=0&id=3e425c46&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689557477\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}