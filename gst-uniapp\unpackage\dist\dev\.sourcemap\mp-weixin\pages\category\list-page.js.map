{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?435b", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?bfbe", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?d870", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?d663", "uni-app:///pages/category/list-page.vue", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?2216", "webpack:///D:/gst/gst-uniapp/pages/category/list-page.vue?1436"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty1", "CustomTabbar", "data", "keyword", "activeIndex", "classifyList", "flid", "son_fls", "goodstype", "open", "onShareAppMessage", "uni", "url", "title", "path", "onLoad", "console", "methods", "loadHotData", "type", "loading", "params", "page", "postClassifylist", "postSonflData", "setTimeout", "id", "navigate", "postSearchCourse", "icon", "navToDetailPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0C1nB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;QACAC;MACA;MACA;QACAC;QACAC;MACA;IACA;MACA,4BAEA;MACA;QACAD;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACAC;IACA;IACAL;MACAE;IACA;IACA;EACA;EACAI;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBACAT;gBACA;kBACAU;oBACAC;kBACA;gBACA;kBACAN;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAO;MAAA;MACA;QAAAf;MAAA;QACA;QACA;MACA;IACA;IACA;IACAgB;MAAA;MACA;QACAC;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAJ;UACAK;QACA;MACA;QACAV;QACA;MACA;IACA;IACAW;MACA;MACA;QACAhB;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACAgB;MACAZ;MACA;MACA;QACAb;MACA;QACAA;QACA;UACAQ;YACAE;YACAgB;UACA;UACA;QACA;MACA;MACAlB;QACAC;MACA;IACA;IACAkB;MACA;MACAnB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAAyoC,CAAgB,2lCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/list-page.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/category/list-page.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list-page.vue?vue&type=template&id=3e425c46&scoped=true&\"\nvar renderjs\nimport script from \"./list-page.vue?vue&type=script&lang=js&\"\nexport * from \"./list-page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-page.vue?vue&type=style&index=0&id=3e425c46&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e425c46\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/list-page.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=template&id=3e425c46&scoped=true&\"", "var components\ntry {\n  components = {\n    uniCollapse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse/uni-collapse\" */ \"@/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue\"\n      )\n    },\n    uniCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item\" */ \"@/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.son_fls, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.class.length\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\r\n\t\t<view class=\"scroll\">\r\n\t\t\t<scroll-view class=\"scroll-left\" scroll-y=\"true\" >\r\n\t\t\t\t<view @click=\"postSonflData(item.id, index)\" :class=\"{active: activeIndex === index}\" v-for=\"(item, index) in classifyList\" :key=\"index\"><text v-if=\"activeIndex === index\" style=\"color: blue;margin-right: 10rpx;font-weight: bold;\">|</text>{{item.name}}</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<scroll-view class=\"scroll-right\" scroll-y=\"true\" >\r\n\t\t\t\t<!-- <view class=\"item\" v-for=\"item in son_fls\" :key=\"item.id\" @click=\"navigate(item.id)\">\r\n\t\t\t\t\t<text>{{item.title}}</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<uni-section >\r\n\t\t\t\t<uni-collapse ref=\"collapse\" v-model=\"value\" >\r\n\t\t\t\t\t<uni-collapse-item  :title=\"item.name\" v-for=\"(item,index) in son_fls\" :key=\"index\" :open=\"open==0?false:true\">\r\n\t\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t\t<empty1 v-if=\" item.class.length === 0\"></empty1>\r\n\t\t\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item1, index1) in item.class\"\r\n\t\t\t\t\t\t\t\t:key=\"index1\" @tap=\"navToDetailPage(item1)\">\r\n\t\t\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item1.picture\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"title\">{{item1.title}}</view>\r\n\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</uni-collapse-item>\r\n\t\t\t\t</uni-collapse>\r\n\t\t\t\t</uni-section>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 自定义底部导航 -->\r\n\t\t<custom-tabbar />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { postClassifylist, postSonflData } from '@/request/index'\r\n\timport {postSearchCourse} from '@/request/search'\r\n\timport empty1 from \"@/components/null\";\r\n\timport CustomTabbar from '@/components/custom-tabbar.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tempty1,\r\n\t\t\tCustomTabbar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tclassifyList: [],\r\n\t\t\t\tflid: '',\r\n\t\t\t\tson_fls: [],\r\n\t\t\t\tgoodstype: '',\r\n\t\t\t\topen:0\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tlet path = getCurrentPages()\r\n\t\t\tlet path_share = path[0].$page.fullPath\r\n\t\t\tlet path_title = path[0].data.title\r\n\t\t\tlet userinfo = uni.getStorageSync('userinfo')\r\n\t\t\tlet base_set = uni.getStorageSync('base_set')\r\n\t\t\tif(userinfo.uid=='' || !userinfo.uid){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'../login/login'\r\n\t\t\t\t})\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: '请先登录后再分享给好友',\r\n\t\t\t\t\tpath: ''\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tif (res.from === 'button') {\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: base_set.title,\r\n\t\t\t\t\tpath: `${path_share}?pid=${userinfo.uid}`\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.cateId = options.id;\r\n\t\t\tthis.style = options.style;\r\n\t\t\tthis.title = options.title;\r\n\t\t\tconsole.log(this.title);\r\n\t\t\t// 动态设置标题\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.title\r\n\t\t\t});\r\n\t\t\tthis.loadHotData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync loadHotData(type = 'add', loading) {\r\n\t\t\t\tuni.showLoading();\r\n\t\t\t\tthis.$http.get(\"v1/course/getCate\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tpage: this.page\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthis.classifyList = res.data.data\r\n\t\t\t\t\tthis.postSonflData(this.classifyList[0].id, 0)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 获取分类列表\r\n\t\t\tpostClassifylist(goodstype) {\r\n\t\t\t\tpostClassifylist({goodstype: goodstype}).then(res => {\r\n\t\t\t\t\tthis.classifyList = res.data.data\r\n\t\t\t\t\tthis.postSonflData(this.classifyList[0].id, 0)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取子分类列表\r\n\t\t\tpostSonflData(id, key) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthis.$refs.collapse.resize();\r\n\t\t\t\t\t},500)\r\n\t\t\t\t});\r\n\t\t\t\tthis.activeIndex = key\r\n\t\t\t\tthis.flid = this.classifyList[key].id\r\n\t\t\t\t// postSonflData({goodstype: this.goodstype, flid: id}).then(res => {\r\n\t\t\t\t// \tthis.son_fls = res.data.data\r\n\t\t\t\t// })\r\n\t\t\t\tthis.$http.get(\"v1/course/getSubClass\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid: id\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthis.son_fls = res.data.data.class\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavigate(id) {\r\n\t\t\t\t// console.log(id)\r\n\t\t\t\tif(this.goodstype == 'course') {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/course-list/course-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/commodity-list/commodity-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpostSearchCourse(e) {\r\n\t\t\t\tconsole.log(123)\r\n\t\t\t\tvar keyword\r\n\t\t\t\tif(e == 'hot') {\r\n\t\t\t\t\tkeyword = e\r\n\t\t\t\t} else {\r\n\t\t\t\t\tkeyword = this.keyword\r\n\t\t\t\t\tif(keyword == '') {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '搜索内容不能为空',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course-list/course-list?keyword=${keyword}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavToDetailPage(item){\r\n\t\t\t\tlet id = item.id\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\r\n\r\n\t// search框 \r\n\t.search {\r\n\t\theight: 90upx;\r\n\t\tbackground-color: #4b89ff;\r\n\t\tpadding: 0 20upx;\r\n\t\tpadding-top: 30upx;\r\n\r\n\t\t&-input {\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 31upx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-top: 10upx;\r\n\t\t\t}\r\n\t\t\timage {\r\n\t\t\t\twidth: 25upx;\r\n\t\t\t\theight: 14upx;\r\n\t\t\t\tmargin: 25upx 20upx 0 10upx;\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\twidth: 710upx;\r\n\t\t\t\theight: 63upx;\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 63upx;\r\n\t\t\t\tfont-size: 24upx;\r\n\t\t\t\tpadding-left: 20upx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\t.goods-search {\r\n\t\t\t\twidth: 28upx;\r\n\t\t\t\theight: 28upx;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 10upx;\r\n\t\t\t\ttop: -5upx;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t\r\n\t.scroll {\r\n\t\theight: calc(100vh - 5upx);\r\n\t\t// background-color: pink;\r\n\t\tdisplay: flex;\r\n\t\t&-left {\r\n\t\t\tflex: 2;\r\n\t\t\t// background-color: red;\r\n\t\t\tview {\r\n\t\t\t\theight: 120upx;\r\n\t\t\t\tbackground-color: #eee;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t//flex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t// border-bottom: 2upx solid #ddd;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tletter-spacing: 2upx;\r\n\t\t\t}\r\n\t\t\t.active {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&-right {\r\n\t\t\tflex: 5;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 0 10upx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t.item {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 220upx;\r\n\t\t\t\theight: 60upx;\r\n\t\t\t\tbackground-color: #eee;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 60upx;\r\n\t\t\t\tborder-radius: 60upx;\r\n\t\t\t\tmargin: 20upx 12upx 0;\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 30upx;\r\n\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\tletter-spacing: 4upx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.list-box {\r\n\t\t// padding-bottom: 20rpx;\r\n\t\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\t\r\n\t\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 150rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-large-box {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, .5) !important;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\t//color: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.des {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\r\n\t\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: blue;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t:last-child {\r\n\t\t\t// border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=style&index=0&id=3e425c46&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-page.vue?vue&type=style&index=0&id=3e425c46&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040518917\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}