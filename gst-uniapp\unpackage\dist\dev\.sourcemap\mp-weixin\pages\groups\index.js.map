{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/groups/index.vue?e479", "webpack:///D:/gst/gst-uniapp/pages/groups/index.vue?853f", "webpack:///D:/gst/gst-uniapp/pages/groups/index.vue?7043", "webpack:///D:/gst/gst-uniapp/pages/groups/index.vue?b729", "uni-app:///pages/groups/index.vue", "webpack:///D:/gst/gst-uniapp/pages/groups/index.vue?dc44", "webpack:///D:/gst/gst-uniapp/pages/groups/index.vue?bdd9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabbar", "data", "pageLoading", "hasGroupPermission", "selectedGroupId", "selectedGroup", "selectedGroupIndex", "groupsLoading", "activeNavIndex", "stickyIndex", "scrollTop", "conceptTutorial", "id", "title", "description", "totalLessons", "completedLessons", "conceptCategoryList", "name", "icon", "expanded", "lessons", "subtitle", "completed", "groupCategoryList", "currentCategoryList", "current<PERSON>iew", "selectedDate", "currentPracticeType", "groupList", "practiceTypes", "count", "reviewCourses", "date", "teacher", "duration", "thumbnail", "groupId", "practices", "type", "questionCount", "status", "ver", "second", "computed", "totalMembers", "filteredReviewCourses", "courses", "filteredPractices", "practice", "onLoad", "onShow", "methods", "scrollToCategory", "query", "onContentScroll", "updateActiveNav", "enter<PERSON><PERSON><PERSON>", "console", "uni", "url", "checkPermission", "userToken", "userInfo", "<PERSON><PERSON><PERSON><PERSON>", "checkGroupAccess", "isLoggedIn", "hasLocalToken", "hasLocalUserInfo", "hasLocalMember", "member<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasMemberPermission", "checkMemberPermission", "now", "endDate", "<PERSON><PERSON><PERSON><PERSON>", "showLoginTip", "showMemberTip", "message", "initializeData", "loadStaticGroupData", "loadConceptTutorialData", "item", "loadGroupCourseData", "params", "enterConceptCategory", "startConceptLearning", "continueConceptLearning", "viewConceptProgress", "selectConceptTutorial", "selectGroupForDetail", "selectGroup", "itemList", "success", "fail", "switchView", "onDateChange", "selectPracticeType", "quickViewReview", "setTimeout", "quickViewPractice", "playCourseReview", "startPractice", "viewGroupMembers", "getGroupColor", "goToLogin", "contactAdmin", "content", "showCancel", "confirmText", "enterGroup", "joinGroup"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCsTtnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAL;QACAM;QACAC;QACAC;QAAA;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,EACA;MACA;MACAC;QACAZ;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;UACAT;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA,GACA;UACAX;UACAC;UACAS;UACAC;QACA;MAEA,EACA;MACA;MACAE;MACAC;MAAA;MACAC;MACAC;MACAC;MACA;MACAC;QACAlB;QACAM;QACAC;QACAY;MACA,GACA;QACAnB;QACAM;QACAC;QACAY;MACA,GACA;QACAnB;QACAM;QACAC;QACAY;MACA,GACA;QACAnB;QACAM;QACAC;QACAY;MACA,EACA;MACA;MACAC;QACApB;QACAC;QACAoB;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAzB;QACAC;QACAoB;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAzB;QACAC;QACAoB;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAzB;QACAC;QACAoB;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;MACAC;QACA1B;QACAC;QACAoB;QACAM;QACAC;QACAL;QACAM;QACAJ;MACA,GACA;QACAzB;QACAC;QACAoB;QACAM;QACAC;QACAL;QACAM;QACAJ;MACA,EACA;MACAK;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;QAAA,OACA;MAAA,EACA;MAEA;QACAC;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;QAAA,OACA;MAAA,EACA;MAEA;QACAV;UAAA;UAAA,OACAW;YAAA;UAAA;QAAA,EACA;MACA;MAEA;QAAA;MAAA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA,sCACA;gBACA;cACA;cACA;cACA;cACA;cAEA;cACA;cACA;gBACA;kBACA;kBACA;gBACA;kBACA;gBACA;cAEA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MAAA;MACA;;MAEA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACAC;;MAEA;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAH;;MAEA;MACA;MACA;MACA;MAEAA;QACAI;QACAC;QACAC;MACA;MAEA;QACAN;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAO;MACA;MACA;MACA;MACA;MACA;MAEAP;MACAA;MACAA;MACAA;MACAA;QAAAM;QAAAE;MAAA;;MAEA;MACA;MACA;MACA;MACAR;QACAS;QACAC;QACAC;MACA;;MAEA;MACA;MAEA;QACAX;QACA;QACA;QACA;MACA;;MAEA;MACA;MACA;MAEAA;QACAY;QACAC;MACA;MAEA;QACAb;QACA;;QAEA;QACA;UACAA;UACA;QACA;MACA;QACAA;QACA;QACA;MACA;MAEAA;IACA;IAEA;IACAc;MACA;MACA;MACA;QACAd;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACA;MACA;MAEAA;QACAe;QACAC;QACAC;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACAjB;QACA9C;QACAM;QACAgB;MACA;IACA;IAEA;IACA0C;MACA;MACA;MAEA;QACA;QACA;QACA;UACAC;QACA;MACA;MAEAnB;QACA9C;QACAM;QACAgB;MACA;IACA;IAEA;IACA4C;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAtB;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAuB;MAAA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAzB;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA0B;UACAxE;QAEA;MACA;QACA;UACA;UACA;UACA;YACAsE;UACA;QACA;MACA;IACA;IAIA;IACAG;MACA3B;;MAEA;MACAC;QACA9C;QACAM;QACAgB;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAmD;MACA5B;MAEAC;QACA9C;QACAM;QACAgB;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAoD;MACA7B;MAEAC;QACA9C;QACAM;QACAgB;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAqD;MACA9B;MAEAC;QACA9C;QACAM;QACAgB;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAsD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACAhC;QACA9C;QACA+E,WACA,aACA,aACA,aACA,YACA;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;QACAC;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAvC;QACA9C;QACAM;QACAgB;MACA;MAEAgE;QACAxC;UACAC;QACA;MACA;IACA;IAEA;IACAwC;MACAzC;QACA9C;QACAM;QACAgB;MACA;MAEAgE;QACAxC;UACAC;QACA;MACA;IACA;IAEA;IACAyC;MACA1C;QACAC;MACA;IACA;IAEA;IACA0C;MACA3C;QACAC;MACA;IACA;IAEA;IACA2C;MACA5C;QACA9C;QACAM;QACAgB;MACA;MAEAgE;QACAxC;UACAC;QACA;MACA;IACA;IAEA;IACA4C;MACA,cACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MACA;IACA;IAIA;IACAC;MACA9C;QACAC;MACA;IACA;IAEA;IACA8C;MACA/C;QACA9C;QACA8F;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAhD;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAH;kBACA9C;kBACA8F;kBACAE;kBACAhB;oBACA;sBACAlC;wBACAC;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAD;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAC;kBACA9C;kBACAM;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4F;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAjD;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAH;kBACA9C;kBACA8F;kBACAE;kBACAhB;oBACA;sBACAlC;wBACAC;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAD;kBACA9C;kBACAM;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAuC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACx/CA;AAAA;AAAA;AAAA;AAA64B,CAAgB,43BAAG,EAAC,C;;;;;;;;;;;ACAj6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6e8c2b60&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e8c2b60\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6e8c2b60&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1 clean-groups-page\">\r\n\t\t\t<!-- 有权限时显示内容 -->\r\n\t\t\t<view v-if=\"hasGroupPermission\" class=\"page-content\">\r\n\t\t\t\t<!-- 简洁的页面头部 -->\r\n\t\t\t\t<view class=\"page-header\">\r\n\t\t\t\t\t<view class=\"header-content\">\r\n\t\t\t\t\t\t<view class=\"title-section\">\r\n\t\t\t\t\t\t\t<text class=\"page-title\">🎓 GST派遣日语培训班</text>\r\n\t\t\t\t\t\t\t<text class=\"page-subtitle\">与同伴一起进步，共同成长</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"stats-section\">\r\n\t\t\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">{{groupList.length}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">个班</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">{{totalMembers}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">名成员</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">48</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">门课程</text>\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 左右联动布局 -->\r\n\t\t\t\t<view class=\"split-layout\">\r\n\t\t\t\t\t<!-- 左侧小组列表 -->\r\n\t\t\t\t\t<view class=\"left-panel\">\r\n\t\t\t\t\t\t<view class=\"panel-header\">\r\n\t\t\t\t\t\t\t<text class=\"panel-title\">学习内容</text>\r\n\t\t\t\t\t\t\t<text class=\"panel-subtitle\">{{groupList.length + 1}}项</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view\r\n\t\t\t\t\t\t\tclass=\"group-list\"\r\n\t\t\t\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t\t\t\t:enable-back-to-top=\"true\"\r\n\t\t\t\t\t\t\t:scroll-with-animation=\"true\"\r\n\t\t\t\t\t\t\t:enhanced=\"true\"\r\n\t\t\t\t\t\t\t:bounces=\"true\"\r\n\t\t\t\t\t\t\t:show-scrollbar=\"false\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<!-- 公共新概念教程 -->\r\n\t\t\t\t\t\t\t<view class=\"concept-tutorial-item\" :class=\"{ 'active': selectedGroupId === 'concept' }\"\r\n\t\t\t\t\t\t\t\t@click=\"selectConceptTutorial()\">\r\n\t\t\t\t\t\t\t\t<view class=\"concept-content\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"concept-icon\">📖</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"concept-title\">新标日本语教程</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"concept-badge\">公共</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"group-item\" v-for=\"(group, index) in groupList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'active': selectedGroupId === group.id }\"\r\n\t\t\t\t\t\t\t\t@click=\"selectGroupForDetail(group, index)\">\r\n\t\t\t\t\t\t\t\t<view class=\"simple-group-content\">\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"group-level-badge\" :style=\"{ background: getGroupColor(index) }\">\r\n\t\t\t\t\t\t\t\t\t\t{{group.name}}\r\n\t\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t\t<text class=\"simple-group-name\">{{group.name}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"simple-status-dot\" :class=\"group.status\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 右侧详情面板 -->\r\n\t\t\t\t\t<view class=\"right-panel\">\r\n\t\t\t\t\t\t<!-- 新概念教程详情 -->\r\n\t\t\t\t\t\t<view v-if=\"selectedGroupId === 'concept'\" class=\"concept-detail-content\">\r\n\t\t\t\t\t\t\t<!-- 教程头部 -->\r\n\t\t\t\t\t\t\t<view class=\"concept-detail-header\">\r\n\t\t\t\t\t\t\t\t<view class=\"concept-bg\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"concept-overlay\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-detail-icon\">📖</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-detail-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-detail-title\">{{conceptTutorial.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-detail-subtitle\">{{conceptTutorial.description}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"concept-progress-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"progress-info-text\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{conceptTutorial.completedLessons}} / {{conceptTutorial.totalLessons}} 课时\r\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 学习内容 - 吸顶二级列表 -->\r\n\t\t\t\t\t\t\t<view class=\"concept-categories\">\r\n\t\t\t\t\t\t\t\t<view class=\"categories-title\">学习内容</view>\r\n\r\n\t\t\t\t\t\t\t\t<!-- 吸顶分类导航 -->\r\n\t\t\t\t\t\t\t\t<view class=\"sticky-nav\">\r\n\t\t\t\t\t\t\t\t\t<scroll-view class=\"nav-scroll\" scroll-x=\"true\" :enhanced=\"true\" :bounces=\"false\" :show-scrollbar=\"false\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"nav-items\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"nav-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'active': activeNavIndex === categoryIndex }\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(category, categoryIndex) in currentCategoryList\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"categoryIndex\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t@click=\"scrollToCategory(categoryIndex)\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"nav-icon\">{{category.icon}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"nav-text\">{{category.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t<!-- 滚动内容区域 -->\r\n\t\t\t\t\t\t\t\t<scroll-view\r\n\t\t\t\t\t\t\t\t\tclass=\"content-scroll\"\r\n\t\t\t\t\t\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t\t\t\t\t\t@scroll=\"onContentScroll\"\r\n\t\t\t\t\t\t\t\t\t:scroll-top=\"scrollTop\"\r\n\t\t\t\t\t\t\t\t\t:scroll-with-animation=\"true\"\r\n\t\t\t\t\t\t\t\t\t:enable-back-to-top=\"true\"\r\n\t\t\t\t\t\t\t\t\t:enhanced=\"true\"\r\n\t\t\t\t\t\t\t\t\t:bounces=\"true\"\r\n\t\t\t\t\t\t\t\t\t:show-scrollbar=\"true\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<view class=\"scroll-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"category-section\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(category, categoryIndex) in currentCategoryList\"\r\n\t\t\t\t\t\t\t\t\t\t\t:key=\"categoryIndex\"\r\n\t\t\t\t\t\t\t\t\t\t\t:id=\"'category-' + categoryIndex\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 分类标题（吸顶） -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"section-header\" :class=\"{ 'sticky': stickyIndex === categoryIndex }\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"header-content\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"header-icon\">{{category.icon}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"header-title\">{{category.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"header-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"lesson-count\">{{category.li ? category.li.length : 0}}课时</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 课程列表 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"section-lessons\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"lesson-card\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(lesson, lessonIndex) in category.li\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t:key=\"lessonIndex\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"enterLesson(category, lesson)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-left\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-number\">{{lessonIndex + 1}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"lesson-title\">{{lesson.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"lesson-subtitle\">{{lesson.subtitle}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-status\" :class=\"lesson.completed ? 'completed' : 'pending'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"status-icon\">{{lesson.completed ? '✓' : '○'}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 小组详情 - 简单列表结构 -->\r\n\t\t\t\t\t\t<view v-else-if=\"selectedGroup\" class=\"detail-content\">\r\n\t\t\t\t\t\t\t<!-- 小组信息头部 -->\r\n\t\t\t\t\t\t\t<view class=\"group-header\">\r\n\t\t\t\t\t\t\t\t<view class=\"group-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"group-avatar\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"selectedGroup.icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"group-details\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"group-name\">{{selectedGroup.name}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"group-desc\">{{selectedGroup.description}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"group-stats\">\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"stat-item\">{{selectedGroup.num}}人</text> -->\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"stat-item\">{{selectedGroup.courseCount}}课程</text> -->\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"stat-item\">{{selectedGroup.progress}}%进度</text> -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 学习内容 - 吸顶二级列表 -->\r\n\t\t\t\t\t\t\t<view class=\"group-categories\">\r\n\t\t\t\t\t\t\t\t<view class=\"categories-title\">学习内容</view>\r\n\r\n\t\t\t\t\t\t\t\t<!-- 吸顶分类导航 -->\r\n\t\t\t\t\t\t\t\t<view class=\"sticky-nav\">\r\n\t\t\t\t\t\t\t\t\t<scroll-view class=\"nav-scroll\" scroll-x=\"true\" :enhanced=\"true\" :bounces=\"false\" :show-scrollbar=\"false\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"nav-items\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"nav-item\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'active': activeNavIndex === categoryIndex }\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(category, categoryIndex) in currentCategoryList\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:key=\"categoryIndex\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t@click=\"scrollToCategory(categoryIndex)\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"nav-icon\">{{category.icon}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"nav-text\">{{category.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t<!-- 滚动内容区域 -->\r\n\t\t\t\t\t\t\t\t<scroll-view\r\n\t\t\t\t\t\t\t\t\tclass=\"content-scroll\"\r\n\t\t\t\t\t\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t\t\t\t\t\t@scroll=\"onContentScroll\"\r\n\t\t\t\t\t\t\t\t\t:scroll-top=\"scrollTop\"\r\n\t\t\t\t\t\t\t\t\t:scroll-with-animation=\"true\"\r\n\t\t\t\t\t\t\t\t\t:enable-back-to-top=\"true\"\r\n\t\t\t\t\t\t\t\t\t:enhanced=\"true\"\r\n\t\t\t\t\t\t\t\t\t:bounces=\"true\"\r\n\t\t\t\t\t\t\t\t\t:show-scrollbar=\"true\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<view class=\"scroll-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"category-section\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(category, categoryIndex) in currentCategoryList\"\r\n\t\t\t\t\t\t\t\t\t\t\t:key=\"categoryIndex\"\r\n\t\t\t\t\t\t\t\t\t\t\t:id=\"'category-' + categoryIndex\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 分类标题（吸顶） -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"section-header\" :class=\"{ 'sticky': stickyIndex === categoryIndex }\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"header-content\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"header-icon\">{{category.icon}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"header-title\">{{category.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"header-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"lesson-count\">{{category.li ? category.li.length : 0}}课时</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 课程列表 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"section-lessons\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"lesson-card\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(lesson, lessonIndex) in category.li\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t:key=\"lessonIndex\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"enterLesson(category, lesson)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-left\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-number\">{{lessonIndex + 1}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"lesson-title\">{{lesson.title}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"lesson-subtitle\">{{lesson.subtitle || ''}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"lesson-status\" :class=\"lesson.completed ? 'completed' : 'pending'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"status-icon\">{{lesson.completed ? '✓' : '○'}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 未选择状态 -->\r\n\t\t\t\t\t\t<view v-else class=\"empty-detail\">\r\n\t\t\t\t\t\t\t<view class=\"empty-icon\">👈</view>\r\n\t\t\t\t\t\t\t<text class=\"empty-title\">选择一个小组</text>\r\n\t\t\t\t\t\t\t<text class=\"empty-desc\">点击左侧小组查看详细信息</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 无权限提示 -->\r\n\t\t\t<view v-else class=\"no-permission-page\">\r\n\t\t\t\t<view class=\"permission-container\">\r\n\t\t\t\t\t<view class=\"permission-icon\">🔒</view>\r\n\t\t\t\t\t<text class=\"permission-title\">访问受限</text>\r\n\t\t\t\t\t<text class=\"permission-desc\">您暂时没有访问学习小组的权限</text>\r\n\t\t\t\t\t<text class=\"permission-hint\">请联系管理员开通权限或使用授权账号登录</text>\r\n\t\t\t\t\t<view class=\"permission-actions\">\r\n\t\t\t\t\t\t<button class=\"btn-login\" @click=\"goToLogin\">重新登录</button>\r\n\t\t\t\t\t\t<button class=\"btn-contact\" @click=\"contactAdmin\">联系管理员</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gui-page>\r\n\r\n\t<!-- 自定义底部导航 -->\r\n\t<custom-tabbar />\r\n</template>\r\n\r\n<script>\r\n\timport CustomTabbar from '@/components/custom-tabbar.vue';\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tCustomTabbar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpageLoading: false,\r\n\t\t\t\thasGroupPermission: false,\r\n\t\t\t\tselectedGroupId: 'concept',\r\n\t\t\t\tselectedGroup: null,\r\n\t\t\t\tselectedGroupIndex: 0,\r\n\t\t\t\tgroupsLoading: false,\r\n\r\n\t\t\t\t// 吸顶列表相关数据\r\n\t\t\t\tactiveNavIndex: 0,\r\n\t\t\t\tstickyIndex: 0,\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\t// 新概念教程数据\r\n\t\t\t\tconceptTutorial: {\r\n\t\t\t\t\tid: 'concept',\r\n\t\t\t\t\ttitle: '新标日本语教程',\r\n\t\t\t\t\tdescription: '适合所有学员的基础教程',\r\n\t\t\t\t\ttotalLessons: 30,\r\n\t\t\t\t\tcompletedLessons: 8,\r\n\t\t\t\t},\r\n\t\t\t\t// 新概念教程的分类列表数据\r\n\t\t\t\tconceptCategoryList: [{\r\n\t\t\t\t\t\tid: 'speaking',\r\n\t\t\t\t\t\tname: '口语',\r\n\t\t\t\t\t\ticon: '🗣️',\r\n\t\t\t\t\t\texpanded: true, // 默认展开第一个\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '基础问候语',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '自我介绍',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '日常对话',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '购物用语',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 5,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '餐厅用语',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'grammar',\r\n\t\t\t\t\t\tname: '语法',\r\n\t\t\t\t\t\ticon: '📝',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 6,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '基本句型',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 7,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '动词变位',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 8,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '形容词活用',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 9,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '助词用法',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 10,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '敬语表达',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'vocabulary',\r\n\t\t\t\t\t\tname: '词汇',\r\n\t\t\t\t\t\ticon: '📚',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 11,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '基础词汇',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 12,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '生活词汇',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 13,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '工作词汇',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 14,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '学习词汇',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 15,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '旅游词汇',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'listening',\r\n\t\t\t\t\t\tname: '听力',\r\n\t\t\t\t\t\ticon: '🎧',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 16,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '基础听力',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 17,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '对话听力',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 18,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '新闻听力',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 19,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '故事听力',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 20,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '综合听力',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'reading',\r\n\t\t\t\t\t\tname: '阅读',\r\n\t\t\t\t\t\ticon: '📖',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 21,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '短文阅读',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 22,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '新闻阅读',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 23,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '小说阅读',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 24,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '说明文阅读',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 25,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '综合阅读',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'writing',\r\n\t\t\t\t\t\tname: '写作',\r\n\t\t\t\t\t\ticon: '✍️',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 26,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '基础写作',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 27,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '日记写作',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 28,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '书信写作',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 29,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '作文写作',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 30,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '应用写作',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 小组专用的分类列表数据\r\n\t\t\t\tgroupCategoryList: [{\r\n\t\t\t\t\t\tid: 'n5-basic',\r\n\t\t\t\t\t\tname: 'N5基础',\r\n\t\t\t\t\t\ticon: '🌱',\r\n\t\t\t\t\t\texpanded: true,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 101,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '五十音图（あ行）',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 102,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '五十音图（か行）',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 103,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '五十音图（さ行）',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 104,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '五十音图（た行）',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 105,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '五十音图（な行）',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'n5-grammar',\r\n\t\t\t\t\t\tname: 'N5语法',\r\n\t\t\t\t\t\ticon: '📖',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 106,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: 'です/である',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 107,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '名词+は+名词',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 108,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '疑问词',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 109,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '数字和时间',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 110,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '动词现在时',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'n5-vocabulary',\r\n\t\t\t\t\t\tname: 'N5词汇',\r\n\t\t\t\t\t\ticon: '📚',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 111,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '家族称呼',\r\n\t\t\t\t\t\t\t\tcompleted: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 112,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '职业名称',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 113,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '日常用品',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 114,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '食物饮料',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 115,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '颜色形状',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'n5-conversation',\r\n\t\t\t\t\t\tname: 'N5会话',\r\n\t\t\t\t\t\ticon: '💬',\r\n\t\t\t\t\t\texpanded: false,\r\n\t\t\t\t\t\tlessons: [{\r\n\t\t\t\t\t\t\t\tid: 116,\r\n\t\t\t\t\t\t\t\ttitle: '第一课',\r\n\t\t\t\t\t\t\t\tsubtitle: '初次见面',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 117,\r\n\t\t\t\t\t\t\t\ttitle: '第二课',\r\n\t\t\t\t\t\t\t\tsubtitle: '日常问候',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 118,\r\n\t\t\t\t\t\t\t\ttitle: '第三课',\r\n\t\t\t\t\t\t\t\tsubtitle: '购物对话',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 119,\r\n\t\t\t\t\t\t\t\ttitle: '第四课',\r\n\t\t\t\t\t\t\t\tsubtitle: '餐厅点餐',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 120,\r\n\t\t\t\t\t\t\t\ttitle: '第五课',\r\n\t\t\t\t\t\t\t\tsubtitle: '问路指路',\r\n\t\t\t\t\t\t\t\tcompleted: false\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 当前显示的分类列表（动态切换）\r\n\t\t\t\tcurrentCategoryList: [],\r\n\t\t\t\tcurrentView: 'review', // 'review' 或 'practice'\r\n\t\t\t\tselectedDate: '',\r\n\t\t\t\tcurrentPracticeType: 'all',\r\n\t\t\t\tgroupList: [],\r\n\t\t\t\t// 练习类型\r\n\t\t\t\tpracticeTypes: [{\r\n\t\t\t\t\t\tid: 'listening',\r\n\t\t\t\t\t\tname: '听力练习',\r\n\t\t\t\t\t\ticon: '🎧',\r\n\t\t\t\t\t\tcount: 25\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'grammar',\r\n\t\t\t\t\t\tname: '语法练习',\r\n\t\t\t\t\t\ticon: '📝',\r\n\t\t\t\t\t\tcount: 30\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'vocabulary',\r\n\t\t\t\t\t\tname: '词汇练习',\r\n\t\t\t\t\t\ticon: '📚',\r\n\t\t\t\t\t\tcount: 40\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 'speaking',\r\n\t\t\t\t\t\tname: '口语练习',\r\n\t\t\t\t\t\ticon: '🗣️',\r\n\t\t\t\t\t\tcount: 15\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 课程回顾数据\r\n\t\t\t\treviewCourses: [{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '第一课：基础发音练习',\r\n\t\t\t\t\t\tdate: '2024-01-15',\r\n\t\t\t\t\t\tteacher: '田中老师',\r\n\t\t\t\t\t\tduration: '45:30',\r\n\t\t\t\t\t\tthumbnail: '/static/imgs/course-thumb1.png',\r\n\t\t\t\t\t\tgroupId: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\ttitle: '第二课：日常问候语',\r\n\t\t\t\t\t\tdate: '2024-01-16',\r\n\t\t\t\t\t\tteacher: '佐藤老师',\r\n\t\t\t\t\t\tduration: '38:20',\r\n\t\t\t\t\t\tthumbnail: '/static/imgs/course-thumb2.png',\r\n\t\t\t\t\t\tgroupId: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\ttitle: '第三课：数字与时间',\r\n\t\t\t\t\t\tdate: '2024-01-17',\r\n\t\t\t\t\t\tteacher: '山田老师',\r\n\t\t\t\t\t\tduration: '42:15',\r\n\t\t\t\t\t\tthumbnail: '/static/imgs/course-thumb3.png',\r\n\t\t\t\t\t\tgroupId: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\ttitle: '第四课：家族称呼',\r\n\t\t\t\t\t\tdate: '2024-01-18',\r\n\t\t\t\t\t\tteacher: '田中老师',\r\n\t\t\t\t\t\tduration: '39:45',\r\n\t\t\t\t\t\tthumbnail: '/static/imgs/course-thumb4.png',\r\n\t\t\t\t\t\tgroupId: 2\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 练习数据\r\n\t\t\t\tpractices: [{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '五十音图听力练习',\r\n\t\t\t\t\t\tdate: '2024-01-15',\r\n\t\t\t\t\t\ttype: '听力练习',\r\n\t\t\t\t\t\tquestionCount: 20,\r\n\t\t\t\t\t\tduration: 15,\r\n\t\t\t\t\t\tstatus: 'completed',\r\n\t\t\t\t\t\tgroupId: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\ttitle: '基础语法选择题',\r\n\t\t\t\t\t\tdate: '2024-01-16',\r\n\t\t\t\t\t\ttype: '语法练习',\r\n\t\t\t\t\t\tquestionCount: 25,\r\n\t\t\t\t\t\tduration: 20,\r\n\t\t\t\t\t\tstatus: 'in-progress',\r\n\t\t\t\t\t\tgroupId: 1\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tver: this.$store.state.ver,\r\n\t\t\t\tsecond:0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 总成员数\r\n\t\t\ttotalMembers() {\r\n\t\t\t\treturn this.groupList.reduce((total, group) => total + group.num, 0);\r\n\t\t\t},\r\n\r\n\t\t\t// 过滤后的课程回顾\r\n\t\t\tfilteredReviewCourses() {\r\n\t\t\t\tlet courses = this.reviewCourses.filter(course =>\r\n\t\t\t\t\t!this.selectedGroup || course.groupId === this.selectedGroup.id\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (this.selectedDate) {\r\n\t\t\t\t\tcourses = courses.filter(course => course.date === this.selectedDate);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn courses.sort((a, b) => new Date(b.date) - new Date(a.date));\r\n\t\t\t},\r\n\r\n\t\t\t// 过滤后的练习\r\n\t\t\tfilteredPractices() {\r\n\t\t\t\tlet practices = this.practices.filter(practice =>\r\n\t\t\t\t\t!this.selectedGroup || practice.groupId === this.selectedGroup.id\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (this.currentPracticeType !== 'all') {\r\n\t\t\t\t\tpractices = practices.filter(practice =>\r\n\t\t\t\t\t\tpractice.type === this.practiceTypes.find(t => t.id === this.currentPracticeType)?.name\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn practices.sort((a, b) => new Date(b.date) - new Date(a.date));\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync onLoad() {\r\n\t\t\tthis.checkGroupAccess();\r\n\t\t\tawait this.initializeData();\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\tif (!this.$store.state.user.token) {\t\t\t\r\n\t\t\t} else {\r\n\t\t\t\tthis.$store.dispatch('refreshUserMember');\r\n\t\t\t}\r\n\t\t\t// 每次显示页面时都检查权限，确保权限状态是最新的\r\n\t\t\t// 但首先确保用户数据已经从本地存储恢复\r\n\t\t\tthis.$store.commit('initUserData');\r\n\r\n\t\t\tthis.checkGroupAccess();\r\n\t\t\t// 刷新静态数据\r\n\t\t\tif (this.hasGroupPermission) {\r\n\t\t\t\tif(this.second==0){\r\n\t\t\t\t\tthis.initializeData();\r\n\t\t\t\t\tthis.second++;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.loadStaticGroupData();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 滚动到指定分类\r\n\t\t\tscrollToCategory(categoryIndex) {\r\n\t\t\t\tthis.activeNavIndex = categoryIndex;\r\n\r\n\t\t\t\t// 计算目标位置\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery.select(`#category-${categoryIndex}`).boundingClientRect((data) => {\r\n\t\t\t\t\tif (data) {\r\n\t\t\t\t\t\tthis.scrollTop = data.top - 100; // 减去导航栏高度\r\n\t\t\t\t\t}\r\n\t\t\t\t}).exec();\r\n\t\t\t},\r\n\r\n\t\t\t// 监听内容滚动\r\n\t\t\tonContentScroll(e) {\r\n\t\t\t\tconst scrollTop = e.detail.scrollTop;\r\n\r\n\t\t\t\t// 根据滚动位置更新活跃的导航项和吸顶状态\r\n\t\t\t\tthis.updateActiveNav(scrollTop);\r\n\t\t\t},\r\n\r\n\t\t\t// 更新活跃的导航项\r\n\t\t\tupdateActiveNav(scrollTop) {\r\n\t\t\t\t// 根据滚动位置计算当前应该高亮的导航项\r\n\t\t\t\t// 简化实现：每200px切换一个分类\r\n\t\t\t\tconst newIndex = Math.floor(scrollTop / 200);\r\n\t\t\t\tif (newIndex !== this.activeNavIndex && newIndex < this.currentCategoryList.length) {\r\n\t\t\t\t\tthis.activeNavIndex = newIndex;\r\n\t\t\t\t\tthis.stickyIndex = newIndex;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 进入课程学习\r\n\t\t\tenterLesson(category, lesson) {\r\n\t\t\t\tconsole.log('进入课程:', category.title, lesson.title);\r\n\r\n\t\t\t\t// 跳转到美化的播放页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/groups/lesson-player?lessonId=${lesson.id}&categoryId=${category.id}&categoryName=${encodeURIComponent(category.title)}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 检查用户权限\r\n\t\t\tcheckPermission() {\r\n\t\t\t\tconsole.log('=== 开始检查小组权限 ===');\r\n\r\n\t\t\t\t// 检查用户是否登录\r\n\t\t\t\tconst userToken = this.$store.state.user.token;\r\n\t\t\t\tconst userInfo = this.$store.state.user.userInfo;\r\n\t\t\t\tconst hasLogin = this.$store.getters.hasLogin;\r\n\r\n\t\t\t\tconsole.log('权限检查数据:', {\r\n\t\t\t\t\tuserToken: userToken ? '存在' : '不存在',\r\n\t\t\t\t\tuserInfo: userInfo,\r\n\t\t\t\t\thasLogin: hasLogin\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (!userToken && !hasLogin) {\r\n\t\t\t\t\tconsole.log('用户未登录，拒绝访问');\r\n\t\t\t\t\tthis.hasGroupPermission = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查用户是否有小组权限\r\n\t\t\t\tthis.checkGroupAccess();\r\n\t\t\t},\r\n\r\n\t\t\t// 检查小组访问权限 - 检查会员状态\r\n\t\t\tcheckGroupAccess() {\r\n\t\t\t\tconst userInfo = this.$store.state.user.userInfo;\r\n\t\t\t\t\t\t\tconst userMember = this.$store.state.user.member;\r\n\t\t\t\t\t\t\tconst userToken = this.$store.state.user.token;\r\n\t\t\t\t\t\t\tconst hasLogin = this.$store.getters.hasLogin;\r\n\t\t\t\t\t\t\tconst isLoggedIn = this.$store.getters.isLoggedIn;\r\n\t\t\t\t\r\n\t\t\t\t\t\t\tconsole.log('=== 检查小组访问权限 ===');\r\n\t\t\t\t\t\t\tconsole.log('用户Token:', userToken ? '存在' : '不存在');\r\n\t\t\t\t\t\t\tconsole.log('用户信息:', userInfo);\r\n\t\t\t\t\t\t\tconsole.log('会员信息:', userMember);\r\n\t\t\t\t\t\t\tconsole.log('登录状态:', { hasLogin, isLoggedIn });\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t// 从本地存储再次确认数据\r\n\t\t\t\t\t\t\tconst localToken = uni.getStorageSync('token');\r\n\t\t\t\t\t\t\tconst localUserInfo = uni.getStorageSync('userInfo');\r\n\t\t\t\t\t\t\tconst localMember = uni.getStorageSync('userMember');\r\n\t\t\t\t\t\t\tconsole.log('本地存储数据:', {\r\n\t\t\t\t\t\t\t\thasLocalToken: !!localToken,\r\n\t\t\t\t\t\t\t\thasLocalUserInfo: !!localUserInfo,\r\n\t\t\t\t\t\t\t\thasLocalMember: !!localMember\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t// 检查用户是否登录\r\n\t\t\t\t\t\t\tconst userLoggedIn = hasLogin || isLoggedIn || (userInfo && userInfo.id) || userToken;\r\n\t\t\t\t\r\n\t\t\t\t\t\t\tif (!userLoggedIn) {\r\n\t\t\t\t\t\t\t\tconsole.log('❌ 用户未登录，无权限访问小组');\r\n\t\t\t\t\t\t\t\tthis.hasGroupPermission = false;\r\n\t\t\t\t\t\t\t\tthis.showLoginTip();\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t// 检查用户是否有会员权限（优先使用内存中的数据，如果没有则使用本地存储）\r\n\t\t\t\t\t\t\tconst memberToCheck = userMember || localMember;\r\n\t\t\t\t\t\t\tconst hasMemberPermission = this.checkMemberPermission(memberToCheck);\r\n\t\t\t\t\r\n\t\t\t\t\t\t\tconsole.log('会员权限检查:', {\r\n\t\t\t\t\t\t\t\tmemberToCheck,\r\n\t\t\t\t\t\t\t\thasMemberPermission\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t\t\t\tif (hasMemberPermission) {\r\n\t\t\t\t\t\t\t\tconsole.log('✅ 用户有会员权限，可以访问小组');\r\n\t\t\t\t\t\t\t\tthis.hasGroupPermission = true;\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 如果内存中没有会员信息但本地有，则同步到内存\r\n\t\t\t\t\t\t\t\tif (!userMember && localMember) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('同步本地会员信息到内存');\r\n\t\t\t\t\t\t\t\t\tthis.$store.commit('setUserMember', localMember);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.log('❌ 用户没有会员权限，无法访问小组');\r\n\t\t\t\t\t\t\t\tthis.hasGroupPermission = false;\r\n\t\t\t\t\t\t\t\tthis.showMemberTip();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t\t\tconsole.log('=== 权限检查完成，结果:', this.hasGroupPermission, '===');\r\n\t\t\t},\r\n\r\n\t\t\t// 检查会员权限\r\n\t\t\tcheckMemberPermission(memberInfo) {\r\n\t\t\t\t// 特殊用户权限（用于测试）\r\n\t\t\t\tconst userInfo = this.$store.state.user.userInfo;\r\n\t\t\t\tif (userInfo && (userInfo.id === 576 || userInfo.name === '彭伟')) {\r\n\t\t\t\t\tconsole.log('✅ 特殊用户权限，允许访问');\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!memberInfo) {\r\n\t\t\t\t\tconsole.log('没有会员信息');\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查会员是否有效\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst endDate = new Date(memberInfo.end_date);\r\n\r\n\t\t\t\tconsole.log('会员有效期检查:', {\r\n\t\t\t\t\tnow: now.toISOString(),\r\n\t\t\t\t\tendDate: endDate.toISOString(),\r\n\t\t\t\t\tisValid: endDate > now\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 如果会员还在有效期内，则有权限\r\n\t\t\t\treturn endDate > now;\r\n\t\t\t},\r\n\r\n\t\t\t// 显示登录提示\r\n\t\t\tshowLoginTip() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 显示会员提示\r\n\t\t\tshowMemberTip() {\r\n\t\t\t\tconst userMember = this.$store.state.user.member;\r\n\t\t\t\tlet message = '需要会员权限才能访问学习小组';\r\n\r\n\t\t\t\tif (userMember) {\r\n\t\t\t\t\tconst endDate = new Date(userMember.end_date);\r\n\t\t\t\t\tconst now = new Date();\r\n\t\t\t\t\tif (endDate < now) {\r\n\t\t\t\t\t\tmessage = '您的会员已过期，请续费后访问学习小组';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: message,\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 3000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 初始化数据 - 使用静态数据\r\n\t\t\tinitializeData() {\r\n\t\t\t\tif (this.hasGroupPermission) {\r\n\t\t\t\t\t// 使用静态数据，不需要API调用\r\n\t\t\t\t\tthis.loadStaticGroupData();\r\n\t\t\t\t\t// 默认选择新概念教程（会自动加载对应的分类数据）\r\n\t\t\t\t\tthis.selectConceptTutorial();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载静态小组数据\r\n\t\t\tloadStaticGroupData() {\r\n\t\t\t\tconsole.log('📋 加载静态小组数据...');\r\n\r\n\t\t\t\t// 静态小组数据\r\n\t\t\t\t// this.groupList = [\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 1,\r\n\t\t\t\t// \t\tname: 'N5基础班',\r\n\t\t\t\t// \t\tdescription: '适合零基础学员',\r\n\t\t\t\t// \t\tlevel: 'N5',\r\n\t\t\t\t// \t\ticon: '/static/imgs/group-n5.png',\r\n\t\t\t\t// \t\tmemberCount: 28,\r\n\t\t\t\t// \t\tcourseCount: 12,\r\n\t\t\t\t// \t\tprogress: 65,\r\n\t\t\t\t// \t\tstatus: 'active'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 2,\r\n\t\t\t\t// \t\tname: 'N4进阶班',\r\n\t\t\t\t// \t\tdescription: '有一定基础的学员',\r\n\t\t\t\t// \t\tlevel: 'N4',\r\n\t\t\t\t// \t\ticon: '/static/imgs/group-n4.png',\r\n\t\t\t\t// \t\tmemberCount: 22,\r\n\t\t\t\t// \t\tcourseCount: 15,\r\n\t\t\t\t// \t\tprogress: 45,\r\n\t\t\t\t// \t\tstatus: 'active'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 3,\r\n\t\t\t\t// \t\tname: 'N3提高班',\r\n\t\t\t\t// \t\tdescription: '中级水平学员',\r\n\t\t\t\t// \t\tlevel: 'N3',\r\n\t\t\t\t// \t\ticon: '/static/imgs/group-n3.png',\r\n\t\t\t\t// \t\tmemberCount: 18,\r\n\t\t\t\t// \t\tcourseCount: 18,\r\n\t\t\t\t// \t\tprogress: 30,\r\n\t\t\t\t// \t\tstatus: 'active'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\tid: 4,\r\n\t\t\t\t// \t\tname: '商务日语班',\r\n\t\t\t\t// \t\tdescription: '职场日语专项训练',\r\n\t\t\t\t// \t\tlevel: '商务',\r\n\t\t\t\t// \t\ticon: '/static/imgs/group-business.png',\r\n\t\t\t\t// \t\tmemberCount: 15,\r\n\t\t\t\t// \t\tcourseCount: 10,\r\n\t\t\t\t// \t\tprogress: 20,\r\n\t\t\t\t// \t\tstatus: 'active'\r\n\t\t\t\t// \t}\r\n\t\t\t\t// ];\r\n\r\n\t\t\t\t// console.log('✅ 静态小组数据加载完成，共', this.groupList.length, '个小组');\r\n\t\t\t\tthis.$http.get(\"v1/course/getGroups\").then(res => {\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.groupList = res.data.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 模拟新概念教程API接口\r\n\t\t\tloadConceptTutorialData() {\r\n\t\t\t\t// console.log('📡 模拟调用新概念教程API...');\r\n\r\n\t\t\t\t// // 模拟API延迟\r\n\t\t\t\t// await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n\t\t\t\t// // 返回新概念教程的分类数据\r\n\t\t\t\t// return {\r\n\t\t\t\t// \tsuccess: true,\r\n\t\t\t\t// \tdata: {\r\n\t\t\t\t// \t\tcategories: this.conceptCategoryList,\r\n\t\t\t\t// \t\ttotalLessons: this.conceptCategoryList.reduce((total, cat) => total + cat.lessons.length, 0),\r\n\t\t\t\t// \t\tcompletedLessons: this.conceptCategoryList.reduce((total, cat) =>\r\n\t\t\t\t// \t\t\ttotal + cat.lessons.filter(lesson => lesson.completed).length, 0\r\n\t\t\t\t// \t\t)\r\n\t\t\t\t// \t}\r\n\t\t\t\t// };\r\n\t\t\t\tthis.$http.get(\"v1/course/getGroupsCourse\").then(res => {\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.conceptCategoryList = res.data.data.class\r\n\t\t\t\t\t\tthis.currentCategoryList = res.data.data.class\r\n\t\t\t\t\t\tthis.currentCategoryList.forEach(item => {\r\n\t\t\t\t\t\t\titem.expanded = false; // 添加新字段及其值\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 模拟小组课程API接口\r\n\t\t\tloadGroupCourseData(groupId) {\r\n\t\t\t\tconsole.log('📡 模拟调用小组课程API，小组ID:', groupId);\r\n\r\n\t\t\t\t// // 模拟API延迟\r\n\t\t\t\t// await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n\t\t\t\t// // 根据不同小组返回不同的课程数据\r\n\t\t\t\t// let categoryData = JSON.parse(JSON.stringify(this.groupCategoryList)); // 深拷贝\r\n\r\n\t\t\t\t// // 根据小组级别调整课程内容\r\n\t\t\t\t// if (groupId === 2) { // N4进阶班\r\n\t\t\t\t// \tcategoryData = categoryData.map(cat => ({\r\n\t\t\t\t// \t\t...cat,\r\n\t\t\t\t// \t\tname: cat.name.replace('N5', 'N4'),\r\n\t\t\t\t// \t\tlessons: cat.lessons.map(lesson => ({\r\n\t\t\t\t// \t\t\t...lesson,\r\n\t\t\t\t// \t\t\tsubtitle: lesson.subtitle + '（进阶）'\r\n\t\t\t\t// \t\t}))\r\n\t\t\t\t// \t}));\r\n\t\t\t\t// } else if (groupId === 3) { // N3提高班\r\n\t\t\t\t// \tcategoryData = categoryData.map(cat => ({\r\n\t\t\t\t// \t\t...cat,\r\n\t\t\t\t// \t\tname: cat.name.replace('N5', 'N3'),\r\n\t\t\t\t// \t\tlessons: cat.lessons.map(lesson => ({\r\n\t\t\t\t// \t\t\t...lesson,\r\n\t\t\t\t// \t\t\tsubtitle: lesson.subtitle + '（提高）'\r\n\t\t\t\t// \t\t}))\r\n\t\t\t\t// \t}));\r\n\t\t\t\t// }\r\n\r\n\t\t\t\t// return {\r\n\t\t\t\t// \tsuccess: true,\r\n\t\t\t\t// \tdata: {\r\n\t\t\t\t// \t\tcategories: categoryData,\r\n\t\t\t\t// \t\tgroupInfo: this.groupList.find(g => g.id === groupId),\r\n\t\t\t\t// \t\ttotalLessons: categoryData.reduce((total, cat) => total + cat.lessons.length, 0),\r\n\t\t\t\t// \t\tcompletedLessons: categoryData.reduce((total, cat) =>\r\n\t\t\t\t// \t\t\ttotal + cat.lessons.filter(lesson => lesson.completed).length, 0\r\n\t\t\t\t// \t\t)\r\n\t\t\t\t// \t}\r\n\t\t\t\t// };\r\n\t\t\t\tthis.$http.get(\"v1/course/getGroupsCourse\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid: groupId\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.groupCategoryList = res.data.data.class\r\n\t\t\t\t\t\tthis.currentCategoryList = res.data.data.class\r\n\t\t\t\t\t\tthis.currentCategoryList.forEach(item => {\r\n\t\t\t\t\t\t\titem.expanded = false; // 添加新字段及其值\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 进入新概念分类学习 - 静态版本\r\n\t\t\tenterConceptCategory(category) {\r\n\t\t\t\tconsole.log('进入新概念分类:', category);\r\n\r\n\t\t\t\t// 显示提示信息\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `即将开放${category.name}课程`,\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 可以跳转到课程列表页面（如果有的话）\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t//     url: `/pages/concept/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\r\n\t\t\t// 开始新概念学习 - 静态版本\r\n\t\t\tstartConceptLearning() {\r\n\t\t\t\tconsole.log('开始新概念学习');\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '开始学习：口语 - 第一课',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 可以跳转到具体的课程页面\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t//     url: '/pages/concept/lesson?lessonId=1&isFirst=true'\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\r\n\t\t\t// 继续新概念学习 - 静态版本\r\n\t\t\tcontinueConceptLearning() {\r\n\t\t\t\tconsole.log('继续新概念学习');\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '继续学习：语法 - 第二课',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 可以跳转到上次学习的课程\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t//     url: '/pages/concept/lesson?lessonId=7&continue=true'\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\r\n\t\t\t// 查看新概念学习进度 - 静态版本\r\n\t\t\tviewConceptProgress() {\r\n\t\t\t\tconsole.log('查看学习进度');\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '学习进度：已完成8/30课时',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 可以跳转到进度页面\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t//     url: '/pages/concept/progress'\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\r\n\t\t\t// 选择新概念教程\r\n\t\t\tselectConceptTutorial() {\r\n\t\t\t\tthis.selectedGroupId = 'concept';\r\n\t\t\t\tthis.selectedGroup = null;\r\n\t\t\t\tthis.selectedGroupIndex = -1;\r\n\t\t\t\tthis.loadConceptTutorialData();\r\n\t\t\t\t// 模拟调用新概念教程API\r\n\t\t\t\t// try {\r\n\t\t\t\t// \tconst response = await this.loadConceptTutorialData();\r\n\t\t\t\t// \tif (response.success) {\r\n\t\t\t\t// \t\tthis.currentCategoryList = response.data.categories;\r\n\t\t\t\t// \t\tconsole.log('✅ 新概念教程数据加载成功:', response.data);\r\n\t\t\t\t// \t}\r\n\t\t\t\t// } catch (error) {\r\n\t\t\t\t// \tconsole.error('❌ 加载新概念教程数据失败:', error);\r\n\t\t\t\t// \t// 降级使用静态数据\r\n\t\t\t\t// \tthis.currentCategoryList = this.conceptCategoryList;\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\r\n\t\t\t// 选择小组用于详情显示（左右联动）\r\n\t\t\tselectGroupForDetail(group, index) {\r\n\t\t\t\tthis.selectedGroupId = group.id;\r\n\t\t\t\tthis.selectedGroup = group;\r\n\t\t\t\tthis.selectedGroupIndex = index;\r\n\t\t\t\tthis.loadGroupCourseData(group.id);\r\n\t\t\t\t// 模拟调用小组课程API\r\n\t\t\t\t// try {\r\n\t\t\t\t// \tconst response = await this.loadGroupCourseData(group.id);\r\n\t\t\t\t// \tif (response.success) {\r\n\t\t\t\t// \t\tthis.currentCategoryList = response.data.categories;\r\n\t\t\t\t// \t\tconsole.log('✅ 小组课程数据加载成功:', group.name, response.data);\r\n\t\t\t\t// \t}\r\n\t\t\t\t// } catch (error) {\r\n\t\t\t\t// \tconsole.error('❌ 加载小组课程数据失败:', error);\r\n\t\t\t\t// \t// 降级使用静态数据\r\n\t\t\t\t// \tthis.currentCategoryList = this.groupCategoryList;\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\r\n\t\t\t// 选择小组 - 显示操作选择弹窗（保留原有功能）\r\n\t\t\tselectGroup(group) {\r\n\t\t\t\tthis.selectedGroupId = group.id;\r\n\t\t\t\tthis.selectedGroup = group;\r\n\r\n\t\t\t\t// 显示操作选择弹窗\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\ttitle: `${group.name} - 请选择操作`,\r\n\t\t\t\t\titemList: [\r\n\t\t\t\t\t\t'🎥 查看课程回顾',\r\n\t\t\t\t\t\t'✍️ 进入练习题库',\r\n\t\t\t\t\t\t'📊 查看小组详情',\r\n\t\t\t\t\t\t'👥 查看小组成员'\r\n\t\t\t\t\t],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tswitch (res.tapIndex) {\r\n\t\t\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t\t\tthis.quickViewReview(group);\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\t\t\tthis.quickViewPractice(group);\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\t\t\tthis.enterGroup(group);\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 3:\r\n\t\t\t\t\t\t\t\tthis.viewGroupMembers(group);\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t// 用户取消选择，重置选中状态\r\n\t\t\t\t\t\tthis.selectedGroupId = null;\r\n\t\t\t\t\t\tthis.selectedGroup = null;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 切换视图\r\n\t\t\tswitchView(view) {\r\n\t\t\t\tthis.currentView = view;\r\n\t\t\t},\r\n\r\n\t\t\t// 日期选择\r\n\t\t\tonDateChange(e) {\r\n\t\t\t\tthis.selectedDate = e.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\t// 选择练习类型\r\n\t\t\tselectPracticeType(type) {\r\n\t\t\t\tthis.currentPracticeType = type.id;\r\n\t\t\t},\r\n\r\n\t\t\t// 快速查看课程回顾 - 直接跳转\r\n\t\t\tquickViewReview(group) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在进入课程回顾',\r\n\t\t\t\t\ticon: 'loading',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/groups/course-review?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\r\n\t\t\t// 快速查看练习 - 直接跳转\r\n\t\t\tquickViewPractice(group) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在进入练习题库',\r\n\t\t\t\t\ticon: 'loading',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/groups/practice?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\r\n\t\t\t// 播放课程回顾视频\r\n\t\t\tplayCourseReview(course) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/groups/course-review?courseId=${course.id}&groupId=${course.groupId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 开始练习\r\n\t\t\tstartPractice(practice) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/groups/practice?practiceId=${practice.id}&groupId=${practice.groupId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 查看小组成员\r\n\t\t\tviewGroupMembers(group) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在加载成员列表',\r\n\t\t\t\t\ticon: 'loading',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/groups/members?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\r\n\t\t\t// 获取小组颜色\r\n\t\t\tgetGroupColor(index) {\r\n\t\t\t\tconst colors = [\r\n\t\t\t\t\t'linear-gradient(135deg, #FF6B6B, #EE4437)', // 红色\r\n\t\t\t\t\t'linear-gradient(135deg, #4ECDC4, #44A08D)', // 青色\r\n\t\t\t\t\t'linear-gradient(135deg, #45B7D1, #96C93D)', // 蓝绿色\r\n\t\t\t\t\t'linear-gradient(135deg, #FFA726, #FB8C00)', // 橙色\r\n\t\t\t\t\t'linear-gradient(135deg, #AB47BC, #8E24AA)' // 紫色\r\n\t\t\t\t];\r\n\t\t\t\treturn colors[index % colors.length];\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 跳转到登录页面\r\n\t\t\tgoToLogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 联系管理员\r\n\t\t\tcontactAdmin() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '联系管理员',\r\n\t\t\t\t\tcontent: '请通过以下方式联系管理员开通权限：\\n\\n微信：admin123\\n电话：400-123-4567\\n邮箱：<EMAIL>',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 进入小组详情\r\n\t\t\tasync enterGroup(group) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 检查用户是否已登录\r\n\t\t\t\t\tconst userToken = uni.getStorageSync('token');\r\n\t\t\t\t\tif (!userToken) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '需要登录',\r\n\t\t\t\t\t\t\tcontent: '请先登录后再查看小组详情',\r\n\t\t\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 跳转到小组详情页面\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/groups/group-detail?groupId=${group.id}&groupName=${group.name}`\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('进入小组详情失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '进入失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加入小组\r\n\t\t\tasync joinGroup(group) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 检查用户是否已登录\r\n\t\t\t\t\tconst userToken = uni.getStorageSync('token');\r\n\t\t\t\t\tif (!userToken) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '需要登录',\r\n\t\t\t\t\t\t\tcontent: '请先登录后再加入小组',\r\n\t\t\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 静态版本 - 直接显示成功提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: `成功加入${group.name}`,\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 刷新静态数据\r\n\t\t\t\t\tthis.loadStaticGroupData();\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加入小组失败:', error);\r\n\t\t\t\t\t// API服务已经显示了错误提示，这里不需要重复显示\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t/* ===== 基础页面样式 ===== */\r\n\t.clean-groups-page {\r\n\t\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\r\n\t.page-content {\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t/* ===== 页面头部样式 ===== */\r\n\t.page-header {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 0 0 30rpx 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.header-content {\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.title-section {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.page-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tdisplay: block;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.page-subtitle {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.stats-section {\r\n\t\tdisplay: flex;\r\n\t\tgap: 25rpx;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.stat-number {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #667eea;\r\n\t\tdisplay: block;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.stat-label {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t/* ===== 左右联动布局 ===== */\r\n\t.split-layout {\r\n\t\tdisplay: flex;\r\n\t\theight: calc(100vh - 200rpx);\r\n\t\tbackground: white;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\toverflow: hidden;\r\n\t\tmargin: 0 20rpx;\r\n\t\tbox-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t\t/* iPad滚动优化 */\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t\ttouch-action: pan-y;\r\n\t}\r\n\r\n\t/* ===== 左侧面板 ===== */\r\n\t.left-panel {\r\n\t\twidth: 200rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-right: 1rpx solid #e9ecef;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.panel-header {\r\n\t\tpadding: 25rpx 15rpx 20rpx;\r\n\t\tbackground: white;\r\n\t\tborder-bottom: 1rpx solid #e9ecef;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.panel-title {\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.panel-subtitle {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.group-list {\r\n\t\tflex: 1;\r\n\t\tpadding: 10rpx 0;\r\n\t\t/* iPad滚动优化 */\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t\ttouch-action: pan-y;\r\n\t\toverscroll-behavior: contain;\r\n\t}\r\n\r\n\t/* ===== 小组列表项 ===== */\r\n\t.group-item {\r\n\t\tmargin: 0 10rpx 15rpx;\r\n\t\tbackground: white;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx 15rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tborder: 2rpx solid transparent;\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.group-item.active {\r\n\t\tborder-color: #667eea;\r\n\t\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));\r\n\t\ttransform: scale(1.05);\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\r\n\t}\r\n\r\n\t.group-item:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.simple-group-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\r\n\t.group-level-badge {\r\n\t\tcolor: white;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 18rpx;\r\n\t\tfont-weight: 700;\r\n\t\tmin-width: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\r\n\t}\r\n\r\n\t.simple-group-name {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.3;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t.simple-status-dot {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: absolute;\r\n\t\ttop: 10rpx;\r\n\t\tright: 10rpx;\r\n\t}\r\n\r\n\t.simple-status-dot.active {\r\n\t\tbackground: #4CAF50;\r\n\t\tbox-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);\r\n\t}\r\n\r\n\t.simple-status-dot.completed {\r\n\t\tbackground: #2196F3;\r\n\t\tbox-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);\r\n\t}\r\n\r\n\t.simple-status-dot.pending {\r\n\t\tbackground: #FF9800;\r\n\t\tbox-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);\r\n\t}\r\n\r\n\t.groups-container {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.page-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t.groups-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 30rpx;\r\n\t}\r\n\r\n\t.group-card {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.group-icon {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #f5f5f5;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.group-icon image {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t}\r\n\r\n\t.group-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.group-name {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.group-desc {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.group-stats {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tbackground: #f8f8f8;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.group-status {\r\n\t\tposition: absolute;\r\n\t\ttop: 20rpx;\r\n\t\tright: 20rpx;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.group-status.active {\r\n\t\tbackground: #e8f5e8;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.group-status.completed {\r\n\t\tbackground: #f0f0f0;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.group-status.pending {\r\n\t\tbackground: #fff7e6;\r\n\t\tcolor: #fa8c16;\r\n\t}\r\n\r\n\t/* 简洁清爽的样式 */\r\n\t.clean-groups-page {\r\n\t\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\r\n\t.page-content {\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t/* 简洁的头部样式 */\r\n\t.page-header {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 0 0 30rpx 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.header-content {\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.title-section {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.page-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tdisplay: block;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.page-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.stats-section {\r\n\t\tdisplay: flex;\r\n\t\tgap: 30rpx;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.stat-number {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #667eea;\r\n\t\tdisplay: block;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.stat-label {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t.header-content-wrapper {\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tpadding: 60rpx 30rpx 50rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.title-section-enhanced {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.title-icon-container {\r\n\t\tposition: relative;\r\n\t\tmargin-right: 25rpx;\r\n\t}\r\n\r\n\t.icon-bg-circle {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 0.2;\r\n\t}\r\n\r\n\t.title-icon-enhanced {\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tfont-size: 70rpx;\r\n\t\tdisplay: block;\r\n\t\ttext-align: center;\r\n\t\tline-height: 100rpx;\r\n\t}\r\n\r\n\t.icon-pulse {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder: 3rpx solid rgba(102, 126, 234, 0.3);\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: iconPulse 3s ease-in-out infinite;\r\n\t}\r\n\r\n\t@keyframes iconPulse {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translate(-50%, -50%) scale(1);\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translate(-50%, -50%) scale(1.2);\r\n\t\t\topacity: 0.3;\r\n\t\t}\r\n\t}\r\n\r\n\t.title-text-enhanced {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.main-title-enhanced {\r\n\t\tfont-size: 52rpx;\r\n\t\tfont-weight: 800;\r\n\t\tcolor: #333;\r\n\t\tline-height: 1.2;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tbackground: linear-gradient(135deg, #333, #667eea);\r\n\t\tbackground-clip: text;\r\n\t\t-webkit-background-clip: text;\r\n\t\t-webkit-text-fill-color: transparent;\r\n\t}\r\n\r\n\t.sub-title-enhanced {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.4;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.title-decoration {\r\n\t\twidth: 80rpx;\r\n\t\theight: 6rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tborder-radius: 3rpx;\r\n\t\tanimation: decorationGlow 2s ease-in-out infinite;\r\n\t}\r\n\r\n\t@keyframes decorationGlow {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\tbox-shadow: 0 0 10rpx rgba(102, 126, 234, 0.3);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\tbox-shadow: 0 0 20rpx rgba(102, 126, 234, 0.6);\r\n\t\t}\r\n\t}\r\n\r\n\t.stats-section-enhanced {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.stat-card-enhanced {\r\n\t\tposition: relative;\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 25rpx 20rpx;\r\n\t\tmin-width: 120rpx;\r\n\t\ttext-align: center;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\r\n\t\tbox-shadow:\r\n\t\t\t0 8rpx 25rpx rgba(0, 0, 0, 0.1),\r\n\t\t\tinset 0 1rpx 0 rgba(255, 255, 255, 0.6);\r\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.stat-card-enhanced:hover {\r\n\t\ttransform: translateY(-5rpx);\r\n\t\tbox-shadow:\r\n\t\t\t0 15rpx 35rpx rgba(0, 0, 0, 0.15),\r\n\t\t\tinset 0 1rpx 0 rgba(255, 255, 255, 0.8);\r\n\t}\r\n\r\n\t.stat-icon-bg {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.stat-number-enhanced {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 800;\r\n\t\tcolor: #333;\r\n\t\tline-height: 1;\r\n\t\tmargin-bottom: 5rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tbackground-clip: text;\r\n\t\t-webkit-background-clip: text;\r\n\t\t-webkit-text-fill-color: transparent;\r\n\t}\r\n\r\n\t.stat-label-enhanced {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.stat-sparkle {\r\n\t\tposition: absolute;\r\n\t\ttop: 10rpx;\r\n\t\tright: 10rpx;\r\n\t\twidth: 8rpx;\r\n\t\theight: 8rpx;\r\n\t\tbackground: #667eea;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: sparkle 2s ease-in-out infinite;\r\n\t}\r\n\r\n\t@keyframes sparkle {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\topacity: 0.3;\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: scale(1.5);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 装饰元素 */\r\n\t.header-decorations {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpointer-events: none;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.deco-circle {\r\n\t\tposition: absolute;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t}\r\n\r\n\t.deco-1 {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\ttop: 20rpx;\r\n\t\tright: 80rpx;\r\n\t\tanimation: float1 6s ease-in-out infinite;\r\n\t}\r\n\r\n\t.deco-2 {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tbottom: 30rpx;\r\n\t\tleft: 60rpx;\r\n\t\tanimation: float2 8s ease-in-out infinite;\r\n\t}\r\n\r\n\t.deco-triangle {\r\n\t\tposition: absolute;\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tborder-left: 15rpx solid transparent;\r\n\t\tborder-right: 15rpx solid transparent;\r\n\t\tborder-bottom: 25rpx solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\r\n\t.deco-3 {\r\n\t\ttop: 60rpx;\r\n\t\tleft: 40rpx;\r\n\t\tanimation: float3 7s ease-in-out infinite;\r\n\t}\r\n\r\n\t.deco-square {\r\n\t\tposition: absolute;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\ttransform: rotate(45deg);\r\n\t}\r\n\r\n\t.deco-4 {\r\n\t\twidth: 20rpx;\r\n\t\theight: 20rpx;\r\n\t\tbottom: 60rpx;\r\n\t\tright: 40rpx;\r\n\t\tanimation: float4 5s ease-in-out infinite;\r\n\t}\r\n\r\n\t@keyframes float1 {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0) rotate(0deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-20rpx) rotate(180deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes float2 {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateX(0) rotate(0deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateX(15rpx) rotate(-180deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes float3 {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0) rotate(0deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(10rpx) rotate(120deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes float4 {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0) rotate(45deg);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-15rpx) rotate(225deg);\r\n\t\t}\r\n\t}\r\n\r\n\t.title-icon {\r\n\t\tfont-size: 60rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.title-text {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #333;\r\n\t\tline-height: 1.2;\r\n\t}\r\n\r\n\t.sub-title {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t.stats-section {\r\n\t\tdisplay: flex;\r\n\t\tgap: 30rpx;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.stat-number {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #667eea;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.stat-label {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t/* 左右联动布局 */\r\n\t.split-layout {\r\n\t\tdisplay: flex;\r\n\t\theight: calc(100vh - 200rpx);\r\n\t\tbackground: white;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\toverflow: hidden;\r\n\t\tmargin: 0 20rpx;\r\n\t\tbox-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t/* 左侧面板 */\r\n\t.left-panel {\r\n\t\twidth: 200rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-right: 1rpx solid #e9ecef;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.panel-header {\r\n\t\tpadding: 25rpx 15rpx 20rpx;\r\n\t\tbackground: white;\r\n\t\tborder-bottom: 1rpx solid #e9ecef;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.panel-title {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.panel-subtitle {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.group-list {\r\n\t\tflex: 1;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t/* 简化的小组列表项 */\r\n\t.group-item {\r\n\t\tmargin: 0 10rpx 15rpx;\r\n\t\tbackground: white;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx 15rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tborder: 2rpx solid transparent;\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.group-item.active {\r\n\t\tborder-color: #667eea;\r\n\t\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));\r\n\t\ttransform: scale(1.05);\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\r\n\t}\r\n\r\n\t.group-item:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.simple-group-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\r\n\t.group-level-badge {\r\n\t\tcolor: white;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 700;\r\n\t\tmin-width: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\r\n\t}\r\n\r\n\t.simple-group-name {\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.3;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t.simple-status-dot {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: absolute;\r\n\t\ttop: 10rpx;\r\n\t\tright: 10rpx;\r\n\t}\r\n\r\n\t.simple-status-dot.active {\r\n\t\tbackground: #4CAF50;\r\n\t\tbox-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);\r\n\t}\r\n\r\n\t.simple-status-dot.completed {\r\n\t\tbackground: #2196F3;\r\n\t\tbox-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);\r\n\t}\r\n\r\n\t.simple-status-dot.pending {\r\n\t\tbackground: #FF9800;\r\n\t\tbox-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);\r\n\t}\r\n\r\n\t/* 新概念教程样式 */\r\n\t.concept-tutorial-item {\r\n\t\tmargin: 0 10rpx 20rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 25rpx 15rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tborder: 2rpx solid transparent;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.concept-tutorial-item::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.concept-tutorial-item.active {\r\n\t\ttransform: scale(1.05);\r\n\t\tbox-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);\r\n\t\tborder-color: rgba(255, 255, 255, 0.3);\r\n\t}\r\n\r\n\t.concept-tutorial-item:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.concept-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.concept-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.concept-title {\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: white;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.3;\r\n\t}\r\n\r\n\t.concept-badge {\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tcolor: white;\r\n\t\tpadding: 4rpx 8rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 18rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 新概念教程详情页面 */\r\n\t.concept-detail-content {\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.concept-detail-header {\r\n\t\tposition: relative;\r\n\t\theight: 200rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.concept-bg {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t}\r\n\r\n\t.concept-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(0, 0, 0, 0.2);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.concept-detail-icon {\r\n\t\tfont-size: 60rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.concept-detail-info {\r\n\t\tflex: 1;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.concept-detail-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.concept-detail-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\topacity: 0.9;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.concept-progress-info {\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tpadding: 8rpx 15rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t.progress-info-text {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t/* 学习分类 */\r\n\t.concept-categories {\r\n\t\tpadding: 30rpx;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.categories-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t/* 小组头部样式 */\r\n\t.group-header {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.group-info {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.group-avatar {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-right: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.group-avatar image {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t.group-details {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.group-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.group-desc {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.group-stats {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.stat-item {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t\tbackground: #f8f9fa;\r\n\t\tpadding: 4rpx 12rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\t\r\n\t}\r\n\r\n\t.group-categories {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.group-categories .categories-title {\r\n\t\tpadding: 20rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t/* 吸顶分类导航样式 */\r\n\t.sticky-nav {\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tposition: sticky;\r\n\t\ttop: 0;\r\n\t\tz-index: 10;\r\n\t}\r\n\r\n\t.nav-scroll {\r\n\t\theight: 88rpx;\r\n\t}\r\n\r\n\t.nav-items {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0 20rpx;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 16rpx 24rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.nav-item.active {\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.nav-icon {\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\r\n\t.nav-text {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.nav-item.active .nav-icon,\r\n\t.nav-item.active .nav-text {\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t/* 滚动内容区域样式 */\r\n\t.content-scroll {\r\n\t\theight: 600rpx; /* 固定高度，可滚动 */\r\n\t\tbackground: #f8f9fa;\r\n\t\t/* iPad滚动优化 */\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t\ttouch-action: pan-y;\r\n\t\toverscroll-behavior: contain;\r\n\t}\r\n\r\n\t.scroll-content {\r\n\t\tpadding-bottom: 40rpx;\r\n\t}\r\n\r\n\t.category-section {\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t/* 分类标题（吸顶）样式 */\r\n\t.section-header {\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tposition: sticky;\r\n\t\ttop: 0;\r\n\t\tz-index: 5;\r\n\t}\r\n\r\n\t.section-header.sticky {\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.header-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 24rpx 30rpx;\r\n\t}\r\n\r\n\t.header-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.header-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 16rpx;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.header-right {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.lesson-count {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t\tbackground: #f0f0f0;\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t/* 课程列表样式 */\r\n\t.section-lessons {\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.lesson-card {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 24rpx 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f8f9fa;\r\n\t\ttransition: background-color 0.3s ease;\r\n\t}\r\n\r\n\t.lesson-card:active {\r\n\t\tbackground: #f8f9fa;\r\n\t}\r\n\r\n\t.lesson-card:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.lesson-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.lesson-number {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);\r\n\t\tcolor: #1890ff;\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 24rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.lesson-info {\r\n\t\tflex: 1;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.lesson-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.lesson-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tdisplay: block;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.lesson-right {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.lesson-status {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.lesson-status.completed {\r\n\t\tbackground: #52c41a;\r\n\t}\r\n\r\n\t.lesson-status.pending {\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder: 2rpx solid #d9d9d9;\r\n\t}\r\n\r\n\t.status-icon {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.lesson-status.completed .status-icon {\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.lesson-status.pending .status-icon {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.simple-category-item {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.category-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.category-header:active {\r\n\t\tbackground: #e9ecef;\r\n\t}\r\n\r\n\t.category-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.category-icon {\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\r\n\t.category-name {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.category-toggle {\r\n\t\tpadding: 8rpx;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.category-toggle.expanded {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\r\n\t.toggle-icon {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t/* 课程列表样式 */\r\n\t.lessons-list {\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.lesson-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 16rpx 20rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\ttransition: background-color 0.3s ease;\r\n\t}\r\n\r\n\t.lesson-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.lesson-item:active {\r\n\t\tbackground: #f8f9fa;\r\n\t}\r\n\r\n\t.lesson-number {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-right: 16rpx;\r\n\t}\r\n\r\n\t.lesson-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.lesson-title {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\r\n\t.lesson-subtitle {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.lesson-status {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.lesson-status.completed {\r\n\t\tbackground: #52c41a;\r\n\t}\r\n\r\n\t.lesson-status.pending {\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder: 2rpx solid #d9d9d9;\r\n\t}\r\n\r\n\t.status-icon {\r\n\t\tfont-size: 16rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.lesson-status.pending .status-icon {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t/* 快速操作 */\r\n\t.concept-actions {\r\n\t\tpadding: 0 30rpx 30rpx;\r\n\t}\r\n\r\n\t.concept-action-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.concept-action-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 15rpx;\r\n\t}\r\n\r\n\t.concept-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.concept-btn.primary {\r\n\t\tbackground: linear-gradient(135deg, #4CAF50, #45A049);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.concept-btn.secondary {\r\n\t\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.concept-btn.tertiary {\r\n\t\tbackground: linear-gradient(135deg, #FF9800, #F57C00);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.concept-btn:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\r\n\t.concept-btn-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.concept-btn-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.concept-btn-title {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.concept-btn-desc {\r\n\t\tfont-size: 22rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t/* 无权限页面样式 */\r\n\t.no-permission-page {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.permission-container {\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 80rpx 60rpx;\r\n\t\ttext-align: center;\r\n\t\tmax-width: 600rpx;\r\n\t\tmargin: 0 40rpx;\r\n\t\tbackdrop-filter: blur(20rpx);\r\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.permission-icon {\r\n\t\tfont-size: 120rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.permission-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.permission-desc {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tdisplay: block;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t.permission-hint {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\tdisplay: block;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t.permission-actions {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 30rpx;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t/* 小屏幕适配 */\r\n\t@media screen and (max-width: 750rpx) {\r\n\t\t.permission-actions {\r\n\t\t\tflex-direction: column;\r\n\t\t\tgap: 20rpx;\r\n\t\t}\r\n\r\n\t\t.btn-login,\r\n\t\t.btn-contact {\r\n\t\t\twidth: 280rpx;\r\n\t\t\tmax-width: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.btn-login,\r\n\t.btn-contact {\r\n\t\tpadding: 24rpx 48rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tborder: none;\r\n\t\tmin-width: 200rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.btn-login {\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tcolor: white;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\r\n\t}\r\n\r\n\t.btn-contact {\r\n\t\tbackground: #f8f9fa;\r\n\t\tcolor: #495057;\r\n\t\tborder: 2rpx solid #e9ecef;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn-login:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);\r\n\t}\r\n\r\n\t.btn-contact:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbackground: #e9ecef;\r\n\t}\r\n\r\n\t/* 右侧面板 */\r\n\t.right-panel {\r\n\t\tflex: 1;\r\n\t\tbackground: white;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.detail-content {\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 详情头部 */\r\n\t.detail-header {\r\n\t\tposition: relative;\r\n\t\theight: 200rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.detail-bg {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.detail-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(0, 0, 0, 0.3);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.detail-avatar {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.detail-avatar image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.detail-info {\r\n\t\tflex: 1;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.detail-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.detail-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\topacity: 0.9;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.detail-level {\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t/* 统计卡片 */\r\n\t.detail-stats {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 30rpx;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.stat-card-detail {\r\n\t\tflex: 1;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 25rpx 20rpx;\r\n\t\ttext-align: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.stat-card-detail:active {\r\n\t\tbackground: #e9ecef;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.stat-icon-detail {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.stat-number-detail {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #333;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.stat-label-detail {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t/* 进度详情 */\r\n\t.progress-detail {\r\n\t\tpadding: 0 30rpx 30rpx;\r\n\t}\r\n\r\n\t.progress-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.progress-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.progress-value {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #667eea;\r\n\t}\r\n\r\n\t.progress-bar-detail {\r\n\t\theight: 12rpx;\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder-radius: 6rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.progress-fill-detail {\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttransition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t}\r\n\r\n\t.progress-desc {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t/* 功能按钮 */\r\n\t.detail-actions {\r\n\t\tflex: 1;\r\n\t\tpadding: 0 30rpx 30rpx;\r\n\t}\r\n\r\n\t.action-row {\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.action-btn-large {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 25rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.action-btn-large.primary {\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn-large.secondary {\r\n\t\tbackground: linear-gradient(135deg, #4ECDC4, #44A08D);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn-large.tertiary {\r\n\t\tbackground: linear-gradient(135deg, #45B7D1, #96C93D);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn-large.quaternary {\r\n\t\tbackground: linear-gradient(135deg, #FFA726, #FB8C00);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn-large:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\r\n\t.btn-icon-large {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.btn-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.btn-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.btn-desc {\r\n\t\tfont-size: 22rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t.btn-arrow-large {\r\n\t\tfont-size: 24rpx;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t/* 空状态 */\r\n\t.empty-detail {\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 60rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.empty-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\topacity: 0.5;\r\n\t}\r\n\r\n\t.empty-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.empty-desc {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t/* 美化的小组卡片 */\r\n\t.beautiful-group-card {\r\n\t\tposition: relative;\r\n\t\tbackground: white;\r\n\t\tborder-radius: 25rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);\r\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.beautiful-group-card:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\r\n\t.beautiful-group-card.selected {\r\n\t\ttransform: translateY(-8rpx);\r\n\t\tbox-shadow: 0 20rpx 40rpx rgba(102, 126, 234, 0.2);\r\n\t}\r\n\r\n\t.card-decoration {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 0 25rpx 0 100rpx;\r\n\t\topacity: 0.1;\r\n\t}\r\n\r\n\t/* 美化的小组头部 */\r\n\t.beautiful-group-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.group-avatar-section {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.avatar-container {\r\n\t\tposition: relative;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.group-avatar-img {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.level-badge {\r\n\t\tposition: absolute;\r\n\t\tbottom: -8rpx;\r\n\t\tright: -8rpx;\r\n\t\tcolor: white;\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\r\n\t}\r\n\r\n\t.group-basic-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.group-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tline-height: 1.3;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.group-subtitle {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.status-indicator {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t}\r\n\r\n\t.status-dot {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\r\n\t.status-indicator.active .status-dot {\r\n\t\tbackground: #4CAF50;\r\n\t}\r\n\r\n\t.status-indicator.completed .status-dot {\r\n\t\tbackground: #2196F3;\r\n\t}\r\n\r\n\t.status-indicator.pending .status-dot {\r\n\t\tbackground: #FF9800;\r\n\t}\r\n\r\n\t.status-text {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 美化的统计数据 */\r\n\t.beautiful-stats {\r\n\t\tdisplay: flex;\r\n\t\tgap: 15rpx;\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.stat-card {\r\n\t\tflex: 1;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 20rpx 15rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.stat-card:active {\r\n\t\tbackground: #e9ecef;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.stat-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\r\n\t.stat-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.stat-value {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.stat-name {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 4rpx;\r\n\t}\r\n\r\n\t/* 美化的进度条 */\r\n\t.beautiful-progress {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.progress-label {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.progress-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.progress-percent {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.progress-track {\r\n\t\theight: 8rpx;\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder-radius: 4rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.progress-bar-fill {\r\n\t\theight: 100%;\r\n\t\tborder-radius: 4rpx;\r\n\t\ttransition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t}\r\n\r\n\t/* 美化的操作按钮 */\r\n\t.beautiful-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 15rpx;\r\n\t}\r\n\r\n\t.action-button {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 18rpx 20rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.action-button.primary {\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-button.secondary {\r\n\t\tbackground: #f8f9fa;\r\n\t\tcolor: #333;\r\n\t\tborder: 2rpx solid #e9ecef;\r\n\t}\r\n\r\n\t.action-button:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.action-button.primary:active {\r\n\t\tbackground: linear-gradient(135deg, #5a6fd8, #6a42a0);\r\n\t}\r\n\r\n\t.action-button.secondary:active {\r\n\t\tbackground: #e9ecef;\r\n\t}\r\n\r\n\t.btn-icon {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.btn-text {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.btn-arrow {\r\n\t\tfont-size: 20rpx;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t/* 操作提示样式 */\r\n\t.operation-tips {\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.tips-card {\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 25rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n\r\n\t.tips-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.tips-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.tips-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.tips-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.group-avatar {\r\n\t\tposition: relative;\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.group-level {\r\n\t\tposition: absolute;\r\n\t\tbottom: -10rpx;\r\n\t\tright: -10rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tcolor: white;\r\n\t\tpadding: 5rpx 10rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.group-status-badge {\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.stat-number {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.stat-label {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.progress-bar {\r\n\t\theight: 8rpx;\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder-radius: 4rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin: 15rpx 0;\r\n\t}\r\n\r\n\t.progress-fill {\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tborder-radius: 4rpx;\r\n\t\ttransition: width 0.3s ease;\r\n\t}\r\n\r\n\t.group-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 15rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\tflex: 1;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 15rpx;\r\n\t\ttext-align: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.action-icon {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.action-text {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.selected-group-content {\r\n\t\tbackground: white;\r\n\t\tmargin: 30rpx 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.content-header {\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\t\tcolor: white;\r\n\t\tpadding: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.content-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.view-toggle {\r\n\t\tdisplay: flex;\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.toggle-btn {\r\n\t\tpadding: 10rpx 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.toggle-btn.active {\r\n\t\tbackground: white;\r\n\t\tcolor: #667eea;\r\n\t}\r\n\r\n\t.date-filter {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t.date-picker {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 15rpx 20rpx;\r\n\t}\r\n\r\n\t.date-icon {\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.date-text {\r\n\t\tflex: 1;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.date-arrow {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.course-list {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\r\n\t.course-item {\r\n\t\tdisplay: flex;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.course-thumbnail {\r\n\t\tposition: relative;\r\n\t\twidth: 160rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.play-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tborder-radius: 50%;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.play-icon {\r\n\t\tcolor: white;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.course-duration {\r\n\t\tposition: absolute;\r\n\t\tbottom: 8rpx;\r\n\t\tright: 8rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t\tcolor: white;\r\n\t\tpadding: 4rpx 8rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\r\n\t.course-details {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.course-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.course-date {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.course-teacher {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.course-tags {\r\n\t\tdisplay: flex;\r\n\t\tgap: 8rpx;\r\n\t}\r\n\r\n\t.tag {\r\n\t\tbackground: #667eea;\r\n\t\tcolor: white;\r\n\t\tpadding: 4rpx 8rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039745665\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}