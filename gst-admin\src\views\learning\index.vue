<template>
  <div class="page-container">
    <div class="page-header">
      <h1>学习记录</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportData" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总学习人数" :value="stats.totalUsers" />
          <div class="stats-extra">
            <el-icon><User /></el-icon>
            <span>活跃学员</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总学习时长" :value="stats.totalDuration" suffix="小时" />
          <div class="stats-extra">
            <el-icon><Timer /></el-icon>
            <span>累计时长</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="完成课程数" :value="stats.completedCourses" />
          <div class="stats-extra">
            <el-icon><Check /></el-icon>
            <span>已完成</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="平均完成率" :value="stats.avgCompletionRate" suffix="%" />
          <div class="stats-extra">
            <el-icon><TrendCharts /></el-icon>
            <span>完成率</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="学员姓名">
          <el-input v-model="filters.userName" placeholder="输入学员姓名" clearable @change="loadLearningRecords" />
        </el-form-item>
        <el-form-item label="课程">
          <el-select v-model="filters.courseId" placeholder="选择课程" clearable @change="loadLearningRecords">
            <el-option 
              v-for="course in courses" 
              :key="course.id" 
              :label="course.title" 
              :value="course.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学习状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadLearningRecords">
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已暂停" value="paused" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="loadLearningRecords"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 学习记录列表 -->
    <el-card>
      <el-table :data="learningRecords" v-loading="loading" stripe>
        <el-table-column prop="userName" label="学员姓名" width="120" />
        <el-table-column prop="courseTitle" label="课程名称" min-width="200" />
        <el-table-column prop="progress" label="学习进度" width="120" align="center">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" :stroke-width="8" />
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="学习时长" width="100" align="center">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastStudyTime" label="最后学习时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.lastStudyTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="score" label="成绩" width="80" align="center">
          <template #default="{ row }">
            <span v-if="row.score !== null">{{ row.score }}分</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="viewDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button link size="small" @click="viewProgress(row)">
              <el-icon><TrendCharts /></el-icon>
              进度
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadLearningRecords"
          @current-change="loadLearningRecords"
        />
      </div>
    </el-card>

    <!-- 学习详情对话框 -->
    <el-dialog
      title="学习详情"
      v-model="showDetailDialog"
      width="800px"
    >
      <div v-if="selectedRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学员姓名">{{ selectedRecord.userName }}</el-descriptions-item>
          <el-descriptions-item label="课程名称">{{ selectedRecord.courseTitle }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDate(selectedRecord.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后学习">{{ formatDate(selectedRecord.lastStudyTime) }}</el-descriptions-item>
          <el-descriptions-item label="学习进度">
            <el-progress :percentage="selectedRecord.progress" />
          </el-descriptions-item>
          <el-descriptions-item label="学习时长">{{ formatDuration(selectedRecord.duration) }}</el-descriptions-item>
          <el-descriptions-item label="完成单元">{{ selectedRecord.completedUnits }}/{{ selectedRecord.totalUnits }}</el-descriptions-item>
          <el-descriptions-item label="成绩">
            <span v-if="selectedRecord.score !== null">{{ selectedRecord.score }}分</span>
            <span v-else>未评分</span>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 学习进度详情 -->
        <div class="progress-detail" v-if="selectedRecord.units">
          <h4>单元学习进度</h4>
          <el-table :data="selectedRecord.units" size="small">
            <el-table-column prop="title" label="单元名称" />
            <el-table-column prop="duration" label="学习时长" width="100">
              <template #default="{ row }">
                {{ formatDuration(row.duration) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'completed' ? 'success' : 'info'" size="small">
                  {{ row.status === 'completed' ? '已完成' : '进行中' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="completedAt" label="完成时间" width="150">
              <template #default="{ row }">
                <span v-if="row.completedAt">{{ formatDate(row.completedAt) }}</span>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 学习进度图表对话框 -->
    <el-dialog
      title="学习进度图表"
      v-model="showProgressDialog"
      width="900px"
    >
      <div v-if="selectedRecord" class="progress-charts">
        <div class="chart-container">
          <h4>学习时长趋势</h4>
          <div id="durationChart" style="height: 300px;"></div>
        </div>
        <div class="chart-container">
          <h4>学习进度趋势</h4>
          <div id="progressChart" style="height: 300px;"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get } from '@/utils/request'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  Download, 
  User, 
  Timer, 
  Check, 
  TrendCharts,
  View
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const learningRecords = ref([])
const courses = ref([])
const total = ref(0)
const showDetailDialog = ref(false)
const showProgressDialog = ref(false)
const selectedRecord = ref(null)

// 统计数据
const stats = reactive({
  totalUsers: 0,
  totalDuration: 0,
  completedCourses: 0,
  avgCompletionRate: 0
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 筛选器
const filters = reactive({
  userName: '',
  courseId: '',
  status: '',
  dateRange: null
})

// 加载学习记录
const loadLearningRecords = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size,
      ...filters
    }
    
    const response = await get('/api/learning-records', params)
    if (response.success) {
      learningRecords.value = response.data.records || []
      total.value = response.data.total || 0
      Object.assign(stats, response.data.stats || {})
    }
  } catch (error) {
    console.error('加载学习记录失败:', error)
    ElMessage.error('加载学习记录失败')
    
    // 使用模拟数据
    learningRecords.value = generateMockRecords()
    total.value = learningRecords.value.length
    generateMockStats()
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockRecords = () => {
  return [
    {
      id: 1,
      userName: '张三',
      courseTitle: 'N5基础日语',
      progress: 75,
      duration: 1200, // 秒
      status: 'in_progress',
      lastStudyTime: new Date(),
      score: 85,
      startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      completedUnits: 15,
      totalUnits: 20
    },
    {
      id: 2,
      userName: '李四',
      courseTitle: 'N4进阶日语',
      progress: 100,
      duration: 2400,
      status: 'completed',
      lastStudyTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      score: 92,
      startTime: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      completedUnits: 25,
      totalUnits: 25
    }
  ]
}

const generateMockStats = () => {
  Object.assign(stats, {
    totalUsers: 1250,
    totalDuration: 15680,
    completedCourses: 890,
    avgCompletionRate: 78.5
  })
}

// 工具函数
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}h ${minutes}m`
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusColor = (status) => {
  const colors = {
    in_progress: 'primary',
    completed: 'success',
    paused: 'warning'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    in_progress: '进行中',
    completed: '已完成',
    paused: '已暂停'
  }
  return texts[status] || status
}

// 查看详情
const viewDetail = (record) => {
  selectedRecord.value = record
  showDetailDialog.value = true
}

// 查看进度图表
const viewProgress = (record) => {
  selectedRecord.value = record
  showProgressDialog.value = true
  // 这里可以集成图表库如ECharts
}

// 导出数据
const exportData = async () => {
  exporting.value = true
  try {
    // 这里实现数据导出逻辑
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  } finally {
    exporting.value = false
  }
}

const refreshData = () => {
  loadLearningRecords()
}

// 组件挂载时加载数据
onMounted(() => {
  loadLearningRecords()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  .stats-extra {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
  }
}

.progress-detail {
  margin-top: 20px;
  
  h4 {
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }
}

.progress-charts {
  .chart-container {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
  }
}

.text-muted {
  color: var(--el-text-color-placeholder);
}
</style>
