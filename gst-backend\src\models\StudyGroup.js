const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const StudyGroup = sequelize.define('StudyGroup', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 100]
      }
    },
    description: {
      type: DataTypes.TEXT
    },
    level: {
      type: DataTypes.ENUM('N5', 'N4', 'N3', 'N2', 'N1'),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('pending', 'active', 'completed', 'cancelled'),
      defaultValue: 'pending',
      allowNull: false
    },
    maxMembers: {
      type: DataTypes.INTEGER,
      defaultValue: 30,
      field: 'max_members',
      validate: {
        min: 1,
        max: 100
      }
    },
    currentMembers: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'current_members',
      validate: {
        min: 0
      }
    },
    teacherId: {
      type: DataTypes.INTEGER,
      field: 'teacher_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    startDate: {
      type: DataTypes.DATEONLY,
      field: 'start_date'
    },
    endDate: {
      type: DataTypes.DATEONLY,
      field: 'end_date'
    },
    schedule: {
      type: DataTypes.JSON,
      comment: '上课时间安排'
    },
    coverImage: {
      type: DataTypes.STRING(255),
      field: 'cover_image',
      validate: {
        isUrl: true
      }
    },
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_public',
      comment: '是否为公共小组'
    },
    avatar: {
      type: DataTypes.STRING(255),
      comment: '小组头像'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      field: 'created_by',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'study_groups',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    indexes: [
      { fields: ['name'] },
      { fields: ['level'] },
      { fields: ['status'] },
      { fields: ['teacher_id'] },
      { fields: ['created_by'] }
    ]
  });

  // 实例方法
  StudyGroup.prototype.isFull = function() {
    return this.currentMembers >= this.maxMembers;
  };

  StudyGroup.prototype.canJoin = function() {
    return this.status === 'active' && !this.isFull();
  };

  StudyGroup.prototype.addMember = async function() {
    if (this.canJoin()) {
      await this.increment('currentMembers');
      return true;
    }
    return false;
  };

  StudyGroup.prototype.removeMember = async function() {
    if (this.currentMembers > 0) {
      await this.decrement('currentMembers');
      return true;
    }
    return false;
  };

  StudyGroup.prototype.activate = async function() {
    this.status = 'active';
    await this.save();
  };

  StudyGroup.prototype.complete = async function() {
    this.status = 'completed';
    await this.save();
  };

  // 关联定义
  StudyGroup.associate = function(models) {
    // 小组有一个教师
    StudyGroup.belongsTo(models.User, {
      foreignKey: 'teacherId',
      as: 'teacher'
    });

    // 小组由用户创建
    StudyGroup.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });

    // 小组有多个成员
    StudyGroup.belongsToMany(models.User, {
      through: models.GroupMember,
      foreignKey: 'groupId',
      as: 'members'
    });

    // 小组成员记录
    StudyGroup.hasMany(models.GroupMember, {
      foreignKey: 'groupId',
      as: 'memberRecords'
    });

    // 小组有多个课程
    StudyGroup.belongsToMany(models.Course, {
      through: models.GroupCourse,
      foreignKey: 'groupId',
      as: 'courses'
    });

    // 小组课程记录
    StudyGroup.hasMany(models.GroupCourse, {
      foreignKey: 'groupId',
      as: 'courseRecords'
    });

    // 小组的学习记录
    StudyGroup.hasMany(models.LearningRecord, {
      foreignKey: 'groupId',
      as: 'learningRecords'
    });

    // 小组的作业
    StudyGroup.hasMany(models.Assignment, {
      foreignKey: 'groupId',
      as: 'assignments'
    });
  };

  return StudyGroup;
};
