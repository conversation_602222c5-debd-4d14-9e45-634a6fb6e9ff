<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户详情"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="user-detail" v-if="user">
      <!-- 用户头像和基本信息 -->
      <div class="user-header">
        <div class="user-avatar">
          <el-avatar :size="80" :src="user.avatar">
            {{ user.realName?.charAt(0) || user.username?.charAt(0) }}
          </el-avatar>
        </div>
        <div class="user-info">
          <h2 class="user-name">{{ user.realName || user.username }}</h2>
          <div class="user-meta">
            <el-tag :type="getRoleType(user.role)" size="large">
              {{ getRoleText(user.role) }}
            </el-tag>
            <el-tag :type="getStatusType(user.status)" size="large">
              {{ getStatusText(user.status) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>用户名</label>
            <span>{{ user.username }}</span>
          </div>
          <div class="info-item">
            <label>真实姓名</label>
            <span>{{ user.realName || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>邮箱地址</label>
            <span>{{ user.email || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>手机号码</label>
            <span>{{ user.phone || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>用户角色</label>
            <el-tag :type="getRoleType(user.role)">{{ getRoleText(user.role) }}</el-tag>
          </div>
          <div class="info-item">
            <label>账户状态</label>
            <el-tag :type="getStatusType(user.status)">{{ getStatusText(user.status) }}</el-tag>
          </div>
          <div class="info-item">
            <label>注册时间</label>
            <span>{{ formatDateTime(user.createdAt) }}</span>
          </div>
          <div class="info-item">
            <label>最后登录</label>
            <span>{{ formatDateTime(user.lastLoginAt) || '从未登录' }}</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section" v-if="user.remark">
        <h3 class="section-title">备注信息</h3>
        <p class="remark-text">{{ user.remark }}</p>
      </div>

      <!-- 学习统计（仅学生显示） -->
      <div class="detail-section" v-if="user.role === 'student'">
        <h3 class="section-title">学习统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ userStats.joinedGroups || 0 }}</div>
            <div class="stat-label">加入小组</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.completedCourses || 0 }}</div>
            <div class="stat-label">完成课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.submittedAssignments || 0 }}</div>
            <div class="stat-label">提交作业</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.studyHours || 0 }}</div>
            <div class="stat-label">学习时长(小时)</div>
          </div>
        </div>
      </div>

      <!-- 教学统计（仅教师显示） -->
      <div class="detail-section" v-if="user.role === 'teacher'">
        <h3 class="section-title">教学统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ userStats.managedGroups || 0 }}</div>
            <div class="stat-label">管理小组</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.createdCourses || 0 }}</div>
            <div class="stat-label">创建课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.assignedTasks || 0 }}</div>
            <div class="stat-label">布置作业</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.studentCount || 0 }}</div>
            <div class="stat-label">学生数量</div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="detail-section">
        <h3 class="section-title">
          最近活动
          <el-button 
            type="text" 
            size="small" 
            @click="loadUserLogs"
            :loading="logsLoading"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </h3>
        
        <div class="logs-list" v-loading="logsLoading">
          <div 
            class="log-item" 
            v-for="log in userLogs" 
            :key="log.id"
          >
            <div class="log-icon">
              <el-icon :class="getLogIconClass(log.action)">
                <component :is="getLogIcon(log.action)" />
              </el-icon>
            </div>
            <div class="log-content">
              <div class="log-text">{{ log.description }}</div>
              <div class="log-time">{{ formatDateTime(log.createdAt) }}</div>
            </div>
          </div>
          
          <el-empty 
            v-if="!logsLoading && userLogs.length === 0" 
            description="暂无活动记录"
            :image-size="60"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="editUser"
          v-if="canEdit"
        >
          编辑用户
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit'])

const authStore = useAuthStore()

// 响应式数据
const logsLoading = ref(false)
const userStats = ref({})
const userLogs = ref([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 权限检查
const canEdit = computed(() => {
  if (!props.user) return false
  if (authStore.isAdmin) return true
  return false
})

// 加载用户统计数据
const loadUserStats = async () => {
  if (!props.user?.id) return
  
  try {
    const response = await get(`/api/users/${props.user.id}/stats`)
    if (response.success) {
      userStats.value = response.data || {}
    }
  } catch (error) {
    console.error('加载用户统计失败:', error)
    // 使用模拟数据
    if (props.user.role === 'student') {
      userStats.value = {
        joinedGroups: 2,
        completedCourses: 15,
        submittedAssignments: 28,
        studyHours: 45
      }
    } else if (props.user.role === 'teacher') {
      userStats.value = {
        managedGroups: 3,
        createdCourses: 12,
        assignedTasks: 35,
        studentCount: 67
      }
    }
  }
}

// 加载用户日志
const loadUserLogs = async () => {
  if (!props.user?.id) return
  
  logsLoading.value = true
  try {
    const response = await get(`/api/users/${props.user.id}/logs`, { limit: 10 })
    if (response.success) {
      userLogs.value = response.data.logs || []
    }
  } catch (error) {
    console.error('加载用户日志失败:', error)
    // 使用模拟数据
    userLogs.value = [
      {
        id: 1,
        action: 'login',
        description: '用户登录系统',
        createdAt: new Date('2024-07-28T10:30:00')
      },
      {
        id: 2,
        action: 'course_complete',
        description: '完成课程：日语N5语法基础',
        createdAt: new Date('2024-07-27T15:20:00')
      },
      {
        id: 3,
        action: 'assignment_submit',
        description: '提交作业：第三课练习题',
        createdAt: new Date('2024-07-26T20:15:00')
      }
    ]
  } finally {
    logsLoading.value = false
  }
}

// 编辑用户
const editUser = () => {
  emit('edit', props.user)
  dialogVisible.value = false
}

// 获取角色类型
const getRoleType = (role) => {
  const types = {
    admin: 'danger',
    teacher: 'warning',
    student: 'primary'
  }
  return types[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    teacher: '教师',
    student: '学生'
  }
  return texts[role] || '未知'
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    active: 'success',
    disabled: 'danger',
    pending: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    active: '正常',
    disabled: '禁用',
    pending: '待审核'
  }
  return texts[status] || '未知'
}

// 获取日志图标
const getLogIcon = (action) => {
  const icons = {
    login: 'User',
    logout: 'SwitchButton',
    course_complete: 'CircleCheck',
    assignment_submit: 'DocumentChecked',
    group_join: 'UserFilled',
    profile_update: 'Edit'
  }
  return icons[action] || 'InfoFilled'
}

// 获取日志图标样式
const getLogIconClass = (action) => {
  const classes = {
    login: 'success-icon',
    logout: 'info-icon',
    course_complete: 'success-icon',
    assignment_submit: 'primary-icon',
    group_join: 'warning-icon',
    profile_update: 'info-icon'
  }
  return classes[action] || 'info-icon'
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.user) {
    loadUserStats()
    loadUserLogs()
  }
})
</script>

<style lang="scss" scoped>
.user-detail {
  .user-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-color-page);
    border-radius: var(--border-radius-base);
    margin-bottom: var(--spacing-lg);
    
    .user-info {
      flex: 1;
      
      .user-name {
        margin: 0 0 var(--spacing-sm) 0;
        font-size: var(--font-size-large);
        font-weight: 600;
        color: var(--text-primary);
      }
      
      .user-meta {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
  }
  
  .detail-section {
    margin-bottom: var(--spacing-xl);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-medium);
      font-weight: 600;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    
    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      
      label {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
        font-weight: 500;
      }
      
      span {
        font-size: var(--font-size-base);
        color: var(--text-primary);
      }
    }
  }
  
  .remark-text {
    margin: 0;
    line-height: 1.6;
    color: var(--text-regular);
    background: var(--bg-color-page);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-base);
    border-left: 4px solid var(--primary-color);
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-lg);
    
    .stat-item {
      text-align: center;
      padding: var(--spacing-lg);
      background: var(--bg-color-page);
      border-radius: var(--border-radius-base);
      
      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: var(--spacing-xs);
      }
      
      .stat-label {
        font-size: var(--font-size-small);
        color: var(--text-secondary);
      }
    }
  }
  
  .logs-list {
    max-height: 300px;
    overflow-y: auto;
    
    .log-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-radius: var(--border-radius-base);
      transition: background-color 0.3s ease;
      
      &:hover {
        background: var(--bg-color-page);
      }
      
      .log-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        &.success-icon {
          background: rgba(103, 194, 58, 0.1);
          color: var(--success-color);
        }
        
        &.primary-icon {
          background: rgba(64, 158, 255, 0.1);
          color: var(--primary-color);
        }
        
        &.warning-icon {
          background: rgba(230, 162, 60, 0.1);
          color: var(--warning-color);
        }
        
        &.info-icon {
          background: rgba(144, 147, 153, 0.1);
          color: var(--info-color);
        }
      }
      
      .log-content {
        flex: 1;
        
        .log-text {
          font-size: var(--font-size-base);
          color: var(--text-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .log-time {
          font-size: var(--font-size-small);
          color: var(--text-secondary);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}
</style>
