{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-single-slider.vue?2d67", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-single-slider.vue?2583", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-single-slider.vue?9149", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-single-slider.vue?a5f2", "uni-app:///GraceUI5/components/gui-single-slider.vue", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-single-slider.vue?f1c2", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-single-slider.vue?8453"], "names": ["name", "props", "barHeight", "type", "default", "<PERSON><PERSON><PERSON><PERSON>", "barColor", "barBgColor", "bglineSize", "bglineColor", "bglineAColor", "barText", "barTextSize", "borderRadius", "canSlide", "data", "left", "startLeft", "width", "barWidthPX", "mounted", "methods", "init", "uni", "size", "rect", "setTimeout", "touchstart", "touchmove", "touchend", "changeBar", "setProgress", "value"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACqC;;;AAGrG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8mB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkCloB;EACAA;EACAC;IACAC;MAAAC;MAAAC;IAAA;IACAC;MAAAF;MAAAC;IAAA;IACAE;MAAAH;MAAAC;IAAA;IACAG;MAAAJ;MAAAC;IAAA;IACAI;MAAAL;MAAAC;IAAA;IACAK;MAAAN;MAAAC;IAAA;IACAM;MAAAP;MAAAC;IAAA;IACAO;MAAAR;MAAAC;IAAA;IACAQ;MAAAT;MAAAC;IAAA;IACAS;MAAAV;MAAAC;IAAA;IACAU;MAAAX;MAAAC;IAAA;EACA;EACAW;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAcAC,mEACA;QAAAC;QAAAC;MAAA;QACA;UACAC;YAAA;UAAA;UACA;QACA;QACA;QACA;QACA;MACA,EACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACAd;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACAe;MAAA;MACA;QAAAL;UAAA;QAAA;QAAA;MAAA;MACA;QAAAM;MAAA;MACA;QAAAA;MAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAAy5B,CAAgB,w4BAAG,EAAC,C;;;;;;;;;;;ACA76B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "GraceUI5/components/gui-single-slider.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./gui-single-slider.vue?vue&type=template&id=3c244fe3&scoped=true&\"\nvar renderjs\nimport script from \"./gui-single-slider.vue?vue&type=script&lang=js&\"\nexport * from \"./gui-single-slider.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gui-single-slider.vue?vue&type=style&index=0&id=3c244fe3&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3c244fe3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"GraceUI5/components/gui-single-slider.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-single-slider.vue?vue&type=template&id=3c244fe3&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-single-slider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-single-slider.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"gui-sg-slider\" \n\t@touchstart=\"touchstart\" \n\************************=\"touchmove\" \n\t@touchend=\"touchend\" \n\tref=\"gracesgslider\" \n\tid=\"gracesgslider\" \n\t:style=\"{height:barHeight+'rpx'}\">\n\t\t<view class=\"gui-sg-slider-line\" \n\t\t:style=\"{\n\t\t\theight:bglineSize+'rpx', backgroundColor:bglineColor, \n\t\t\tmarginTop:((barHeight - bglineSize) / 2)+'rpx', \n\t\t\tborderRadius:borderRadius}\"></view>\n\t\t<view class=\"gui-sg-slider-a-line\" \n\t\t:style=\"{\n\t\t\twidth:(left+25)+'px', \n\t\t\ttop:((barHeight - bglineSize) / 2)+'rpx', \n\t\t\tbackgroundColor:bglineAColor, \n\t\t\theight:bglineSize+'rpx', \n\t\t\tborderRadius:borderRadius}\"></view>\n\t\t<text class=\"gui-sg-slider-bar gui-block-text\"\n\t\t:style=\"{\n\t\t\twidth:barWidth+'rpx', height:barHeight+'rpx', \n\t\t\t'line-height':barHeight+'rpx', \n\t\t\tbackgroundImage:barBgColor, \n\t\t\tcolor:barColor, left:left+'px', \n\t\t\tfontSize:barTextSize, \n\t\t\tborderRadius:borderRadius}\">{{barText}}</text>\n\t</view>\n</template>\n<script>\n// #ifdef APP-NVUE\nconst dom = weex.requireModule('dom');\n// #endif\nexport default{\n\tname  : \"gui-single-slider\",\n\tprops : {\n\t\tbarHeight    : {type:Number,  default:32},\n\t\tbarWidth     : {type:Number,  default:168},\n\t\tbarColor     : {type:String,  default:'#FFFFFF'},\n\t\tbarBgColor   : {type:String,  default:'linear-gradient(to right, #3688FF,#3688FF)'},\n\t\tbglineSize   : {type:Number,  default:2},\n\t\tbglineColor  : {type:String,  default:'rgba(54,136,255,0.5)'},\n\t\tbglineAColor : {type:String,  default:'#3688FF'},\n\t\tbarText      : {type:String,  default:''},\n\t\tbarTextSize  : {type:String,  default:'20rpx'},\n\t\tborderRadius : {type:String,  default:'32rpx'},\n\t\tcanSlide     : {type:Boolean, default:true}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tleft       : 0,\n\t\t\tstartLeft  : 0,\n\t\t\twidth      : 0,\n\t\t\tbarWidthPX : 30\n\t\t}\n\t},\n\tmounted:function(){\n\t\tthis.init();\n\t},\n\tmethods:{\n\t\tinit : function(){\n\t\t\t// #ifdef APP-NVUE\n\t\t\tvar el = this.$refs.gracesgslider;\n\t\t\tdom.getComponentRect(el, (res) => {\n\t\t\t\tif(res.result == 0){\n\t\t\t\t\tsetTimeout(()=>{this.init();}, 100);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.startLeft  = res.size.left;\n\t\t\t\tthis.width      = res.size.width;\n\t\t\t\tthis.barWidthPX = uni.upx2px(this.barWidth);\n\t\t\t});\n\t\t\t// #endif\n\t\t\t// #ifndef APP-NVUE\n\t\t\tuni.createSelectorQuery().in(this).select('#gracesgslider').fields(\n\t\t\t\t{size: true, rect:true}, (res) => {\n\t\t\t\t\tif(res == null){\n\t\t\t\t\t\tsetTimeout(()=>{this.init();}, 100);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthis.startLeft  = res.left;\n\t\t\t\t\tthis.width      = res.width;\n\t\t\t\t\tthis.barWidthPX = uni.upx2px(this.barWidth);\n\t\t\t\t}\n\t\t\t).exec();\n\t\t\t// #endif\n\t\t},\n\t\ttouchstart : function (e) {\n\t\t\tif(!this.canSlide){return ;}\n\t\t\tvar touch = e.touches[0] || e.changedTouches[0];\n\t\t\tthis.changeBar(touch.pageX);\n\t\t},\n\t\ttouchmove : function (e) {\n\t\t\tif(!this.canSlide){return ;}\n\t\t\tvar touch = e.touches[0] || e.changedTouches[0];\n\t\t\tthis.changeBar(touch.pageX);\n\t\t},\n\t\ttouchend : function (e) {\n\t\t\tif(!this.canSlide){return ;}\n\t\t\tvar touch = e.touches[0] || e.changedTouches[0];\n\t\t\tthis.changeBar(touch.pageX, true);\n\t\t},\n\t\tchangeBar : function(x){\n\t\t\tvar left = x - this.startLeft;\n\t\t\tif(left <= 0){\n\t\t\t\tthis.left = 0;\n\t\t\t\tthis.$emit('change', 0);\n\t\t\t}else if(left + this.barWidthPX > this.width){\n\t\t\t\tleft = this.width - this.barWidthPX;\n\t\t\t\tthis.left = left;\n\t\t\t\tthis.$emit('change', 100);\n\t\t\t}else{\n\t\t\t\tthis.left = left;\n\t\t\t\tvar scale = this.left / (this.width - this.barWidthPX);\n\t\t\t\tthis.$emit('change', Math.round(scale * 100));\n\t\t\t}\n\t\t},\n\t\tsetProgress : function (value){\n\t\t\tif(this.width < 1){ setTimeout(()=>{this.setProgress(value), 300}); return ;}\n\t\t\tif(value < 0){value = 0;}\n\t\t\tif(value > 100){value = 100;}\n\t\t\tthis.left = ( value / 100 ) * (this.width - this.barWidthPX);\n\t\t}\n\t}\n}\n</script>\n<style scoped>\n.gui-sg-slider{overflow:hidden; position:relative;}\n.gui-sg-slider-a-line{position:absolute; left:0; top:0;}\n.gui-sg-slider-bar{position:absolute; left:0; top:0; border-radius:50rpx; font-size:20rpx; text-align:center; color:#323232; overflow:hidden;}\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-single-slider.vue?vue&type=style&index=0&id=3c244fe3&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-single-slider.vue?vue&type=style&index=0&id=3c244fe3&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699110033\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}