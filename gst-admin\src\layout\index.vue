<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <aside class="layout-sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon" v-if="!sidebarCollapsed">🎌</div>
          <span class="logo-text" v-if="!sidebarCollapsed">GST管理系统</span>
          <span class="logo-mini" v-else>🎌</span>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <!-- 仪表板 -->
          <el-menu-item index="/dashboard">
            <el-icon><DataBoard /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          
          <!-- 内容管理 -->
          <el-sub-menu index="content" v-if="hasContentPermission">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>内容管理</span>
            </template>
            <el-menu-item index="/content/groups">
              <el-icon><UserFilled /></el-icon>
              <span>小组管理</span>
            </el-menu-item>
            <el-menu-item index="/content/courses">
              <el-icon><Reading /></el-icon>
              <span>课程管理</span>
            </el-menu-item>
            <el-menu-item index="/content/assignments">
              <el-icon><EditPen /></el-icon>
              <span>作业管理</span>
            </el-menu-item>
            <el-menu-item index="/content/categories">
              <el-icon><FolderOpened /></el-icon>
              <span>分类管理</span>
            </el-menu-item>
          </el-sub-menu>
          
          <!-- 小程序管理 -->
          <el-sub-menu index="miniprogram" v-if="authStore.isAdmin">
            <template #title>
              <el-icon><Iphone /></el-icon>
              <span>小程序管理</span>
            </template>
            <el-menu-item index="/miniprogram/visual-editor">
              <el-icon><Brush /></el-icon>
              <span>可视化编辑器</span>
            </el-menu-item>
            <el-menu-item index="/miniprogram/page-manager">
              <el-icon><Document /></el-icon>
              <span>页面管理</span>
            </el-menu-item>
            <el-menu-item index="/miniprogram/banners">
              <el-icon><Picture /></el-icon>
              <span>轮播图管理</span>
            </el-menu-item>
            <el-menu-item index="/miniprogram/home-config">
              <el-icon><HomeFilled /></el-icon>
              <span>首页配置</span>
            </el-menu-item>
            <el-menu-item index="/miniprogram/menu-manager">
              <el-icon><Menu /></el-icon>
              <span>菜单管理</span>
            </el-menu-item>
          </el-sub-menu>
          
          <!-- 系统管理 -->
          <el-sub-menu index="system" v-if="authStore.isAdmin">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/users">
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="/system/permissions">
              <el-icon><Lock /></el-icon>
              <span>权限管理</span>
            </el-menu-item>
            <el-menu-item index="/system/data-import">
              <el-icon><Upload /></el-icon>
              <span>数据导入</span>
            </el-menu-item>
            <el-menu-item index="/system/database">
              <el-icon><Coin /></el-icon>
              <span>数据库管理</span>
            </el-menu-item>
            <el-menu-item index="/system/backup">
              <el-icon><FolderAdd /></el-icon>
              <span>备份管理</span>
            </el-menu-item>
            <el-menu-item index="/system/logs">
              <el-icon><Document /></el-icon>
              <span>操作日志</span>
            </el-menu-item>
            <el-menu-item index="/system/settings">
              <el-icon><Tools /></el-icon>
              <span>系统设置</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </nav>
    </aside>
    
    <!-- 主内容区 -->
    <div class="layout-main" :class="{ expanded: sidebarCollapsed }">
      <!-- 顶部导航 -->
      <header class="layout-header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <div class="user-info">
            <el-dropdown @command="handleUserCommand">
              <div class="user-avatar">
                <el-avatar :size="32" :src="authStore.user?.avatar">
                  {{ authStore.user?.realName?.charAt(0) || authStore.user?.username?.charAt(0) }}
                </el-avatar>
                <span class="user-name">{{ authStore.user?.realName || authStore.user?.username }}</span>
                <el-icon class="user-dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    账户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <main class="layout-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in">
            <component :is="Component" :key="route.path" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 权限检查
const hasContentPermission = computed(() => {
  return authStore.hasRole(['admin', 'teacher'])
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbList = []
  
  matched.forEach(item => {
    if (item.path !== '/') {
      breadcrumbList.push({
        path: item.path,
        title: item.meta.title
      })
    }
  })
  
  return breadcrumbList
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  localStorage.setItem('sidebar-collapsed', sidebarCollapsed.value)
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到账户设置页面
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    authStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 初始化侧边栏状态
const initSidebar = () => {
  const collapsed = localStorage.getItem('sidebar-collapsed')
  if (collapsed !== null) {
    sidebarCollapsed.value = collapsed === 'true'
  }
}

// 监听路由变化，更新页面标题
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - GST日语培训班管理系统`
    }
  },
  { immediate: true }
)

// 组件挂载时初始化
initSidebar()
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;
  background-color: var(--bg-color-page);
}

.layout-sidebar {
  width: var(--sidebar-width);
  background: var(--bg-color);
  border-right: 1px solid var(--border-base);
  transition: width 0.3s ease;
  
  &.collapsed {
    width: var(--sidebar-collapsed-width);
  }
  
  .sidebar-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--border-base);
    
    .logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .logo-icon {
        font-size: 32px;
        line-height: 1;
      }
      
      .logo-text {
        font-size: var(--font-size-large);
        font-weight: 600;
        color: var(--primary-color);
      }
      
      .logo-mini {
        font-size: var(--font-size-extra-large);
        font-weight: 700;
        color: var(--primary-color);
      }
    }
  }
  
  .sidebar-nav {
    height: calc(100vh - var(--header-height));
    overflow-y: auto;
    
    .sidebar-menu {
      border-right: none;
      
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        height: 48px;
        line-height: 48px;
        
        &:hover {
          background-color: var(--bg-color-page);
        }
      }
      
      :deep(.el-menu-item.is-active) {
        background-color: var(--primary-color);
        color: white;
        
        .el-icon {
          color: white;
        }
      }
    }
  }
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease;
  
  &.expanded {
    margin-left: var(--sidebar-collapsed-width);
  }
}

.layout-header {
  height: var(--header-height);
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    
    .sidebar-toggle {
      font-size: var(--font-size-large);
      color: var(--text-regular);
    }
    
    .breadcrumb {
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: var(--text-regular);
          font-weight: 400;
          
          &:hover {
            color: var(--primary-color);
          }
        }
        
        &:last-child .el-breadcrumb__inner {
          color: var(--text-primary);
          font-weight: 500;
        }
      }
    }
  }
  
  .header-right {
    .user-info {
      .user-avatar {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        cursor: pointer;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-base);
        transition: background-color 0.3s ease;
        
        &:hover {
          background-color: var(--bg-color-page);
        }
        
        .user-name {
          font-size: var(--font-size-base);
          color: var(--text-primary);
          font-weight: 500;
        }
        
        .user-dropdown-icon {
          font-size: var(--font-size-small);
          color: var(--text-secondary);
        }
      }
    }
  }
}

.layout-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--bg-color-page);
  padding: var(--spacing-lg);
}
</style>
