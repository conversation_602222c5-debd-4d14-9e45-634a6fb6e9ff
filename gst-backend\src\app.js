const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

const logger = require('./utils/logger');

const app = express();

// 安全中间件
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: false
}));

// 压缩中间件
app.use(compression());

// 日志中间件
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000,
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  }
});
app.use('/api/', limiter);

// 获取本机IP地址
const os = require('os');
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();

// CORS配置 - 动态支持所有局域网访问
app.use(cors({
  origin: function (origin, callback) {
    // 允许没有origin的请求（如移动应用、Postman等）
    if (!origin) return callback(null, true);

    // 允许的域名和端口
    const allowedPorts = ['3005', '8080', '8081'];
    const allowedHosts = [
      'localhost',
      '127.0.0.1',
      localIP
    ];

    // 检查是否是允许的localhost或IP访问
    const url = new URL(origin);
    const isAllowedPort = allowedPorts.includes(url.port);
    const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
    const isLocalIP = url.hostname === localIP;
    const isPrivateIP = /^(192\.168\.|10\.|172\.(1[6-9]|2\d|3[01])\.)/.test(url.hostname);

    if (isAllowedPort && (isLocalhost || isLocalIP || isPrivateIP)) {
      console.log(`✅ CORS allowed origin: ${origin}`);
      callback(null, true);
    } else {
      console.log(`🚫 CORS blocked origin: ${origin}`);
      console.log(`   - Port: ${url.port} (allowed: ${allowedPorts.join(', ')})`);
      console.log(`   - Host: ${url.hostname} (localhost: ${isLocalhost}, localIP: ${isLocalIP}, privateIP: ${isPrivateIP})`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 媒体文件静态服务（支持视频流）
app.use('/uploads/media', express.static(path.join(__dirname, '../uploads/media'), {
  setHeaders: (res, path) => {
    // 为视频文件设置适当的headers支持流媒体
    if (path.endsWith('.mp4') || path.endsWith('.avi') || path.endsWith('.mov')) {
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Type', 'video/mp4');
    }
    if (path.endsWith('.mp3') || path.endsWith('.wav') || path.endsWith('.aac')) {
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Type', 'audio/mpeg');
    }
  }
}));

// 健康检查
app.get('/health', async (req, res) => {
  try {
    const { sequelize } = require('./config/database');
    await sequelize.authenticate();
    
    res.json({
      success: true,
      message: 'GST日语培训班API服务运行正常',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: {
        type: 'PostgreSQL',
        status: 'connected'
      },
      uptime: Math.floor(process.uptime())
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '数据库连接失败',
      database: { status: 'disconnected' }
    });
  }
});

// API路由
app.use('/api/auth', require('./routes/auth'));
app.use('/api/dashboard', require('./routes/dashboard'));
app.use('/api/users', require('./routes/users-simple'));
app.use('/api/groups', require('./routes/groups-simple'));
app.use('/api/courses', require('./routes/courses-simple'));
app.use('/api/course-units', require('./routes/courseUnits'));
app.use('/api/media', require('./routes/media'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/learning', require('./routes/learning-simple'));
app.use('/api/assignments', require('./routes/assignments-simple'));
app.use('/api/system', require('./routes/system-simple'));
app.use('/api/banners', require('./routes/banners'));
app.use('/api/home-configs', require('./routes/home-configs'));
app.use('/api/menus', require('./routes/menus'));
app.use('/api/import', require('./routes/import'));
app.use('/api/database', require('./routes/database'));
app.use('/api/batch', require('./routes/batch'));
app.use('/api/backups', require('./routes/backups'));
app.use('/api/roles', require('./routes/roles'));
app.use('/api/permissions', require('./routes/permissions'));
app.use('/api/logs', require('./routes/logs'));
app.use('/api/learning-records', require('./routes/learning-records'));
app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/pages', require('./routes/pages'));
app.use('/api/home-config', require('./routes/home-config'));
app.use('/api/v1/course', require('./routes/home'));
app.use('/api/home', require('./routes/home'));

// API文档
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'GST日语培训班API',
    version: '1.0.0',
    database: 'PostgreSQL',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      groups: '/api/groups',
      courses: '/api/courses',
      learning: '/api/learning',
      assignments: '/api/assignments',
      system: '/api/system'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理
app.use((err, req, res, next) => {
  logger.error('服务器错误:', err);
  
  if (err.name === 'SequelizeConnectionError') {
    return res.status(500).json({
      success: false,
      message: 'PostgreSQL数据库连接失败'
    });
  }
  
  if (err.name === 'SequelizeValidationError') {
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors: err.errors.map(e => ({
        field: e.path,
        message: e.message
      }))
    });
  }
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || '服务器内部错误'
  });
});

module.exports = app;
