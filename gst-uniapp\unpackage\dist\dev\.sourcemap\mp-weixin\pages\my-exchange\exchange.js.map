{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/my-exchange/exchange.vue?2352", "webpack:///D:/gst/gst-uniapp/pages/my-exchange/exchange.vue?d430", "webpack:///D:/gst/gst-uniapp/pages/my-exchange/exchange.vue?6810", "webpack:///D:/gst/gst-uniapp/pages/my-exchange/exchange.vue?8e02", "uni-app:///pages/my-exchange/exchange.vue", "webpack:///D:/gst/gst-uniapp/pages/my-exchange/exchange.vue?dcaa", "webpack:///D:/gst/gst-uniapp/pages/my-exchange/exchange.vue?4300"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "nodata", "data", "sn", "list", "onShow", "onHide", "created", "computed", "valided", "methods", "exchangeValid", "uni", "icon", "title", "console", "onExchangeValidHandler", "onExchangeHandler", "type", "url", "apiExchangeValid", "params", "id", "r_type", "apiExchange", "showCancel", "navToDetailPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCqCznB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;EACAC;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACA;UACA;UACAC;QACA;UACA;UACAH;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAE;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACAC;MACA;MACA;QACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;YACAN;cACAO;YACA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACAF;QACAC;QACAL;MACA;QACA;MACA;QACAN;UACAa;UACAX;QACA;QACA;MACA;IACA;IACA;IACAY;MACA;MACA;MACAd;QACAO;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my-exchange/exchange.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my-exchange/exchange.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./exchange.vue?vue&type=template&id=32f7efa7&scoped=true&\"\nvar renderjs\nimport script from \"./exchange.vue?vue&type=script&lang=js&\"\nexport * from \"./exchange.vue?vue&type=script&lang=js&\"\nimport style0 from \"./exchange.vue?vue&type=style&index=0&id=32f7efa7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"32f7efa7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my-exchange/exchange.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=template&id=32f7efa7&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\"  class=\"root-box\">\n\t\t<!-- 页面主体 -->\n\t\t<view slot=\"gBody\" class=\"root-box\">\n\t\t\t<view class=\"search-box lp-flex\">\n\t\t\t\t<view class=\"input-box lp-flex-center\">\n\t\t\t\t\t<input class=\"sn-input\" v-model=\"sn\" style=\"text-align: center;\" placeholder=\"请输入兑换码并提交验证\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"btn\" @tap=\"onExchangeValidHandler\">提交验证</view>\n\t\t\t</view>\n\t\t\t<scroll-view v-if=\"list.length>0\" scroll-y=\"true\" @scrolltolower=\"onScrolltolowerHandler\">\r\n\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item, index) in list\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"navToDetailPage(item)\">\r\n\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\" >\r\n\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item.wk.picture\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t<view class=\"title\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t<nodata v-else-if=\"total == 0\" text=\"暂无数据\"></nodata>\n\t\t\t<!-- 兑换 -->\n\t\t\t<view class=\"btn-box\">\n\t\t\t\t<view class=\"btn\" :class=\"{disabled:!valided}\" @tap=\"onExchangeHandler\">确认兑换</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\n\timport nodata from '@/components/nodata/nodata.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tnodata\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsn: '',\n\t\t\t\tlist: []\n\t\t\t}\n\t\t},\n\t\tonShow: function() {},\n\t\tonHide: function() {},\n\t\tcreated: function() {},\n\t\tcomputed: {\n\t\t\tvalided: function() {\n\t\t\t\treturn this.list.length > 0;\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\texchangeValid: function() {\n\t\t\t\tif (this.sn == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写有效兑换码'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.apiExchangeValid(this.sn).then(data => {\n\t\t\t\t\tif (data) {\n\t\t\t\t\t\tthis.list = [data]\r\n\t\t\t\t\t\tconsole.log(this.list)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.list = [];\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '找不到数据'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// hander\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\t\t\t\n\t\t\t/**\n\t\t\t * 核实\n\t\t\t */\n\t\t\tonExchangeValidHandler: function() {\n\t\t\t\tthis.exchangeValid();\n\t\t\t},\n\t\t\t/**\n\t\t\t * 兑换\n\t\t\t */\n\t\t\tonExchangeHandler: function() {\r\n\t\t\t\tlet type = ''\r\n\t\t\t\tif(this.list[0].course_id==0){\r\n\t\t\t\t\ttype = 'member'\r\n\t\t\t\t}\n\t\t\t\tif (this.valided) {\n\t\t\t\t\tthis.apiExchange(this.list[0].id,getApp().globalData.r_type,type).then(data => {\n\t\t\t\t\t\tif (data.status) {\n\t\t\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t\t\t// \ttitle: '兑换成功',\n\t\t\t\t\t\t\t// });\n\t\t\t\t\t\t\t// setTimeout(() => {\n\t\t\t\t\t\t\t// \tuni.navigateBack();\n\t\t\t\t\t\t\t// }, 1500);\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: `/pages/my-exchange/exchange-success`\r\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tapiExchangeValid: function(sn) {\n\t\t\t\treturn this.$http.get('/v1/member/course_detail', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tid: sn,\r\n\t\t\t\t\t\tr_type:getApp().globalData.r_type\n\t\t\t\t\t}\n\t\t\t\t}).then(res => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t})\n\t\t\t},\n\t\t\tapiExchange: function(id,r_type,type) {\n\t\t\t\treturn this.$http.post('/v1/member/doExchange', {\n\t\t\t\t\tid,\r\n\t\t\t\t\tr_type,\r\n\t\t\t\t\ttype\n\t\t\t\t}).then(res => {\n\t\t\t\t\treturn Promise.resolve(res.data);\n\t\t\t\t}).catch(res => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\ttitle: res.data.code\n\t\t\t\t\t})\n\t\t\t\t\treturn Promise.resolve(res.data);\n\t\t\t\t})\n\t\t\t},\r\n\t\t\t//详情\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.course_id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.root-box {\n\t\tflex: 1;\n\t\tbackground-color: #f2f2f2;\n\n\t\t/* 搜索框 */\n\t\t.search-box {\n\t\t\tpadding: 30rpx;\n\t\t\tfont-size: 28rpx;\n\n\t\t\t.input-box {\n\t\t\t\tflex: 1;\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\tborder: solid 1px $uni-color-primary;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tmargin-right: 30rpx;\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tbackground: $uni-color-primary;\n\t\t\t\tborder: solid 1px $uni-color-primary;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tcolor: #fff;\n\t\t\t\tpadding: 0 20rpx;\n\t\t\t\tline-height: 80rpx;\n\t\t\t}\n\t\t}\n\n\t\t/* 列表 */\n\t\tscroll-view {\n\t\t\tmax-height: 100%;\n\t\t}\n\n\t\t.list-box {\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\r\n\t\t\t.item-box {\r\n\t\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\r\n\t\t\t\t.top-box {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\r\n\t\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\t\theight: auto;\r\n\t\t\t\t\t\tmin-height: 200rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\r\n\t\t\t\t\t\t.cover {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.button {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t.cover-box {\r\n\t\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\t\theight: auto;\r\n\t\t\t\t\t\tmin-height: 200rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\r\n\t\t\t\t\t\t.cover {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.button {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\t\t\t.info-box {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\t\theight: auto;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\r\n\t\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.total {\r\n\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.des {\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\t\r\n\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.price {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t\t.end {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t\t.margin-tb-sm {\r\n\t\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\r\n\t\t\t\t\t.text-sm {\r\n\t\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t.uni-row {\r\n\t\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t.align-center {\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\n\n\t\t/* 兑换按钮 */\n\t\t.btn-box {\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\n\t\t\t.btn {\n\t\t\t\tpadding: 30rpx;\n\t\t\t\tbackground: $uni-color-error;\n\t\t\t\ttext-align: center;\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\n\t\t\t.disabled {\n\t\t\t\tbackground: $uni-bg-color-grey;\n\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=style&index=0&id=32f7efa7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=style&index=0&id=32f7efa7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699114308\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}