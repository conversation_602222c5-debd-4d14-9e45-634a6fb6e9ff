const { sequelize } = require('./src/config/database');
require('dotenv').config();

// 导入所有模型
require('./src/models');

async function testDatabase() {
  try {
    console.log('🔄 测试数据库连接...');
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 获取User模型
    const { User } = sequelize.models;
    
    // 查询所有用户
    const users = await User.findAll();
    console.log(`📊 数据库中共有 ${users.length} 个用户:`);
    
    users.forEach(user => {
      console.log(`- ID: ${user.id}, 用户名: ${user.username}, 邮箱: ${user.email}, 角色: ${user.role}, 状态: ${user.status}`);
    });
    
    // 尝试查找admin用户
    const adminUser = await User.findOne({
      where: {
        username: 'admin'
      }
    });
    
    if (adminUser) {
      console.log('✅ 找到admin用户:', {
        id: adminUser.id,
        username: adminUser.username,
        email: adminUser.email,
        role: adminUser.role,
        status: adminUser.status
      });
    } else {
      console.log('❌ 未找到admin用户');
    }
    
    // 查看所有表
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log('📋 数据库中的表:', tables);
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

testDatabase();
