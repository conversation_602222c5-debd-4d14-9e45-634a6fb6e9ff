const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'gst_japanese_training.sqlite');

console.log('📍 检查courses表中的数据...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 连接到SQLite数据库成功');
});

// 查询courses表中的数据
db.all("SELECT COUNT(*) as count FROM courses", [], (err, rows) => {
  if (err) {
    console.error('❌ 查询courses表失败:', err.message);
  } else {
    console.log(`\n📊 courses表总记录数: ${rows[0].count}`);
  }
  
  // 查询前10条记录
  db.all("SELECT id, title, category_id, level, status, created_by FROM courses ORDER BY id LIMIT 10", [], (err, courseRows) => {
    if (err) {
      console.error('❌ 查询courses记录失败:', err.message);
    } else {
      console.log('\n📋 前10条课程记录:');
      courseRows.forEach((row, index) => {
        console.log(`${index + 1}. ID:${row.id} 标题:"${row.title}" 分类ID:${row.category_id} 等级:${row.level} 状态:${row.status} 创建者:${row.created_by}`);
      });
    }
    
    // 查询按状态分组的统计
    db.all("SELECT status, COUNT(*) as count FROM courses GROUP BY status", [], (err, statusRows) => {
      if (err) {
        console.error('❌ 查询状态统计失败:', err.message);
      } else {
        console.log(`\n📈 课程状态统计:`);
        statusRows.forEach((row) => {
          console.log(`- ${row.status}: ${row.count}条`);
        });
      }
      
      // 关闭数据库连接
      db.close((err) => {
        if (err) {
          console.error('❌ 关闭数据库失败:', err.message);
        } else {
          console.log('\n✅ 数据库连接已关闭');
        }
      });
    });
  });
});
