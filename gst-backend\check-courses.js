const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'gst_japanese_training.sqlite');

console.log('📍 检查categories表和课程分类关联...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 连接到SQLite数据库成功');
});

// 查询分类表
db.all("SELECT * FROM categories LIMIT 10", [], (err, categoryRows) => {
  if (err) {
    console.error('❌ 查询categories表失败:', err.message);
  } else {
    console.log(`\n📊 categories表记录数: ${categoryRows.length}`);
    console.log('📋 前10条分类记录:');
    categoryRows.forEach((row, index) => {
      console.log(`${index + 1}. ID:${row.id} 名称:"${row.name}" 描述:"${row.description}"`);
    });
  }

  // 查询课程和分类的关联
  db.all(`
    SELECT
      c.id,
      c.title,
      c.category_id,
      cat.name as category_name,
      c.category as course_type
    FROM courses c
    LEFT JOIN categories cat ON c.category_id = cat.id
    WHERE c.category_id IS NOT NULL
    LIMIT 10
  `, [], (err, courseRows) => {
    if (err) {
      console.error('❌ 查询课程分类关联失败:', err.message);
    } else {
      console.log(`\n📊 有分类关联的课程数: ${courseRows.length}`);
      console.log('📋 课程分类关联示例:');
      courseRows.forEach((row, index) => {
        console.log(`${index + 1}. 课程:"${row.title}" 分类ID:${row.category_id} 分类名:"${row.category_name}" 课程类型:${row.course_type}`);
      });
    }

    // 统计分类关联情况
    db.all(`
      SELECT
        COUNT(*) as total_courses,
        COUNT(category_id) as courses_with_category,
        COUNT(CASE WHEN category_id IS NULL THEN 1 END) as courses_without_category
      FROM courses
    `, [], (err, statRows) => {
      if (err) {
        console.error('❌ 查询统计失败:', err.message);
      } else {
        const stat = statRows[0];
        console.log(`\n📈 课程分类统计:`);
        console.log(`- 总课程数: ${stat.total_courses}`);
        console.log(`- 有分类的课程: ${stat.courses_with_category}`);
        console.log(`- 无分类的课程: ${stat.courses_without_category}`);
        console.log(`- 分类覆盖率: ${((stat.courses_with_category / stat.total_courses) * 100).toFixed(1)}%`);
      }

      // 关闭数据库连接
      db.close((err) => {
        if (err) {
          console.error('❌ 关闭数据库失败:', err.message);
        } else {
          console.log('\n✅ 数据库连接已关闭');
        }
      });
    });
  });
});
