const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { Course, User, LearningRecord } = require('../models');
const { auth, requireTeacher } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 获取课程列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    category,
    level,
    status = 'published',
    page = 1,
    limit = 10,
    search
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { status };

  if (category) whereClause.category = category;
  if (level) whereClause.level = level;
  if (search) {
    whereClause[Course.sequelize.Op.or] = [
      { title: { [Course.sequelize.Op.iLike]: `%${search}%` } },
      { description: { [Course.sequelize.Op.iLike]: `%${search}%` } }
    ];
  }

  const { rows: courses, count } = await Course.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName']
      },
      {
        model: require('../models').Category,
        as: 'courseCategory',
        attributes: ['id', 'name'],
        required: false
      }
    ],
    order: [['orderNum', 'ASC'], ['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  // 处理课程数据，添加分类名称
  const processedCourses = courses.map(course => {
    const courseData = course.toJSON();
    courseData.categoryName = courseData.courseCategory?.name || null;
    return courseData;
  });

  res.json({
    success: true,
    data: {
      courses: processedCourses,
      total: count,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    }
  });
}));

// 获取课程详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const course = await Course.findByPk(id, {
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName', 'email']
      }
    ]
  });

  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  // 增加浏览量
  await course.increment('viewCount');

  res.json({
    success: true,
    data: { course }
  });
}));

// 创建课程
router.post('/', auth, requireTeacher, [
  body('title').isLength({ min: 2, max: 200 }).withMessage('课程标题长度必须在2-200个字符之间'),
  body('description').optional().isLength({ max: 1000 }).withMessage('描述不能超过1000个字符'),
  body('level').isIn(['N5', 'N4', 'N3', 'N2', 'N1']).withMessage('等级必须是N5-N1之一'),
  body('category').isLength({ min: 1, max: 50 }).withMessage('分类代码不能为空且不能超过50个字符'),
  body('duration').optional().isInt({ min: 1 }).withMessage('课程时长必须是正整数'),
  body('difficulty').optional().isInt({ min: 1, max: 5 }).withMessage('难度等级必须在1-5之间')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  const {
    title,
    description,
    content,
    level,
    category,
    duration = 60,
    difficulty = 1,
    orderNum = 0,
    coverImage,
    videoUrl,
    audioUrl,
    attachments = []
  } = req.body;

  // 检查课程标题是否已存在
  const existingCourse = await Course.findOne({ where: { title } });
  if (existingCourse) {
    return res.status(409).json({
      success: false,
      message: '课程标题已存在'
    });
  }

  // 创建课程
  const course = await Course.create({
    title,
    description,
    content,
    level,
    category,
    duration,
    difficulty,
    orderNum,
    coverImage,
    videoUrl,
    audioUrl,
    attachments,
    createdBy: req.user.id,
    status: 'draft'
  });

  logger.info(`课程创建成功: ${course.title} (${course.id}) by ${req.user.username}`);

  res.status(201).json({
    success: true,
    message: '课程创建成功',
    data: { course }
  });
}));

// 更新课程
router.put('/:id', auth, requireTeacher, [
  body('title').optional().isLength({ min: 2, max: 200 }),
  body('description').optional().isLength({ max: 1000 }),
  body('level').optional().isIn(['N5', 'N4', 'N3', 'N2', 'N1']),
  body('category').optional().isLength({ min: 1, max: 50 }),
  body('duration').optional().isInt({ min: 1 }),
  body('difficulty').optional().isInt({ min: 1, max: 5 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  const { id } = req.params;

  const course = await Course.findByPk(id);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  // 权限检查：只有创建者或管理员可以修改
  if (course.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  // 更新课程
  await course.update(req.body);

  logger.info(`课程更新成功: ${course.title} (${course.id}) by ${req.user.username}`);

  res.json({
    success: true,
    message: '课程更新成功',
    data: { course }
  });
}));

// 发布课程
router.patch('/:id/publish', auth, requireTeacher, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const course = await Course.findByPk(id);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  // 权限检查
  if (course.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  await course.update({ status: 'published' });

  logger.info(`课程发布成功: ${course.title} (${course.id}) by ${req.user.username}`);

  res.json({
    success: true,
    message: '课程发布成功',
    data: { course }
  });
}));

// 删除课程
router.delete('/:id', auth, requireTeacher, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const course = await Course.findByPk(id);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  // 权限检查
  if (course.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  // 检查是否有学习记录
  const learningRecords = await LearningRecord.count({
    where: { courseId: id }
  });

  if (learningRecords > 0) {
    return res.status(400).json({
      success: false,
      message: '该课程已有学习记录，无法删除'
    });
  }

  await course.destroy();

  logger.info(`课程删除成功: ${course.title} (${course.id}) by ${req.user.username}`);

  res.json({
    success: true,
    message: '课程删除成功'
  });
}));

// 点赞课程
router.post('/:id/like', auth, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const course = await Course.findByPk(id);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  await course.increment('likeCount');

  res.json({
    success: true,
    message: '点赞成功',
    data: { likeCount: course.likeCount + 1 }
  });
}));

module.exports = router;
