const express = require('express');
const router = express.Router();

// 获取分析报告数据
router.get('/', (req, res) => {
  try {
    const { startDate, endDate, type = 'user' } = req.query;
    
    // 模拟概览数据
    const overview = {
      totalUsers: 12580,
      userGrowth: 15.6,
      totalCourses: 156,
      courseGrowth: 8.3,
      totalLearningTime: 45680,
      learningGrowth: 22.1,
      totalRevenue: 256800,
      revenueGrowth: 18.9
    };
    
    // 生成用户数据
    const userData = Array.from({ length: 30 }, (_, i) => ({
      date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
      newUsers: Math.floor(Math.random() * 50) + 10,
      activeUsers: Math.floor(Math.random() * 200) + 100,
      retentionRate: Math.floor(Math.random() * 30) + 60,
      avgSessionTime: `${Math.floor(Math.random() * 30) + 15}分钟`
    }));
    
    // 生成课程数据
    const courseData = [
      { 
        courseName: 'N5基础日语', 
        enrollments: 1250, 
        completions: 980, 
        completionRate: 78.4, 
        avgRating: 4.6, 
        revenue: 125000 
      },
      { 
        courseName: 'N4进阶日语', 
        enrollments: 890, 
        completions: 720, 
        completionRate: 80.9, 
        avgRating: 4.7, 
        revenue: 89000 
      },
      { 
        courseName: '商务日语', 
        enrollments: 560, 
        completions: 420, 
        completionRate: 75.0, 
        avgRating: 4.5, 
        revenue: 84000 
      },
      { 
        courseName: 'N3中级日语', 
        enrollments: 720, 
        completions: 540, 
        completionRate: 75.0, 
        avgRating: 4.4, 
        revenue: 108000 
      },
      { 
        courseName: '日语口语', 
        enrollments: 450, 
        completions: 320, 
        completionRate: 71.1, 
        avgRating: 4.3, 
        revenue: 67500 
      }
    ];
    
    // 生成学习数据
    const learningData = Array.from({ length: 30 }, (_, i) => ({
      date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
      totalLearningTime: `${Math.floor(Math.random() * 500) + 800}小时`,
      avgLearningTime: `${Math.floor(Math.random() * 30) + 45}分钟`,
      completedLessons: Math.floor(Math.random() * 100) + 200,
      activeStudents: Math.floor(Math.random() * 50) + 150
    }));
    
    // 生成图表数据
    const chartData = {
      userGrowthTrend: userData.map(item => ({
        date: item.date,
        newUsers: item.newUsers,
        activeUsers: item.activeUsers
      })),
      courseCompletionRate: courseData.map(item => ({
        courseName: item.courseName,
        completionRate: item.completionRate,
        enrollments: item.enrollments
      })),
      revenueAnalysis: courseData.map(item => ({
        courseName: item.courseName,
        revenue: item.revenue,
        enrollments: item.enrollments
      })),
      learningTimeDistribution: [
        { timeSlot: '08:00-10:00', count: 1250, percentage: 15.2 },
        { timeSlot: '10:00-12:00', count: 1890, percentage: 23.1 },
        { timeSlot: '14:00-16:00', count: 1560, percentage: 19.0 },
        { timeSlot: '16:00-18:00', count: 1340, percentage: 16.3 },
        { timeSlot: '19:00-21:00', count: 2160, percentage: 26.4 }
      ],
      userDistribution: [
        { region: '北京', count: 2580, percentage: 20.5 },
        { region: '上海', count: 2240, percentage: 17.8 },
        { region: '广州', count: 1890, percentage: 15.0 },
        { region: '深圳', count: 1560, percentage: 12.4 },
        { region: '杭州', count: 1340, percentage: 10.7 },
        { region: '其他', count: 2970, percentage: 23.6 }
      ]
    };
    
    res.json({
      success: true,
      data: {
        overview,
        userData,
        courseData,
        learningData,
        chartData
      }
    });
  } catch (error) {
    console.error('获取分析报告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分析报告失败'
    });
  }
});

// 获取用户分析数据
router.get('/users', (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    // 生成用户增长趋势数据
    const userGrowthTrend = Array.from({ length: days }, (_, i) => {
      const date = new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000);
      return {
        date: date.toISOString().split('T')[0],
        newUsers: Math.floor(Math.random() * 50) + 10,
        activeUsers: Math.floor(Math.random() * 200) + 100,
        totalUsers: 12000 + i * 20 + Math.floor(Math.random() * 30)
      };
    });
    
    // 用户行为分析
    const userBehavior = {
      avgSessionDuration: 28.5, // 分钟
      avgPagesPerSession: 12.3,
      bounceRate: 23.4, // 百分比
      returnUserRate: 67.8 // 百分比
    };
    
    // 用户留存分析
    const retentionAnalysis = {
      day1: 85.2,
      day7: 62.8,
      day30: 45.6,
      day90: 32.1
    };
    
    res.json({
      success: true,
      data: {
        userGrowthTrend,
        userBehavior,
        retentionAnalysis
      }
    });
  } catch (error) {
    console.error('获取用户分析数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户分析数据失败'
    });
  }
});

// 获取课程分析数据
router.get('/courses', (req, res) => {
  try {
    // 课程热度排行
    const coursePopularity = [
      { courseId: 1, courseName: 'N5基础日语', views: 15680, enrollments: 1250, rating: 4.6 },
      { courseId: 2, courseName: 'N4进阶日语', views: 12340, enrollments: 890, rating: 4.7 },
      { courseId: 3, courseName: '商务日语', views: 9870, enrollments: 560, rating: 4.5 },
      { courseId: 4, courseName: 'N3中级日语', views: 8560, enrollments: 720, rating: 4.4 },
      { courseId: 5, courseName: '日语口语', views: 7230, enrollments: 450, rating: 4.3 }
    ];
    
    // 课程完成率分析
    const completionRateAnalysis = coursePopularity.map(course => ({
      ...course,
      completions: Math.floor(course.enrollments * (0.7 + Math.random() * 0.2)),
      avgStudyTime: Math.floor(Math.random() * 20) + 15 // 小时
    }));
    
    // 课程收入分析
    const revenueAnalysis = coursePopularity.map(course => ({
      courseId: course.courseId,
      courseName: course.courseName,
      revenue: course.enrollments * (Math.floor(Math.random() * 50) + 100),
      avgPrice: Math.floor(Math.random() * 50) + 100
    }));
    
    res.json({
      success: true,
      data: {
        coursePopularity,
        completionRateAnalysis,
        revenueAnalysis
      }
    });
  } catch (error) {
    console.error('获取课程分析数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程分析数据失败'
    });
  }
});

// 获取学习分析数据
router.get('/learning', (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    // 学习时长趋势
    const learningTimeTrend = Array.from({ length: days }, (_, i) => {
      const date = new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000);
      return {
        date: date.toISOString().split('T')[0],
        totalHours: Math.floor(Math.random() * 500) + 800,
        avgHours: Math.floor(Math.random() * 3) + 2,
        activeStudents: Math.floor(Math.random() * 50) + 150
      };
    });
    
    // 学习效果分析
    const learningEffectiveness = {
      avgCompletionRate: 76.8,
      avgScore: 82.5,
      passRate: 89.2,
      improvementRate: 15.6
    };
    
    // 学习习惯分析
    const learningHabits = {
      peakHours: ['19:00-21:00', '14:00-16:00', '10:00-12:00'],
      avgSessionLength: 45, // 分钟
      preferredDevices: [
        { device: '手机', percentage: 65.2 },
        { device: '电脑', percentage: 28.7 },
        { device: '平板', percentage: 6.1 }
      ]
    };
    
    res.json({
      success: true,
      data: {
        learningTimeTrend,
        learningEffectiveness,
        learningHabits
      }
    });
  } catch (error) {
    console.error('获取学习分析数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习分析数据失败'
    });
  }
});

// 获取收入分析数据
router.get('/revenue', (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    // 收入趋势
    const revenueTrend = Array.from({ length: days }, (_, i) => {
      const date = new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000);
      return {
        date: date.toISOString().split('T')[0],
        revenue: Math.floor(Math.random() * 5000) + 2000,
        orders: Math.floor(Math.random() * 50) + 20,
        avgOrderValue: Math.floor(Math.random() * 100) + 150
      };
    });
    
    // 收入来源分析
    const revenueSource = [
      { source: '课程购买', amount: 180000, percentage: 70.2 },
      { source: '会员订阅', amount: 45000, percentage: 17.6 },
      { source: '一对一辅导', amount: 25000, percentage: 9.8 },
      { source: '其他', amount: 6200, percentage: 2.4 }
    ];
    
    // 付费用户分析
    const paidUserAnalysis = {
      totalPaidUsers: 3250,
      conversionRate: 25.8,
      avgLifetimeValue: 680,
      churnRate: 8.5
    };
    
    res.json({
      success: true,
      data: {
        revenueTrend,
        revenueSource,
        paidUserAnalysis
      }
    });
  } catch (error) {
    console.error('获取收入分析数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收入分析数据失败'
    });
  }
});

// 导出分析报告
router.post('/export', (req, res) => {
  try {
    const { type = 'comprehensive', format = 'pdf', dateRange } = req.body;
    
    // 这里应该实现报告导出逻辑
    res.json({
      success: true,
      data: {
        downloadUrl: `/api/analytics/download/report_${type}_${Date.now()}.${format}`
      },
      message: '分析报告导出成功'
    });
  } catch (error) {
    console.error('导出分析报告失败:', error);
    res.status(500).json({
      success: false,
      message: '导出分析报告失败'
    });
  }
});

module.exports = router;
