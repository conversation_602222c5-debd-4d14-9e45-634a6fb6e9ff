{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/lp-record/lp-record.vue?f67a", "webpack:///D:/gst/gst-uniapp/components/lp-record/lp-record.vue?58ed", "webpack:///D:/gst/gst-uniapp/components/lp-record/lp-record.vue?2279", "webpack:///D:/gst/gst-uniapp/components/lp-record/lp-record.vue?ea8a", "uni-app:///components/lp-record/lp-record.vue", "webpack:///D:/gst/gst-uniapp/components/lp-record/lp-record.vue?1c8a", "webpack:///D:/gst/gst-uniapp/components/lp-record/lp-record.vue?9b58"], "names": ["name", "components", "lpAudioPlayer", "recordClock", "<PERSON><PERSON><PERSON>", "props", "voicePath", "type", "default", "source", "maxTime", "minTime", "canPuase", "data", "frame", "recordTime", "isRecording", "isRecordPaused", "playing", "playPath", "recorder<PERSON>anager", "innerAudioContext", "themelist", "val", "computed", "showRecordTime", "strs", "watch", "created", "uni", "scope", "success", "console", "<PERSON><PERSON><PERSON><PERSON>", "onReady", "methods", "initValue", "showPicker", "closePicker", "recordReset", "recordStart", "_this", "duration", "recordPause", "recordResume", "recordEnd", "force", "title", "icon", "playVoice", "stopVoice", "initSound", "destorySound", "resetTime", "startTime", "pauseTime", "clearInterval", "resetDraw", "startDraw", "pauseDraw", "onMoveHandler", "onTouchStartHandler", "onLongpressHandler", "onTouchEndHandler", "onRecordEndHandler", "onConfirmHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgD1nB;EACAA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MAAA;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MAAA;MACAH;MACAC;IACA;IACAG;MAAA;MACAJ;MACAC;IACA;IACAI;MAAA;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAGAC;MAAA;MACAC;MAAA;MACAC,YACA;QACAtB;QAAAuB;MACA,GACA;QACAvB;QAAAuB;MACA,GACA;QACAvB;QAAAuB;MACA;IAEA;EACA;EACAC;IACAC;MACA;MACA;MACA;MAEA;MACAC;MAEA;IACA;EACA;EACAC;EACAC;IACA;IAEA;IACA;IACA;MACAC;QACAC;QACAC;MACA;IACA;MACAC;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACAF;EACA;EACAG;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MAEAR;MAEAS;;MAEA;MACA;QAAAC;MAAA;MAEAD;IACA;IAEAE;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACAC;MAEA;QACA;UACA;UACA;QACA;QACA;QACAjB;UACAkB;UACAC;QACA;QACA;MACA;MACAhB;MACA;IACA;IAEAiB;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACAnB;QACAS;QACAA;MACA;;MAEA;MACA;QACAT;QACAS;QACAA;MACA;;MAEA;MACA;QACAT;QACAS;QACAA;MACA;;MAEA;MACA;QACAT;QACAS;QACAA;QACAA;QACAA;MACA;IACA;IAEAW;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;IAEA;IAEAC;MACA;MACA;MACAb;QACAA;QACA;QACA;MACA;IAEA;IAEAc;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAC;MACA;IAAA,CACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;IAAA,CACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;IAEA;IAEA;AACA;AACA;IACAC;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpYA;AAAA;AAAA;AAAA;AAAipC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACArqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/lp-record/lp-record.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./lp-record.vue?vue&type=template&id=57b6003a&\"\nvar renderjs\nimport script from \"./lp-record.vue?vue&type=script&lang=js&\"\nexport * from \"./lp-record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./lp-record.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/lp-record/lp-record.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-record.vue?vue&type=template&id=57b6003a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-record.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"lp-record\">\n\t\t<view @tap=\"showPicker\">\n\t\t\t<slot></slot>\n\t\t</view>\n\t\t<!-- 遮罩层 -->\n\t\t<!-- <view class=\"mask\" @tap.stop=\"closePicker\" v-if=\"isShow\" @touchmove.stop.prevent=\"onMoveHandler\"></view> -->\n\t\t<!-- 多选控件 -->\n\t\t<view class=\"conbox record\">\n\t\t\t<view class=\"panel source-player\" v-if=\"source\">\n\t\t\t\t<view class=\"head\">原音播放</view>\n\t\t\t\t<!-- <lp-audio-player ref=\"audioPlayer\" :nobroud=\"true\" :audio=\"{src:source}\"></lp-audio-player> -->\r\n\t\t\t\t<zaudio :theme=\"themelist[0].val\" :autoplay=\"true\" :continue=\"true\"></zaudio>\n\t\t\t</view>\n\n\t\t\t<view class=\"panel\">\n\t\t\t\t<!-- 此处可放置倒计时，可根据需要自行添加 -->\n\t\t\t\t<view class=\"time\">\n\t\t\t\t\t{{showRecordTime}}\n\t\t\t\t</view>\n\t\t\t\t<view class=\"c999\">\n\t\t\t\t\t最短{{minTime}}秒，最长{{maxTime}}秒\n\t\t\t\t</view>\n\t\t\t\t<view class=\"record-box\" @touchmove.stop.prevent=\"onMoveHandler\">\n\t\t\t\t\t<!-- 空占位 -->\n\t\t\t\t\t<view class=\"btn empty\" v-if=\"isRecording\" style=\"background: unset;\"></view>\n\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t<view class=\"btn stop\" @touchstart.stop=\"stopVoice\" v-if=\"playPath && playing==1\"><text class=\"gui-icons\">&#xe64b;</text></view>\n\t\t\t\t\t\t<view class=\"btn play\" @touchstart.stop=\"playVoice\" v-if=\"playPath && playing==0\"><text class=\"gui-icons\" style=\"margin-left: 8rpx;\">&#xe649;</text></view>\n\t\t\t\t\t</template>\n\t\t\t\t\t<view class=\"btn recording\" @touchstart=\"onTouchStartHandler\" @longpress=\"onLongpressHandler\" @touchend=\"onTouchEndHandler\">\n\t\t\t\t\t\t<text class=\"ws-icon\" :class=\"{flash:isRecording&&!isRecordPaused}\" style=\"font-size: 70rpx;\">\n\t\t\t\t\t\t\t{{isRecording&&!isRecordPaused ? '&#xe76a;' : '&#xeb6b;'}}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn confirm\" @touchstart.stop=\"onRecordEndHandler\" v-if=\"isRecording\"><text class=\"ws-icon\">&#xe611;</text></view>\n\t\t\t\t\t<view class=\"btn confirm\" @touchstart.stop=\"onConfirmHandler\" v-if=\"!isRecording && playPath\"><text class=\"gui-icons\">&#xe60f;</text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"c666 fz32 domess\">{{isRecording ? (isRecordPaused ? '已暂停' : '录音中') : (playPath ? '录音完成' : '点击开始录音')}}</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\timport lpAudioPlayer from '@/components/audio-player/audio-player.vue';\n\timport recordClock from './record-clock.vue';\r\n\timport zaudio from '@/components/uniapp-zaudio/zaudio';\n\n\texport default {\n\t\tname: 'lp-record',\n\t\tcomponents: {\n\t\t\tlpAudioPlayer,\n\t\t\trecordClock,\r\n\t\t\tzaudio\n\t\t},\n\t\tprops: {\n\t\t\tvoicePath: { //默认地址\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 原声\n\t\t\tsource: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tmaxTime: { // 录音最大时长，单位秒\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 15\n\t\t\t},\n\t\t\tminTime: { // 录音最大时长，单位毫秒\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 5\n\t\t\t},\n\t\t\tcanPuase: { // 能否录制过程中暂停，App不可以\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tframe: 50, // 执行绘画的频率，单位毫秒\n\t\t\t\trecordTime: 0, //录音时长\n\t\t\t\tisRecording: false, //是否录制中\n\t\t\t\tisRecordPaused: false, //是否录制暂停\n\t\t\t\tplaying: 0, //是否播放中\n\t\t\t\tplayPath: \"\",\n\n\n\t\t\t\trecorderManager: null, //录音\n\t\t\t\tinnerAudioContext: null, //播放\r\n\t\t\t\tthemelist: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'样式1',val:'theme1',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'样式2',val:'theme2',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'样式3',val:'theme3',\r\n\t\t\t\t\t}\r\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tshowRecordTime() {\n\t\t\t\tvar strs = \"\";\n\t\t\t\tvar m = Math.floor(this.recordTime / 60);\n\t\t\t\tif (m < 10) strs = \"0\" + m;\n\n\t\t\t\tvar s = this.recordTime % 60;\n\t\t\t\tstrs += (s < 10) ? \":0\" + s : \":\" + s;\n\n\t\t\t\treturn strs\n\t\t\t},\n\t\t},\n\t\twatch: {},\n\t\tcreated() {\n\t\t\tvar _this = this;\n\n\t\t\tthis.initValue();\n\t\t\t//获取录音权限\n\t\t\ttry {\n\t\t\t\tuni.authorize({\n\t\t\t\t\tscope: 'scope.record',\n\t\t\t\t\tsuccess() {}\n\t\t\t\t})\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t\tthis.showPicker();\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\tif (this.isRecording) {\n\t\t\t\tthis.recordEnd(true);\n\t\t\t}\n\t\t\tthis.stopVoice();\n\t\t\tthis.pauseTime();\n\t\t},\n\t\tonReady() {\n\t\t\tconsole.log('onReady');\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//组件数据初始化  进入时、关闭时调用初始化\n\t\t\tinitValue() {},\n\t\t\t//显示组件\n\t\t\tshowPicker() {\n\t\t\t\tthis.recordReset();\n\t\t\t\tthis.initSound();\n\t\t\t\t//this.$emit('open');\n\t\t\t},\n\t\t\t//关闭组件\n\t\t\tclosePicker() {\n\t\t\t\t//点遮罩 点取消关闭说明用户不想修改，所以这里对数据进行初始化\n\t\t\t\t//this.initValue(); \n\t\t\t\tif (this.isRecording) {\n\t\t\t\t\tthis.recordEnd();\n\t\t\t\t}\n\t\t\t\tthis.destorySound();\n\t\t\t\tthis.stopVoice();\n\t\t\t\tif (this.$refs.audioPlayer) {\n\t\t\t\t\tthis.$refs.audioPlayer.pause();\n\t\t\t\t}\n\t\t\t\t//this.$emit('close');\n\t\t\t},\n\n\t\t\trecordReset: function() {\n\t\t\t\tthis.playPath = \"\"; //音频地址\t\t\n\t\t\t\tthis.stopVoice();\n\t\t\t\tthis.resetTime();\n\t\t\t\tthis.resetDraw();\n\t\t\t},\n\n\t\t\trecordStart: function() {\n\t\t\t\tlet _this = this;\n\t\t\t\t\n\t\t\t\tconsole.log('recordStart',this.recorderManager);\n\n\t\t\t\t_this.resetTime();\n\n\t\t\t\t// 开始录音\n\t\t\t\tthis.recorderManager.start({duration:this.maxTime*1000});\n\n\t\t\t\t_this.resetDraw();\n\t\t\t},\n\n\t\t\trecordPause: function() {\n\t\t\t\tthis.recorderManager.pause();\n\t\t\t},\n\n\t\t\trecordResume: function() {\n\t\t\t\tthis.recorderManager.resume();\n\t\t\t},\n\n\t\t\trecordEnd: function(force) {\n\t\t\t\tlet recordTime = this.recordTime;\n\t\t\t\tforce = !!force;\n\n\t\t\t\tif (!force && recordTime < this.minTime) {\n\t\t\t\t\tif (recordTime <= 0) {\n\t\t\t\t\t\t//==点击事件==;\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\t//==小于5秒==;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: \"不能小于\" + this.minTime + \"秒,请重新录制\",\n\t\t\t\t\t\ticon: \"none\"\n\t\t\t\t\t})\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tconsole.log('recordEnd');\n\t\t\t\tthis.recorderManager.stop();\n\t\t\t},\n\n\t\t\tplayVoice() {\n\t\t\t\tif (this.playPath) {\n\t\t\t\t\tthis.innerAudioContext.src = this.playPath;\n\t\t\t\t\tthis.innerAudioContext.play();\n\t\t\t\t\tthis.playing = 1;\n\t\t\t\t}\n\t\t\t},\n\t\t\tstopVoice() {\n\t\t\t\tif(this.innerAudioContext){\n\t\t\t\t\tthis.innerAudioContext.stop();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.playing = 0;\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// Source\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tinitSound: function() {\n\t\t\t\tthis.recorderManager = uni.getRecorderManager(); //录音\n\t\t\t\tthis.innerAudioContext = uni.createInnerAudioContext(); //播放\n\t\t\t\tvar _this = this;\n\t\t\t\t\n\t\t\t\tthis.recorderManager.onStart(function(){\n\t\t\t\t\tconsole.log('开始录音');\n\t\t\t\t\t_this.startTime();\n\t\t\t\t\t_this.isRecording = true;\n\t\t\t\t});\n\n\t\t\t\t//录音暂停事件\n\t\t\t\tthis.recorderManager.onPause(function() {\n\t\t\t\t\tconsole.log('录音暂停');\n\t\t\t\t\t_this.isRecordPaused = true;\n\t\t\t\t\t_this.pauseTime();\n\t\t\t\t});\n\n\t\t\t\t// 录音恢复事件\n\t\t\t\tthis.recorderManager.onResume(function() {\n\t\t\t\t\tconsole.log('录音继续');\n\t\t\t\t\t_this.isRecordPaused = false;\n\t\t\t\t\t_this.startTime();\n\t\t\t\t})\n\n\t\t\t\t//录音停止事件\n\t\t\t\tthis.recorderManager.onStop(function(res) {\n\t\t\t\t\tconsole.log('开始结束' + JSON.stringify(res));\n\t\t\t\t\t_this.pauseTime();\n\t\t\t\t\t_this.isRecording = false;\n\t\t\t\t\t_this.playPath = res.tempFilePath;\n\t\t\t\t\t_this.onConfirmHandler();\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tdestorySound: function() {\n\t\t\t\tif (this.recorderManager) {\n\t\t\t\t\tthis.recorderManager.stop();\n\t\t\t\t}\n\t\t\t\tif (this.innerAudioContext) {\n\t\t\t\t\tthis.innerAudioContext.stop();\n\t\t\t\t}\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// Timer\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tresetTime: function() {\n\t\t\t\tthis.recordTime = 0;\n\t\t\t\tthis.pauseTime();\n\n\t\t\t},\n\n\t\t\tstartTime: function() {\n\t\t\t\tlet _this = this;\n\t\t\t\tthis.pauseTime();\n\t\t\t\t_this.timeObj = setInterval(function() {\n\t\t\t\t\t_this.recordTime++;\n\t\t\t\t\t//this.$refs.recordClock.setValue(_this.recordTime / _this.maxTime);\n\t\t\t\t\tif (_this.recordTime == _this.maxTime) _this.recordEnd();\n\t\t\t\t}, 1000);\n\n\t\t\t},\n\n\t\t\tpauseTime: function() {\n\t\t\t\tclearInterval(this.timeObj);\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// Draw\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 从置\n\t\t\t */\n\t\t\tresetDraw: function() {\n\t\t\t\t//this.$refs.recordClock.resetDraw();\n\t\t\t},\n\n\t\t\tstartDraw: function() {\n\t\t\t\t//this.$refs.recordClock.startDraw();\n\t\t\t},\n\n\t\t\tpauseDraw: function() {\n\t\t\t\t//this.$refs.recordClock.pauseDraw();\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 事件取消\n\t\t\t */\n\t\t\tonMoveHandler() {\n\t\t\t\treturn false;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * \n\t\t\t */\n\t\t\tonTouchStartHandler: function() {\n\t\t\t\t// 可以暂停情况下，开始录制\n\t\t\t\tif (this.canPuase) {\n\t\t\t\t\tif (this.isRecording) {\n\t\t\t\t\t\tthis.isRecordPaused ? this.recordResume() : this.recordPause();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.recordReset();\n\t\t\t\t\t\tthis.recordStart();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.recordReset();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 长按\n\t\t\t */\n\t\t\tonLongpressHandler: function() {\n\t\t\t\t// 不可以暂停情况下，开始录制\n\t\t\t\tif (!this.canPuase) {\n\t\t\t\t\tthis.recordStart();\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 长按结束\n\t\t\t */\n\t\t\tonTouchEndHandler: function() {\n\t\t\t\tif (!this.canPuase) {\n\t\t\t\t\tthis.recordEnd();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonRecordEndHandler: function() {\n\t\t\t\tthis.recordEnd();\n\t\t\t},\n\n\t\t\t//点击确定\n\t\t\tonConfirmHandler() {\n\t\t\t\t// var data = {},list = {},textStr = \"\",indexStr = \"\";\t\t\t\t\t\t\t\t\n\t\t\t\tthis.$emit('recconfirm', this.playPath)\n\n\t\t\t\t//确定后更新默认初始值,这样再次进入默认初值就是最后选择的\n\t\t\t\t// this.defaultArr = textStr;\n\t\t\t\t//关闭\n\t\t\t\tthis.closePicker();\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.lp-record {\n\t\tposition: relative;\n\t\tz-index: 99;\n\n\t\t.mask {\n\t\t\tposition: fixed;\n\t\t\tz-index: 1000;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\t}\n\n\t\t.conbox {\n\t\t\tbackground: #fff;\n\t\t}\n\n\t\t.c666 {\n\t\t\tcolor: #666;\n\t\t}\n\n\t\t.c999 {\n\t\t\tcolor: #999;\n\t\t}\n\n\t\t.fz28 {\n\t\t\tfont-size: 28rpx;\n\t\t}\n\n\t\t.fz32 {\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t.source-player {\n\t\t\tpadding: 50rpx 0rpx;\n\t\t\tborder-bottom: solid 1px #eeeeee;\n\n\t\t\t.head {\n\t\t\t\ttext-align: left;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\t\t}\n\n\t\t.record {\n\t\t\ttext-align: center;\n\n\t\t\t.time {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 60rpx;\n\t\t\t\tcolor: #000;\n\t\t\t\tline-height: 100rpx;\n\t\t\t\tmargin-top: 50rpx;\n\t\t\t}\n\n\t\t\t.domess {\n\t\t\t\tmargin-bottom: 50rpx;\n\t\t\t}\n\n\n\t\t\t.record-box {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: row;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\twidth: 90rpx;\n\t\t\t\theight: 90rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tcolor: #fff;\n\t\t\t\tbackground-color: $uni-color-primary;\n\t\t\t\t\n\t\t\t\ttext{\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.recording {\n\t\t\t\t//position: absolute;\n\t\t\t\t//top: 10px;\n\t\t\t\t//left: 10px;\n\t\t\t\tmargin: 20rpx;\n\t\t\t\twidth: 80px;\n\t\t\t\theight: 80px;\n\t\t\t\tbackground-color: $uni-color-error;\n\t\t\t\tz-index: 100;\n\t\t\t\tfont-size: 35px;\n\t\t\t}\n\n\n\t\t\t.stop {}\n\n\t\t\t.play {\n\t\t\t\tmargin-left: 5rpx;\n\t\t\t}\n\n\t\t\t.confirm {}\n\n\n\t\t}\n\n\t}\n\n\t.flash {\n\t\tanimation: 2s opacity2 0s infinite;\n\t\t-webkit-animation: 2s opacity2 0s infinite;\n\t\t-moz-animation: 2s opacity2 0s infinite;\n\t}\n\n\t@keyframes opacity2 {\n\t\t0% {\n\t\t\topacity: 0.1\n\t\t}\n\n\t\t50% {\n\t\t\topacity: .8;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 0.1;\n\t\t}\n\t}\n\n\t@-webkit-keyframes opacity2 {\n\t\t0% {\n\t\t\topacity: 0.1\n\t\t}\n\n\t\t50% {\n\t\t\topacity: .8;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 0.1;\n\t\t}\n\t}\n\n\t@-moz-keyframes opacity2 {\n\t\t0% {\n\t\t\topacity: 0.1\n\t\t}\n\n\t\t50% {\n\t\t\topacity: .8;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 0.1;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-record.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./lp-record.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689565215\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}