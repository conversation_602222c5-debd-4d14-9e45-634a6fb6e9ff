<template>
  <div class="page-container">
    <div class="page-header">
      <h1>权限管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加权限
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧角色列表 -->
      <el-col :span="8">
        <el-card class="roles-card">
          <template #header>
            <div class="card-header">
              <span>角色列表</span>
              <el-button size="small" @click="showRoleDialog = true">
                <el-icon><Plus /></el-icon>
                添加角色
              </el-button>
            </div>
          </template>

          <div class="roles-list">
            <div
              v-for="role in roles"
              :key="role.id"
              class="role-item"
              :class="{ active: selectedRole?.id === role.id }"
              @click="selectRole(role)"
            >
              <div class="role-info">
                <div class="role-name">{{ role.name }}</div>
                <div class="role-desc">{{ role.description }}</div>
                <div class="role-stats">
                  <el-tag size="small">{{ role.userCount || 0 }}个用户</el-tag>
                  <el-tag size="small" type="info">{{ role.permissionCount || 0 }}个权限</el-tag>
                </div>
              </div>
              <div class="role-actions">
                <el-button link size="small" @click.stop="editRole(role)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button link size="small" @click.stop="deleteRole(role)" class="danger-button">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧权限配置 -->
      <el-col :span="16">
        <el-card class="permissions-card">
          <template #header>
            <div class="card-header">
              <span v-if="selectedRole">{{ selectedRole.name }} - 权限配置</span>
              <span v-else>请选择角色</span>
            </div>
          </template>

          <div v-if="!selectedRole" class="no-selection">
            <el-empty description="请从左侧选择一个角色来配置权限" :image-size="80" />
          </div>

          <div v-else class="permissions-config">
            <!-- 权限树 -->
            <el-tree
              ref="permissionTreeRef"
              :data="permissionTree"
              :props="treeProps"
              show-checkbox
              node-key="id"
              :default-checked-keys="selectedRole.permissions || []"
              @check="handlePermissionChange"
              class="permission-tree"
            >
              <template #default="{ node, data }">
                <div class="tree-node">
                  <el-icon class="node-icon">
                    <component :is="getPermissionIcon(data.type)" />
                  </el-icon>
                  <span class="node-label">{{ node.label }}</span>
                  <span class="node-desc">{{ data.description }}</span>
                </div>
              </template>
            </el-tree>

            <!-- 操作按钮 -->
            <div class="permission-actions">
              <el-button @click="expandAll">展开全部</el-button>
              <el-button @click="collapseAll">收起全部</el-button>
              <el-button @click="checkAll">全选</el-button>
              <el-button @click="uncheckAll">取消全选</el-button>
              <el-button type="primary" @click="savePermissions" :loading="saving">
                保存权限配置
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加/编辑角色对话框 -->
    <el-dialog
      :title="editingRole ? '编辑角色' : '添加角色'"
      v-model="showRoleDialog"
      width="500px"
    >
      <el-form ref="roleFormRef" :model="roleForm" :rules="roleFormRules" label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="code">
          <el-input v-model="roleForm.code" placeholder="请输入角色标识，如：admin" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="roleForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRoleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRole" :loading="saving">
          {{ editingRole ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 添加权限对话框 -->
    <el-dialog
      title="添加权限"
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form ref="permissionFormRef" :model="permissionForm" :rules="permissionFormRules" label-width="100px">
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限标识" prop="code">
          <el-input v-model="permissionForm.code" placeholder="请输入权限标识，如：user:create" />
        </el-form-item>
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="permissionForm.type" placeholder="选择权限类型">
            <el-option label="菜单" value="menu" />
            <el-option label="按钮" value="button" />
            <el-option label="接口" value="api" />
          </el-select>
        </el-form-item>
        <el-form-item label="父级权限" prop="parentId">
          <el-tree-select
            v-model="permissionForm.parentId"
            :data="permissionTree"
            :props="treeProps"
            placeholder="选择父级权限（可选）"
            clearable
          />
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="permissionForm.sort" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="savePermission" :loading="saving">
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Edit,
  Delete,
  Menu,
  Operation,
  Link
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const showRoleDialog = ref(false)
const roles = ref([])
const selectedRole = ref(null)
const editingRole = ref(null)
const permissionTreeRef = ref()

// 权限树配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 权限树数据
const permissionTree = ref([])

// 角色表单
const roleForm = reactive({
  name: '',
  code: '',
  description: '',
  status: 'active'
})

const roleFormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '角色标识只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ]
}

// 权限表单
const permissionForm = reactive({
  name: '',
  code: '',
  type: 'menu',
  parentId: null,
  description: '',
  sort: 0
})

const permissionFormRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_:]*$/, message: '权限标识只能包含字母、数字、下划线和冒号，且以字母开头', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 加载角色数据
const loadRoles = async () => {
  loading.value = true
  try {
    const response = await get('/api/roles')
    if (response.success) {
      roles.value = response.data.roles || []
    }
  } catch (error) {
    console.error('加载角色数据失败:', error)
    ElMessage.error('加载角色数据失败')

    // 使用模拟数据
    roles.value = generateMockRoles()
  } finally {
    loading.value = false
  }
}

// 加载权限树
const loadPermissionTree = async () => {
  try {
    const response = await get('/api/permissions/tree')
    if (response.success) {
      permissionTree.value = response.data.tree || []
    }
  } catch (error) {
    console.error('加载权限树失败:', error)

    // 使用模拟数据
    permissionTree.value = generateMockPermissionTree()
  }
}

// 生成模拟角色数据
const generateMockRoles = () => {
  return [
    {
      id: 1,
      name: '超级管理员',
      code: 'admin',
      description: '拥有系统所有权限',
      status: 'active',
      userCount: 2,
      permissionCount: 25,
      permissions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    },
    {
      id: 2,
      name: '教师',
      code: 'teacher',
      description: '可以管理课程和学生',
      status: 'active',
      userCount: 15,
      permissionCount: 12,
      permissions: [1, 2, 5, 6, 9, 10]
    },
    {
      id: 3,
      name: '学生',
      code: 'student',
      description: '只能查看和学习课程',
      status: 'active',
      userCount: 1250,
      permissionCount: 5,
      permissions: [1, 2]
    }
  ]
}

// 生成模拟权限树
const generateMockPermissionTree = () => {
  return [
    {
      id: 1,
      name: '仪表板',
      code: 'dashboard',
      type: 'menu',
      description: '查看系统概览',
      children: []
    },
    {
      id: 2,
      name: '内容管理',
      code: 'content',
      type: 'menu',
      description: '管理系统内容',
      children: [
        {
          id: 3,
          name: '课程管理',
          code: 'course',
          type: 'menu',
          description: '管理课程信息',
          children: [
            {
              id: 4,
              name: '创建课程',
              code: 'course:create',
              type: 'button',
              description: '创建新课程'
            },
            {
              id: 5,
              name: '编辑课程',
              code: 'course:edit',
              type: 'button',
              description: '编辑课程信息'
            },
            {
              id: 6,
              name: '删除课程',
              code: 'course:delete',
              type: 'button',
              description: '删除课程'
            }
          ]
        },
        {
          id: 7,
          name: '用户管理',
          code: 'user',
          type: 'menu',
          description: '管理系统用户',
          children: [
            {
              id: 8,
              name: '创建用户',
              code: 'user:create',
              type: 'button',
              description: '创建新用户'
            },
            {
              id: 9,
              name: '编辑用户',
              code: 'user:edit',
              type: 'button',
              description: '编辑用户信息'
            },
            {
              id: 10,
              name: '删除用户',
              code: 'user:delete',
              type: 'button',
              description: '删除用户'
            }
          ]
        }
      ]
    }
  ]
}

// 选择角色
const selectRole = (role) => {
  selectedRole.value = role
  // 设置权限树的选中状态
  nextTick(() => {
    if (permissionTreeRef.value) {
      permissionTreeRef.value.setCheckedKeys(role.permissions || [])
    }
  })
}

// 编辑角色
const editRole = (role) => {
  editingRole.value = role
  Object.assign(roleForm, {
    name: role.name,
    code: role.code,
    description: role.description,
    status: role.status
  })
  showRoleDialog.value = true
}

// 删除角色
const deleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色"${role.name}"吗？`, '确认删除', {
      type: 'warning'
    })

    await del(`/api/roles/${role.id}`)
    ElMessage.success('角色删除成功')
    await loadRoles()

    if (selectedRole.value?.id === role.id) {
      selectedRole.value = null
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
    }
  }
}

// 保存角色
const saveRole = async () => {
  const roleFormRef = ref()
  try {
    await roleFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    if (editingRole.value) {
      await put(`/api/roles/${editingRole.value.id}`, roleForm)
      ElMessage.success('角色更新成功')
    } else {
      await post('/api/roles', roleForm)
      ElMessage.success('角色创建成功')
    }

    showRoleDialog.value = false
    resetRoleForm()
    await loadRoles()
  } catch (error) {
    console.error('保存角色失败:', error)
    ElMessage.error('保存角色失败')
  } finally {
    saving.value = false
  }
}

// 保存权限
const savePermission = async () => {
  const permissionFormRef = ref()
  try {
    await permissionFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    await post('/api/permissions', permissionForm)
    ElMessage.success('权限创建成功')

    showCreateDialog.value = false
    resetPermissionForm()
    await loadPermissionTree()
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  } finally {
    saving.value = false
  }
}

// 权限变更处理
const handlePermissionChange = (data, checked) => {
  // 这里可以实时保存权限变更
  console.log('权限变更:', data, checked)
}

// 保存权限配置
const savePermissions = async () => {
  if (!selectedRole.value) return

  const checkedKeys = permissionTreeRef.value.getCheckedKeys()
  const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
  const allPermissions = [...checkedKeys, ...halfCheckedKeys]

  saving.value = true
  try {
    await put(`/api/roles/${selectedRole.value.id}/permissions`, {
      permissions: allPermissions
    })

    ElMessage.success('权限配置保存成功')
    selectedRole.value.permissions = allPermissions
    selectedRole.value.permissionCount = checkedKeys.length
  } catch (error) {
    console.error('保存权限配置失败:', error)
    ElMessage.error('保存权限配置失败')
  } finally {
    saving.value = false
  }
}

// 树操作
const expandAll = () => {
  const allKeys = getAllNodeKeys(permissionTree.value)
  allKeys.forEach(key => {
    permissionTreeRef.value.store.nodesMap[key].expanded = true
  })
}

const collapseAll = () => {
  const allKeys = getAllNodeKeys(permissionTree.value)
  allKeys.forEach(key => {
    permissionTreeRef.value.store.nodesMap[key].expanded = false
  })
}

const checkAll = () => {
  const allKeys = getAllNodeKeys(permissionTree.value)
  permissionTreeRef.value.setCheckedKeys(allKeys)
}

const uncheckAll = () => {
  permissionTreeRef.value.setCheckedKeys([])
}

// 获取所有节点key
const getAllNodeKeys = (nodes) => {
  let keys = []
  nodes.forEach(node => {
    keys.push(node.id)
    if (node.children && node.children.length > 0) {
      keys = keys.concat(getAllNodeKeys(node.children))
    }
  })
  return keys
}

// 获取权限图标
const getPermissionIcon = (type) => {
  const icons = {
    menu: 'Menu',
    button: 'Operation',
    api: 'Link'
  }
  return icons[type] || 'Menu'
}

// 重置表单
const resetRoleForm = () => {
  editingRole.value = null
  Object.assign(roleForm, {
    name: '',
    code: '',
    description: '',
    status: 'active'
  })
}

const resetPermissionForm = () => {
  Object.assign(permissionForm, {
    name: '',
    code: '',
    type: 'menu',
    parentId: null,
    description: '',
    sort: 0
  })
}

const refreshData = () => {
  loadRoles()
  loadPermissionTree()
}

// 组件挂载时加载数据
onMounted(() => {
  loadRoles()
  loadPermissionTree()
})
</script>

<style lang="scss" scoped>
.roles-card, .permissions-card {
  height: calc(100vh - 200px);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.roles-list {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.role-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  &.active {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .role-info {
    flex: 1;

    .role-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }

    .role-desc {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
    }

    .role-stats {
      display: flex;
      gap: 8px;
    }
  }

  .role-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  &:hover .role-actions {
    opacity: 1;
  }
}

.permissions-config {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

.permission-tree {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  padding: 16px;

  .tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .node-icon {
      color: var(--el-color-primary);
    }

    .node-label {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .node-desc {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
      margin-left: auto;
    }
  }
}

.permission-actions {
  display: flex;
  gap: 8px;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 16px;
}

.no-selection {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-row {
    flex-direction: column;
  }

  .el-col {
    width: 100% !important;
    margin-bottom: 20px;
  }

  .roles-card, .permissions-card {
    height: auto;
    min-height: 400px;
  }
}
</style>
