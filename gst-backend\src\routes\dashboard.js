const express = require('express');
const { Op } = require('sequelize');
const { auth } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const { 
  User, 
  StudyGroup, 
  Course, 
  CourseUnit,
  Category,
  LearningRecord, 
  Assignment,
  OperationLog 
} = require('../models');

const router = express.Router();

// 获取仪表板统计数据
router.get('/stats', auth, asyncHandler(async (req, res) => {
  try {
    // 基础统计数据
    const [
      totalUsers,
      totalGroups,
      totalCourses,
      totalAssignments,
      totalCategories,
      totalCourseUnits,
      totalLearningRecords
    ] = await Promise.all([
      User.count(),
      StudyGroup.count(),
      Course.count(),
      Assignment.count(),
      Category.count(),
      CourseUnit.count(),
      LearningRecord.count()
    ]);

    // 用户角色分布
    const userRoleStats = await User.findAll({
      attributes: [
        'role',
        [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
      ],
      group: ['role']
    });

    const roleDistribution = {};
    userRoleStats.forEach(stat => {
      roleDistribution[stat.role] = parseInt(stat.dataValues.count);
    });

    // 课程状态分布
    const courseStatusStats = await Course.findAll({
      attributes: [
        'status',
        [Course.sequelize.fn('COUNT', Course.sequelize.col('id')), 'count']
      ],
      group: ['status']
    });

    const courseStatusDistribution = {};
    courseStatusStats.forEach(stat => {
      courseStatusDistribution[stat.status] = parseInt(stat.dataValues.count);
    });

    // 最近7天的学习记录统计
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentLearningStats = await LearningRecord.findAll({
      attributes: [
        [LearningRecord.sequelize.fn('DATE', LearningRecord.sequelize.col('created_at')), 'date'],
        [LearningRecord.sequelize.fn('COUNT', LearningRecord.sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [Op.gte]: sevenDaysAgo
        }
      },
      group: [LearningRecord.sequelize.fn('DATE', LearningRecord.sequelize.col('created_at'))],
      order: [[LearningRecord.sequelize.fn('DATE', LearningRecord.sequelize.col('created_at')), 'ASC']]
    });

    // 最近7天的用户注册统计
    const recentUserStats = await User.findAll({
      attributes: [
        [User.sequelize.fn('DATE', User.sequelize.col('created_at')), 'date'],
        [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [Op.gte]: sevenDaysAgo
        }
      },
      group: [User.sequelize.fn('DATE', User.sequelize.col('created_at'))],
      order: [[User.sequelize.fn('DATE', User.sequelize.col('created_at')), 'ASC']]
    });

    // 活跃用户统计（最近30天有学习记录的用户）
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeUsers = await LearningRecord.findAll({
      attributes: [
        [LearningRecord.sequelize.fn('COUNT', LearningRecord.sequelize.fn('DISTINCT', LearningRecord.sequelize.col('user_id'))), 'count']
      ],
      where: {
        created_at: {
          [Op.gte]: thirtyDaysAgo
        }
      }
    });

    const activeUserCount = activeUsers[0] ? parseInt(activeUsers[0].dataValues.count) : 0;

    // 最受欢迎的课程（按学习记录数量排序）
    const popularCourses = await Course.findAll({
      attributes: [
        'id',
        'title',
        [Course.sequelize.fn('COUNT', Course.sequelize.col('LearningRecords.id')), 'learningCount']
      ],
      include: [
        {
          model: LearningRecord,
          attributes: [],
          required: false
        }
      ],
      group: ['Course.id'],
      order: [[Course.sequelize.fn('COUNT', Course.sequelize.col('LearningRecords.id')), 'DESC']],
      limit: 5
    });

    // 最近的操作日志
    const recentLogs = await OperationLog.findAll({
      include: [
        {
          model: User,
          as: 'User',
          attributes: ['id', 'username', 'realName']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    // 系统健康状态
    const systemHealth = {
      database: 'healthy',
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      },
      uptime: Math.floor(process.uptime()),
      version: '1.0.0'
    };

    // 构建响应数据
    const stats = {
      // 基础统计
      overview: {
        totalUsers,
        totalGroups,
        totalCourses,
        totalAssignments,
        totalCategories,
        totalCourseUnits,
        totalLearningRecords,
        activeUsers: activeUserCount
      },
      
      // 分布统计
      distributions: {
        userRoles: roleDistribution,
        courseStatus: courseStatusDistribution
      },
      
      // 趋势数据
      trends: {
        learningActivity: recentLearningStats.map(stat => ({
          date: stat.dataValues.date,
          count: parseInt(stat.dataValues.count)
        })),
        userRegistration: recentUserStats.map(stat => ({
          date: stat.dataValues.date,
          count: parseInt(stat.dataValues.count)
        }))
      },
      
      // 热门内容
      popular: {
        courses: popularCourses.map(course => ({
          id: course.id,
          title: course.title,
          learningCount: parseInt(course.dataValues.learningCount)
        }))
      },
      
      // 最近活动
      recentActivity: recentLogs.map(log => ({
        id: log.id,
        action: log.action,
        description: log.description,
        user: log.User ? {
          id: log.User.id,
          username: log.User.username,
          realName: log.User.realName
        } : null,
        createdAt: log.createdAt
      })),
      
      // 系统状态
      system: systemHealth
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取仪表板统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
}));

// 获取实时数据（WebSocket或轮询使用）
router.get('/realtime', auth, asyncHandler(async (req, res) => {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

  // 最近一小时的活动统计
  const recentActivity = await Promise.all([
    // 新用户注册
    User.count({
      where: {
        createdAt: {
          [Op.gte]: oneHourAgo
        }
      }
    }),
    
    // 学习活动
    LearningRecord.count({
      where: {
        createdAt: {
          [Op.gte]: oneHourAgo
        }
      }
    }),
    
    // 系统操作
    OperationLog.count({
      where: {
        createdAt: {
          [Op.gte]: oneHourAgo
        }
      }
    })
  ]);

  res.json({
    success: true,
    data: {
      timestamp: now.toISOString(),
      activity: {
        newUsers: recentActivity[0],
        learningRecords: recentActivity[1],
        systemOperations: recentActivity[2]
      },
      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
        },
        uptime: Math.floor(process.uptime())
      }
    }
  });
}));

module.exports = router;
