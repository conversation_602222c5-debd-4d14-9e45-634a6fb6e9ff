{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/webView/webView.vue?f6f1", "webpack:///D:/gst/gst-uniapp/pages/webView/webView.vue?a6f5", "webpack:///D:/gst/gst-uniapp/pages/webView/webView.vue?2554", "webpack:///D:/gst/gst-uniapp/pages/webView/webView.vue?4d9c", "uni-app:///pages/webView/webView.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "onLoad", "console", "data", "webviewStyles", "progress", "color"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;;;AAGtD;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAomB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCQxnB;EACAC;IACA;IACA;MACAC;MACA;IACA;EACA;EACAC;IACA;MACAA;MACAC;QACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/webView/webView.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/webView/webView.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./webView.vue?vue&type=template&id=7447d33a&\"\nvar renderjs\nimport script from \"./webView.vue?vue&type=script&lang=js&\"\nexport * from \"./webView.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/webView/webView.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webView.vue?vue&type=template&id=7447d33a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webView.vue?vue&type=script&lang=js&\"", "<template>\n    <view style=\"height: 100vh;\">\n\t\t<!-- <web-view src=\"./up.html\"></web-view> -->\n        <web-view :webview-styles=\"webviewStyles\" :src=\"data.link\"></web-view>\n    </view>\n</template>\n\n<script>\n    export default {\n\t\tonLoad(option) {\n\t\t\tthis.data = JSON.parse(decodeURIComponent(option.data));\n\t\t\tif(this.data.haveData){\n\t\t\t\tconsole.log(this.data,\"02\");\n\t\t\t\tthis.data.link = this.data.link + \"?data=\" + encodeURIComponent(this.data.datas);\n\t\t\t}\n\t\t},\n        data() {\n            return {\n\t\t\t\tdata:{},\n                webviewStyles: {\n                    progress: {\n                        color: '#FF3333'\n                    }\n                }\n            }\n        }\n    }\n</script>\n\n<style>\n\n</style>"], "sourceRoot": ""}