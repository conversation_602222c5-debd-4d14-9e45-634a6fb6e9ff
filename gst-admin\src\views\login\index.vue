<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-pattern"></div>
    </div>
    
    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <div class="logo">
            <div class="logo-icon">
              <div class="logo-circle">GST</div>
            </div>
            <div class="logo-text">
              <h1>GST日语培训班</h1>
              <p>管理系统</p>
            </div>
          </div>
        </div>
        
        <div class="login-form">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            size="large"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="authStore.loginLoading"
                @click="handleLogin"
                class="login-button"
              >
                {{ authStore.loginLoading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="demo-accounts">
          <el-divider>演示账号</el-divider>
          <div class="account-list">
            <div 
              class="account-item"
              @click="fillAccount('admin', '123456')"
            >
              <el-tag type="danger" size="small">管理员</el-tag>
              <span>admin / 123456</span>
            </div>
            <div 
              class="account-item"
              @click="fillAccount('teacher1', '123456')"
            >
              <el-tag type="warning" size="small">教师</el-tag>
              <span>teacher1 / 123456</span>
            </div>
            <div 
              class="account-item"
              @click="fillAccount('pengwei', '123456')"
            >
              <el-tag type="info" size="small">学生</el-tag>
              <span>pengwei / 123456</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="login-footer">
      <p>&copy; 2024 GST日语培训班管理系统. All rights reserved.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    const result = await authStore.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    if (result.success) {
      // 登录成功，跳转到仪表板
      router.push('/dashboard')
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 填充演示账号
const fillAccount = (username, password) => {
  loginForm.username = username
  loginForm.password = password
}

// 组件挂载时检查登录状态
onMounted(() => {
  if (authStore.isLoggedIn) {
    router.push('/dashboard')
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 400px;
  padding: 0 var(--spacing-lg);
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: var(--spacing-xl);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    
    .logo-icon {
      .logo-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--success-color));
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: bold;
        color: white;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }
    
    .logo-text {
      text-align: left;
      
      h1 {
        margin: 0;
        font-size: var(--font-size-extra-large);
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1.2;
      }
      
      p {
        margin: 0;
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        font-weight: 400;
      }
    }
  }
}

.login-form {
  margin-bottom: var(--spacing-lg);
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: var(--font-size-medium);
    font-weight: 600;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
  
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--border-light);
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      &.is-focus {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }
  }
}

.demo-accounts {
  .account-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .account-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-sm) var(--spacing-md);
      background: var(--bg-color-page);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--border-lighter);
      
      &:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        
        :deep(.el-tag) {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border-color: rgba(255, 255, 255, 0.3);
        }
      }
      
      span {
        font-size: var(--font-size-small);
        color: var(--text-regular);
        font-family: 'Courier New', monospace;
      }
    }
  }
}

.login-footer {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  
  p {
    margin: 0;
    font-size: var(--font-size-small);
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-card {
    margin: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
  
  .login-header .logo {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .logo-text {
      text-align: center;
    }
  }
}
</style>
