{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/search-result-item.vue?d9e0", "webpack:///D:/gst/gst-uniapp/components/search-result-item.vue?d7b3", "webpack:///D:/gst/gst-uniapp/components/search-result-item.vue?fc48", "webpack:///D:/gst/gst-uniapp/components/search-result-item.vue?2a72", "uni-app:///components/search-result-item.vue", "webpack:///D:/gst/gst-uniapp/components/search-result-item.vue?e567", "webpack:///D:/gst/gst-uniapp/components/search-result-item.vue?ec01"], "names": ["name", "props", "item", "type", "required", "keyword", "default", "computed", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACqC;;;AAGtG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgCpnB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;EACA;EACAC;IACAC;MACA;;MAEA;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAq4B,CAAgB,y4BAAG,EAAC,C;;;;;;;;;;;ACAz5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/search-result-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./search-result-item.vue?vue&type=template&id=0e7bdf99&scoped=true&\"\nvar renderjs\nimport script from \"./search-result-item.vue?vue&type=script&lang=js&\"\nexport * from \"./search-result-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search-result-item.vue?vue&type=style&index=0&id=0e7bdf99&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0e7bdf99\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/search-result-item.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search-result-item.vue?vue&type=template&id=0e7bdf99&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.item.tags && _vm.item.tags.length > 0\n  var l0 = g0 ? _vm.item.tags.slice(0, 3) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search-result-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search-result-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"search-result-item\" @click=\"handleClick\">\n\t\t<common-image \n\t\t\t:src=\"item.picture\" \n\t\t\twidth=\"160rpx\" \n\t\t\theight=\"120rpx\"\n\t\t\t:lazy-load=\"true\"\n\t\t\tborder-radius=\"8rpx\"\n\t\t\tclass=\"course-image\"\n\t\t/>\n\t\t<view class=\"item-content\">\n\t\t\t<text class=\"course-title\" v-html=\"highlightedTitle\"></text>\n\t\t\t<text class=\"course-desc\">{{item.des || item.description || item.jianjie}}</text>\n\t\t\t<view class=\"course-meta\">\n\t\t\t\t<text class=\"price\">￥{{item.Cost || item.price || 0}}</text>\n\t\t\t\t<text class=\"students\">{{item.student_count || item.viewnum || 0}}人学习</text>\n\t\t\t\t<text class=\"lessons\">{{item.lesson_count || item.count || 0}}课时</text>\n\t\t\t</view>\n\t\t\t<view class=\"course-tags\" v-if=\"item.tags && item.tags.length > 0\">\n\t\t\t\t<text \n\t\t\t\t\tclass=\"tag\" \n\t\t\t\t\tv-for=\"tag in item.tags.slice(0, 3)\" \n\t\t\t\t\t:key=\"tag\"\n\t\t\t\t>\n\t\t\t\t\t{{tag}}\n\t\t\t\t</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'SearchResultItem',\n\tprops: {\n\t\titem: {\n\t\t\ttype: Object,\n\t\t\trequired: true\n\t\t},\n\t\tkeyword: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t}\n\t},\n\tcomputed: {\n\t\thighlightedTitle() {\n\t\t\tif (!this.keyword || !this.item.title) return this.item.title;\n\n\t\t\t// 简化版高亮，避免正则表达式在小程序中的兼容性问题\n\t\t\tconst title = this.item.title;\n\t\t\tconst keyword = this.keyword.toLowerCase();\n\t\t\tconst titleLower = title.toLowerCase();\n\n\t\t\tif (titleLower.includes(keyword)) {\n\t\t\t\tconst index = titleLower.indexOf(keyword);\n\t\t\t\tconst before = title.substring(0, index);\n\t\t\t\tconst match = title.substring(index, index + keyword.length);\n\t\t\t\tconst after = title.substring(index + keyword.length);\n\t\t\t\treturn `${before}<span class=\"highlight\">${match}</span>${after}`;\n\t\t\t}\n\n\t\t\treturn title;\n\t\t}\n\t},\n\tmethods: {\n\t\thandleClick() {\n\t\t\tthis.$emit('click', this.item);\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.search-result-item {\n\tdisplay: flex;\n\tpadding: 30rpx;\n\tbackground: #fff;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\ttransition: background-color 0.3s ease;\n}\n\n.search-result-item:active {\n\tbackground-color: #f8f9fa;\n}\n\n.course-image {\n\tmargin-right: 20rpx;\n\tflex-shrink: 0;\n}\n\n.item-content {\n\tflex: 1;\n\tmin-width: 0;\n}\n\n.course-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tline-height: 1.4;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.course-title /deep/ .highlight {\n\tcolor: #2094CE;\n\tbackground: rgba(32, 148, 206, 0.1);\n\tpadding: 2rpx 4rpx;\n\tborder-radius: 4rpx;\n\tfont-weight: bold;\n}\n\n.course-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 12rpx;\n\tline-height: 1.4;\n\tdisplay: -webkit-box;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.course-meta {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n\tmargin-bottom: 12rpx;\n\tflex-wrap: wrap;\n}\n\n.price {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #ff4d4f;\n}\n\n.students, .lessons {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.course-tags {\n\tdisplay: flex;\n\tgap: 10rpx;\n\tflex-wrap: wrap;\n}\n\n.tag {\n\tfont-size: 22rpx;\n\tcolor: #2094CE;\n\tbackground: rgba(32, 148, 206, 0.1);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n\tborder: 1rpx solid rgba(32, 148, 206, 0.2);\n}\n\n/* 响应式设计 */\n@media (max-width: 750rpx) {\n\t.search-result-item {\n\t\tflex-direction: column;\n\t\ttext-align: center;\n\t}\n\t\n\t.course-image {\n\t\tmargin-right: 0;\n\t\tmargin-bottom: 20rpx;\n\t\talign-self: center;\n\t}\n\t\n\t.course-meta {\n\t\tjustify-content: center;\n\t}\n\t\n\t.course-tags {\n\t\tjustify-content: center;\n\t}\n}\n\n/* 深色模式支持 */\n@media (prefers-color-scheme: dark) {\n\t.search-result-item {\n\t\tbackground: #1f1f1f;\n\t\tborder-bottom-color: #333;\n\t}\n\t\n\t.search-result-item:active {\n\t\tbackground-color: #2a2a2a;\n\t}\n\t\n\t.course-title {\n\t\tcolor: #fff;\n\t}\n\t\n\t.course-desc {\n\t\tcolor: #ccc;\n\t}\n\t\n\t.students, .lessons {\n\t\tcolor: #999;\n\t}\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search-result-item.vue?vue&type=style&index=0&id=0e7bdf99&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search-result-item.vue?vue&type=style&index=0&id=0e7bdf99&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689557996\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}