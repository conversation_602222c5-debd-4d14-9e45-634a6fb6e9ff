{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/audio-player/audio-player.vue?b445", "webpack:///D:/gst/gst-uniapp/components/audio-player/audio-player.vue?fda3", "webpack:///D:/gst/gst-uniapp/components/audio-player/audio-player.vue?bb42", "webpack:///D:/gst/gst-uniapp/components/audio-player/audio-player.vue?8737", "uni-app:///components/audio-player/audio-player.vue", "webpack:///D:/gst/gst-uniapp/components/audio-player/audio-player.vue?f7a5", "webpack:///D:/gst/gst-uniapp/components/audio-player/audio-player.vue?a0c2"], "names": ["props", "color", "type", "default", "mini", "<PERSON><PERSON><PERSON>", "autoPlay", "audio", "title", "singer", "epname", "coverImgUrl", "src", "data", "playStatus", "player", "playTime", "timer", "audioLength", "<PERSON><PERSON><PERSON><PERSON>", "created", "progress", "ref", "methods", "load", "console", "progressChange", "clearTimeout", "needTime", "timeFormat", "s", "minute", "second", "pause", "play", "destroy"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAymB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiB7nB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;QACAK;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;UACA;QACA;QACA;QACA;QACA;QACAC;QACA;QACA;UACAC;QACA;QACA;MACA;MAAA;IACA;IACA;MACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA,kCAEA;EACA;EACAC;IACAC;MACA;MACAC;MACA;MACA;MACA;MACAnB;IACA;IACAoB;MAAA;MACA;QACAC;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACA;QACAA;QACA;QACA;UACAC;QACA;QACA;UACAC;QACA;QACA;MACA;IAEA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjJA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/audio-player/audio-player.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./audio-player.vue?vue&type=template&id=bf9f8ef8&scoped=true&\"\nvar renderjs\nimport script from \"./audio-player.vue?vue&type=script&lang=js&\"\nexport * from \"./audio-player.vue?vue&type=script&lang=js&\"\nimport style0 from \"./audio-player.vue?vue&type=style&index=0&id=bf9f8ef8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bf9f8ef8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/audio-player/audio-player.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio-player.vue?vue&type=template&id=bf9f8ef8&scoped=true&\"", "var components\ntry {\n  components = {\n    guiSingleSlider: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-single-slider\" */ \"@/GraceUI5/components/gui-single-slider.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio-player.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio-player.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"gui-player\" :class=\"{mini:mini,nobroud:nobroud}\">\n\t\t<!-- 播放控制 -->\n\t\t<view class=\"gui-player-console\">\n\t\t\t<view class=\"gui-player-console-c\">\n\t\t\t\t<text class=\"gui-player-tool gui-icons\" style=\"font-size:33rpx;\" @tap=\"pause\" v-if=\"playStatus == 1\">&#xe64b;</text>\n\t\t\t\t<text class=\"gui-player-tool gui-icons\" style=\"font-size:33rpx;margin-left: 4rpx;\" @tap=\"play\" v-if=\"playStatus == 2\">&#xe649;</text>\n\t\t\t</view>\n\t\t\t<!-- 播放进度 -->\n\t\t\t<view v-if=\"!mini\" class=\"progress-bar\">\n\t\t\t\t<gui-single-slider ref=\"graceSingleSlider\" @change=\"progressChange\" :barWidth=\"100\" :barText=\"playTime\" barColor=\"#999\"\n\t\t\t\t barBgColor=\"linear-gradient(to right, #eeeeee,#eeeeee)\" bglineColor=\"#eeeeee\" bglineAColor=\"#28b28b\"></gui-single-slider>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\texport default {\n\t\tprops: {\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#333'\n\t\t\t},\n\t\t\tmini: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tnobroud:{\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tautoPlay: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\taudio: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: {\n\t\t\t\t\ttitle: \"我们都一样\",\n\t\t\t\t\tsinger: \"张杰\",\n\t\t\t\t\tepname: \"杰哥精选\",\n\t\t\t\t\tcoverImgUrl: \"https://7465-test01-632ffe-1258717418.tcb.qcloud.la/personal/player/images/jie02.jpg?sign=00e5e68d81145037000a162e2220736a&t=1556345760\",\n\t\t\t\t\tsrc: \"https://7465-test01-632ffe-1258717418.tcb.qcloud.la/personal/player/song/%E6%88%91%E4%BB%AC%E9%83%BD%E4%B8%80%E6%A0%B7%20-%20%E5%BC%A0%E6%9D%B0.mp3?sign=008d62b6bea06a8a6814b5f284fac0ac&t=1556345730\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tplayStatus: 2,\n\t\t\t\tplayer: null,\n\t\t\t\tplayTime: '00:00',\n\t\t\t\ttimer: null,\n\t\t\t\taudioLength: 1\n\t\t\t}\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\tthis.destroy();\n\t\t},\n\t\tcreated: function() {\n\t\t\tthis.player = uni.createInnerAudioContext();\n\t\t\tthis.player.onTimeUpdate(() => {\n\t\t\t\ttry {\n\t\t\t\t\tif (this.playStatus != 1) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthis.audioLength = this.player.duration;\n\t\t\t\t\t// 调整进度\n\t\t\t\t\tvar progress = this.player.currentTime / this.audioLength;\n\t\t\t\t\tprogress = Math.round(progress * 100);\n\t\t\t\t\tvar ref = this.$refs.graceSingleSlider;\n\t\t\t\t\tif (ref) {\n\t\t\t\t\t\tref.setProgress(progress);\n\t\t\t\t\t}\n\t\t\t\t\tthis.playTime = this.timeFormat(this.player.currentTime);\n\t\t\t\t} catch (e) {};\n\t\t\t});\n\t\t\tthis.player.onPlay(() => {\n\t\t\t\tthis.playStatus = 1;\n\t\t\t\tthis.audioLength = this.player.duration;\n\t\t\t});\n\t\t\tthis.player.onPause(() => {\n\t\t\t\tthis.playStatus = 2;\n\t\t\t});\n\t\t\tthis.player.onEnded(() => {\n\t\t\t\tthis.playStatus = 2;\n\t\t\t});\r\n\t\t\tthis.load(this.audio, this.autoPlay);\n\t\t\tif (this.player.currentTime < 1) {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tload: function(audio, autoPlay) {\n\t\t\t\t//this.player.title = audio.title;\r\n\t\t\t\tconsole.log('audio.src'+audio.src);\n\t\t\t\tthis.player.singer = audio.singer;\n\t\t\t\tthis.player.coverImgUrl = audio.coverImgUrl;\n\t\t\t\tthis.player.src = audio.src;\n\t\t\t\tautoPlay && this.player.play();\n\t\t\t},\n\t\t\tprogressChange: function(e) {\n\t\t\t\tif (this.timer != null) {\n\t\t\t\t\tclearTimeout(this.timer);\n\t\t\t\t}\n\t\t\t\tthis.player.pause();\n\t\t\t\tvar needTime = this.audioLength * e / 100;\n\t\t\t\tneedTime = Math.round(needTime);\n\t\t\t\tthis.playTime = this.timeFormat(needTime);\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis.player.seek(needTime);\n\t\t\t\t\tthis.player.play();\n\t\t\t\t}, 800);\n\t\t\t},\n\t\t\ttimeFormat: function(s) {\n\t\t\t\ts = Math.round(s);\n\t\t\t\tif (s < 60) {\n\t\t\t\t\tif (s < 10) {\n\t\t\t\t\t\treturn '00:0' + s;\n\t\t\t\t\t}\n\t\t\t\t\treturn '00:' + s;\n\t\t\t\t} else {\n\t\t\t\t\tvar second = s % 60;\n\t\t\t\t\ts = s - second;\n\t\t\t\t\tvar minute = s / 60;\n\t\t\t\t\tif (minute < 10) {\n\t\t\t\t\t\tminute = '0' + minute;\n\t\t\t\t\t}\n\t\t\t\t\tif (second < 10) {\n\t\t\t\t\t\tsecond = '0' + second;\n\t\t\t\t\t}\n\t\t\t\t\treturn minute + ':' + second;\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t\tpause: function() {\n\t\t\t\tthis.player && this.player.pause();\n\t\t\t},\n\t\t\tplay: function() {\n\t\t\t\tthis.player && this.player.play();\n\t\t\t},\n\t\t\tdestroy: function() {\n\t\t\t\tthis.player && this.player.destroy();\n\t\t\t},\n\t\t}\n\t}\n</script>\n<style lang=\"scss\" scoped>\n\t.gui-player {\n\t\tpadding: 30rpx;\n\t\tborder: solid 1px #eee;\n\t\tborder-radius: 10rpx;\n\n\t\t.gui-player-console {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.gui-player-tool {\n\t\t\twidth: 96rpx;\n\t\t\tline-height: 96rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 33rpx;\n\t\t\tdisplay: block;\n\t\t\tflex-shrink: 0;\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\t.gui-player-console-c {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tbackground: #28b28b;\n\t\t\tborder-radius: 50%;\n\t\t\twidth: 96rpx;\n\t\t\theight: 96rpx;\n\t\t}\n\n\t\t.progress-bar {\n\t\t\tflex: 1;\n\t\t\tpadding: 25rpx;\n\t\t}\n\t}\n\n\t.mini ,.nobroud{\n\t\tpadding: unset;\n\t\tborder: unset;\n\t}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio-player.vue?vue&type=style&index=0&id=bf9f8ef8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio-player.vue?vue&type=style&index=0&id=bf9f8ef8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689563907\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}