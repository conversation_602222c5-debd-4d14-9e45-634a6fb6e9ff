const express = require('express');
const router = express.Router();

// 模拟角色数据
let roles = [
  {
    id: 1,
    name: '超级管理员',
    code: 'admin',
    description: '拥有系统所有权限',
    status: 'active',
    userCount: 2,
    permissionCount: 25,
    permissions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 2,
    name: '教师',
    code: 'teacher',
    description: '可以管理课程和学生',
    status: 'active',
    userCount: 15,
    permissionCount: 12,
    permissions: [1, 2, 5, 6, 9, 10],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 3,
    name: '学生',
    code: 'student',
    description: '只能查看和学习课程',
    status: 'active',
    userCount: 1250,
    permissionCount: 5,
    permissions: [1, 2],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

let nextId = 4;

// 获取角色列表
router.get('/', (req, res) => {
  try {
    const { page = 1, limit = 20, name, status } = req.query;
    
    let filteredRoles = [...roles];
    
    // 筛选
    if (name) {
      filteredRoles = filteredRoles.filter(role => 
        role.name.includes(name)
      );
    }
    
    if (status) {
      filteredRoles = filteredRoles.filter(role => role.status === status);
    }
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedRoles = filteredRoles.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        roles: paginatedRoles,
        total: filteredRoles.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色列表失败'
    });
  }
});

// 获取单个角色
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const role = roles.find(r => r.id === parseInt(id));
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    res.json({
      success: true,
      data: { role }
    });
  } catch (error) {
    console.error('获取角色失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色失败'
    });
  }
});

// 创建角色
router.post('/', (req, res) => {
  try {
    const { name, code, description, status = 'active' } = req.body;
    
    // 验证必填字段
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: '角色名称和标识不能为空'
      });
    }
    
    // 检查角色标识是否已存在
    const existingRole = roles.find(r => r.code === code);
    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: '角色标识已存在'
      });
    }
    
    const newRole = {
      id: nextId++,
      name,
      code,
      description: description || '',
      status,
      userCount: 0,
      permissionCount: 0,
      permissions: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    roles.push(newRole);
    
    res.status(201).json({
      success: true,
      data: { role: newRole },
      message: '角色创建成功'
    });
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({
      success: false,
      message: '创建角色失败'
    });
  }
});

// 更新角色
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, status } = req.body;
    
    const roleIndex = roles.findIndex(r => r.id === parseInt(id));
    if (roleIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    // 检查角色标识是否已被其他角色使用
    if (code) {
      const existingRole = roles.find(r => r.code === code && r.id !== parseInt(id));
      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: '角色标识已存在'
        });
      }
    }
    
    // 更新角色信息
    const updatedRole = {
      ...roles[roleIndex],
      ...(name && { name }),
      ...(code && { code }),
      ...(description !== undefined && { description }),
      ...(status && { status }),
      updatedAt: new Date()
    };
    
    roles[roleIndex] = updatedRole;
    
    res.json({
      success: true,
      data: { role: updatedRole },
      message: '角色更新成功'
    });
  } catch (error) {
    console.error('更新角色失败:', error);
    res.status(500).json({
      success: false,
      message: '更新角色失败'
    });
  }
});

// 删除角色
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    const roleIndex = roles.findIndex(r => r.id === parseInt(id));
    if (roleIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    // 检查是否有用户使用该角色
    const role = roles[roleIndex];
    if (role.userCount > 0) {
      return res.status(400).json({
        success: false,
        message: '该角色下还有用户，无法删除'
      });
    }
    
    roles.splice(roleIndex, 1);
    
    res.json({
      success: true,
      message: '角色删除成功'
    });
  } catch (error) {
    console.error('删除角色失败:', error);
    res.status(500).json({
      success: false,
      message: '删除角色失败'
    });
  }
});

// 更新角色权限
router.put('/:id/permissions', (req, res) => {
  try {
    const { id } = req.params;
    const { permissions = [] } = req.body;
    
    const roleIndex = roles.findIndex(r => r.id === parseInt(id));
    if (roleIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    // 更新角色权限
    roles[roleIndex] = {
      ...roles[roleIndex],
      permissions,
      permissionCount: permissions.length,
      updatedAt: new Date()
    };
    
    res.json({
      success: true,
      data: { role: roles[roleIndex] },
      message: '权限配置更新成功'
    });
  } catch (error) {
    console.error('更新角色权限失败:', error);
    res.status(500).json({
      success: false,
      message: '更新角色权限失败'
    });
  }
});

module.exports = router;
