@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-6af323f0 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-6af323f0 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-6af323f0 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-6af323f0 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-6af323f0 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-6af323f0 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-6af323f0::after {
  border: none;
}
.u-hover-class.data-v-6af323f0 {
  opacity: 0.7;
}
.u-primary-light.data-v-6af323f0 {
  color: #ecf5ff;
}
.u-warning-light.data-v-6af323f0 {
  color: #fdf6ec;
}
.u-success-light.data-v-6af323f0 {
  color: #f5fff0;
}
.u-error-light.data-v-6af323f0 {
  color: #fef0f0;
}
.u-info-light.data-v-6af323f0 {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-6af323f0 {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-6af323f0 {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-6af323f0 {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-6af323f0 {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-6af323f0 {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-6af323f0 {
  color: #398ade;
}
.u-warning-dark.data-v-6af323f0 {
  color: #f1a532;
}
.u-success-dark.data-v-6af323f0 {
  color: #53c21d;
}
.u-error-dark.data-v-6af323f0 {
  color: #e45656;
}
.u-info-dark.data-v-6af323f0 {
  color: #767a82;
}
.u-primary-dark-bg.data-v-6af323f0 {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-6af323f0 {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-6af323f0 {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-6af323f0 {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-6af323f0 {
  background-color: #767a82;
}
.u-primary-disabled.data-v-6af323f0 {
  color: #9acafc;
}
.u-warning-disabled.data-v-6af323f0 {
  color: #f9d39b;
}
.u-success-disabled.data-v-6af323f0 {
  color: #a9e08f;
}
.u-error-disabled.data-v-6af323f0 {
  color: #f7b2b2;
}
.u-info-disabled.data-v-6af323f0 {
  color: #c4c6c9;
}
.u-primary.data-v-6af323f0 {
  color: #3c9cff;
}
.u-warning.data-v-6af323f0 {
  color: #f9ae3d;
}
.u-success.data-v-6af323f0 {
  color: #5ac725;
}
.u-error.data-v-6af323f0 {
  color: #f56c6c;
}
.u-info.data-v-6af323f0 {
  color: #909399;
}
.u-primary-bg.data-v-6af323f0 {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-6af323f0 {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-6af323f0 {
  background-color: #5ac725;
}
.u-error-bg.data-v-6af323f0 {
  background-color: #f56c6c;
}
.u-info-bg.data-v-6af323f0 {
  background-color: #909399;
}
.u-main-color.data-v-6af323f0 {
  color: #303133;
}
.u-content-color.data-v-6af323f0 {
  color: #606266;
}
.u-tips-color.data-v-6af323f0 {
  color: #909193;
}
.u-light-color.data-v-6af323f0 {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-6af323f0 {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-6af323f0 {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-6af323f0 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-6af323f0 {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-6af323f0::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-6af323f0 {
  background: #fff;
}
.search.data-v-6af323f0 {
  height: 90rpx;
  position: fixed;
  top: 0;
  z-index: 5;
  width: 100%;
  display: flex;
  flex-direction: row;
  background-color: #2094CE;
  color: #ffffff;
  align-items: center;
  justify-content: center;
}
.yzb-search.data-v-6af323f0 {
  color: #999999;
  font-size: 28rpx;
  margin: 0 15rpx;
  margin-top: 8rpx;
}
.search-input.data-v-6af323f0 {
  width: 77%;
  height: 65rpx;
  border-radius: 50rpx;
  display: flex;
  flex-direction: row;
  background-color: #ffffff;
  align-items: center;
}
.search-input image.data-v-6af323f0 {
  width: 35rpx;
  height: 35rpx;
  margin-right: 10rpx;
}
.search-input input.data-v-6af323f0 {
  width: 65%;
  font-size: 28rpx;
  color: #333333;
}
.btn-search.data-v-6af323f0 {
  margin-left: 20rpx;
}
.btn-cancel.data-v-6af323f0 {
  margin-left: 20rpx;
}
.searchRecent-title.data-v-6af323f0 {
  height: 60rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.searchRecent-title text.data-v-6af323f0 {
  font-size: 80rpx;
  color: #666666;
}
.history.data-v-6af323f0 {
  flex-wrap: wrap;
  display: flex;
  flex-direction: row;
}
.searchRecent-content.data-v-6af323f0 {
  margin: 15rpx 20rpx 15rpx 0;
  padding: 8rpx 30rpx;
  background-color: #f4f4f4;
  display: flex;
  border-radius: 5rpx;
}
.type.data-v-6af323f0 {
  width: 100%;
  height: 80rpx;
  position: fixed;
  top: 90rpx;
  z-index: 5;
}
.type-view.data-v-6af323f0 {
  width: 25%;
  height: 80rpx;
}
.type-view-line.data-v-6af323f0 {
  width: 45rpx;
  height: 4rpx;
  border-radius: 15rpx;
}
.wonderful.data-v-6af323f0 {
  width: 95%;
  flex-wrap: wrap;
}
.wonderful-content.data-v-6af323f0 {
  width: 47%;
  height: 340rpx;
  position: relative;
}
.wonderful-playImg.data-v-6af323f0 {
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  top: 60rpx;
  left: 125rpx;
}
.wonderful-content-img.data-v-6af323f0 {
  width: 100%;
  height: 190rpx;
  border-radius: 12rpx;
}
.placeholder-90.data-v-6af323f0 {
  width: 100%;
  height: 90rpx;
}
.placeholder-170.data-v-6af323f0 {
  width: 100%;
  height: 170rpx;
}
.forum.data-v-6af323f0 {
  /* margin-top: 100upx; */
}
.forum-line.data-v-6af323f0 {
  border-top: 5rpx #efefef solid;
}
.forum-top.data-v-6af323f0 {
  width: 94.5%;
}
.forum-top-left image.data-v-6af323f0 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.share.data-v-6af323f0 {
  width: 50rpx;
  height: 50rpx;
}
.forum-top-content.data-v-6af323f0 {
  width: 94.5%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.forum-img.data-v-6af323f0 {
  width: 94.5%;
  flex-wrap: wrap;
  padding-bottom: 0;
  /* justify-content: space-around; */
}
.forum-img-image.data-v-6af323f0 {
  width: 226rpx;
  height: 226rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
}
.forum-img-image.data-v-6af323f0:nth-of-type(3n) {
  margin-right: 0rpx;
}
.forum-btn.data-v-6af323f0 {
  height: 100rpx;
}
.btn.data-v-6af323f0 {
  width: 250rpx;
}
.btn image.data-v-6af323f0 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.list-box .item-box.data-v-6af323f0 {
  padding: 10rpx 10rpx;
  background-color: #fff;
  border-radius: 10rpx;
  color: #666;
  font-size: 28rpx;
  border-bottom: 1rpx solid #ebebeb;
}
.list-box .item-box .top-box.data-v-6af323f0 {
  position: relative;
  padding: 20rpx;
}
.list-box .item-box .top-box .cover-box-hot.data-v-6af323f0 {
  width: 35%;
  height: auto;
  min-height: 120rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot .cover.data-v-6af323f0 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box-hot .cover.data-v-6af323f0 :after {
  background-color: red;
  border-radius: 10rpx;
  color: #fff;
  content: "hot";
  font-size: 25rpx;
  line-height: 1;
  padding: 2rpx 6rpx;
  position: absolute;
  left: 5rpx;
  top: 5rpx;
}
.list-box .item-box .top-box .cover-box-hot .button.data-v-6af323f0 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-box.data-v-6af323f0 {
  width: 150rpx;
  height: auto;
  min-height: 150rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box .cover.data-v-6af323f0 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box .button.data-v-6af323f0 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-large-box.data-v-6af323f0 {
  width: 100%;
  height: auto;
  height: 200rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-large-box .cover.data-v-6af323f0 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-large-box .button.data-v-6af323f0 {
  position: absolute;
  bottom: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: white;
  padding: 15rpx 20rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .info-box.data-v-6af323f0 {
  flex: 1;
  margin-left: 15rpx;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.list-box .item-box .top-box .info-box .publish-date.data-v-6af323f0 {
  font-size: 32rpx;
  font-weight: bold;
}
.list-box .item-box .top-box .info-box .lang-box.data-v-6af323f0 {
  font-size: 24rpx;
}
.list-box .item-box .top-box .info-box .title.data-v-6af323f0 {
  font-weight: bold;
  font-size: 24rpx;
  color: #666666;
}
.list-box .item-box .top-box .info-box .end-date.data-v-6af323f0 {
  font-size: 20rpx;
  color: #999999;
}
.list-box .item-box .top-box .info-box .total.data-v-6af323f0 {
  font-size: 20rpx;
  color: #39b54a;
}
.list-box .item-box .top-box .info-box .des.data-v-6af323f0 {
  font-size: 22rpx;
  color: #8f8f94;
}
.list-box .item-box .top-box .info-box .price.data-v-6af323f0 {
  font-size: 24rpx;
  color: red;
  float: right;
}
.list-box .item-box .top-box .info-box .end.data-v-6af323f0 {
  font-size: 24rpx;
  color: blue;
  width: 100%;
}
.list-box .item-box .margin-tb-sm.data-v-6af323f0 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.list-box .item-box .margin-tb-sm .text-sm.data-v-6af323f0 {
  font-size: 24rpx;
}
.list-box .item-box .margin-tb-sm .title.data-v-6af323f0 {
  overflow: hidden;
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
.list-box .item-box .margin-tb-sm .uni-row.data-v-6af323f0 {
  flex-direction: row;
}
.list-box .item-box .margin-tb-sm .align-center.data-v-6af323f0 {
  align-items: center;
}
.list-box .item-box .margin-tb-sm .margin-left-sm.data-v-6af323f0 {
  margin-left: 20rpx;
}

