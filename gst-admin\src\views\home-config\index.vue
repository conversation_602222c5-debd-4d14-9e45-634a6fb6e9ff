﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>首页配置</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="previewHome">
          <el-icon><View /></el-icon>
          预览首页
        </el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧配置面板 -->
      <el-col :span="8">
        <el-card class="config-panel">
          <template #header>
            <div class="card-header">
              <span>首页模块配置</span>
            </div>
          </template>

          <el-collapse v-model="activeCollapse" accordion>
            <!-- 轮播图配置 -->
            <el-collapse-item title="轮播图设置" name="banner">
              <div class="config-section">
                <el-form-item label="显示轮播图">
                  <el-switch v-model="homeConfig.banner.enabled" />
                </el-form-item>
                <el-form-item label="自动播放">
                  <el-switch v-model="homeConfig.banner.autoplay" />
                </el-form-item>
                <el-form-item label="播放间隔">
                  <el-input-number
                    v-model="homeConfig.banner.interval"
                    :min="1000"
                    :max="10000"
                    :step="1000"
                    suffix="ms"
                  />
                </el-form-item>
                <el-form-item label="显示指示器">
                  <el-switch v-model="homeConfig.banner.indicators" />
                </el-form-item>
                <el-form-item label="高度设置">
                  <el-input-number
                    v-model="homeConfig.banner.height"
                    :min="150"
                    :max="400"
                    suffix="px"
                  />
                </el-form-item>
              </div>
            </el-collapse-item>

            <!-- 分类导航配置 -->
            <el-collapse-item title="分类导航" name="categories">
              <div class="config-section">
                <el-form-item label="显示分类导航">
                  <el-switch v-model="homeConfig.categories.enabled" />
                </el-form-item>
                <el-form-item label="显示方式">
                  <el-radio-group v-model="homeConfig.categories.layout">
                    <el-radio label="grid">网格布局</el-radio>
                    <el-radio label="list">列表布局</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="每行显示">
                  <el-input-number
                    v-model="homeConfig.categories.columns"
                    :min="2"
                    :max="5"
                  />
                </el-form-item>
                <el-form-item label="显示图标">
                  <el-switch v-model="homeConfig.categories.showIcon" />
                </el-form-item>
              </div>
            </el-collapse-item>

            <!-- 推荐课程配置 -->
            <el-collapse-item title="推荐课程" name="courses">
              <div class="config-section">
                <el-form-item label="显示推荐课程">
                  <el-switch v-model="homeConfig.courses.enabled" />
                </el-form-item>
                <el-form-item label="标题">
                  <el-input v-model="homeConfig.courses.title" placeholder="推荐课程" />
                </el-form-item>
                <el-form-item label="显示数量">
                  <el-input-number
                    v-model="homeConfig.courses.limit"
                    :min="4"
                    :max="20"
                  />
                </el-form-item>
                <el-form-item label="排序方式">
                  <el-select v-model="homeConfig.courses.sortBy">
                    <el-option label="最新发布" value="created_at" />
                    <el-option label="最受欢迎" value="popularity" />
                    <el-option label="评分最高" value="rating" />
                    <el-option label="手动排序" value="manual" />
                  </el-select>
                </el-form-item>
                <el-form-item label="显示价格">
                  <el-switch v-model="homeConfig.courses.showPrice" />
                </el-form-item>
                <el-form-item label="显示评分">
                  <el-switch v-model="homeConfig.courses.showRating" />
                </el-form-item>
              </div>
            </el-collapse-item>

            <!-- 公告通知配置 -->
            <el-collapse-item title="公告通知" name="notice">
              <div class="config-section">
                <el-form-item label="显示公告">
                  <el-switch v-model="homeConfig.notice.enabled" />
                </el-form-item>
                <el-form-item label="公告内容">
                  <el-input
                    v-model="homeConfig.notice.content"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入公告内容"
                  />
                </el-form-item>
                <el-form-item label="显示方式">
                  <el-radio-group v-model="homeConfig.notice.type">
                    <el-radio label="scroll">滚动显示</el-radio>
                    <el-radio label="static">静态显示</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="背景颜色">
                  <el-color-picker v-model="homeConfig.notice.backgroundColor" />
                </el-form-item>
                <el-form-item label="文字颜色">
                  <el-color-picker v-model="homeConfig.notice.textColor" />
                </el-form-item>
              </div>
            </el-collapse-item>

            <!-- 快捷功能配置 -->
            <el-collapse-item title="快捷功能" name="shortcuts">
              <div class="config-section">
                <el-form-item label="显示快捷功能">
                  <el-switch v-model="homeConfig.shortcuts.enabled" />
                </el-form-item>
                <el-form-item label="功能列表">
                  <div class="shortcuts-list">
                    <div
                      v-for="(shortcut, index) in homeConfig.shortcuts.items"
                      :key="index"
                      class="shortcut-item"
                    >
                      <el-input v-model="shortcut.name" placeholder="功能名称" size="small" />
                      <el-input v-model="shortcut.icon" placeholder="图标名称" size="small" />
                      <el-input v-model="shortcut.path" placeholder="跳转路径" size="small" />
                      <el-button
                        @click="removeShortcut(index)"
                        size="small"
                        type="danger"
                        link
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                    <el-button @click="addShortcut" size="small" type="primary" plain>
                      <el-icon><Plus /></el-icon>
                      添加功能
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-collapse-item>

            <!-- 底部信息配置 -->
            <el-collapse-item title="底部信息" name="footer">
              <div class="config-section">
                <el-form-item label="显示底部信息">
                  <el-switch v-model="homeConfig.footer.enabled" />
                </el-form-item>
                <el-form-item label="版权信息">
                  <el-input v-model="homeConfig.footer.copyright" placeholder="© 2024 GST日语培训" />
                </el-form-item>
                <el-form-item label="联系电话">
                  <el-input v-model="homeConfig.footer.phone" placeholder="************" />
                </el-form-item>
                <el-form-item label="客服微信">
                  <el-input v-model="homeConfig.footer.wechat" placeholder="GST_Service" />
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>

      <!-- 右侧预览面板 -->
      <el-col :span="16">
        <el-card class="preview-panel">
          <template #header>
            <div class="card-header">
              <span>首页预览</span>
              <div class="preview-actions">
                <el-button-group>
                  <el-button
                    :type="previewDevice === 'mobile' ? 'primary' : ''"
                    @click="previewDevice = 'mobile'"
                    size="small"
                  >
                    <el-icon><Iphone /></el-icon>
                    手机
                  </el-button>
                  <el-button
                    :type="previewDevice === 'tablet' ? 'primary' : ''"
                    @click="previewDevice = 'tablet'"
                    size="small"
                  >
                    <el-icon><Monitor /></el-icon>
                    平板
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>

          <div class="preview-container">
            <div
              class="preview-phone"
              :class="{ 'preview-tablet': previewDevice === 'tablet' }"
            >
              <div class="preview-header">
                <div class="preview-status-bar"></div>
                <div class="preview-title">GST日语</div>
              </div>

              <div class="preview-content">
                <!-- 轮播图预览 -->
                <div v-if="homeConfig.banner.enabled" class="preview-banner">
                  <div class="banner-placeholder">
                    <el-icon><Picture /></el-icon>
                    <span>轮播图区域</span>
                  </div>
                </div>

                <!-- 公告预览 -->
                <div
                  v-if="homeConfig.notice.enabled"
                  class="preview-notice"
                  :style="{
                    backgroundColor: homeConfig.notice.backgroundColor,
                    color: homeConfig.notice.textColor
                  }"
                >
                  <el-icon><Bell /></el-icon>
                  <span>{{ homeConfig.notice.content || '暂无公告' }}</span>
                </div>

                <!-- 分类导航预览 -->
                <div v-if="homeConfig.categories.enabled" class="preview-categories">
                  <div class="section-title">课程分类</div>
                  <div
                    class="categories-grid"
                    :style="{ gridTemplateColumns: `repeat(${homeConfig.categories.columns}, 1fr)` }"
                  >
                    <div v-for="i in 6" :key="i" class="category-item">
                      <div class="category-icon">N{{ i }}</div>
                      <div class="category-name">日语N{{ i }}</div>
                    </div>
                  </div>
                </div>

                <!-- 推荐课程预览 -->
                <div v-if="homeConfig.courses.enabled" class="preview-courses">
                  <div class="section-title">{{ homeConfig.courses.title || '推荐课程' }}</div>
                  <div class="courses-list">
                    <div v-for="i in Math.min(homeConfig.courses.limit, 4)" :key="i" class="course-item">
                      <div class="course-image"></div>
                      <div class="course-info">
                        <div class="course-title">日语课程 {{ i }}</div>
                        <div class="course-meta">
                          <span v-if="homeConfig.courses.showRating" class="course-rating">★ 4.8</span>
                          <span v-if="homeConfig.courses.showPrice" class="course-price">¥199</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 快捷功能预览 -->
                <div v-if="homeConfig.shortcuts.enabled && homeConfig.shortcuts.items.length > 0" class="preview-shortcuts">
                  <div class="section-title">快捷功能</div>
                  <div class="shortcuts-grid">
                    <div v-for="shortcut in homeConfig.shortcuts.items" :key="shortcut.name" class="shortcut-item">
                      <div class="shortcut-icon">{{ shortcut.icon || '📚' }}</div>
                      <div class="shortcut-name">{{ shortcut.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 底部信息预览 -->
                <div v-if="homeConfig.footer.enabled" class="preview-footer">
                  <div class="footer-info">
                    <div>{{ homeConfig.footer.copyright }}</div>
                    <div v-if="homeConfig.footer.phone">电话：{{ homeConfig.footer.phone }}</div>
                    <div v-if="homeConfig.footer.wechat">微信：{{ homeConfig.footer.wechat }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get, put } from '@/utils/request'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  View,
  Check,
  Delete,
  Plus,
  Picture,
  Bell,
  Iphone,
  Monitor
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const activeCollapse = ref('banner')
const previewDevice = ref('mobile')

// 首页配置数据
const homeConfig = reactive({
  banner: {
    enabled: true,
    autoplay: true,
    interval: 3000,
    indicators: true,
    height: 200
  },
  categories: {
    enabled: true,
    layout: 'grid',
    columns: 4,
    showIcon: true
  },
  courses: {
    enabled: true,
    title: '推荐课程',
    limit: 8,
    sortBy: 'popularity',
    showPrice: true,
    showRating: true
  },
  notice: {
    enabled: true,
    content: '欢迎来到GST日语培训，开启您的日语学习之旅！',
    type: 'scroll',
    backgroundColor: '#f0f9ff',
    textColor: '#1e40af'
  },
  shortcuts: {
    enabled: true,
    items: [
      { name: '我的课程', icon: '📚', path: '/my-courses' },
      { name: '学习记录', icon: '📊', path: '/learning-records' },
      { name: '在线测试', icon: '📝', path: '/tests' },
      { name: '学习社区', icon: '👥', path: '/community' }
    ]
  },
  footer: {
    enabled: true,
    copyright: '© 2024 GST日语培训',
    phone: '************',
    wechat: 'GST_Service'
  }
})

// 加载首页配置
const loadHomeConfig = async () => {
  loading.value = true
  try {
    const response = await get('/api/home-config')
    if (response.success && response.data.config) {
      Object.assign(homeConfig, response.data.config)
    }
  } catch (error) {
    console.error('加载首页配置失败:', error)
    ElMessage.error('加载首页配置失败')
  } finally {
    loading.value = false
  }
}

// 保存配置
const saveConfig = async () => {
  saving.value = true
  try {
    await put('/api/home-config', { config: homeConfig })
    ElMessage.success('首页配置保存成功')
  } catch (error) {
    console.error('保存首页配置失败:', error)
    ElMessage.error('保存首页配置失败')
  } finally {
    saving.value = false
  }
}

// 预览首页
const previewHome = () => {
  // 这里可以打开新窗口预览首页
  window.open('/preview/home', '_blank')
  ElMessage.info('预览功能开发中...')
}

// 添加快捷功能
const addShortcut = () => {
  homeConfig.shortcuts.items.push({
    name: '',
    icon: '📱',
    path: ''
  })
}

// 删除快捷功能
const removeShortcut = (index) => {
  homeConfig.shortcuts.items.splice(index, 1)
}

const refreshData = () => {
  loadHomeConfig()
}

// 组件挂载时加载数据
onMounted(() => {
  loadHomeConfig()
})
</script>

<style lang="scss" scoped>
.config-panel, .preview-panel {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-section {
  .el-form-item {
    margin-bottom: 16px;
  }
}

// 快捷功能配置样式
.shortcuts-list {
  .shortcut-item {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;

    .el-input {
      flex: 1;
    }
  }
}

// 预览面板样式
.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.preview-phone {
  width: 375px;
  height: 667px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;

  &.preview-tablet {
    width: 480px;
    height: 640px;
    border-radius: 12px;
  }
}

.preview-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  text-align: center;

  .preview-status-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-bottom: 8px;
  }

  .preview-title {
    font-size: 18px;
    font-weight: 600;
  }
}

.preview-content {
  height: calc(100% - 60px);
  overflow-y: auto;
  padding: 0;
}

// 轮播图预览
.preview-banner {
  height: 200px;
  background: linear-gradient(45deg, #f0f2f5, #e6f7ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;

  .banner-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 32px;
    }
  }
}

// 公告预览
.preview-notice {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;

  .el-icon {
    font-size: 16px;
  }
}

// 分类导航预览
.preview-categories {
  padding: 16px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
  }

  .categories-grid {
    display: grid;
    gap: 12px;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      text-align: center;

      .category-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .category-name {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

// 推荐课程预览
.preview-courses {
  padding: 16px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
  }

  .courses-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    .course-item {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .course-image {
        height: 80px;
        background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);
      }

      .course-info {
        padding: 8px;

        .course-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
          color: #333;
        }

        .course-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;

          .course-rating {
            color: #f39c12;
          }

          .course-price {
            color: #e74c3c;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 快捷功能预览
.preview-shortcuts {
  padding: 16px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
  }

  .shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;

    .shortcut-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px;
      text-align: center;

      .shortcut-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }

      .shortcut-name {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

// 底部信息预览
.preview-footer {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  margin-top: auto;

  .footer-info {
    text-align: center;
    font-size: 12px;
    color: #666;
    line-height: 1.5;

    div {
      margin-bottom: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-row {
    flex-direction: column;
  }

  .el-col {
    width: 100% !important;
    margin-bottom: 20px;
  }

  .preview-phone {
    width: 100%;
    max-width: 375px;
    height: 500px;
  }
}
</style>
