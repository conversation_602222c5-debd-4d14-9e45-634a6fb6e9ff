{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/article/sign-in-share/sign-in-share.vue?0353", "webpack:///D:/gst/gst-uniapp/pages/article/sign-in-share/sign-in-share.vue?01ae", "webpack:///D:/gst/gst-uniapp/pages/article/sign-in-share/sign-in-share.vue?2277", "webpack:///D:/gst/gst-uniapp/pages/article/sign-in-share/sign-in-share.vue?13d4", "uni-app:///pages/article/sign-in-share/sign-in-share.vue", "webpack:///D:/gst/gst-uniapp/pages/article/sign-in-share/sign-in-share.vue?c981", "webpack:///D:/gst/gst-uniapp/pages/article/sign-in-share/sign-in-share.vue?7d44"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "isReady", "article_id", "poster", "posterImage", "canvasId", "course", "article", "code", "onLoad", "created", "methods", "ready", "uni", "title", "shareFc", "_app", "_this", "type", "formData", "posterCanvasId", "delayTimeScale", "backgroundImage", "drawArray", "bgObj", "bgScale", "rs", "text", "fontWeight", "size", "color", "alpha", "textAlign", "textBaseline", "serialNum", "dy", "infoCallBack", "console", "dx", "url", "circleSet", "dWidth", "dHeight", "mode", "setCanvasWH", "d", "saveImage", "filePath", "success", "share", "hideQr", "onShareAppMessage", "path", "imageUrl", "apiGetShareDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAynB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsD7oB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IAEA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACAC;QACAC;MACA;MACA;QACAD;QACA;QACA;QACA;QAEA;MACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;kBACAC;kBAAA;kBACAC;kBACAC;oBACA;kBAAA,CAEA;kBACAC;kBAAA;kBACAC;kBAAA;kBACA;AACA;AACA;AACA;AACA;kBACAC;kBACAC,oCAIA;oBAAA,IAHAC;sBACAN;sBACAO;oBAEA;oBACA;oBACA;;oBAEA;oBACA;sBACAC;sBACA;sBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;sBACA;sBACA;wBACAR;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;0BACAC;0BACA;4BACAC;0BACA;wBACA;sBACA;sBACA;;sBAEA;sBACA;wBACApB;wBACAqB;wBACAR;wBACAO;wBACAH;wBACAC;0BACA;0BACA;4BACAI;4BAAA;4BACAC;4BAAA;4BACAC;4BACA;AACA;AACA;0BACA;wBACA;sBACA,GACA;wBACAxB;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAI;wBACAH;sBACA,GACA;wBACAjB;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAI;wBACAH;sBACA;sBACA;sBACA;wBACAjB;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAI;wBACAH;sBACA,GACA;wBACAjB;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAI;wBACAH;sBACA;sBACA;sBACA;wBACAjB;wBACAqB;wBACAR;wBACAO;wBACAH;wBACAM;wBACAC;wBACAC;sBACA;sBACA;sBACA;wBACAzB;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAE;0BACA;4BACAE;4BACAH;0BACA;wBACA;sBACA,GACA;wBACAjB;wBACAS;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAE;0BACA;4BACAE;4BACAH;0BACA;wBACA;sBACA;sBACA;sBACA;sBACA;wBACAjB;wBACAqB;wBACAR;wBACAO;wBACAH;wBACAM;wBACAC;sBACA,EACA;oBACA;kBACA;kBACAE,yCAIA;oBAAA,IAHApB;sBACAN;sBACAO;oBACA;oBACA;kBACA;gBACA;cAAA;gBAzMAoB;gBA0MA7B;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;gBACAqB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAS;MACAT;MACAxB;QACAkC;QACAC;UACAhC;QACA;MACA;IACA;IACAiC;;MAMA;IAAA,CAEA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;QACAtC;QACAuC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAF;QACAlD;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7WA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/article/sign-in-share/sign-in-share.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/article/sign-in-share/sign-in-share.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./sign-in-share.vue?vue&type=template&id=acb34606&scoped=true&\"\nvar renderjs\nimport script from \"./sign-in-share.vue?vue&type=script&lang=js&\"\nexport * from \"./sign-in-share.vue?vue&type=script&lang=js&\"\nimport style0 from \"./sign-in-share.vue?vue&type=style&index=0&id=acb34606&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"acb34606\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article/sign-in-share/sign-in-share.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=template&id=acb34606&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    guiHeaderLeading: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-header-leading\" */ \"@/GraceUI5/components/gui-header-leading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" :customHeader=\"true\" class=\"root-box\">\n\t\t<!-- 自定义头部导航 -->\n\t\t<view slot=\"gHeader\">\n\t\t\t<view class=\"gui-flex gui-nowrap gui-rows gui-align-items-center gui-padding head-box\">\n\t\t\t\t<!-- 使用组件实现返回按钮及返回首页按钮 -->\n\t\t\t\t<gui-header-leading></gui-header-leading>\n\t\t\t\t<!-- 导航文本此处也可以是其他自定义内容 -->\n\t\t\t\t<view class=\"gui-header-content nav-box\">\n\n\t\t\t\t</view>\n\t\t\t\t<!-- 如果右侧有其他内容可以利用条件编译和定位来实现-->\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 页面主体 -->\n\t\t<view slot=\"gBody\" class=\"body-box lp-flex-column\">\n\t\t\t<view class=\"content-box lp-flex-column\">\n\t\t\t\t<!-- 说明 -->\n\t\t\t\t<!-- 生成海报 -->\n\t\t\t\t<view class=\"loading\" v-if=\"!isReady\">海报生成中...</view>\n\t\t\t\t<!-- 图片展示由自己实现 -->\n\t\t\t\t<view class=\"pop-box lp-flex-column lp-flex-center\" v-else>\n\t\t\t\t\t<view class=\"poster-box lp-flex-center\">\n\t\t\t\t\t\t<image :src=\"posterImage || ''\" mode=\"heightFix\" class=\"posterImage\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"lp-flex-center btn-box\">\n\t\t\t\t\t\t<view class=\"lp-flex-column lp-flex-center btn\">\n\t\t\t\t\t\t\t<button type=\"primary\" size=\"mini\" @tap.prevent.stop=\"saveImage()\">\n\t\t\t\t\t\t\t\t<view class=\"lp-flex-center icon-box\">\n\t\t\t\t\t\t\t\t\t<text class=\"gui-icons\">&#xe63d;</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<text>保存图片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"lp-flex-column lp-flex-center btn\">\n\t\t\t\t\t\t\t<button type=\"primary\" open-type=\"share\" size=\"mini\">\n\t\t\t\t\t\t\t\t<view class=\"lp-flex-center icon-box\"> \n\t\t\t\t\t\t\t\t\t<text class=\"gui-icons\">&#xe63e;</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<text>微信分享</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 画布 -->\n\t\t\t\t<view class=\"hideCanvasView\">\n\t\t\t\t\t<canvas class=\"hideCanvas\" id=\"default_PosterCanvasId\" canvas-id=\"default_PosterCanvasId\" :style=\"{width: (poster.width||10) + 'px', height: (poster.height||10) + 'px'}\"></canvas>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\n\timport _app from '@/js_sdk/QuShe-SharerPoster/QS-SharePoster/app.js';\n\timport {\n\t\tgetSharePoster\n\t} from '@/js_sdk/QuShe-SharerPoster/QS-SharePoster/QS-SharePoster.js';\n\texport default {\n\t\tcomponents: {},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisReady: false,\n\t\t\t\tarticle_id: 4,\n\t\t\t\tposter: {},\n\t\t\t\tposterImage: '',\n\t\t\t\tcanvasId: 'default_PosterCanvasId',\n\t\t\t\tcourse: null,\n\t\t\t\tarticle: null,\n\t\t\t\tcode: null\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.article_id = options.article_id;\n\t\t\t// 获取分享数据\n\t\t\tthis.ready();\n\t\t},\n\t\tcreated() {\n\t\t\tthis.userInfo = this.$store.state.user.userInfo;\n\n\t\t\tconst monthEnglish = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Spt\", \"Oct\", \"Nov\", \"Dec\"];\n\t\t\tthis.month = monthEnglish[new Date().getMonth()];\n\t\t\tthis.date = new Date().getDate();\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tready: function() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle:'加载数据...'\n\t\t\t\t});\n\t\t\t\tthis.apiGetShareDate(this.article_id).then(data => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.article = data;\n\t\t\t\t\tthis.course = this.article.course;\n\t\t\t\t\tthis.code = this.article.code;\n\t\t\t\t\t\n\t\t\t\t\tthis.shareFc();\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync shareFc() {\n\t\t\t\ttry {\n\t\t\t\t\t_app.log('准备生成:' + new Date())\n\t\t\t\t\tconst d = await getSharePoster({\n\t\t\t\t\t\t_this: this, //若在组件中使用 必传\n\t\t\t\t\t\ttype: 'testShareType',\n\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\t//访问接口获取背景图携带自定义数据\n\n\t\t\t\t\t\t},\n\t\t\t\t\t\tposterCanvasId: this.canvasId, //canvasId\n\t\t\t\t\t\tdelayTimeScale: 20, //延时系数\n\t\t\t\t\t\t/* background: {\n\t\t\t\t\t\t\twidth: 1080,\n\t\t\t\t\t\t\theight: 1920,\n\t\t\t\t\t\t\tbackgroundColor: '#666'\n\t\t\t\t\t\t}, */\n\t\t\t\t\t\tbackgroundImage: '/static/imgs/sign_in_bg.jpg',\n\t\t\t\t\t\tdrawArray: ({\n\t\t\t\t\t\t\tbgObj,\n\t\t\t\t\t\t\ttype,\n\t\t\t\t\t\t\tbgScale\n\t\t\t\t\t\t}) => {\n\t\t\t\t\t\t\tconst dx = bgObj.width * 0.3;\n\t\t\t\t\t\t\tconst fontSize = bgObj.width * 0.040;\n\t\t\t\t\t\t\tconst lineHeight = bgObj.height * 0.04;\n\n\t\t\t\t\t\t\t//可直接return数组，也可以return一个promise对象, 但最终resolve一个数组, 这样就可以方便实现后台可控绘制海报\n\t\t\t\t\t\t\treturn new Promise((rs, rj) => {\n\t\t\t\t\t\t\t\trs([\n\t\t\t\t\t\t\t\t\t// 标题\n\t\t\t\t\t\t\t\t\t/* {\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: '坚持打卡天数',\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize,\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tdx: 410,\n\t\t\t\t\t\t\t\t\t\tdy: 200\n\t\t\t\t\t\t\t\t\t}, */\n\t\t\t\t\t\t\t\t\t// 天数\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.article.clock_num,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'blod',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize * 3,\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tdy: 350,\n\t\t\t\t\t\t\t\t\t\tinfoCallBack(textLength) {\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(textLength,bgObj.width);\n\t\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t\tdx: (bgObj.width - textLength) / 2,\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t// 内容框\n\n\t\t\t\t\t\t\t\t\t// 用户信息\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'image',\n\t\t\t\t\t\t\t\t\t\turl: this.userInfo.avatarUrl,\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\tdx: 100,\n\t\t\t\t\t\t\t\t\t\tdy: 550,\n\t\t\t\t\t\t\t\t\t\tinfoCallBack(imageInfo) {\n\t\t\t\t\t\t\t\t\t\t\tlet scale = bgObj.width * 0.2 / imageInfo.height;\n\t\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t\tcircleSet: {}, // 圆形图片 , 若circleSet与roundRectSet一同设置 优先circleSet设置\n\t\t\t\t\t\t\t\t\t\t\t\tdWidth: 100, // 因为设置了圆形图片 所以要乘以2\n\t\t\t\t\t\t\t\t\t\t\t\tdHeight: 100,\n\t\t\t\t\t\t\t\t\t\t\t\t/* roundRectSet: { // 圆角矩形\n\t\t\t\t\t\t\t\t\t\t\t\t\tr: imageInfo.width * .1\n\t\t\t\t\t\t\t\t\t\t\t\t} */\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.userInfo.nickName,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize,\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tdx: 240,\n\t\t\t\t\t\t\t\t\t\tdy: 580\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.course.title,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize * 0.8,\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tdx: 240,\n\t\t\t\t\t\t\t\t\t\tdy: 640\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t// 打卡日期\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.month,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize * 0.6,\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tdx: 830,\n\t\t\t\t\t\t\t\t\t\tdy: 555\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.date,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize * 0.6,\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tdx: this.date > 9 ? 837 : 845,\n\t\t\t\t\t\t\t\t\t\tdy: 610\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t// 课程封面\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'image',\n\t\t\t\t\t\t\t\t\t\turl: this.article.picture,\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\tdx: 70,\n\t\t\t\t\t\t\t\t\t\tdy: 720,\n\t\t\t\t\t\t\t\t\t\tdWidth: bgObj.width - 140,\n\t\t\t\t\t\t\t\t\t\tdHeight: 1000,\n\t\t\t\t\t\t\t\t\t\tmode: 'aspectFill'\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t// 课程信息\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.article.publish_date,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'blod',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize * 1.2,\n\t\t\t\t\t\t\t\t\t\tcolor: '#333',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'center',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tinfoCallBack(textLength) {\n\t\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t\tdx: bgObj.width / 2,\n\t\t\t\t\t\t\t\t\t\t\t\tdy: 1780\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\t\t\ttext: this.article.title,\n\t\t\t\t\t\t\t\t\t\tfontWeight: 'normal',\n\t\t\t\t\t\t\t\t\t\tsize: fontSize,\n\t\t\t\t\t\t\t\t\t\tcolor: '#666',\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'center',\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\n\t\t\t\t\t\t\t\t\t\tserialNum: 1,\n\t\t\t\t\t\t\t\t\t\tinfoCallBack(textLength) {\n\t\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t\tdx: bgObj.width / 2,\n\t\t\t\t\t\t\t\t\t\t\t\tdy: 1840\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t// 海报出处\n\t\t\t\t\t\t\t\t\t// 二维码\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\ttype: 'image',\n\t\t\t\t\t\t\t\t\t\turl: this.code.url,\n\t\t\t\t\t\t\t\t\t\talpha: 1,\n\t\t\t\t\t\t\t\t\t\tdx: bgObj.width - 285,\n\t\t\t\t\t\t\t\t\t\tdy: bgObj.height - 340,\n\t\t\t\t\t\t\t\t\t\tdWidth: 220,\n\t\t\t\t\t\t\t\t\t\tdHeight: 220,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t]);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsetCanvasWH: ({\n\t\t\t\t\t\t\tbgObj,\n\t\t\t\t\t\t\ttype,\n\t\t\t\t\t\t\tbgScale\n\t\t\t\t\t\t}) => { // 为动态设置画布宽高的方法，\n\t\t\t\t\t\t\tthis.poster = bgObj;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t_app.log('海报生成成功, 时间:' + new Date() + '， 临时路径: ' + d.poster.tempFilePath)\n\t\t\t\t\tthis.posterImage = d.poster.tempFilePath;\n\t\t\t\t\tthis.isReady = true;\n\t\t\t\t\t//this.$refs.popup.show()\n\t\t\t\t} catch (e) {\n\t\t\t\t\t_app.hideLoading();\n\t\t\t\t\t_app.showToast(JSON.stringify(e));\n\t\t\t\t\tconsole.log(JSON.stringify(e));\n\t\t\t\t}\n\t\t\t},\n\t\t\tsaveImage() {\n\t\t\t\tconsole.log('saveImage', this.posterImage);\n\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\tfilePath: this.posterImage,\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t_app.showToast('保存成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tshare() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t_app.getShare(false, false, 2, '', '', '', this.poster.finalPath, false, false);\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\t//_app.showToast('分享了');\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\thideQr() {\n\t\t\t\tthis.$refs.popup.hide()\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tonShareAppMessage(res) {\n\t\t\t\treturn {\n\t\t\t\t\tpath: \"/pages/article/detail/index?article_id=\" + this.article_id,\n\t\t\t\t\ttitle: '我正在参加人民中国“' + this.course.title + '\"你也一起来学习吧！',\n\t\t\t\t\timageUrl: this.posterImage\n\t\t\t\t}\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tapiGetShareDate: function(article_id) {\n\t\t\t\treturn this.$http.post('/v1/share/code', {\n\t\t\t\t\tpath: '/pages/article/detail/index?article_id=' + article_id,\n\t\t\t\t\tarticle_id\n\t\t\t\t}).then((res) => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t});\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.body-box {\n\t\tflex: 1;\n\n\t\t.content-box {\n\t\t\tflex: 1;\n\n\t\t\t.pop-box {\n\t\t\t\tflex: 1;\n\n\t\t\t\t.poster-box {\n\t\t\t\t\tmargin: 30rpx;\n\n\t\t\t\t\t.posterImage {\n\t\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\t\theight: 70vh;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t.btn-box {\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tfont-size: 28rpx;\n\n\t\t\t\t\t.btn {\n\t\t\t\t\t\tmargin: 20rpx 40rpx;\n\n\t\t\t\t\t\t.gui-icons {\n\t\t\t\t\t\t\tfont-size: 42rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tbutton {\n\t\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\t\theight: 100rpx;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tmargin-bottom: 10rpx;\n\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.loading {\n\t\tpadding: 50rpx;\n\t\tcolor: $uni-text-color-grey;\n\t}\n\n\t.hideCanvasView {\n\t\tposition: relative;\n\t}\n\n\t.hideCanvas {\n\t\tposition: fixed;\n\t\ttop: -99999rpx;\n\t\tleft: -99999rpx;\n\t\tz-index: -99999;\n\t}\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=style&index=0&id=acb34606&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./sign-in-share.vue?vue&type=style&index=0&id=acb34606&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041066200\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}