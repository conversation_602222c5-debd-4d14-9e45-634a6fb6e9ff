<view><view class="user-section"><image class="bg" src="/static/user-bg.jpg"></image><view data-event-opts="{{[['tap',[['login']]]]}}" class="user-info-box" bindtap="__e"><view class="portrait-box"><image class="portrait" src="{{user.userInfo.avatar||'/static/missing-face.png'}}"></image></view><view class="info-box" style="margin-left:40rpx;text-align:left;"><view class="username">{{user.userInfo.name||'暂未登录'}}</view><block wx:if="{{!is_login}}"><view class="sign">点击立即登录</view></block><view class="vip-box"><block wx:if="{{user.member&&user.member.status==1}}"><text class="vip">VIP<text style="font-size:20rpx;">{{'到期日期 '+user.member.end_date}}</text></text></block></view></view></view></view><view data-event-opts="{{[['touchstart',[['coverTouchstart',['$event']]]],['touchmove',[['coverTouchmove',['$event']]]],['touchend',[['coverTouchend',['$event']]]]]}}" class="cover-container" style="{{'transform:'+(coverTransform)+';'+('transition:'+(coverTransition)+';')}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"><image class="arc" src="/static/arc.png"></image><view class="history-section icon"><list-cell vue-id="380011e0-1" icon="icon-gouwuche_" iconColor="#e07472" title="我的订单" data-event-opts="{{[['^eventClick',[['navTo',['/pages/order/list']]]]]}}" bind:eventClick="__e" bind:__l="__l"></list-cell><list-cell vue-id="380011e0-2" icon="icon-share" iconColor="#9789f7" title="我的兑换" data-event-opts="{{[['^eventClick',[['navTo',['/pages/my-exchange/my-exchange']]]]]}}" bind:eventClick="__e" bind:__l="__l"></list-cell><list-cell vue-id="380011e0-3" icon="icon-shoucang" iconColor="#e07472" title="我的收藏" data-event-opts="{{[['^eventClick',[['navTo',['/pages/order/collect']]]]]}}" bind:eventClick="__e" bind:__l="__l"></list-cell><list-cell vue-id="380011e0-4" icon="icon-pinglun-copy" iconColor="#e07472" title="联系我们" data-event-opts="{{[['^eventClick',[['navTo',['/pages/about-us/about-us']]]]]}}" bind:eventClick="__e" bind:__l="__l"></list-cell><list-cell vue-id="380011e0-5" icon="icon-huifu" iconColor="#e07472" title="退出登录" data-event-opts="{{[['^tap',[['out']]]]}}" bind:tap="__e" bind:__l="__l"></list-cell></view></view><test-tabbar vue-id="380011e0-6" bind:__l="__l"></test-tabbar></view>