<template>
  <div class="page-container">
    <div class="page-header">
      <h1>小组管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          v-if="authStore.hasRole(['admin', 'teacher'])"
        >
          <el-icon><Plus /></el-icon>
          创建小组
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item label="等级">
          <el-select v-model="filters.level" placeholder="选择等级" clearable @change="loadGroups">
            <el-option label="N5" value="N5" />
            <el-option label="N4" value="N4" />
            <el-option label="N3" value="N3" />
            <el-option label="N2" value="N2" />
            <el-option label="N1" value="N1" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadGroups">
            <el-option label="待审核" value="pending" />
            <el-option label="活跃" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="搜索小组名称"
            clearable
            @input="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 小组列表 -->
    <div class="groups-grid">
      <el-card 
        v-for="group in groups" 
        :key="group.id" 
        class="group-card"
        shadow="hover"
      >
        <template #header>
          <div class="card-header">
            <div class="group-title">
              <h3>{{ group.name }}</h3>
              <el-tag :type="getStatusType(group.status)" size="small">
                {{ getStatusText(group.status) }}
              </el-tag>
            </div>
            <div class="group-actions">
              <el-dropdown @command="handleCommand">
                <el-button link size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`view-${group.id}`">
                      <el-icon><View /></el-icon>
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="`edit-${group.id}`"
                      v-if="authStore.hasRole(['admin', 'teacher'])"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="`join-${group.id}`"
                      v-if="authStore.isStudent && group.currentMembers < group.maxMembers"
                    >
                      <el-icon><UserFilled /></el-icon>
                      加入小组
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="`delete-${group.id}`"
                      divided
                      v-if="authStore.isAdmin"
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>

        <div class="group-content">
          <p class="group-description">{{ group.description || '暂无描述' }}</p>
          
          <div class="group-meta">
            <div class="meta-item">
              <el-icon><Trophy /></el-icon>
              <span>{{ group.level }}</span>
            </div>
            <div class="meta-item">
              <el-icon><UserFilled /></el-icon>
              <span>{{ group.currentMembers || 0 }}/{{ group.maxMembers || 30 }}人</span>
            </div>
            <div class="meta-item" v-if="group.teacher">
              <el-icon><Avatar /></el-icon>
              <span>{{ group.teacher.realName || group.teacher.username }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>{{ formatDate(group.createdAt) }}</span>
            </div>
          </div>

          <div class="group-progress">
            <div class="progress-label">
              <span>成员进度</span>
              <span>{{ Math.round((group.currentMembers || 0) / (group.maxMembers || 30) * 100) }}%</span>
            </div>
            <el-progress 
              :percentage="Math.round((group.currentMembers || 0) / (group.maxMembers || 30) * 100)"
              :stroke-width="6"
              :show-text="false"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty 
      v-if="!loading && groups.length === 0" 
      description="暂无小组数据"
      :image-size="120"
    >
      <el-button 
        type="primary" 
        @click="showCreateDialog = true"
        v-if="authStore.hasRole(['admin', 'teacher'])"
      >
        创建第一个小组
      </el-button>
    </el-empty>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[12, 24, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadGroups"
        @current-change="loadGroups"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <GroupForm
      v-model="showCreateDialog"
      :group="selectedGroup"
      @success="handleFormSuccess"
    />

    <!-- 详情对话框 -->
    <GroupDetail
      v-model="showDetailDialog"
      :group="selectedGroup"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import GroupForm from './components/GroupForm.vue'
import GroupDetail from './components/GroupDetail.vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const groups = ref([])
const total = ref(0)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedGroup = ref(null)

// 筛选器
const filters = reactive({
  level: '',
  status: '',
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 12
})

// 搜索防抖
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    pagination.page = 1
    loadGroups()
  }, 500)
}

// 加载小组数据
const loadGroups = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...filters
    }
    
    const response = await get('/api/groups', params, { showLoading: false })
    if (response.success) {
      groups.value = response.data.groups || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载小组数据失败:', error)
    // 使用模拟数据
    groups.value = generateMockGroups()
    total.value = groups.value.length
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockGroups = () => {
  return [
    {
      id: 1,
      name: 'N5基础入门班',
      description: '适合零基础学员，从五十音图开始学习',
      level: 'N5',
      status: 'active',
      currentMembers: 15,
      maxMembers: 30,
      teacher: { realName: '张老师', username: 'teacher1' },
      createdAt: new Date('2024-01-15')
    },
    {
      id: 2,
      name: 'N4进阶学习班',
      description: '掌握基础语法，提升日常会话能力',
      level: 'N4',
      status: 'active',
      currentMembers: 22,
      maxMembers: 25,
      teacher: { realName: '李老师', username: 'teacher2' },
      createdAt: new Date('2024-02-01')
    },
    {
      id: 3,
      name: 'N3中级提升班',
      description: '深入学习语法，培养阅读理解能力',
      level: 'N3',
      status: 'pending',
      currentMembers: 8,
      maxMembers: 20,
      teacher: { realName: '王老师', username: 'teacher3' },
      createdAt: new Date('2024-03-10')
    }
  ]
}

// 刷新数据
const refreshData = () => {
  loadGroups()
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const group = groups.value.find(g => g.id === parseInt(id))
  
  switch (action) {
    case 'view':
      selectedGroup.value = group
      showDetailDialog.value = true
      break
    case 'edit':
      selectedGroup.value = group
      showCreateDialog.value = true
      break
    case 'join':
      handleJoinGroup(group)
      break
    case 'delete':
      handleDeleteGroup(group)
      break
  }
}

// 加入小组
const handleJoinGroup = async (group) => {
  try {
    await ElMessageBox.confirm(`确定要加入小组"${group.name}"吗？`, '确认加入', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    // 这里应该调用API
    ElMessage.success('加入小组成功')
    loadGroups()
  } catch {
    // 用户取消
  }
}

// 删除小组
const handleDeleteGroup = async (group) => {
  try {
    await ElMessageBox.confirm(`确定要删除小组"${group.name}"吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API
    ElMessage.success('删除成功')
    loadGroups()
  } catch {
    // 用户取消
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedGroup.value = null
  loadGroups()
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    active: 'success',
    completed: 'info',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    active: '活跃',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadGroups()
})
</script>

<style lang="scss" scoped>
.filter-section {
  background: var(--bg-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  margin-bottom: var(--spacing-lg);
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.group-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-light);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .group-title {
      flex: 1;
      
      h3 {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-medium);
        font-weight: 600;
        color: var(--text-primary);
      }
    }
    
    .group-actions {
      margin-left: var(--spacing-sm);
    }
  }
  
  .group-content {
    .group-description {
      color: var(--text-secondary);
      font-size: var(--font-size-small);
      line-height: 1.5;
      margin: 0 0 var(--spacing-md) 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .group-meta {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-md);
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-small);
        color: var(--text-regular);
        
        .el-icon {
          color: var(--primary-color);
        }
      }
    }
    
    .group-progress {
      .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
        font-size: var(--font-size-small);
        color: var(--text-regular);
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

@media (max-width: 768px) {
  .groups-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    .el-form {
      flex-direction: column;
      
      .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
      }
    }
  }
}
</style>
