# Tea Support SDK for TypeScript/Node.js

[![NPM version][npm-image]][npm-url]
[![build status][travis-image]][travis-url]
[![codecov][cov-image]][cov-url]
[![<PERSON> de<PERSON>][david-image]][david-url]
[![npm download][download-image]][download-url]

[npm-image]: https://img.shields.io/npm/v/@alicloud/tea-typescript.svg?style=flat-square
[npm-url]: https://npmjs.org/package/@alicloud/tea-typescript
[travis-image]: https://img.shields.io/travis/aliyun/tea-typescript.svg?style=flat-square
[travis-url]: https://travis-ci.org/aliyun/tea-typescript
[cov-image]: https://codecov.io/gh/aliyun/tea-typescript/branch/master/graph/badge.svg
[cov-url]: https://codecov.io/gh/aliyun/tea-typescript
[david-image]: https://img.shields.io/david/aliyun/tea-typescript.svg?style=flat-square
[david-url]: https://david-dm.org/aliyun/tea-typescript
[download-image]: https://img.shields.io/npm/dm/@alicloud/tea-typescript.svg?style=flat-square
[download-url]: https://npmjs.org/package/@alicloud/tea-typescript

Core SDK 用于处理底层的 HTTP/HTTPS 协议的请求和接受。

## Installation

它在生成的 SDK 中被应用

```bash
$ npm install @alicloud/tea-typescript
```

## License
The MIT license
