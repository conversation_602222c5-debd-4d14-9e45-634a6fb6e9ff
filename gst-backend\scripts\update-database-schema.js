const { sequelize } = require('../src/models');

async function updateDatabaseSchema() {
  try {
    console.log('🔄 开始更新数据库结构...');

    // 获取查询接口
    const queryInterface = sequelize.getQueryInterface();

    // 检查并添加 study_groups.is_public 字段
    try {
      const studyGroupsTableInfo = await queryInterface.describeTable('study_groups');
      
      if (!studyGroupsTableInfo.is_public) {
        console.log('📝 添加 study_groups.is_public 字段...');
        await queryInterface.addColumn('study_groups', 'is_public', {
          type: sequelize.Sequelize.BOOLEAN,
          defaultValue: true,
          comment: '是否为公共小组'
        });
        console.log('✅ study_groups.is_public 字段添加成功');
      } else {
        console.log('✅ study_groups.is_public 字段已存在');
      }
    } catch (error) {
      console.log('⚠️ study_groups 表可能不存在，跳过...');
    }

    // 检查并添加 courses.is_public 字段
    try {
      const coursesTableInfo = await queryInterface.describeTable('courses');
      
      if (!coursesTableInfo.is_public) {
        console.log('📝 添加 courses.is_public 字段...');
        await queryInterface.addColumn('courses', 'is_public', {
          type: sequelize.Sequelize.BOOLEAN,
          defaultValue: false,
          comment: '是否为公共课程'
        });
        console.log('✅ courses.is_public 字段添加成功');
      } else {
        console.log('✅ courses.is_public 字段已存在');
      }
    } catch (error) {
      console.log('⚠️ courses 表可能不存在，跳过...');
    }

    // 检查并添加 course_units.is_free 字段
    try {
      const courseUnitsTableInfo = await queryInterface.describeTable('course_units');
      
      if (!courseUnitsTableInfo.is_free) {
        console.log('📝 添加 course_units.is_free 字段...');
        await queryInterface.addColumn('course_units', 'is_free', {
          type: sequelize.Sequelize.BOOLEAN,
          defaultValue: false,
          comment: '是否免费试听'
        });
        console.log('✅ course_units.is_free 字段添加成功');
      } else {
        console.log('✅ course_units.is_free 字段已存在');
      }

      if (!courseUnitsTableInfo.video_id) {
        console.log('📝 添加 course_units.video_id 字段...');
        await queryInterface.addColumn('course_units', 'video_id', {
          type: sequelize.Sequelize.STRING,
          allowNull: true,
          comment: '视频ID'
        });
        console.log('✅ course_units.video_id 字段添加成功');
      } else {
        console.log('✅ course_units.video_id 字段已存在');
      }

      if (!courseUnitsTableInfo.play_id) {
        console.log('📝 添加 course_units.play_id 字段...');
        await queryInterface.addColumn('course_units', 'play_id', {
          type: sequelize.Sequelize.STRING,
          allowNull: true,
          comment: '播放ID'
        });
        console.log('✅ course_units.play_id 字段添加成功');
      } else {
        console.log('✅ course_units.play_id 字段已存在');
      }
    } catch (error) {
      console.log('⚠️ course_units 表可能不存在，跳过...');
    }

    console.log('🎉 数据库结构更新完成！');

  } catch (error) {
    console.error('❌ 数据库结构更新失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  updateDatabaseSchema().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = updateDatabaseSchema;
