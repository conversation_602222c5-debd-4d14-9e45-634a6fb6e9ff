{"name": "@alicloud/gateway-pop", "version": "0.0.6", "description": "", "main": "dist/client.js", "scripts": {"test": "mocha -r ts-node/register -r source-map-support/register test/**/*.spec.ts", "test-cov": "nyc -e .ts -r=html -r=text -r=lcov npm run test", "build": "tsc", "prepublishOnly": "tsc"}, "author": "", "license": "ISC", "devDependencies": {"@types/mocha": "^7.0.1", "@types/node": "^12.12.26", "mocha": "^7.0.1", "nyc": "^15.0.0", "source-map-support": "^0.5.16", "ts-node": "^8.6.2", "typescript": "^3.7.5"}, "dependencies": {"@alicloud/tea-typescript": "^1.7.1", "@alicloud/gateway-spi": "^0.0.8", "@alicloud/credentials": "^2", "@alicloud/tea-util": "^1.4.8", "@alicloud/openapi-util": "^0.3.2", "@alicloud/endpoint-util": "^0.0.1", "@alicloud/darabonba-encode-util": "^0.0.2", "@alicloud/darabonba-signature-util": "^0.0.4", "@alicloud/darabonba-string": "^1.0.2", "@alicloud/darabonba-map": "^0.0.1", "@alicloud/darabonba-array": "^0.1.0"}, "files": ["dist", "src"]}