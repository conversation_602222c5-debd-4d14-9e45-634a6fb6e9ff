{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/article/detail.vue?0728", "webpack:///D:/gst/gst-uniapp/pages/article/detail.vue?a7fe", "webpack:///D:/gst/gst-uniapp/pages/article/detail.vue?9e1d", "webpack:///D:/gst/gst-uniapp/pages/article/detail.vue?6fde", "uni-app:///pages/article/detail.vue", "webpack:///D:/gst/gst-uniapp/pages/article/detail.vue?1cb1", "webpack:///D:/gst/gst-uniapp/pages/article/detail.vue?c02a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniLoadMore", "empty", "data", "tabCurrentIndex", "navList", "state", "text", "loadingType", "orderList", "current_page", "project_id", "created", "uni", "title", "content", "success", "url", "onLoad", "methods", "loadData", "navItem", "item", "console", "page", "changeTab", "tabClick", "deleteOrder", "setTimeout", "cancelOrder", "stateTip", "stateTipColor", "index", "orderStateExp", "apiGetCourseList", "params", "id", "navToDetailPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqDvnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC;IACA;EACA;EAEAC;IACA;MACAC;QACAC;QACAC;QACAC;UACA;YACAH;cACAI;YACA;UACA;YACA;AACA;AACA;UAFA;QAIA;MACA;IACA;EAEA;EACAC;IACA;AACA;AACA;AACA;IACA;IACA;IAKA;MACA;IACA;EAGA;EAEAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UACAC;UACA;UACA;YACA;YACA;UACA;UACA;QACA;QACA;QACAC;QACAd;UACAY;QACA;QACA;QACA;;QAEA;QACAA;QAEAG;QACA;MACA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACAd;QACAC;MACA;MACAc;QACA;QACAf;MACA;MACAA;QACAI;MACA;IACA;IACA;IACAY;MAAA;MACAhB;QACAC;MACA;MACAc;QACA,2BAGA;UAFAE;UACAC;QAEAT;UACAhB;UACAwB;UACAC;QACA;;QAEA;QACA;QACA;UAAA;QAAA;QACAC;QAEAnB;MACA;IACA;IAEA;IACAoB;MACA;QACAF;MACA;QACA;UACAD;UACA;QACA;UACAA;UACA;QACA;UACAA;UACAC;UACA;;QAEA;MAAA;;MAEA;QACAD;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAG;MACA;QACAC;UACAC;UACAZ;QACA;MACA;QACA;MACA;IACA;IACA;IACAa;MACA;MACA;MACAxB;QACAI;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrSA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/article/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/article/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=7e72117a&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7e72117a&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.navList, function (tabItem, tabIndex) {\n    var $orig = _vm.__get_orig(tabItem)\n    var g0 = tabItem.loaded === true && tabItem.orderList.length === 0\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"navbar\">\r\n\t\t\t<view v-for=\"(item, index) in navList\" :key=\"index\" class=\"nav-item\"\r\n\t\t\t\t:class=\"{current: tabCurrentIndex === index}\" @click=\"tabClick(index)\">\r\n\t\t\t\t{{item.text}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<swiper :current=\"tabCurrentIndex\" class=\"swiper-box\" duration=\"300\" @change=\"changeTab\">\r\n\t\t\t<swiper-item class=\"tab-content\" v-for=\"(tabItem,tabIndex) in navList\" :key=\"tabIndex\">\r\n\t\t\t\t<scroll-view class=\"list-scroll-content\" scroll-y @scrolltolower=\"loadData\">\r\n\t\t\t\t\t<!-- 空白页 -->\r\n\t\t\t\t\t<empty v-if=\"tabItem.loaded === true && tabItem.orderList.length === 0\"></empty>\r\n\r\n\t\t\t\t\t<!-- 订单列表 -->\r\n\t\t\t\t\t<view v-for=\"(item,index) in tabItem.orderList\" :key=\"index\" class=\"order-item\"\r\n\t\t\t\t\t\t@click=\"navToDetailPage(item)\">\r\n\t\t\t\t\t\t<view class=\"item-box\">\r\n\t\t\t\t\t\t\t<view class=\"top-box\">\r\n\t\t\t\t\t\t\t\t<text class=\"publish-date\">{{item.publish_date}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"type-box\" style = \"margin-right: 20rpx;\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"gui-icons\">{{item.type == 1 ? '&#xe62f;' : '&#xe656;'}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"lang\">{{item.trans}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"center-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"left-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"title\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"info-box\"> -->\r\n\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"gui-icons\">{{item.type == 2 ? '&#xe605;' : '&#xe656;'}}</text> -->\r\n\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"txt\">{{item.clocks_num}} 人已打卡</text>\r\n\t\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cover-box\"  style = \"margin-right: 20rpx;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"cover\"\r\n\t\t\t\t\t\t\t\t\t\t:src=\"item.picture+'?x-oss-process=image/resize,m_lfit,h_150,w_150'\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<uni-load-more :status=\"tabItem.loadingType\"></uni-load-more>\r\n\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';\r\n\timport empty from \"@/components/empty\";\r\n\timport Json from '@/Json';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniLoadMore,\r\n\t\t\tempty\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabCurrentIndex: 0,\r\n\t\t\t\tnavList: [{\r\n\t\t\t\t\t\tstate: 0,\r\n\t\t\t\t\t\ttext: '全部',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page:1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tstate: 1,\r\n\t\t\t\t\t// \ttext: '待完成',\r\n\t\t\t\t\t// \tloadingType: 'more',\r\n\t\t\t\t\t// \torderList: [],\r\n\t\t\t\t\t// \tcurrent_page:1\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstate: 2,\r\n\t\t\t\t\t\ttext: '已完成',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page:1\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tproject_id: 1\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tcreated() {\r\n\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/login/login\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t/**\r\n\t\t\t * 修复app端点击除全部订单外的按钮进入时不加载数据的问题\r\n\t\t\t * 替换onLoad下代码即可\r\n\t\t\t */\r\n\t\t\tthis.project_id = options.project_id\r\n\t\t\tthis.tabCurrentIndex = +options.state;\r\n\t\t\t// #ifndef MP\r\n\t\t\tthis.loadData()\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP\r\n\t\t\tif (options.state == 0) {\r\n\t\t\t\tthis.loadData()\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t//获取订单列表\r\n\t\t\tloadData(source) {\r\n\t\t\t\t//这里是将订单挂载到tab列表下\r\n\t\t\t\tlet index = this.tabCurrentIndex;\r\n\t\t\t\tlet navItem = this.navList[index];\r\n\t\t\t\tlet state = navItem.state;\r\n\t\t\t\tlet page = navItem.current_page;\r\n\r\n\t\t\t\tif (source === 'tabChange' && navItem.loaded === true) {\r\n\t\t\t\t\t//tab切换只有第一次需要加载数据\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (navItem.loadingType === 'loading') {\r\n\t\t\t\t\t//防止重复加载\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnavItem.loadingType = 'loading';\r\n\t\t\t\tthis.apiGetCourseList(this.project_id,page).then(pagination => {\r\n\t\t\t\t\tthis.empty = false;\r\n\t\t\t\t\tthis.current_page = pagination.current_page;\r\n\t\t\t\t\tthis.total_page = pagination.last_page;\r\n\t\t\t\t\tthis.total = pagination.total;\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tlet orderList = pagination.data.filter(item=>{\r\n\t\t\t\t\t\t//添加不同状态下订单的表现形式\r\n\t\t\t\t\t\titem = Object.assign(item, this.orderStateExp(item.state));\r\n\t\t\t\t\t\t//演示数据所以自己进行状态筛选\r\n\t\t\t\t\t\tif(state === 0){\r\n\t\t\t\t\t\t\t//0为全部订单\r\n\t\t\t\t\t\t\treturn item;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn item.state === state\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//let orderList = pagination.data;\r\n\t\t\t\t\tconsole.log(orderList)\r\n\t\t\t\t\torderList.forEach(item=>{\r\n\t\t\t\t\t\tnavItem.orderList.push(item);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页\r\n\t\t\t\t\tthis.$set(navItem, 'loaded', true);\r\n\t\t\t\t\t\r\n\t\t\t\t\t//判断是否还有数据， 有改为 more， 没有改为noMore \r\n\t\t\t\t\tnavItem.loadingType = 'noMore';\r\n\t\t\t\t\t\r\n\t\t\t\t\tpage++;\r\n\t\t\t\t\tthis.navList[index].current_page = page;\r\n\t\t\t\t})\r\n\r\n\r\n\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t// \tlet orderList = Json.orderList.filter(item=>{\r\n\t\t\t\t// \t\t//添加不同状态下订单的表现形式\r\n\t\t\t\t// \t\titem = Object.assign(item, this.orderStateExp(item.state));\r\n\t\t\t\t// \t\t//演示数据所以自己进行状态筛选\r\n\t\t\t\t// \t\tif(state === 0){\r\n\t\t\t\t// \t\t\t//0为全部订单\r\n\t\t\t\t// \t\t\treturn item;\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t\treturn item.state === state\r\n\t\t\t\t// \t});\r\n\t\t\t\t// \torderList.forEach(item=>{\r\n\t\t\t\t// \t\tnavItem.orderList.push(item);\r\n\t\t\t\t// \t})\r\n\t\t\t\t// \t//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页\r\n\t\t\t\t// \tthis.$set(navItem, 'loaded', true);\r\n\r\n\t\t\t\t// \t//判断是否还有数据， 有改为 more， 没有改为noMore \r\n\t\t\t\t// \tnavItem.loadingType = 'more';\r\n\t\t\t\t// }, 600);\t\r\n\t\t\t},\r\n\r\n\t\t\t//swiper 切换\r\n\t\t\tchangeTab(e) {\r\n\t\t\t\tthis.tabCurrentIndex = e.target.current;\r\n\t\t\t\tthis.loadData('tabChange');\r\n\t\t\t},\r\n\t\t\t//顶部tab点击\r\n\t\t\ttabClick(index) {\r\n\t\t\t\tthis.tabCurrentIndex = index;\r\n\t\t\t},\r\n\t\t\t//删除订单\r\n\t\t\tdeleteOrder(index) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '请稍后'\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.navList[this.tabCurrentIndex].orderList.splice(index, 1);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}, 600)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/projects/course\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//取消订单\r\n\t\t\tcancelOrder(item) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '请稍后'\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tstateTip,\r\n\t\t\t\t\t\tstateTipColor\r\n\t\t\t\t\t} = this.orderStateExp(9);\r\n\t\t\t\t\titem = Object.assign(item, {\r\n\t\t\t\t\t\tstate: 9,\r\n\t\t\t\t\t\tstateTip,\r\n\t\t\t\t\t\tstateTipColor\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\t//取消订单后删除待付款中该项\r\n\t\t\t\t\tlet list = this.navList[1].orderList;\r\n\t\t\t\t\tlet index = list.findIndex(val => val.id === item.id);\r\n\t\t\t\t\tindex !== -1 && list.splice(index, 1);\r\n\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}, 600)\r\n\t\t\t},\r\n\r\n\t\t\t//订单状态文字和颜色\r\n\t\t\torderStateExp(state) {\r\n\t\t\t\tlet stateTip = '',\r\n\t\t\t\t\tstateTipColor = '#fa436a';\r\n\t\t\t\tswitch (+state) {\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\tstateTip = '已完成';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\tstateTip = '待发货';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 9:\r\n\t\t\t\t\t\tstateTip = '订单已关闭';\r\n\t\t\t\t\t\tstateTipColor = '#909399';\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t//更多自定义\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\tstateTip,\r\n\t\t\t\t\tstateTipColor\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tapiGetCourseList: function(id,page) {\r\n\t\t\t\treturn this.$http.get('/v1/article/index', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid,\r\n\t\t\t\t\t\tpage\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情页\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/article/detail/index?article_id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage,\r\n\t.content {\r\n\t\tbackground: $page-color-base;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.swiper-box {\r\n\t\theight: calc(100% - 40px);\r\n\t}\r\n\r\n\t.list-scroll-content {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.navbar {\r\n\t\tdisplay: flex;\r\n\t\theight: 40px;\r\n\t\tpadding: 0 5px;\r\n\t\tbackground: #fff;\r\n\t\tbox-shadow: 0 1px 5px rgba(0, 0, 0, .06);\r\n\t\tposition: relative;\r\n\t\tz-index: 10;\r\n\r\n\t\t.nav-item {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&.current {\r\n\t\t\t\tcolor: $base-color;\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 44px;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-bottom: 2px solid $base-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.uni-swiper-item {\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t.order-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding-left: 30upx;\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 16upx;\r\n\r\n\t\t.i-top {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 80upx;\r\n\t\t\tpadding-right: 30upx;\r\n\t\t\tfont-size: $font-base;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.time {\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t.state {\r\n\t\t\t\tcolor: $base-color;\r\n\t\t\t}\r\n\r\n\t\t\t.del-btn {\r\n\t\t\t\tpadding: 10upx 0 10upx 36upx;\r\n\t\t\t\tfont-size: $font-lg;\r\n\t\t\t\tcolor: $font-color-light;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 0;\r\n\t\t\t\t\theight: 30upx;\r\n\t\t\t\t\tborder-left: 1px solid $border-color-dark;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 20upx;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* 多条商品 */\r\n\t\t.goods-box {\r\n\t\t\theight: 160upx;\r\n\t\t\tpadding: 20upx 0;\r\n\t\t\twhite-space: nowrap;\r\n\r\n\t\t\t.goods-item {\r\n\t\t\t\twidth: 120upx;\r\n\t\t\t\theight: 120upx;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tmargin-right: 24upx;\r\n\t\t\t}\r\n\r\n\t\t\t.goods-img {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* 单条商品 */\r\n\t\t.goods-box-single {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 20upx 0;\r\n\r\n\t\t\t.goods-img {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 120upx;\r\n\t\t\t\theight: 120upx;\r\n\t\t\t}\r\n\r\n\t\t\t.right {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tpadding: 0 30upx 0 24upx;\r\n\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.attr-box {\r\n\t\t\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\t\t\tcolor: $font-color-light;\r\n\t\t\t\t\tpadding: 10upx 12upx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.price {\r\n\t\t\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\t\t\tcolor: $font-color-dark;\r\n\r\n\t\t\t\t\t&:before {\r\n\t\t\t\t\t\tcontent: '￥';\r\n\t\t\t\t\t\tfont-size: $font-sm;\r\n\t\t\t\t\t\tmargin: 0 2upx 0 8upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.price-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\talign-items: baseline;\r\n\t\t\tpadding: 20upx 30upx;\r\n\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\tcolor: $font-color-light;\r\n\r\n\t\t\t.num {\r\n\t\t\t\tmargin: 0 8upx;\r\n\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t}\r\n\r\n\t\t\t.price {\r\n\t\t\t\tfont-size: $font-lg;\r\n\t\t\t\tcolor: $font-color-dark;\r\n\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tcontent: '￥';\r\n\t\t\t\t\tfont-size: $font-sm;\r\n\t\t\t\t\tmargin: 0 2upx 0 8upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.action-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100upx;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding-right: 30upx;\r\n\t\t}\r\n\r\n\t\t.action-btn {\r\n\t\t\twidth: 160upx;\r\n\t\t\theight: 60upx;\r\n\t\t\tmargin: 0;\r\n\t\t\tmargin-left: 24upx;\r\n\t\t\tpadding: 0;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 60upx;\r\n\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 100px;\r\n\r\n\t\t\t&:after {\r\n\t\t\t\tborder-radius: 100px;\r\n\t\t\t}\r\n\r\n\t\t\t&.recom {\r\n\t\t\t\tbackground: #fff9f9;\r\n\t\t\t\tcolor: $base-color;\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tborder-color: #f7bcc8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t/* load-more */\r\n\t.uni-load-more {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\theight: 80upx;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\r\n\t.uni-load-more__text {\r\n\t\tfont-size: 28upx;\r\n\t\tcolor: #999\r\n\t}\r\n\r\n\t.uni-load-more__img {\r\n\t\theight: 24px;\r\n\t\twidth: 24px;\r\n\t\tmargin-right: 10px\r\n\t}\r\n\r\n\t.uni-load-more__img>view {\r\n\t\tposition: absolute\r\n\t}\r\n\r\n\t.uni-load-more__img>view view {\r\n\t\twidth: 6px;\r\n\t\theight: 2px;\r\n\t\tborder-top-left-radius: 1px;\r\n\t\tborder-bottom-left-radius: 1px;\r\n\t\tbackground: #999;\r\n\t\tposition: absolute;\r\n\t\topacity: .2;\r\n\t\ttransform-origin: 50%;\r\n\t\tanimation: load 1.56s ease infinite\r\n\t}\r\n\r\n\t.uni-load-more__img>view view:nth-child(1) {\r\n\t\ttransform: rotate(90deg);\r\n\t\ttop: 2px;\r\n\t\tleft: 9px\r\n\t}\r\n\r\n\t.uni-load-more__img>view view:nth-child(2) {\r\n\t\ttransform: rotate(180deg);\r\n\t\ttop: 11px;\r\n\t\tright: 0\r\n\t}\r\n\r\n\t.uni-load-more__img>view view:nth-child(3) {\r\n\t\ttransform: rotate(270deg);\r\n\t\tbottom: 2px;\r\n\t\tleft: 9px\r\n\t}\r\n\r\n\t.uni-load-more__img>view view:nth-child(4) {\r\n\t\ttop: 11px;\r\n\t\tleft: 0\r\n\t}\r\n\r\n\t.load1,\r\n\t.load2,\r\n\t.load3 {\r\n\t\theight: 24px;\r\n\t\twidth: 24px\r\n\t}\r\n\r\n\t.load2 {\r\n\t\ttransform: rotate(30deg)\r\n\t}\r\n\r\n\t.load3 {\r\n\t\ttransform: rotate(60deg)\r\n\t}\r\n\r\n\t.load1 view:nth-child(1) {\r\n\t\tanimation-delay: 0s\r\n\t}\r\n\r\n\t.load2 view:nth-child(1) {\r\n\t\tanimation-delay: .13s\r\n\t}\r\n\r\n\t.load3 view:nth-child(1) {\r\n\t\tanimation-delay: .26s\r\n\t}\r\n\r\n\t.load1 view:nth-child(2) {\r\n\t\tanimation-delay: .39s\r\n\t}\r\n\r\n\t.load2 view:nth-child(2) {\r\n\t\tanimation-delay: .52s\r\n\t}\r\n\r\n\t.load3 view:nth-child(2) {\r\n\t\tanimation-delay: .65s\r\n\t}\r\n\r\n\t.load1 view:nth-child(3) {\r\n\t\tanimation-delay: .78s\r\n\t}\r\n\r\n\t.load2 view:nth-child(3) {\r\n\t\tanimation-delay: .91s\r\n\t}\r\n\r\n\t.load3 view:nth-child(3) {\r\n\t\tanimation-delay: 1.04s\r\n\t}\r\n\r\n\t.load1 view:nth-child(4) {\r\n\t\tanimation-delay: 1.17s\r\n\t}\r\n\r\n\t.load2 view:nth-child(4) {\r\n\t\tanimation-delay: 1.3s\r\n\t}\r\n\r\n\t.load3 view:nth-child(4) {\r\n\t\tanimation-delay: 1.43s\r\n\t}\r\n\r\n\t@-webkit-keyframes load {\r\n\t\t0% {\r\n\t\t\topacity: 1\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: .2\r\n\t\t}\r\n\t}\r\n\r\n\t/* 历史列表 */\r\n\t\r\n\r\n\t\t\t.item-box {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\tborder-bottom: solid 1px #eeeeee;\r\n\r\n\t\t\t\t.top-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.center-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\r\n\t\t\t\t\t.left-box {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.info-box {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\r\n\t\t\t\t\t\t\t.gui-icons {\r\n\t\t\t\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.cover-box {\r\n\t\t\t\t\t\tmargin-left: 30rpx;\r\n\r\n\t\t\t\t\t\t.cover {\r\n\t\t\t\t\t\t\twidth: 260rpx;\r\n\t\t\t\t\t\t\theight: 150rpx;\r\n\t\t\t\t\t\t\tborder-radius: 5px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040522233\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}