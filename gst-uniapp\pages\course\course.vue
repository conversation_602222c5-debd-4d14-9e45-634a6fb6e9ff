<template>
	<view class="container">

		<!-- 媒体部分 -->
		<view>
			<!-- 视频 -->
			<video  v-if="menuinfo.Cost==0 || menuinfo.is_buy==1"style="width: 100%;" :model="menuinfo" :src="videosrc" @error="videoErrorCallback"
				@timeupdate="video_timeUpdate" @fullscreenchange="fullscreen" @play='video_onplay' autoplay="true"
				@pause='video_onpause' @ended='video_onend' :controls="video_controls"  show-center-play-btn
				enable-play-gesture :poster="menuinfo.picture">
			</video>
			<view class="cover-box lp-flex-center" v-if="menuinfo.Cost>0 && menuinfo.is_buy==0">
				<image class="cover" :src="menuinfo.picture" mode="widthFix"></image>
				<view class="button"  style="background-color:#333;">{{menuinfo.count}}课时</view>
			</view>
		</view>

		<view class="shipinpart-info" style="background-color: #ffffff;">
			<text class="info-span4" >￥{{menuinfo.Cost?menuinfo.Cost:0}}</text>
			<text class="info-span1" style="margin-top: 10rpx;">{{menuinfo.title}}</text>
			<!-- <view class="info-span2">
				<text>{{menuinfo.jianjie}}</text>
				<text>更新中</text>
			</view> -->
			<view class="info-span5">
				<text class="info-span3">
					<!-- <text>{{menuinfo.count}}课时</text> -->
					<!-- <text>|</text> -->
					<!-- <text>{{menuinfo.viewnum ? menuinfo.viewnum : '0'}}人学过</text> -->
				</text>
				<!-- 	<text class="info-span4" >￥{{menuinfo.price}}</text>
				<text class="info-span4" v-if="action=='credit' ">{{creditinfo.credit}}积分</text>
				<text class="info-span4" v-if="action=='seckill' ">秒杀价:￥{{seckillinfo.price}}</text>
				<text class="info-span4" v-if="action=='pintuan' ">拼团价:￥{{pintuaninfo.price}}</text> -->
			</view>
		</view>
		
		<view class="shipinpart-info" style="background-color: #ffffff;margin-top: 20rpx;padding: 20rpx;">
			<text class="info-span1" style="margin-top: 10rpx;">课程介绍</text>
			<view class="kcjs" :class="{dis:btnnum == 0}" :model="teacher" style="margin-top: 20rpx;">
			<view class="kcjs-brief">
				<view class="kcjs-brief-center">
					<rich-text :nodes="menuinfo.content"></rich-text>
				</view>
			</view>
			</view>
		</view>
		
		<view class="shipinpart-info" style="background-color: #ffffff;margin-top: 20rpx;">
			<text class="info-span1" style="margin-top: 10rpx;">课程目录</text>
			<view class="kcml" style="margin-bottom: 122upx;margin-top: 20rpx;" :class="{dis:btnnum == 1}">
			<view class="kcml-list" v-for="(item,index) in menuinfo.li" :key="item.id" @click="openvideo(item)">
				<view >
					<text  v-if="item.id == videoId" class="fontcolor" >{{(index+1)+' . '+item.title}}</text>
					<text  v-else>{{(index+1)+' . '+item.title}}</text>
				</view>
			</view>
			</view>
		</view>


		<!-- 课程介绍、课程目录、附件、用户评价 -->
	<!-- 	<view class="kechengpart">
			<view class="kechengpart-title">
				<view class="kechengpart-title-item" v-for="(item, index) in kechengList" :key="index">
					<view @click="muluchange(index)" :class="{btna:btnnum == index}">{{item}}</view>
					<text :class="{_underline: btnnum == index}"></text>
				</view>
			</view>
			<view class="kechengpart-content"> -->


				<!-- 课程介绍 -->
				<!-- <view class="kcjs" :class="{dis:btnnum == 0}" :model="teacher"> -->
					<!-- <view class="kcjs-lecturer">
						<view class="kcjs-lecturer-top">
							<image src="../../static/kechengjiangshi.png" mode=""></image>
							<text>课程讲师</text>
						</view>
						<view class="jiangshi-right" @click="jsdetail">
							<text>讲师主页</text>
							<image src="../../static/jt.png" mode=""></image>
						</view>
						<view class="kcjs-lecturer-bottom" >
							<image :src="teacher.img?(HOST_URL + teacher.img):'' " mode="aspectFit"></image>
						</view>
					</view> -->
					<!-- <view class="kcjs-brief"> -->
						<!-- <view class="kcjs-brief-top">
							<image src="../../static/kechengjianjie.png" mode=""></image>
							<text>课程简介</text>
						</view> -->
						<!-- <view class="kcjs-brief-center"> -->
							<!-- <u-parse :content="menuinfo.content"></u-parse> -->
				<!-- 			<rich-text :nodes="menuinfo.content"></rich-text>
						</view>
					</view>
				</view> -->


				<!-- 课程目录 -->
			<!-- 	<view class="kcml" style="margin-bottom: 122upx;" :class="{dis:btnnum == 1}">
					<view class="kcml-list" v-for="(item,index) in menuinfo.li" :key="item.id" @click="openvideo(item)">
						<view > -->
							<!-- 	<text class="yinpin" v-if="item.media === 'audio'">音频</text>
							<text class="shipin" v-else-if="item.media === 'video'">视频</text>
							<text class="tuwen" v-else>图文</text> -->
							<!-- <text  v-if="item.id == videoId" class="fontcolor" >{{(index+1)+' . '+item.title}}</text>
							<text  v-else>{{(index+1)+' . '+item.title}}</text>
						</view> -->
						<!-- <view class="kcml-list-right" v-if="!is_free && item.is_pay==0">
							<view v-if="item.is_sk == 1">
								<text v-if="item.media == 'video'">试看</text>
								<text v-else-if="item.media == 'audio'">试听</text>
								<text v-if="item.media == 'tuwen'">试读</text>
							</view>
							<image v-else-if="item.is_sk == 0" src="../../static/lock.png" mode=""></image>
						</view> -->
				<!-- 	</view>
				</view>
 -->


				<!-- 用户评价 -->
				<!-- <view class="yhpj" :class="{dis:btnnum == 2}">
					<view class="yhpj-list" v-if="comment.length > 0">
						<view class="item" v-for="(item, index) in comment" :key="index">
							<image class="item-left" :src="item.avatar" mode="aspectFit"></image>
							<view class="item-right">
								<view class="item-right-top">
									<text>{{item.nickname}}</text>
									<text>{{item.addtime}}</text>
								</view>
								<view class="item-right-bottom">
									<text>{{item.comment}}</text>
								</view>
							</view>
						</view>
					</view>
					<image v-else class="nopl" src="../../static/nopl.png" mode="aspectFit"></image>
				</view> -->
			<!-- </view> -->
	<!-- 	</view> -->


		<!-- 立即购买 -->
			<view class="buy" v-if="btnnum <= 2">
			<view  class="p-b-btn" style="margin-left: 10rpx;" @click="collect" :style="{color:menuinfo.is_collect==1?'#f00':''}">
				<text class="yticon icon-shoucang2"></text>
				<text>收藏</text>
			</view>	
			<view class="p-b-btn">
				<!-- <text class="yticon icon-dianhua-copy"></text> -->
				<image src="/static/kf.png" style="width: 50rpx;height: 50rpx;"></image>
				<text>客服</text>
				<button class='contact-btn' open-type='contact'>a</button> 
			</view>
			
			<view class="buy-right" @click="pay1" v-if="menuinfo.Cost>0 && menuinfo.is_buy==0 && is_show===true">立即购买</view>
			<view class="buy-right" v-if="menuinfo.Cost>0 && menuinfo.is_buy==1">已购买</view>
			<view class="buy-right" @click="exchange" v-if="!is_free && action=='credit' ">立即兑换</view>
			<view class="buy-right" @click="seckill" v-if="!is_free && action=='seckill' ">立即秒杀</view>
			<view class="buy-right" @click="pintuan" v-if="!is_free && action=='pintuan' ">开始拼团</view>
			<view class="buy-right" v-if="menuinfo.Cost==0">免费</view>
		</view>

		<zaudio></zaudio>

	</view>
</template>

<script>
	import uParse from '@/components/gaoyia-parse/parse.vue'
	import {
		getCourseDetails
	} from '@/request/courses.js'
	import {
		checkUserinfo
	} from '@/request/checkUserinfo'
	// #ifndef MP-WEIXIN
	const innerAudioContext = uni.createInnerAudioContext()
	// #endif
	// #ifdef MP-WEIXIN
	const innerAudioContext = uni.getBackgroundAudioManager();
	// #endif
	export default {
		// computed: {
		//     contentMobile() {
		//       // 小程序中rich-text 样式设置需要通过正则给富文本添加样式
		//       if (this.menuinfo.content)
		//         return this.menuinfo.content.replace(
		//           /font-size:[^;]+;/gi, 
		// 		  'font-size:10px;'
		//         );
		//       return "";
		//     },
		// },
		components: {
			uParse
		},
		data() {
			return {
				kechengList: ['课程介绍', '目录'],
				btnnum: 0,
				currentTime: '',
				coursemenus: [],
				menuinfo: [],
				teacher: [],
				courses: [],
				comment: [],
				HOST_URL: uni.HOST_URL,
				videostate: 'video',
				durationTime: "", //音频的总时长
				coursehour: 0, // 课时
				audio_paused: true,
				audio_progress: 0,
				gaosi: false,
				t1: '',
				t2: '', //video计时器id
				t3: '', //audio计时器id
				videosrc: '',
				free_time: '',
				video_controls: true,
				videofullscreen: false,
				menuid: '',
				sonid: '',
				videoStudyTime: 0, //秒
				audioStudyTime: 0, //秒
				is_free: false,
				son_is_pay: false,
				uid: '',
				is_dingyue: false,
				videoContext: {},
				creditinfo: {},
				action: '',
				seckillinfo: {},
				pintuaninfo: {},
				project: {},
				videoId:1,
				id:1,
				option:0,
				is_show:true
			}
		},
		onShareAppMessage() {
		
			return {
					title: this.menuinfo.title,
					path: `/pages/course/course?id=${this.menuinfo.id}`,
					imageUrl: ''
				}
			
		},
		// 分享到朋友圈
		onShareTimeline() {
			return {
				title: this.menuinfo.title,
				path: `/pages/course/course?id=${this.menuinfo.id}`,
				imageUrl: ''
			};
		},
		onReady: function(res) {
			this.videoContext = uni.createVideoContext('myVideo')
			let platform = this.$store.state.systemInfo.platform
			if(platform=='ios'){
				this.is_show = false;
			}
		},
		onLoad(option) {
			
			//保存当前页面的option对象
			this.option = option;
			this.id = option.id
			wx.showShareMenu({
					withShareTicket:true,
					menus:["shareAppMessage","shareTimeline"]
				})
			this.getdetail()
		},
		onShow() {
			
			//checkUserinfo()
			let audio = innerAudioContext;
			audio.onTimeUpdate((e) => {
				this.durationTime = this.format(audio.duration)
				this.currentTime = this.format(audio.currentTime)
				let progress = (audio.currentTime / audio.duration).toFixed(2)
				this.audio_progress = progress * 100
				// if(!this.is_free){
				// 	if(!this.son_is_pay){
				// 		if(audio.currentTime>=this.free_time){
				// 			audio.pause()
				// 			this.currentTime='00:00'
				// 			audio.seek(0)
				// 			this.audio_progress=0
				// 			uni.showModal({
				// 				title: '试听结束',
				// 				content: '需要解锁该课程吗?',
				// 				success: (res) =>{
				// 					var that=this
				// 					if (res.confirm) {
				// 						this.pay()
				// 					} else if (res.cancel) {
				// 						// that.currentTime='00:00'
				// 						// audio.seek(0)
				// 						// that.audio_progress=0
				// 					}
				// 				}
				// 			});
				// 		}
				// 	}
				// }
				// console.log(this.currentTime)
			})
			audio.onSeeked((e) => {
				this.currentTime = this.format(audio.currentTime)
			})
			audio.onPlay((e) => {
				this.videoContext.pause()
				this.audio_paused = false
				this.gaosi_ani()
				this.gaosi = true
				this.countAtime()
			})
			audio.onPause((e) => {
				this.audio_paused = true
				clearInterval(this.t1)
				clearInterval(this.t3)
				this.gaosi = false
				this.saveAtime()
			})
			audio.onStop((e) => {
				this.audio_paused = true
				clearInterval(this.t1)
				clearInterval(this.t3)
				this.saveAtime()
			})
			audio.onEnded((e) => {
				clearInterval(this.t1)
				clearInterval(this.t3)
				this.audio_paused = true
				this.currentTime = '00:00'
				audio.seek(0)
				this.saveAtime()
			})
		},
		onUnload() {
			clearInterval(this.t1)
			clearInterval(this.t2)
			clearInterval(this.t3)
			this.menuid = ''
			this.sonid = ''
			this.saveAtime()
			this.saveVtime()
		},
		methods: {
			getdetail(){
				uni.showLoading();
				this.apiGetCourseList(this.id).then(pagination => {
					console.log('pagination' + pagination)
					uni.hideLoading();
					this.menuinfo = pagination;
					// var strings = this.menuinfo.content;    
					// //请求到的富文本数据文件
					// const regex = new RegExp(/font-size:^;/,'gi');    
					// //创建一个正则匹配规则			
					// var richtext = strings.replace(regex, `font-size:10px;`);
					// // 将想要的样式属性携带在标签上，替换原来的标签即可。
					// console.log('richtext' + richtext)
					// this.menuinfo.content = richtext;
					var richtext1 = this.menuinfo.content.replace(/<p/g, "<p style='font-size:14px;color:#333;background-color:#fff;'")
					var richtext2 = richtext1.replace(/font-size.*?;/g, 'font-size:14px;')
					var richtext3 = richtext2.replace(/font-color.*?;/g, 'font-color:#333;')
					this.menuinfo.content = richtext3;
				    // 最后将修改后的富文本传到data中，渲染到视图层即可。

					if(this.menuinfo.Cost==0){
						this.videosrc = pagination.li[0].url;
						this.videoId = pagination.li[0].id;
					}else{
						if(this.menuinfo.is_buy==0){
							// uni.showModal({
							// 		title: '提示',
							// 	    content: '购买课程后可观看?',
							// 				success: (res) =>{
							// 					var that=this
							// 					if (res.confirm) {
							// 						this.pay1()
							// 					} else if (res.cancel) {
							// 						// that.currentTime='00:00'
							// 						// audio.seek(0)
							// 						// that.audio_progress=0
							// 					}
							// 				}
							// });
						}else{
							this.videosrc = pagination.li[0].url;
							this.videoId = pagination.li[0].id;
						}
					}
					
					
				})
			},
			jsdetail() {
				uni.navigateTo({
					url: '../tutor-introduced/tutor-introduced?tid=' + this.teacher.id
				})
			},
			get_creditinfo(id, goodstype) {
				const BASE_URL = uni.BASE_URL
				uni.request({
					url: BASE_URL + 'index/credit/creditinfo',
					data: {
						id: id,
						goodstype: goodstype
					},
					method: 'POST',
					success: (res) => {
						console.log(res.data)
						this.creditinfo = res.data.data
					},
					fail: (res) => {
						console.log(res.data);
					}
				});
			},
			get_seckillinfo(id, goodstype) {
				const BASE_URL = uni.BASE_URL
				uni.request({
					url: BASE_URL + 'index/seckill/seckillinfo',
					data: {
						id: id,
						goodstype: goodstype
					},
					method: 'POST',
					success: (res) => {
						console.log(res.data)
						this.seckillinfo = res.data.data
					},
					fail: (res) => {
						console.log(res.data);
					}
				});
			},
			get_pintuaninfo(id, goodstype) {
				const BASE_URL = uni.BASE_URL
				uni.request({
					url: BASE_URL + 'index/pintuan/pintuaninfo',
					data: {
						id: id,
						goodstype: goodstype
					},
					method: 'POST',
					success: (res) => {
						console.log(res.data)
						this.pintuaninfo = res.data.data
					},
					fail: (res) => {
						console.log(res.data);
					}
				});
			},
			saveStudytime(studytime, media) {
				const BASE_URL = uni.BASE_URL
				uni.request({
					url: BASE_URL + 'index/user/save_studytime',
					method: 'POST',
					data: {
						uid: this.uid,
						media: media,
						studytime: studytime
					},
					success: (res) => {
						console.log(res.data);
					},
					fail: (res) => {
						console.log(res.data);
					}
				});
			},
			saveAtime() {
				var atime = uni.getStorageSync('atime')
				var data = {}
				if (atime) {
					data.atime = atime.atime + this.audioStudyTime
					data.nowtime = (new Date()).getTime()
					uni.setStorageSync('atime', data)
					this.saveStudytime(data.atime, 'audio')
				} else {
					data.atime = this.audioStudyTime
					data.nowtime = (new Date()).getTime()
					uni.setStorageSync('atime', data)
					this.saveStudytime(data.atime, 'audio')
				}
			},
			saveVtime() {
				var vtime = uni.getStorageSync('vtime')
				var data = {}
				if (vtime) {
					data.vtime = vtime.vtime + this.videoStudyTime
					data.nowtime = (new Date()).getTime()
					uni.setStorageSync('vtime', data)
					this.saveStudytime(data.vtime, 'video')
				} else {
					data.vtime = this.videoStudyTime
					data.nowtime = (new Date()).getTime()
					uni.setStorageSync('vtime', data)
					this.saveStudytime(data.vtime, 'video')
				}
			},
			countVtime() {
				this.t2 = setInterval(() => {
					this.videoStudyTime++
					//console.log(this.videoStudyTime)
				}, 1000)
			},
			countAtime() {
				this.t3 = setInterval(() => {
					this.audioStudyTime++
					//console.log(this.audioStudyTime)
				}, 1000)
			},
			dingyue() {
				const BASE_URL = uni.BASE_URL
				uni.request({
					url: BASE_URL + 'index/courses/dingyue',
					method: 'POST',
					data: {
						uid: this.uid,
						media: this.menuinfo.media,
						menuid: this.menuid,
						goodstype: 'course'
					},
					success: (res) => {
						console.log(res.data);
						this.is_dingyue = res.data.data == 1 ? true : false
					},
					fail: (res) => {
						console.log(res.data);
					}
				});
			},
			pay() {
				if (this.sonid == '') {
					uni.navigateTo({
						url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +
							'&goodstype=course'
					})
				} else {
					uni.navigateTo({
						url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&sonid=' +
							this.sonid + '&goodstype=course'
					})
				}
			},
			pay1() {
				let _this = this;
				if (!this.$store.state.user.token) {
					uni.showModal({
						title: '请先登录',
						content: '登录后才能进行操作',
						success: function(res) {
							if (res.confirm) {
								_this.reload = true;
								uni.navigateTo({
									url: "/pages/login/login?type=wx"
								})
							} else if (res.cancel) {
								uni.navigateBack();
							}
						}
					});
				} else {
				this.apiPay(this.menuinfo.id, this.menuinfo.Cost,'class').then(data => {
					console.log(data);
					// 仅作为示例，非真实参数信息。
					uni.requestPayment({
						provider: 'wxpay',
						...data.pay,
						success: function(res) {
							uni.hideLoading();
							uni.showToast({
								icon: 'none',
								title: '支付成功',
							})
							// 此处的代码为刷新当前页面
							var pages = getCurrentPages();
							var curPage = pages[pages.length - 1];
							curPage.onLoad(_this.option);
						},
						fail: function(err) {
							uni.hideLoading();
							uni.showToast({
								icon: 'none',
								title: '支付失败',
							})
							console.log('fail:' + JSON.stringify(err));
						}
					});
				});
				}
			},
			collect() {
				let _this = this;
				if (!this.$store.state.user.token) {
					uni.showModal({
						title: '请先登录',
						content: '登录后才能进行操作',
						success: function(res) {
							if (res.confirm) {
								_this.reload = true;
								uni.navigateTo({
									url: "/pages/login/login?type=wx"
								})
							} else if (res.cancel) {
								uni.navigateBack();
							}
						}
					});
				} else {
				this.$http.get("v1/course/docollect", {
					params: {
						id:_this.menuinfo.id,
					}
				}).then(res => {
					console.log(res);
					if (res.data.code == 0) {
						// 此处的代码为刷新当前页面
						uni.showToast({
							title: res.data.data,
							icon: 'none'
						});
						var pages = getCurrentPages();
						var curPage = pages[pages.length - 1];
						curPage.onLoad(_this.option);
					} else{
						uni.showToast({
							title: '操作失败',
							icon: 'none'
						});
					}
				});
				}
			},
			exchange() {
				uni.navigateTo({
					url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +
						'&goodstype=course&action=credit'
				})
			},
			seckill() {
				uni.navigateTo({
					url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +
						'&goodstype=course&action=seckill'
				})
			},
			pintuan() {
				uni.navigateTo({
					url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +
						'&goodstype=course&action=pintuan'
				})
			},
			fullscreen(e) {
				this.videofullscreen = e.detail.fullScreen
			},
			video_onplay() {
				// this.sign()
				console.log('play')
				if(this.menuinfo.is_buy==1){
					this.countVtime()
					let audio = innerAudioContext;
					audio.pause()
				}else{
					// uni.showToast({
					// 	title: '请购买',
					// 	icon: 'none'
					// });
				}
				
			},
			video_onpause() {
				clearInterval(this.t2)
				this.saveVtime()
			},
			video_onend() {
				clearInterval(this.t2)
				this.saveVtime()
			},
			video_timeUpdate(e) {
				let video_currentTime = e.detail.currentTime
				let video_duration = e.detail.duration
				// if(!this.is_free || !this.son_is_pay){
				// 	if(!this.son_is_pay){
				// 		if(video_currentTime>=this.free_time){
				// 			if(this.videofullscreen){
				// 				this.videoContext.exitFullScreen()
				// 			}
				// 			this.videoContext.pause()
				// 			uni.showModal({
				// 				title: '试看结束',
				// 				content: '需要解锁该课程吗?',
				// 				success: function (res) {
				// 					if (res.confirm) {
				// 						this.pay()
				// 					} else if (res.cancel) {
				// 						this.videoContext.seek(0)
				// 					}
				// 				}
				// 			});
				// 		}
				// 	}
				// }
			},
			gaosi_ani() {
				if (this.gaosi == true) {
					this.t1 = setInterval(function() {
						this.gaosi = false
						if (this.gaosi == false) {
							this.gaosi = true
						}
					}, 4000)
				}
			},
			progress_text(e) {
				let audio = innerAudioContext
				let progress = e.detail.value
				this.currentTime = this.format((progress / 100) * audio.duration)
			},
			progress_change(e) {
				let audio = innerAudioContext
				let progress = e.detail.value
				this.currentTime = this.format((progress / 100) * audio.duration)
				audio.seek((progress / 100) * audio.duration)
			},
			change_slide(e) {
				let audio = innerAudioContext;
				if (e == 1) {
					audio.seek(audio.currentTime + 15)
				} else {
					audio.seek(audio.currentTime - 15)
				}
			},
			play() {
				let audio = innerAudioContext;
				if (audio.paused) {
					audio.play()

				} else {
					audio.pause()

				}
			},
			muluchange(e) {
				this.btnnum = e
			},
			videoErrorCallback(e) {
				// uni.showModal({
				// 	content: e.target.errMsg,
				// 	showCancel: false
				// })
			},
			openvideo(item) {
				console.log(item)
				if(this.menuinfo.Cost==0){
					this.sonid = item.id
					this.son_is_pay = true
					this.videostate = 'video'
					this.videosrc = item.url
					this.videoId = item.id
					this.videoContext.pause();
					this.videoContext.play();
				}else{
					if(this.menuinfo.is_buy==0){
						uni.showModal({
								title: '提示',
							    content: '购买课程后可观看?',
										success: (res) =>{
											var that=this
											if (res.confirm) {
												this.pay1()
											} else if (res.cancel) {
												// that.currentTime='00:00'
												// audio.seek(0)
												// that.audio_progress=0
											}
										}
						});
					}else{
						this.sonid = item.id
						this.son_is_pay = true
						this.videostate = 'video'
						this.videosrc = item.url
						this.videoId = item.id
						this.videoContext.pause();
						this.videoContext.play();
					}
				}
			
				
				//this.video_onplay()
				//this.sign()
				// var menu_free=this.is_free || item.is_pay==1?1:0
				// if(this.is_free || item.is_pay==1){
				// 	this.son_is_pay=true
				// 	if(item.media === 'video') {
				// 		this.videostate = 'video'
				// 		this.videosrc = item.src
				// 	} else if (item.media === 'audio') {
				// 		this.videostate = 'audio'
				// 		if(item.src){
				// 			this.gaosi=true
				// 			let audio=innerAudioContext;
				// 			//#ifndef MP-WEIXIN
				// 			audio.src = item.src
				// 			audio.autoplay = true

				// 			//#endif
				// 			//#ifdef MP-WEIXIN
				// 			audio.title = item.coursename;
				// 			audio.singer = this.teacher.imgname;
				// 			audio.coverImgUrl = this.HOST_URL+this.menuinfo.thumb;
				// 			audio.src = item.src;
				// 			//#endif
				// 		}
				// 	} else {
				// 		uni.navigateTo({
				// 			url: '/pages/tuwen/tuwen?twid='+item.id+'&is_free='+menu_free,
				// 		})
				// 	}
				// }else{
				// 	this.son_is_pay=false
				// 	if(item.is_sk==1){
				// 		this.free_time=item.freesecond
				// 		if(item.media === 'video') {
				// 			this.videostate = 'video'
				// 			this.videosrc = item.src
				// 		} else if (item.media === 'audio') {
				// 			this.videostate = 'audio'
				// 			if(item.src){
				// 				this.gaosi=true
				// 				let audio=innerAudioContext;
				// 				//#ifndef MP-WEIXIN
				// 				audio.src = item.src
				// 				audio.autoplay = true
				// 				//audio.loop = true
				// 				//#endif
				// 				//#ifdef MP-WEIXIN
				// 				audio.title = item.coursename;
				// 				audio.singer = this.teacher.imgname;
				// 				audio.coverImgUrl = this.HOST_URL+this.menuinfo.thumb;
				// 				audio.src = item.src;
				// 				//#endif
				// 			}
				// 		} else {
				// 			uni.navigateTo({
				// 				url: '/pages/tuwen/tuwen?twid='+item.id+'&is_free='+menu_free,
				// 			})
				// 		}
				// 	}else{
				// 		uni.showModal({
				// 			title: '提示',
				// 			content: '要解锁单个课程吗?',
				// 			success: function (res) {
				// 				if (res.confirm) {
				// 					this.pay()
				// 				} else if (res.cancel) {
				// 					console.log('用户点击取消');
				// 				}
				// 			}
				// 		});
				// 	}
				// }
			},
			//格式化时长
			format(num) {
				return '0'.repeat(2 - String(Math.floor(num / 60)).length) + Math.floor(num / 60) + ':' + '0'.repeat(2 -
					String(
						Math.floor(num % 60)).length) + Math.floor(num % 60)
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiGetCourseList: function(id) {
				return this.$http.get('/v1/course/course_detail', {
					params: {
						id
		
					}
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			},
			sign(){
				setTimeout(() => {
					this.$http.post("v1/member/doSign", {
						id: this.project.pid,
						pid: this.project.id,
						cid:this.videoId
					}).then(res => {
				
					});
				}, 5000);
			},
			/**
			 * 创建订单
			 * @param {Object} project_id
			 * @param {Object} money
			 */
			apiPay: function(id, money,type) {
				return this.$http.post('/v1/course_sign_up', {
					id,
					money,
					type
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},
		}
	}
</script>
<style lang="less" scoped>
	page {
		background-color: #f3f3f3;
		font-family: SimHei;
	}

	.container {
		background-color: #f3f3f3;
	}

	.video {
		width: 100%;
		height: auto;
	}

	// 视频部分
	.shipinpart {
		height: 400rpx;
		background-color: #fff;

		&-media {
			height: 400rpx;
			background-color: #fff;

			.audio_bg {
				position: absolute;
				z-index: -19;
				transition: all .4s cubic-bezier(0.42, 0, 0.58, 1) 0s;
			}

			.donghua(@DHname) {
				@keyframes @DHname {
					0% {
						filter: blur(0upx);
					}

					5% {
						filter: blur(1upx);
					}

					10% {
						filter: blur(2upx);
					}

					15% {
						filter: blur(3upx);
					}

					20% {
						filter: blur(4upx);
					}

					25% {
						filter: blur(5upx);
					}

					30% {
						filter: blur(6upx);
					}

					35% {
						filter: blur(7upx);
					}

					40% {
						filter: blur(8upx);
					}

					45% {
						filter: blur(9upx);
					}

					50% {
						filter: blur(10upx);
					}

					55% {
						filter: blur(9upx);
					}

					60% {
						filter: blur(8upx);
					}

					65% {
						filter: blur(7upx);
					}

					70% {
						filter: blur(6upx);
					}

					75% {
						filter: blur(5upx);
					}

					80% {
						filter: blur(4upx);
					}

					85% {
						filter: blur(3upx);
					}

					90% {
						filter: blur(2upx);
					}

					95% {
						filter: blur(1upx);
					}

					100% {
						filter: blur(0upx);
					}
				}
			}

			;
			.donghua(myDongHua);

			.animation(@animation-name, @animation-duration, @animation-iteration-count) {
				animation: @arguments;
			}

			.gaosi_filter {
				.animation(myDongHua, 4s, infinite);
				transition: all 4s cubic-bezier(0.42, 0, 0.58, 1) 0s;
			}

			image {
				width: 100%;
				height: 400rpx;
			}

			.audio {
				width: 100%;
				height: 400rpx;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 19;
				padding-bottom: 20upx;

				.bofang {
					width: 80upx;
					height: 80upx;

					image {
						width: 80upx;
						height: 80upx;
					}
				}

				&-wrapper {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					position: absolute;
					bottom: 0;
					color: #333;
					background-image: linear-gradient(to bottom, rgba(255, 255, 255, .25), #fff);

					.prev,
					.next {
						width: 48upx;
						height: 44upx;
						margin: 0 20upx;
					}

					.audio-number {
						font-size: 24upx;
					}

					.audio-slider {
						flex: 1;
					}
				}
			}
		}

		&-info {
			z-index: 19;
			padding: 0 20upx;
			display: flex;
			flex-direction: column;

			.info-span1 {
				margin-top: 35upx;
				font-size: 32upx;
				font-weight: 700;
				color: #070707;
				letter-spacing: 5upx;
			}

			.info-span2 {
				letter-spacing: 5upx;
				font-size: 26upx;
				color: #373737;
				margin-top: 16upx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				text:nth-child(2) {
					letter-spacing: 1upx;
					width: 110upx;
					height: 43upx;
					border-radius: 10upx;
					background-color: #e4edff;
					color: #4b89ff;
					text-align: center;
					line-height: 43upx;
					margin-left: 50upx;
					margin-right: 30upx;
					box-shadow: 0upx 2upx 3upx 1upx #8dbeff;
				}
			}

			.info-span5 {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 10rpx;
			}

			.info-span3 {
				margin-top: 18upx;
				font-size: 24upx;
				color: #b3b3b3;
				display: flex;
				align-items: center;

				text:nth-child(2) {
					margin: 0 15upx;
				}
			}

			.info-span4 {
				margin-top: 22upx;
				color: #E98438;
				font-size: 50upx;
				margin-right: 40upx;
			}
		}
	}


	// 课程部分
	.kechengpart {
		margin-top: 10upx;
		background-color: #fff;
		min-height: 1000rpx;

		&-title {
			height: 125upx;
			// background-color: red;
			background-color: #fff;
			padding-top: 45upx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-around;

			&-item {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;

				view {
					font-size: 30upx;
					color: #575757;
				}

				.btna {
					// 需要追加到view上的class
					font-weight: 700;
					color: #131313;
				}

				._underline {
					// 需要追加到view下方的下划线class
					width: 70upx;
					height: 7upx;
					background-color: #2f77ff;
					border-radius: 5upx;
					margin-top: 15upx;
				}
			}

		}

		&-content {

			// 课程简介
			.kcjs {
				padding: 0 20upx;
				background-color: #fff;
				display: none;

				&-lecturer {
					&-top {
						height: 60upx;
						display: inline-flex;

						image {
							width: 47upx;
							height: 33upx;
							margin-top: 4upx;
						}

						text {
							font-size: 26upx;
							font-weight: 700;
							color: #020202;
							margin-left: 15upx;
						}
					}

					.jiangshi-right {
						display: inline-flex;
						align-items: center;
						float: right;

						text {
							font-size: 22upx;
							color: #6d6d6d;
							margin-right: 0;
						}

						image {
							width: 20upx;
							height: 22upx;
							margin-left: 5upx;
						}
					}

					&-bottom {
						display: flex;
						align-items: center;
						justify-content: space-between;

						image {
							width: 100%;
							height: 400upx;
						}

						.jiangshi-left {
							display: flex;
							align-items: center;

							image {
								width: 80upx;
								height: 80upx;
								border-radius: 40upx;
							}

							text {
								font-size: 26upx;
								color: #333;
								margin-left: 20upx;
							}
						}

						.jiangshi-right {
							display: flex;
							align-items: center;

							text {
								font-size: 26upx;
								color: #C0C0C0;
								margin-right: 0;
							}

							image {
								width: 20upx;
								height: 26upx;
								margin-left: 5upx;
							}
						}

					}
				}

				&-brief {
					margin-top: 40upx;
					margin-bottom: 150upx;

					&-top {
						height: 75upx;
						display: flex;

						image {
							width: 53upx;
							height: 49upx;
						}

						text {
							font-size: 26upx;
							font-weight: 700;
							color: #020202;
							margin-left: 15upx;
							margin-top: 4upx;
						}
					}

					&-center {
						padding-bottom: 30upx;

						text {
							font-size: 26upx;
							color: #313131;
						}
					}
				}
			}

			// 课程目录
			.kcml {
				display: none;
				padding: 0 20upx;
				background-color: #fff;

				&-list {
					padding: 20upx;
					box-sizing: border-box;
					display: flex;
					justify-content: space-between;
					border-bottom: 1rpx solid #eee;

					&-left {
						width: 436upx;
						height: 60upx;
						line-height: 60upx;

						.yinpin {
							display: inline-block;
							width: 64upx;
							height: 32upx;
							font-size: 20upx;
							color: #ff6969;
							border: 2upx solid #ff6969;
							border-radius: 5upx;
							box-sizing: border-box;
							text-align: center;
							line-height: 28upx;
							margin-right: 15upx;
							vertical-align: middle;
						}

						.shipin {
							display: inline-block;
							width: 64upx;
							height: 32upx;
							font-size: 20upx;
							color: #398cff;
							border: 2upx solid #398cff;
							border-radius: 5upx;
							box-sizing: border-box;
							text-align: center;
							line-height: 24upx;
							margin-right: 15upx;
							vertical-align: middle;
						}

						.tuwen {
							display: inline-block;
							width: 64upx;
							height: 32upx;
							font-size: 20upx;
							color: #8bc34a;
							border: 2upx solid #8bc34a;
							border-radius: 5upx;
							box-sizing: border-box;
							text-align: center;
							line-height: 24upx;
							margin-right: 15upx;
							vertical-align: middle;
						}

						text:nth-child(2) {
							font-size: 25upx;
							color: #070707;
							display: inline-block;
							white-space: nowrap;
							width: 80%;
							overflow: hidden;
							text-overflow: ellipsis;
							vertical-align: middle;
						}
					}

					&-right {
						text {
							font-size: 24upx;
							color: #007AFF;
							vertical-align: middle;
						}

						image {
							width: 30upx;
							height: 30upx;
							vertical-align: middle;
						}
					}
				}
			}

			// 用户评价
			.yhpj {
				display: none;
				text-align: center;
				margin-bottom: 140upx;
				background-color: #fff;
				padding: 30rpx 0;

				&-list {
					.item {
						display: flex;
						padding: 0 20upx;
						margin-top: 40upx;

						&-left {
							width: 100upx;
							height: 100upx;
							border-radius: 50%;
							margin-right: 20upx;
						}

						&-right {
							display: flex;
							flex-direction: column;

							&-top {
								display: flex;
								align-items: center;

								text:nth-child(1) {
									font-size: 32upx;
									color: #333;
								}

								text:nth-child(2) {
									font-size: 26upx;
									color: #999;
									margin-left: 20upx;
								}
							}

							&-bottom {
								max-width: 500upx;
								// height: 60upx;
								margin-top: 10upx;
								padding: 20upx;
								border-radius: 10upx;
								background-color: #fff;
								text-align: left;

								text {
									font-size: 30upx;
									font-weight: 400;
									color: #333;
								}
							}
						}
					}
				}
			}

			.nopl {
				width: 334upx;
				height: 243upx;
				margin-top: 20upx;
			}

			.dis {
				display: block;
			}
		}
	}


	.buy {
		width: 100%;
		height: 122upx;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		border-radius: 40upx 40upx 0 0;
		border-top: 1rpx solid #eee;

		&-left {
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			align-items: center;

			image {
				width: 37upx;
				height: 37upx;
			}

			text {
				// margin-top: 15upx;
				font-size: 24upx;
				color: #ff6229;
			}
			.info-span4 {
				color: #ff6229;
				font-size: 50upx;
				margin-right: 40upx;
			}
		}

		.sharebtn {
			margin: 0;
			padding: 0;
			outline: none;
			border-radius: 0;
			background-color: transparent;
			line-height: inherit;
			width: max-content;
		}

		.sharebtn:after {
			border: none;
		}

		&-right {
			width: 450upx;
			height: 80upx;
			background-image: linear-gradient(to right, #4498ff, #1763ff);
			border-radius: 80upx;
			font-size: 34upx;
			font-weight: 700;
			color: #fff;
			// border: 3upx solid #fff;
			text-align: center;
			line-height: 80upx;
			box-shadow: 0rpx 2rpx 2rpx 1rpx #8dbeff;
			letter-spacing: 7rpx;
		}
	}
	.fontcolor{
		color:#FF0066;
	}
	.cover-box {
		width: 100%;
		// height: auto;
		min-height: 200rpx;
		position: relative;
		
		.cover {
			width: 100%;
			height: 100%;
			// border-radius: 10rpx;
		}
		.button{
			position: absolute;
			bottom: 5%;
			right: 5%;
			transform: translateX(0);
			border-radius: 30rpx;
			background-color: blue;
			color: white;
			padding: 10rpx 20rpx;
			font-size: 40rpx;
		}
	}
	.p-b-btn{
		display:flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		// font-size: $font-sm;
		// color: $font-color-base;
		width: 96upx;
		.yticon{
			font-size: 40upx;
			line-height: 48upx;
			// color: $font-color-light;
		}
		&.active, &.active .yticon{
			// color: $uni-color-primary;
		}
		.icon-fenxiang2{
			font-size: 42upx;
			transform: translateY(-2upx);
		}
		.icon-shoucang{
			font-size: 46upx;
		}
	}
	.contact-btn {
	  display: inline-block;
	  position: absolute;
	  width: 20%;
	  background: salmon;
	    opacity: 0;
	}
</style>
