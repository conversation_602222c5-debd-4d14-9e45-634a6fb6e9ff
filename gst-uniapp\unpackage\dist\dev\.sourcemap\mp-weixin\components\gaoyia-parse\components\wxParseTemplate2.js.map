{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTemplate2.vue?7963", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTemplate2.vue?4db1", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTemplate2.vue?41da", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTemplate2.vue?bb2f", "uni-app:///components/gaoyia-parse/components/wxParseTemplate2.vue"], "names": ["name", "props", "node", "components", "wxParseTemplate", "wxParseImg", "wxParseVideo", "wxParseAudio", "wxParseTable", "methods", "wxParseATap", "href", "e", "parent"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;;;AAG/D;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4nB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6DhpB;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACA,IACAC,OACAC,wBADAD;MAEA;MACA;MACA;QACAE;MACA;MACAA;IACA;EACA;AACA;AAAA,2B", "file": "components/gaoyia-parse/components/wxParseTemplate2.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxParseTemplate2.vue?vue&type=template&id=28b59d66&\"\nvar renderjs\nimport script from \"./wxParseTemplate2.vue?vue&type=script&lang=js&\"\nexport * from \"./wxParseTemplate2.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gaoyia-parse/components/wxParseTemplate2.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTemplate2.vue?vue&type=template&id=28b59d66&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTemplate2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTemplate2.vue?vue&type=script&lang=js&\"", "<template>\r\n\t\t<!--判断是否是标签节点-->\r\n\t<block v-if=\"node.node == 'element'\">\r\n\t\t<!--button类型-->\r\n\t\t<button v-if=\"node.tag == 'button'\" type=\"default\" size=\"mini\" :class=\"node.classStr\" :style=\"node.styleStr\">\r\n\t\t\t<wx-parse-template :node=\"node\" />\r\n\t\t</button>\r\n\t\t\r\n\t\t<!--a类型-->\r\n\t\t<view v-else-if=\"node.tag == 'a'\" @click=\"wxParseATap(node.attr,$event)\" :class=\"node.classStr\" :data-href=\"node.attr.href\" :style=\"node.styleStr\">\r\n\t\t\t<block v-for=\"(node, index) of node.nodes\" :key=\"index\">\r\n\t\t\t\t<wx-parse-template :node=\"node\" />\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!--li类型-->\r\n\t\t<view v-else-if=\"node.tag == 'li'\" :class=\"node.classStr\" :style=\"node.styleStr\">\r\n\t\t\t<block v-for=\"(node, index) of node.nodes\" :key=\"index\">\r\n\t\t\t\t<wx-parse-template :node=\"node\" />\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!--table类型-->\r\n\t\t<wx-parse-table v-else-if=\"node.tag == 'table'\" :class=\"node.classStr\" :style=\"node.styleStr\" :node=\"node\" />\r\n\t\t\r\n\t\t<!--br类型-->\r\n\t\t<!-- #ifndef H5 -->\r\n\t\t\t<text v-else-if=\"node.tag == 'br'\">\\n</text>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t\t<br v-else-if=\"node.tag == 'br'\">\r\n\t\t<!-- #endif -->\r\n\t\t\r\n\t\t<!--video类型-->\r\n\t\t<wx-parse-video :node=\"node\" v-else-if=\"node.tag == 'video'\"/>\r\n\t\r\n\t\t<!--audio类型-->\r\n\t\t<wx-parse-audio :node=\"node\" v-else-if=\"node.tag == 'audio'\"/>\r\n\t\r\n\t\t<!--img类型-->\r\n\t\t<wx-parse-img :node=\"node\" v-else-if=\"node.tag == 'img'\" :style=\"node.styleStr\"/>\r\n\t\r\n\t\t<!--其他标签-->\r\n\t\t<view v-else :class=\"node.classStr\" :style=\"node.styleStr\">\r\n\t\t\t<block v-for=\"(node, index) of node.nodes\" :key=\"index\">\r\n\t\t\t\t<wx-parse-template :node=\"node\" />\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</block>\r\n\t\r\n\t<!--判断是否是文本节点-->\r\n\t<block v-else-if=\"node.node == 'text'\">{{node.text}}</block>\r\n</template>\r\n\r\n<script>\r\n\timport wxParseTemplate from './wxParseTemplate3';\r\n\timport wxParseImg from './wxParseImg';\r\n\timport wxParseVideo from './wxParseVideo';\r\n\timport wxParseAudio from './wxParseAudio';\r\n\timport wxParseTable from './wxParseTable';\r\n\t\r\n\texport default {\r\n\t\tname: 'wxParseTemplate2',\r\n\t\tprops: {\r\n\t\t\tnode: {},\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\twxParseTemplate,\r\n\t\t\twxParseImg,\r\n\t\t\twxParseVideo,\r\n\t\t\twxParseAudio,\r\n\t\t\twxParseTable\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\twxParseATap(attr,e) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\thref\r\n\t\t\t\t} = e.currentTarget.dataset;\r\n\t\t\t\tif (!href) return;\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\twhile(!parent.preview || typeof parent.preview !== 'function') {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t}\r\n\t\t\t\tparent.navigate(href, e, attr);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n"], "sourceRoot": ""}