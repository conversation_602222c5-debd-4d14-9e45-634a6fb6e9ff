
.custom-tabbar.data-v-da768fe2 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	border-top: 2rpx solid #e5e5e5;
	z-index: 99999;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.test-bar.data-v-da768fe2 {
	background: red;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.tabbar-content.data-v-da768fe2 {
	display: flex;
	height: 120rpx;
	align-items: center;
	background: #ffffff;
}
.tab-item.data-v-da768fe2 {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx 0;
	cursor: pointer;
}
.tab-item.data-v-da768fe2:active {
	background: rgba(0, 0, 0, 0.1);
}
.tab-text.data-v-da768fe2 {
	font-size: 24rpx;
	color: #333;
	margin: 4rpx 0;
}

