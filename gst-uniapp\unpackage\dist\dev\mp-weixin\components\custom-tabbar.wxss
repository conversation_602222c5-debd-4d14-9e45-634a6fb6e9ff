
.custom-tabbar.data-v-da768fe2 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	border-top: 1rpx solid #e5e5e5;
	z-index: 9999;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.tabbar-content.data-v-da768fe2 {
	display: flex;
	height: 100rpx;
	align-items: center;
}
.tab-item.data-v-da768fe2 {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	padding: 8rpx 0;
	transition: all 0.3s ease;
}
.tab-item.data-v-da768fe2:active {
	background: rgba(0, 0, 0, 0.05);
}
.tab-icon.data-v-da768fe2 {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 4rpx;
}
.tab-text.data-v-da768fe2 {
	font-size: 20rpx;
	color: #C0C4CC;
	transition: color 0.3s ease;
}
.tab-text.active.data-v-da768fe2 {
	color: #2094CE;
	font-weight: 600;
}
.member-badge.data-v-da768fe2 {
	position: absolute;
	top: 2rpx;
	right: 20rpx;
	background: linear-gradient(135deg, #ff6b6b, #ffa500);
	border-radius: 20rpx;
	padding: 2rpx 8rpx;
	-webkit-transform: scale(0.8);
	        transform: scale(0.8);
}
.member-text.data-v-da768fe2 {
	font-size: 16rpx;
	color: #fff;
	font-weight: 600;
}

/* 动画效果 */
.tab-item.active .tab-icon.data-v-da768fe2 {
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
}

/* 全局样式：为页面内容添加底部安全距离 */

/* 注意：这个样式会影响全局，确保页面内容不被tabBar遮挡 */

