


































































































































































































































































































































































































































































































































/* ===== 基础页面样式 ===== */
.groups-page.data-v-59cfa49a {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}
.page-content.data-v-59cfa49a {
	position: relative;
	z-index: 1;
}
/* ===== 页面头部样式 ===== */
.page-header.data-v-59cfa49a {
	background: white;
	border-radius: 0 0 30rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.header-content.data-v-59cfa49a {
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.title-section.data-v-59cfa49a {
	flex: 1;
}
.page-title.data-v-59cfa49a {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}
.page-subtitle.data-v-59cfa49a {
	font-size: 22rpx;
	color: #666;
}
.stats-section.data-v-59cfa49a {
	display: flex;
	gap: 25rpx;
}
.stat-item.data-v-59cfa49a {
	text-align: center;
}
.stat-number.data-v-59cfa49a {
	font-size: 28rpx;
	font-weight: 700;
	color: #667eea;
	display: block;
	line-height: 1;
}
.stat-label.data-v-59cfa49a {
	font-size: 18rpx;
	color: #999;
	margin-top: 5rpx;
}
/* ===== 左右联动布局 ===== */
.split-layout.data-v-59cfa49a {
	display: flex;
	height: calc(100vh - 200rpx);
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
	margin: 0 20rpx;
	box-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);
}
/* ===== 左侧面板 ===== */
.left-panel.data-v-59cfa49a {
	width: 200rpx;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}
.panel-header.data-v-59cfa49a {
	padding: 25rpx 15rpx 20rpx;
	background: white;
	border-bottom: 1rpx solid #e9ecef;
	text-align: center;
}
.panel-title.data-v-59cfa49a {
	font-size: 22rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.panel-subtitle.data-v-59cfa49a {
	font-size: 18rpx;
	color: #999;
}
.group-list.data-v-59cfa49a {
	flex: 1;
	padding: 10rpx 0;
}
/* ===== 小组列表项 ===== */
.group-item.data-v-59cfa49a {
	margin: 0 10rpx 15rpx;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	text-align: center;
	position: relative;
}
.group-item.active.data-v-59cfa49a {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	-webkit-transform: scale(1.05);
	        transform: scale(1.05);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.group-item.data-v-59cfa49a:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.simple-group-content.data-v-59cfa49a {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}
.group-level-badge.data-v-59cfa49a {
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	font-size: 18rpx;
	font-weight: 700;
	min-width: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}
.simple-group-name.data-v-59cfa49a {
	font-size: 20rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	line-height: 1.3;
	word-break: break-all;
}
.simple-status-dot.data-v-59cfa49a {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}
.simple-status-dot.active.data-v-59cfa49a {
	background: #4CAF50;
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}
.simple-status-dot.completed.data-v-59cfa49a {
	background: #2196F3;
	box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
}
.simple-status-dot.pending.data-v-59cfa49a {
	background: #FF9800;
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
}
/* ===== 新概念教程样式 ===== */
.concept-tutorial-item.data-v-59cfa49a {
	margin: 0 10rpx 20rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 15rpx;
	padding: 25rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	position: relative;
	overflow: hidden;
}
.concept-tutorial-item.active.data-v-59cfa49a {
	-webkit-transform: scale(1.05);
	        transform: scale(1.05);
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
	border-color: rgba(255,255,255,0.3);
}
.concept-tutorial-item.data-v-59cfa49a:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.concept-content.data-v-59cfa49a {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	position: relative;
	z-index: 1;
}
.concept-icon.data-v-59cfa49a {
	font-size: 32rpx;
	margin-bottom: 5rpx;
}
.concept-title.data-v-59cfa49a {
	font-size: 20rpx;
	font-weight: 600;
	color: white;
	text-align: center;
	line-height: 1.3;
}
.concept-badge.data-v-59cfa49a {
	background: rgba(255,255,255,0.2);
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 16rpx;
	font-weight: 500;
}
/* ===== 右侧面板 ===== */
.right-panel.data-v-59cfa49a {
	flex: 1;
	display: flex;
	flex-direction: column;
}
/* ===== 新概念教程详情 ===== */
.concept-detail-content.data-v-59cfa49a {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.concept-detail-header.data-v-59cfa49a {
	position: relative;
	height: 180rpx;
	overflow: hidden;
}
.concept-bg.data-v-59cfa49a {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
}
.concept-overlay.data-v-59cfa49a {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.2);
	display: flex;
	align-items: center;
	padding: 30rpx;
}
.concept-detail-icon.data-v-59cfa49a {
	font-size: 50rpx;
	margin-right: 20rpx;
}
.concept-detail-info.data-v-59cfa49a {
	flex: 1;
	color: white;
}
.concept-detail-title.data-v-59cfa49a {
	font-size: 32rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}
.concept-detail-subtitle.data-v-59cfa49a {
	font-size: 22rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}
.concept-progress-info.data-v-59cfa49a {
	background: rgba(255,255,255,0.2);
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	display: inline-block;
}
.progress-info-text.data-v-59cfa49a {
	font-size: 18rpx;
	font-weight: 600;
}
/* ===== 学习分类 ===== */
.concept-categories.data-v-59cfa49a {
	padding: 30rpx;
	flex: 1;
}
.categories-title.data-v-59cfa49a {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}
.categories-grid.data-v-59cfa49a {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}
.category-card.data-v-59cfa49a {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}
.category-card.data-v-59cfa49a:active {
	background: #e9ecef;
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.category-icon.data-v-59cfa49a {
	font-size: 28rpx;
	margin-right: 15rpx;
}
.category-info.data-v-59cfa49a {
	flex: 1;
}
.category-name.data-v-59cfa49a {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.category-desc.data-v-59cfa49a {
	font-size: 20rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}
.category-lessons.data-v-59cfa49a {
	font-size: 18rpx;
	color: #999;
}
.category-arrow.data-v-59cfa49a {
	font-size: 18rpx;
	color: #999;
}
/* ===== 快速操作 ===== */
.concept-actions.data-v-59cfa49a {
	padding: 0 30rpx 30rpx;
}
.concept-action-title.data-v-59cfa49a {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}
.concept-action-buttons.data-v-59cfa49a {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}
.concept-btn.data-v-59cfa49a {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}
.concept-btn.primary.data-v-59cfa49a {
	background: linear-gradient(135deg, #4CAF50, #45A049);
	color: white;
}
.concept-btn.secondary.data-v-59cfa49a {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}
.concept-btn.tertiary.data-v-59cfa49a {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}
.concept-btn.data-v-59cfa49a:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.concept-btn-icon.data-v-59cfa49a {
	font-size: 28rpx;
	margin-right: 15rpx;
}
.concept-btn-content.data-v-59cfa49a {
	flex: 1;
}
.concept-btn-title.data-v-59cfa49a {
	font-size: 24rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}
.concept-btn-desc.data-v-59cfa49a {
	font-size: 20rpx;
	opacity: 0.9;
}
/* ===== 小组详情 ===== */
.detail-content.data-v-59cfa49a {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.detail-header.data-v-59cfa49a {
	position: relative;
	height: 180rpx;
	overflow: hidden;
}
.detail-bg.data-v-59cfa49a {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.detail-overlay.data-v-59cfa49a {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.3);
	display: flex;
	align-items: center;
	padding: 30rpx;
}
.detail-avatar.data-v-59cfa49a {
	width: 70rpx;
	height: 70rpx;
	margin-right: 20rpx;
}
.detail-avatar image.data-v-59cfa49a {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
}
.detail-info.data-v-59cfa49a {
	flex: 1;
	color: white;
}
.detail-title.data-v-59cfa49a {
	font-size: 32rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}
.detail-subtitle.data-v-59cfa49a {
	font-size: 22rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}
.detail-level.data-v-59cfa49a {
	background: rgba(255,255,255,0.2);
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 18rpx;
	font-weight: 600;
	display: inline-block;
}
/* ===== 统计卡片 ===== */
.detail-stats.data-v-59cfa49a {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}
.stat-card-detail.data-v-59cfa49a {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
}
.stat-card-detail.data-v-59cfa49a:active {
	background: #e9ecef;
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.stat-icon-detail.data-v-59cfa49a {
	font-size: 32rpx;
	margin-bottom: 10rpx;
}
.stat-number-detail.data-v-59cfa49a {
	font-size: 28rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.stat-label-detail.data-v-59cfa49a {
	font-size: 20rpx;
	color: #666;
}
/* ===== 进度详情 ===== */
.progress-detail.data-v-59cfa49a {
	padding: 0 30rpx 30rpx;
}
.progress-header.data-v-59cfa49a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}
.progress-title.data-v-59cfa49a {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
}
.progress-value.data-v-59cfa49a {
	font-size: 26rpx;
	font-weight: 700;
	color: #667eea;
}
.progress-bar-detail.data-v-59cfa49a {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 15rpx;
}
.progress-fill-detail.data-v-59cfa49a {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.progress-desc.data-v-59cfa49a {
	font-size: 20rpx;
	color: #666;
	text-align: center;
}
/* ===== 功能按钮 ===== */
.detail-actions.data-v-59cfa49a {
	flex: 1;
	padding: 0 30rpx 30rpx;
}
.action-row.data-v-59cfa49a {
	margin-bottom: 15rpx;
}
.action-btn-large.data-v-59cfa49a {
	display: flex;
	align-items: center;
	padding: 25rpx;
	border-radius: 20rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}
.action-btn-large.primary.data-v-59cfa49a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.action-btn-large.secondary.data-v-59cfa49a {
	background: #f8f9fa;
	color: #333;
	border: 2rpx solid #e9ecef;
}
.action-btn-large.data-v-59cfa49a:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.btn-icon-large.data-v-59cfa49a {
	font-size: 32rpx;
	margin-right: 20rpx;
}
.btn-content.data-v-59cfa49a {
	flex: 1;
}
.btn-title.data-v-59cfa49a {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}
.btn-desc.data-v-59cfa49a {
	font-size: 20rpx;
	opacity: 0.9;
}
.btn-arrow-large.data-v-59cfa49a {
	font-size: 22rpx;
	opacity: 0.8;
}
/* ===== 空状态 ===== */
.empty-detail.data-v-59cfa49a {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
	text-align: center;
}
.empty-icon.data-v-59cfa49a {
	font-size: 60rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}
.empty-title.data-v-59cfa49a {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 15rpx;
}
.empty-desc.data-v-59cfa49a {
	font-size: 22rpx;
	color: #999;
	line-height: 1.5;
}
/* ===== 无权限页面样式 ===== */
.no-permission-page.data-v-59cfa49a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.permission-container.data-v-59cfa49a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 80rpx 60rpx;
	text-align: center;
	max-width: 600rpx;
	margin: 0 40rpx;
	-webkit-backdrop-filter: blur(20rpx);
	        backdrop-filter: blur(20rpx);
	box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}
.permission-icon.data-v-59cfa49a {
	font-size: 80rpx;
	margin-bottom: 40rpx;
	opacity: 0.8;
}
.permission-title.data-v-59cfa49a {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}
.permission-desc.data-v-59cfa49a {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
	display: block;
	line-height: 1.5;
}
.permission-hint.data-v-59cfa49a {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 60rpx;
	display: block;
	line-height: 1.5;
}
.permission-actions.data-v-59cfa49a {
	display: flex;
	gap: 30rpx;
	justify-content: center;
}
.btn-login.data-v-59cfa49a, .btn-contact.data-v-59cfa49a {
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	font-size: 26rpx;
	border: none;
	min-width: 160rpx;
	transition: all 0.3s ease;
}
.btn-login.data-v-59cfa49a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.btn-contact.data-v-59cfa49a {
	background: #f0f0f0;
	color: #666;
}
.btn-login.data-v-59cfa49a:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.btn-contact.data-v-59cfa49a:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}

