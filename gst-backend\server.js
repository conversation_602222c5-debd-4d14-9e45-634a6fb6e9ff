const app = require('./src/app');
const { sequelize } = require('./src/config/database');
const logger = require('./src/utils/logger');
require('dotenv').config();

const PORT = process.env.PORT || 8005;
const HOST = process.env.HOST || '0.0.0.0'; // 监听所有网络接口

// 获取本机IP地址
const os = require('os');
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();

// 数据库连接和服务器启动
async function startServer() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    logger.info('✅ 数据库连接成功');
    
    // 同步数据库模型 - 开发环境下不自动同步，避免数据丢失
    // 如需同步数据库结构，请手动运行初始化脚本
    // if (process.env.NODE_ENV === 'development') {
    //   await sequelize.sync({ alter: true });
    //   logger.info('✅ 数据库模型同步完成 (保留数据)');
    // }
    logger.info('✅ 跳过数据库同步，使用现有数据库结构');
    
    // 启动服务器
    const server = app.listen(PORT, HOST, () => {
      logger.info(`🚀 GST日语培训班API服务器启动成功！`);
      logger.info(`📡 本地访问: http://localhost:${PORT}`);
      logger.info(`🌐 局域网访问: http://${localIP}:${PORT}`);
      logger.info(`🔍 健康检查: http://${localIP}:${PORT}/health`);
      logger.info(`📚 API文档: http://${localIP}:${PORT}/api`);
      logger.info(`⏰ 启动时间: ${new Date().toLocaleString()}`);
      logger.info(`🌍 运行环境: ${process.env.NODE_ENV}`);
      logger.info(`🔗 允许跨域访问: 已启用CORS`);
      logger.info(`🌐 支持访问域名: localhost, ${localIP}, 局域网IP`);
      logger.info(`📱 支持端口: 3005, 8080, 8081`);
    });
    
    return server;
    
  } catch (error) {
    logger.error('❌ 服务器启动失败:', error);
    
    if (error.name === 'SequelizeConnectionError') {
      logger.error('数据库连接失败，请检查：');
      logger.error('1. MySQL服务是否启动');
      logger.error('2. 数据库配置是否正确');
      logger.error('3. 数据库是否存在');
    }
    
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，正在优雅关闭服务器...');
  try {
    await sequelize.close();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('收到SIGINT信号，正在优雅关闭服务器...');
  try {
    await sequelize.close();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
  }
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
