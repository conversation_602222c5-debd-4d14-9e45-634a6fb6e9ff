(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/groups/index"],{2529:function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("7eb4")),s=i(o("ee10")),r={data:function(){return{pageLoading:!1,hasGroupPermission:!1,selectedGroupId:"concept",selectedGroup:null,selectedGroupIndex:0,groupsLoading:!1,conceptTutorial:{id:"concept",title:"新标日本语教程",description:"适合所有学员的基础教程",totalLessons:30,completedLessons:8},conceptCategoryList:[{id:"speaking",name:"口语",icon:"🗣️",expanded:!0,lessons:[{id:1,title:"第一课",subtitle:"基础问候语",completed:!0},{id:2,title:"第二课",subtitle:"自我介绍",completed:!0},{id:3,title:"第三课",subtitle:"日常对话",completed:!1},{id:4,title:"第四课",subtitle:"购物用语",completed:!1},{id:5,title:"第五课",subtitle:"餐厅用语",completed:!1}]},{id:"grammar",name:"语法",icon:"📝",expanded:!1,lessons:[{id:6,title:"第一课",subtitle:"基本句型",completed:!0},{id:7,title:"第二课",subtitle:"动词变位",completed:!1},{id:8,title:"第三课",subtitle:"形容词活用",completed:!1},{id:9,title:"第四课",subtitle:"助词用法",completed:!1},{id:10,title:"第五课",subtitle:"敬语表达",completed:!1}]},{id:"vocabulary",name:"词汇",icon:"📚",expanded:!1,lessons:[{id:11,title:"第一课",subtitle:"基础词汇",completed:!0},{id:12,title:"第二课",subtitle:"生活词汇",completed:!1},{id:13,title:"第三课",subtitle:"工作词汇",completed:!1},{id:14,title:"第四课",subtitle:"学习词汇",completed:!1},{id:15,title:"第五课",subtitle:"旅游词汇",completed:!1}]},{id:"listening",name:"听力",icon:"🎧",expanded:!1,lessons:[{id:16,title:"第一课",subtitle:"基础听力",completed:!1},{id:17,title:"第二课",subtitle:"对话听力",completed:!1},{id:18,title:"第三课",subtitle:"新闻听力",completed:!1},{id:19,title:"第四课",subtitle:"故事听力",completed:!1},{id:20,title:"第五课",subtitle:"综合听力",completed:!1}]},{id:"reading",name:"阅读",icon:"📖",expanded:!1,lessons:[{id:21,title:"第一课",subtitle:"短文阅读",completed:!1},{id:22,title:"第二课",subtitle:"新闻阅读",completed:!1},{id:23,title:"第三课",subtitle:"小说阅读",completed:!1},{id:24,title:"第四课",subtitle:"说明文阅读",completed:!1},{id:25,title:"第五课",subtitle:"综合阅读",completed:!1}]},{id:"writing",name:"写作",icon:"✍️",expanded:!1,lessons:[{id:26,title:"第一课",subtitle:"基础写作",completed:!1},{id:27,title:"第二课",subtitle:"日记写作",completed:!1},{id:28,title:"第三课",subtitle:"书信写作",completed:!1},{id:29,title:"第四课",subtitle:"作文写作",completed:!1},{id:30,title:"第五课",subtitle:"应用写作",completed:!1}]}],groupCategoryList:[{id:"n5-basic",name:"N5基础",icon:"🌱",expanded:!0,lessons:[{id:101,title:"第一课",subtitle:"五十音图（あ行）",completed:!0},{id:102,title:"第二课",subtitle:"五十音图（か行）",completed:!0},{id:103,title:"第三课",subtitle:"五十音图（さ行）",completed:!1},{id:104,title:"第四课",subtitle:"五十音图（た行）",completed:!1},{id:105,title:"第五课",subtitle:"五十音图（な行）",completed:!1}]},{id:"n5-grammar",name:"N5语法",icon:"📖",expanded:!1,lessons:[{id:106,title:"第一课",subtitle:"です/である",completed:!0},{id:107,title:"第二课",subtitle:"名词+は+名词",completed:!1},{id:108,title:"第三课",subtitle:"疑问词",completed:!1},{id:109,title:"第四课",subtitle:"数字和时间",completed:!1},{id:110,title:"第五课",subtitle:"动词现在时",completed:!1}]},{id:"n5-vocabulary",name:"N5词汇",icon:"📚",expanded:!1,lessons:[{id:111,title:"第一课",subtitle:"家族称呼",completed:!0},{id:112,title:"第二课",subtitle:"职业名称",completed:!1},{id:113,title:"第三课",subtitle:"日常用品",completed:!1},{id:114,title:"第四课",subtitle:"食物饮料",completed:!1},{id:115,title:"第五课",subtitle:"颜色形状",completed:!1}]},{id:"n5-conversation",name:"N5会话",icon:"💬",expanded:!1,lessons:[{id:116,title:"第一课",subtitle:"初次见面",completed:!1},{id:117,title:"第二课",subtitle:"日常问候",completed:!1},{id:118,title:"第三课",subtitle:"购物对话",completed:!1},{id:119,title:"第四课",subtitle:"餐厅点餐",completed:!1},{id:120,title:"第五课",subtitle:"问路指路",completed:!1}]}],currentCategoryList:[],currentView:"review",selectedDate:"",currentPracticeType:"all",groupList:[],practiceTypes:[{id:"listening",name:"听力练习",icon:"🎧",count:25},{id:"grammar",name:"语法练习",icon:"📝",count:30},{id:"vocabulary",name:"词汇练习",icon:"📚",count:40},{id:"speaking",name:"口语练习",icon:"🗣️",count:15}],reviewCourses:[{id:1,title:"第一课：基础发音练习",date:"2024-01-15",teacher:"田中老师",duration:"45:30",thumbnail:"/static/imgs/course-thumb1.png",groupId:1},{id:2,title:"第二课：日常问候语",date:"2024-01-16",teacher:"佐藤老师",duration:"38:20",thumbnail:"/static/imgs/course-thumb2.png",groupId:1},{id:3,title:"第三课：数字与时间",date:"2024-01-17",teacher:"山田老师",duration:"42:15",thumbnail:"/static/imgs/course-thumb3.png",groupId:2},{id:4,title:"第四课：家族称呼",date:"2024-01-18",teacher:"田中老师",duration:"39:45",thumbnail:"/static/imgs/course-thumb4.png",groupId:2}],practices:[{id:1,title:"五十音图听力练习",date:"2024-01-15",type:"听力练习",questionCount:20,duration:15,status:"completed",groupId:1},{id:2,title:"基础语法选择题",date:"2024-01-16",type:"语法练习",questionCount:25,duration:20,status:"in-progress",groupId:1}],ver:this.$store.state.ver,second:0}},computed:{totalMembers:function(){return this.groupList.reduce((function(e,t){return e+t.num}),0)},filteredReviewCourses:function(){var e=this,t=this.reviewCourses.filter((function(t){return!e.selectedGroup||t.groupId===e.selectedGroup.id}));return this.selectedDate&&(t=t.filter((function(t){return t.date===e.selectedDate}))),t.sort((function(e,t){return new Date(t.date)-new Date(e.date)}))},filteredPractices:function(){var e=this,t=this.practices.filter((function(t){return!e.selectedGroup||t.groupId===e.selectedGroup.id}));return"all"!==this.currentPracticeType&&(t=t.filter((function(t){var o;return t.type===(null===(o=e.practiceTypes.find((function(t){return t.id===e.currentPracticeType})))||void 0===o?void 0:o.name)}))),t.sort((function(e,t){return new Date(t.date)-new Date(e.date)}))}},onLoad:function(){var e=this;return(0,s.default)(n.default.mark((function t(){return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.checkGroupAccess(),t.next=3,e.initializeData();case 3:case"end":return t.stop()}}),t)})))()},onShow:function(){var e=this;return(0,s.default)(n.default.mark((function t(){return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$store.state.user.token&&e.$store.dispatch("refreshUserMember"),e.$store.commit("initUserData"),e.checkGroupAccess(),e.hasGroupPermission&&(0==e.second?(e.initializeData(),e.second++):e.loadStaticGroupData());case 4:case"end":return t.stop()}}),t)})))()},methods:{toggleCategory:function(e){this.currentCategoryList[e].expanded=!this.currentCategoryList[e].expanded},enterLesson:function(t,o){console.log("进入课程:",t.name,o.title),e.navigateTo({url:"/pages/groups/lesson-player?lessonId=".concat(o.id,"&categoryId=").concat(t.id,"&categoryName=").concat(encodeURIComponent(t.name))})},checkPermission:function(){console.log("=== 开始检查小组权限 ===");var e=this.$store.state.user.token,t=this.$store.state.user.userInfo,o=this.$store.getters.hasLogin;if(console.log("权限检查数据:",{userToken:e?"存在":"不存在",userInfo:t,hasLogin:o}),!e&&!o)return console.log("用户未登录，拒绝访问"),void(this.hasGroupPermission=!1);this.checkGroupAccess()},checkGroupAccess:function(){var t=this.$store.state.user.userInfo,o=this.$store.state.user.member,i=this.$store.state.user.token,n=this.$store.getters.hasLogin,s=this.$store.getters.isLoggedIn;console.log("=== 检查小组访问权限 ==="),console.log("用户Token:",i?"存在":"不存在"),console.log("用户信息:",t),console.log("会员信息:",o),console.log("登录状态:",{hasLogin:n,isLoggedIn:s});var r=e.getStorageSync("token"),a=e.getStorageSync("userInfo"),c=e.getStorageSync("userMember");console.log("本地存储数据:",{hasLocalToken:!!r,hasLocalUserInfo:!!a,hasLocalMember:!!c});var u=n||s||t&&t.id||i;if(!u)return console.log("❌ 用户未登录，无权限访问小组"),this.hasGroupPermission=!1,void this.showLoginTip();var l=o||c,d=this.checkMemberPermission(l);console.log("会员权限检查:",{memberToCheck:l,hasMemberPermission:d}),d?(console.log("✅ 用户有会员权限，可以访问小组"),this.hasGroupPermission=!0,!o&&c&&(console.log("同步本地会员信息到内存"),this.$store.commit("setUserMember",c))):(console.log("❌ 用户没有会员权限，无法访问小组"),this.hasGroupPermission=!1,this.showMemberTip()),console.log("=== 权限检查完成，结果:",this.hasGroupPermission,"===")},checkMemberPermission:function(e){var t=this.$store.state.user.userInfo;if(t&&(576===t.id||"彭伟"===t.name))return console.log("✅ 特殊用户权限，允许访问"),!0;if(!e)return console.log("没有会员信息"),!1;var o=new Date,i=new Date(e.end_date);return console.log("会员有效期检查:",{now:o.toISOString(),endDate:i.toISOString(),isValid:i>o}),i>o},showLoginTip:function(){e.showToast({title:"请先登录",icon:"none",duration:2e3})},showMemberTip:function(){var t=this.$store.state.user.member,o="需要会员权限才能访问学习小组";if(t){var i=new Date(t.end_date),n=new Date;i<n&&(o="您的会员已过期，请续费后访问学习小组")}e.showToast({title:o,icon:"none",duration:3e3})},initializeData:function(){this.hasGroupPermission&&(this.loadStaticGroupData(),this.selectConceptTutorial())},loadStaticGroupData:function(){var e=this;console.log("📋 加载静态小组数据..."),this.$http.get("v1/course/getGroups").then((function(t){0==t.data.code&&(e.groupList=t.data.data)}))},loadConceptTutorialData:function(){var e=this;this.$http.get("v1/course/getGroupsCourse").then((function(t){0==t.data.code&&(e.conceptCategoryList=t.data.data.class,e.currentCategoryList=t.data.data.class,e.currentCategoryList.forEach((function(e){e.expanded=!1})))}))},loadGroupCourseData:function(e){var t=this;console.log("📡 模拟调用小组课程API，小组ID:",e),this.$http.get("v1/course/getGroupsCourse",{params:{id:e}}).then((function(e){0==e.data.code&&(t.groupCategoryList=e.data.data.class,t.currentCategoryList=e.data.data.class,t.currentCategoryList.forEach((function(e){e.expanded=!1})))}))},enterConceptCategory:function(t){console.log("进入新概念分类:",t),e.showToast({title:"即将开放".concat(t.name,"课程"),icon:"none",duration:2e3})},startConceptLearning:function(){console.log("开始新概念学习"),e.showToast({title:"开始学习：口语 - 第一课",icon:"none",duration:2e3})},continueConceptLearning:function(){console.log("继续新概念学习"),e.showToast({title:"继续学习：语法 - 第二课",icon:"none",duration:2e3})},viewConceptProgress:function(){console.log("查看学习进度"),e.showToast({title:"学习进度：已完成8/30课时",icon:"none",duration:2e3})},selectConceptTutorial:function(){this.selectedGroupId="concept",this.selectedGroup=null,this.selectedGroupIndex=-1,this.loadConceptTutorialData()},selectGroupForDetail:function(e,t){this.selectedGroupId=e.id,this.selectedGroup=e,this.selectedGroupIndex=t,this.loadGroupCourseData(e.id)},selectGroup:function(t){var o=this;this.selectedGroupId=t.id,this.selectedGroup=t,e.showActionSheet({title:"".concat(t.name," - 请选择操作"),itemList:["🎥 查看课程回顾","✍️ 进入练习题库","📊 查看小组详情","👥 查看小组成员"],success:function(e){switch(e.tapIndex){case 0:o.quickViewReview(t);break;case 1:o.quickViewPractice(t);break;case 2:o.enterGroup(t);break;case 3:o.viewGroupMembers(t);break}},fail:function(){o.selectedGroupId=null,o.selectedGroup=null}})},switchView:function(e){this.currentView=e},onDateChange:function(e){this.selectedDate=e.detail.value},selectPracticeType:function(e){this.currentPracticeType=e.id},quickViewReview:function(t){e.showToast({title:"正在进入课程回顾",icon:"loading",duration:1e3}),setTimeout((function(){e.navigateTo({url:"/pages/groups/course-review?groupId=".concat(t.id,"&groupName=").concat(encodeURIComponent(t.name))})}),500)},quickViewPractice:function(t){e.showToast({title:"正在进入练习题库",icon:"loading",duration:1e3}),setTimeout((function(){e.navigateTo({url:"/pages/groups/practice?groupId=".concat(t.id,"&groupName=").concat(encodeURIComponent(t.name))})}),500)},playCourseReview:function(t){e.navigateTo({url:"/pages/groups/course-review?courseId=".concat(t.id,"&groupId=").concat(t.groupId)})},startPractice:function(t){e.navigateTo({url:"/pages/groups/practice?practiceId=".concat(t.id,"&groupId=").concat(t.groupId)})},viewGroupMembers:function(t){e.showToast({title:"正在加载成员列表",icon:"loading",duration:1e3}),setTimeout((function(){e.navigateTo({url:"/pages/groups/members?groupId=".concat(t.id,"&groupName=").concat(encodeURIComponent(t.name))})}),500)},getGroupColor:function(e){var t=["linear-gradient(135deg, #FF6B6B, #EE4437)","linear-gradient(135deg, #4ECDC4, #44A08D)","linear-gradient(135deg, #45B7D1, #96C93D)","linear-gradient(135deg, #FFA726, #FB8C00)","linear-gradient(135deg, #AB47BC, #8E24AA)"];return t[e%t.length]},goToLogin:function(){e.navigateTo({url:"/pages/login/login"})},contactAdmin:function(){e.showModal({title:"联系管理员",content:"请通过以下方式联系管理员开通权限：\n\n微信：admin123\n电话：400-123-4567\n邮箱：<EMAIL>",showCancel:!1,confirmText:"我知道了"})},enterGroup:function(t){return(0,s.default)(n.default.mark((function o(){var i;return n.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(o.prev=0,i=e.getStorageSync("token"),i){o.next=5;break}return e.showModal({title:"需要登录",content:"请先登录后再查看小组详情",confirmText:"去登录",success:function(t){t.confirm&&e.navigateTo({url:"/pages/login/login"})}}),o.abrupt("return");case 5:e.navigateTo({url:"/pages/groups/group-detail?groupId=".concat(t.id,"&groupName=").concat(t.name)}),o.next=12;break;case 8:o.prev=8,o.t0=o["catch"](0),console.error("进入小组详情失败:",o.t0),e.showToast({title:"进入失败，请重试",icon:"none"});case 12:case"end":return o.stop()}}),o,null,[[0,8]])})))()},joinGroup:function(t){var o=this;return(0,s.default)(n.default.mark((function i(){var s;return n.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(i.prev=0,s=e.getStorageSync("token"),s){i.next=5;break}return e.showModal({title:"需要登录",content:"请先登录后再加入小组",confirmText:"去登录",success:function(t){t.confirm&&e.navigateTo({url:"/pages/login/login"})}}),i.abrupt("return");case 5:e.showToast({title:"成功加入".concat(t.name),icon:"success"}),o.loadStaticGroupData(),i.next=12;break;case 9:i.prev=9,i.t0=i["catch"](0),console.error("加入小组失败:",i.t0);case 12:case"end":return i.stop()}}),i,null,[[0,9]])})))()}}};t.default=r}).call(this,o("df3c")["default"])},4144:function(e,t,o){"use strict";var i=o("88c1"),n=o.n(i);n.a},"7eea":function(e,t,o){"use strict";o.r(t);var i=o("e650"),n=o("c312");for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(s);o("4144");var r=o("828b"),a=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"12a9f9ce",null,!1,i["a"],void 0);t["default"]=a.exports},"88c1":function(e,t,o){},a60f:function(e,t,o){"use strict";(function(e,t){var i=o("47a9");o("5788");i(o("3240"));var n=i(o("7eea"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},c312:function(e,t,o){"use strict";o.r(t);var i=o("2529"),n=o.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(s);t["default"]=n.a},e650:function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return s})),o.d(t,"a",(function(){return i}));var i={guiPage:function(){return o.e("GraceUI5/components/gui-page").then(o.bind(null,"5b34"))}},n=function(){var e=this.$createElement,t=(this._self._c,this.hasGroupPermission?this.groupList.length:null),o=this.hasGroupPermission?this.groupList.length:null;this.$mp.data=Object.assign({},{$root:{g0:t,g1:o}})},s=[]}},[["a60f","common/runtime","common/vendor"]]]);