const { sequelize } = require('../src/models');

async function fixImportData() {
  try {
    console.log('🔄 开始修复导入数据问题...');

    // 1. 检查原始数据表是否存在
    console.log('📋 检查原始数据表...');
    
    const [tables] = await sequelize.query(`
      SELECT name FROM sqlite_master WHERE type='table' 
      AND name IN ('courses_classify', 'courses_item', 'courses_original')
    `);
    
    const existingTables = tables.map(t => t.name);
    console.log('存在的原始表:', existingTables);

    // 2. 检查当前courses表中是否有classify_id字段
    let hasClassifyId = false;
    try {
      const [result] = await sequelize.query('SELECT classify_id FROM courses LIMIT 1');
      hasClassifyId = true;
      console.log('✅ courses表中存在classify_id字段');
    } catch (error) {
      console.log('❌ courses表中不存在classify_id字段');
    }

    // 3. 如果有classify_id字段，修复分类关联
    if (hasClassifyId) {
      console.log('\n📋 修复课程分类关联...');
      
      // 获取所有有classify_id的课程
      const [coursesWithClassifyId] = await sequelize.query(`
        SELECT id, title, classify_id, category_id 
        FROM courses 
        WHERE classify_id IS NOT NULL AND classify_id != ''
        LIMIT 20
      `);

      console.log(`找到 ${coursesWithClassifyId.length} 个有classify_id的课程（示例）`);

      for (const course of coursesWithClassifyId) {
        // 解析classify_id（可能是逗号分隔的多个ID）
        const classifyIds = course.classify_id.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
        
        if (classifyIds.length > 0) {
          const primaryClassifyId = classifyIds[0];
          
          // 检查这个分类ID是否在categories表中存在
          const [categoryExists] = await sequelize.query(`
            SELECT id, name FROM categories WHERE id = ?
          `, { replacements: [primaryClassifyId] });

          if (categoryExists.length > 0) {
            // 分类存在，更新课程的category_id
            await sequelize.query(`
              UPDATE courses SET category_id = ? WHERE id = ?
            `, { replacements: [primaryClassifyId, course.id] });

            console.log(`✅ 课程 "${course.title}" 关联到分类 "${categoryExists[0].name}" (ID: ${primaryClassifyId})`);
          } else {
            console.log(`⚠️  课程 "${course.title}" 的分类ID ${primaryClassifyId} 不存在`);
          }

          // 如果有多个分类，记录日志
          if (classifyIds.length > 1) {
            console.log(`   注意：课程 "${course.title}" 原本属于多个分类: ${classifyIds.join(', ')}`);
          }
        }
      }
    }

    // 4. 检查课程单元关联
    console.log('\n📋 检查课程单元关联...');
    
    // 检查是否有courses_item表
    if (existingTables.includes('courses_item')) {
      const [courseItems] = await sequelize.query(`
        SELECT id, title, wk_id, status 
        FROM courses_item 
        WHERE status = 1
        LIMIT 10
      `);

      console.log(`courses_item表中有 ${courseItems.length} 个课程单元（示例）`);

      // 检查这些课程单元是否已经导入到course_units表
      for (const item of courseItems) {
        const [existingUnit] = await sequelize.query(`
          SELECT id FROM course_units WHERE title = ? AND course_id = ?
        `, { replacements: [item.title, item.wk_id] });

        if (existingUnit.length === 0) {
          console.log(`⚠️  课程单元 "${item.title}" (课程ID: ${item.wk_id}) 未导入到course_units表`);
        } else {
          console.log(`✅ 课程单元 "${item.title}" 已存在于course_units表`);
        }
      }
    }

    // 5. 统计当前数据状态
    console.log('\n📊 当前数据统计:');
    
    const [courseStats] = await sequelize.query(`
      SELECT 
        COUNT(*) as total_courses,
        COUNT(category_id) as courses_with_category,
        COUNT(*) - COUNT(category_id) as courses_without_category
      FROM courses
    `);

    const [categoryStats] = await sequelize.query(`
      SELECT COUNT(*) as total_categories FROM categories
    `);

    const [unitStats] = await sequelize.query(`
      SELECT COUNT(*) as total_units FROM course_units
    `);

    console.log(`  - 总课程数: ${courseStats[0].total_courses}`);
    console.log(`  - 有分类的课程: ${courseStats[0].courses_with_category}`);
    console.log(`  - 无分类的课程: ${courseStats[0].courses_without_category}`);
    console.log(`  - 总分类数: ${categoryStats[0].total_categories}`);
    console.log(`  - 总课程单元数: ${unitStats[0].total_units}`);

    // 6. 显示分类分布
    console.log('\n📋 课程分类分布:');
    const [categoryDistribution] = await sequelize.query(`
      SELECT 
        c.id,
        c.name,
        COUNT(co.id) as course_count
      FROM categories c
      LEFT JOIN courses co ON c.id = co.category_id
      GROUP BY c.id, c.name
      ORDER BY course_count DESC
      LIMIT 10
    `);

    categoryDistribution.forEach(cat => {
      console.log(`  - ${cat.name} (ID: ${cat.id}): ${cat.course_count} 个课程`);
    });

    // 7. 检查孤立的课程单元
    console.log('\n📋 检查孤立的课程单元:');
    const [orphanUnits] = await sequelize.query(`
      SELECT cu.id, cu.title, cu.course_id
      FROM course_units cu
      LEFT JOIN courses c ON cu.course_id = c.id
      WHERE c.id IS NULL
      LIMIT 5
    `);

    if (orphanUnits.length > 0) {
      console.log(`⚠️  发现 ${orphanUnits.length} 个孤立的课程单元（课程不存在）:`);
      orphanUnits.forEach(unit => {
        console.log(`    - "${unit.title}" (课程ID: ${unit.course_id})`);
      });
    } else {
      console.log('✅ 没有发现孤立的课程单元');
    }

    // 8. 提供修复建议
    console.log('\n💡 修复建议:');
    
    if (courseStats[0].courses_without_category > 0) {
      console.log(`  1. 有 ${courseStats[0].courses_without_category} 个课程没有分类，建议：`);
      console.log('     - 检查原始数据的classify_id字段');
      console.log('     - 或者手动分配默认分类');
    }

    if (orphanUnits.length > 0) {
      console.log(`  2. 有 ${orphanUnits.length} 个课程单元的课程不存在，建议：`);
      console.log('     - 检查courses_item表中的wk_id字段');
      console.log('     - 确保对应的课程已经导入');
    }

    console.log('🎉 数据检查完成！');

  } catch (error) {
    console.error('❌ 数据修复失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixImportData().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = fixImportData;
