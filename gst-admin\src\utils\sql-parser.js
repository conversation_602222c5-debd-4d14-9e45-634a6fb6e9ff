/**
 * SQL文件解析器
 */

/**
 * 解析SQL文件内容
 * @param {string} sqlContent SQL文件内容
 * @returns {Object} 解析结果
 */
export function parseSQLFile(sqlContent) {
  const result = {
    tables: [],
    errors: [],
    warnings: []
  }

  try {
    // 清理SQL内容，移除注释和多余空白
    const cleanedSQL = cleanSQLContent(sqlContent)
    
    // 提取表结构
    const tableStructures = extractTableStructures(cleanedSQL)
    
    // 提取插入数据
    const insertStatements = extractInsertStatements(cleanedSQL)
    
    // 合并表结构和数据
    for (const tableName in tableStructures) {
      const table = {
        name: tableName,
        structure: tableStructures[tableName],
        data: insertStatements[tableName] || [],
        recordCount: (insertStatements[tableName] || []).length
      }
      result.tables.push(table)
    }
    
  } catch (error) {
    result.errors.push(`SQL解析失败: ${error.message}`)
  }

  return result
}

/**
 * 清理SQL内容
 * @param {string} sql SQL内容
 * @returns {string} 清理后的SQL
 */
function cleanSQLContent(sql) {
  // 移除多行注释
  sql = sql.replace(/\/\*[\s\S]*?\*\//g, '')
  
  // 移除单行注释
  sql = sql.replace(/--.*$/gm, '')
  
  // 移除多余的空白行
  sql = sql.replace(/\n\s*\n/g, '\n')
  
  return sql.trim()
}

/**
 * 提取表结构
 * @param {string} sql 清理后的SQL
 * @returns {Object} 表结构映射
 */
function extractTableStructures(sql) {
  const tables = {}
  
  // 匹配CREATE TABLE语句
  const createTableRegex = /CREATE TABLE\s+(?:IF NOT EXISTS\s+)?`?(\w+)`?\s*\(([\s\S]*?)\)\s*ENGINE/gi
  
  let match
  while ((match = createTableRegex.exec(sql)) !== null) {
    const tableName = match[1]
    const tableDefinition = match[2]
    
    tables[tableName] = parseTableDefinition(tableDefinition)
  }
  
  return tables
}

/**
 * 解析表定义
 * @param {string} definition 表定义字符串
 * @returns {Array} 字段定义数组
 */
function parseTableDefinition(definition) {
  const fields = []
  
  // 分割字段定义
  const lines = definition.split(',').map(line => line.trim())
  
  for (const line of lines) {
    // 跳过PRIMARY KEY、INDEX等约束定义
    if (line.toUpperCase().includes('PRIMARY KEY') || 
        line.toUpperCase().includes('INDEX') ||
        line.toUpperCase().includes('KEY ') ||
        line.toUpperCase().includes('CONSTRAINT')) {
      continue
    }
    
    // 解析字段定义
    const fieldMatch = line.match(/`?(\w+)`?\s+(\w+(?:\([^)]+\))?)\s*(.*)/i)
    if (fieldMatch) {
      const fieldName = fieldMatch[1]
      const fieldType = fieldMatch[2]
      const fieldOptions = fieldMatch[3] || ''
      
      fields.push({
        name: fieldName,
        type: fieldType,
        nullable: !fieldOptions.toUpperCase().includes('NOT NULL'),
        autoIncrement: fieldOptions.toUpperCase().includes('AUTO_INCREMENT'),
        defaultValue: extractDefaultValue(fieldOptions),
        comment: extractComment(fieldOptions)
      })
    }
  }
  
  return fields
}

/**
 * 提取默认值
 * @param {string} options 字段选项
 * @returns {string|null} 默认值
 */
function extractDefaultValue(options) {
  const defaultMatch = options.match(/DEFAULT\s+([^,\s]+)/i)
  if (defaultMatch) {
    let value = defaultMatch[1]
    // 移除引号
    if ((value.startsWith("'") && value.endsWith("'")) ||
        (value.startsWith('"') && value.endsWith('"'))) {
      value = value.slice(1, -1)
    }
    return value
  }
  return null
}

/**
 * 提取注释
 * @param {string} options 字段选项
 * @returns {string|null} 注释
 */
function extractComment(options) {
  const commentMatch = options.match(/COMMENT\s+['"](.*?)['"]/i)
  return commentMatch ? commentMatch[1] : null
}

/**
 * 提取INSERT语句
 * @param {string} sql 清理后的SQL
 * @returns {Object} 插入数据映射
 */
function extractInsertStatements(sql) {
  const insertData = {}
  
  // 匹配INSERT INTO语句
  const insertRegex = /INSERT INTO\s+`?(\w+)`?\s+VALUES\s+([\s\S]*?);/gi
  
  let match
  while ((match = insertRegex.exec(sql)) !== null) {
    const tableName = match[1]
    const valuesString = match[2]
    
    if (!insertData[tableName]) {
      insertData[tableName] = []
    }
    
    // 解析VALUES
    const values = parseInsertValues(valuesString)
    insertData[tableName].push(...values)
  }
  
  return insertData
}

/**
 * 解析INSERT VALUES
 * @param {string} valuesString VALUES字符串
 * @returns {Array} 数据行数组
 */
function parseInsertValues(valuesString) {
  const rows = []
  
  // 匹配每个值组 (value1, value2, ...)
  const valueGroupRegex = /\((.*?)\)/g
  
  let match
  while ((match = valueGroupRegex.exec(valuesString)) !== null) {
    const valueString = match[1]
    const values = parseValueString(valueString)
    rows.push(values)
  }
  
  return rows
}

/**
 * 解析值字符串
 * @param {string} valueString 值字符串
 * @returns {Array} 值数组
 */
function parseValueString(valueString) {
  const values = []
  let current = ''
  let inQuotes = false
  let quoteChar = ''
  let i = 0
  
  while (i < valueString.length) {
    const char = valueString[i]
    
    if (!inQuotes && (char === "'" || char === '"')) {
      inQuotes = true
      quoteChar = char
      current += char
    } else if (inQuotes && char === quoteChar) {
      // 检查是否是转义的引号
      if (i + 1 < valueString.length && valueString[i + 1] === quoteChar) {
        current += char + char
        i++ // 跳过下一个字符
      } else {
        inQuotes = false
        current += char
      }
    } else if (!inQuotes && char === ',') {
      values.push(processValue(current.trim()))
      current = ''
    } else {
      current += char
    }
    
    i++
  }
  
  // 添加最后一个值
  if (current.trim()) {
    values.push(processValue(current.trim()))
  }
  
  return values
}

/**
 * 处理单个值
 * @param {string} value 原始值
 * @returns {any} 处理后的值
 */
function processValue(value) {
  // NULL值
  if (value.toUpperCase() === 'NULL') {
    return null
  }
  
  // 字符串值（移除引号）
  if ((value.startsWith("'") && value.endsWith("'")) ||
      (value.startsWith('"') && value.endsWith('"'))) {
    return value.slice(1, -1).replace(/''/g, "'").replace(/""/g, '"')
  }
  
  // 数字值
  if (/^\d+$/.test(value)) {
    return parseInt(value, 10)
  }
  
  if (/^\d+\.\d+$/.test(value)) {
    return parseFloat(value)
  }
  
  // 其他值保持原样
  return value
}

/**
 * 将SQL数据转换为CSV格式
 * @param {Object} table 表数据
 * @returns {string} CSV内容
 */
export function convertTableToCSV(table) {
  if (!table.structure || !table.data) {
    throw new Error('表数据不完整')
  }
  
  const lines = []
  
  // 添加表头
  const headers = table.structure.map(field => field.name)
  lines.push(headers.join(','))
  
  // 添加数据行
  for (const row of table.data) {
    const csvRow = row.map(cell => {
      if (cell === null) return ''
      
      // 如果包含逗号、引号或换行符，需要用引号包围
      const cellStr = String(cell)
      if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
        return `"${cellStr.replace(/"/g, '""')}"`
      }
      return cellStr
    })
    lines.push(csvRow.join(','))
  }
  
  return lines.join('\n')
}

/**
 * 映射表名到系统类型
 * @param {string} tableName 表名
 * @returns {string} 系统类型
 */
export function mapTableNameToType(tableName) {
  const mapping = {
    'courses_classify': 'categories',
    'courses': 'courses',
    'courses_item': 'courses', // 课程项目表映射到课程
    'course_units': 'course_units',
    'course_chapters': 'course_units', // 如果有章节表也映射到单元
    'course_items': 'course_units' // 课程项目也可以映射到单元
  }

  return mapping[tableName] || tableName
}

export default {
  parseSQLFile,
  convertTableToCSV,
  mapTableNameToType
}
