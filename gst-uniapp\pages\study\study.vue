<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#ffffff;">
			<!-- <view class="first">
				<view>{{num}}</view>
				<view style="color: rgb(224, 198, 173);">人正在学习</view>
			</view> -->
			<view>
				<view class="h2title">我的课程</view>
				<view v-if=" goodsList.length===0" class="empty">
					<image src="/static/null.png" mode="aspectFit"></image>
					<view class="empty-tips">
						您还没有加入课程
						<!-- <navigator class="navigator" url="../index/index" open-type="switchTab">随便逛逛></navigator> -->
					</view>
					<view class="empty-tips">
						从推荐课程中看看有没有喜欢的吧~
						<!-- <navigator class="navigator" url="../index/index" open-type="switchTab">随便逛逛></navigator> -->
					</view>
				</view>
				<view class="content" v-else>
					<view class="list-box">
						<view class="item-box lp-flex-column" v-for="(item, index) in goodsList" :key="index"
							@click="navToDetailPage(item)">
							<view class="top-box lp-flex">
								<view class="cover-box lp-flex-center" >
									<image class="cover" :src="item.picture"></image>
								</view>
								<view class="info-box lp-flex-column">
									<view class="title">{{item.title}}</view>
									<view class="des">{{item.des}}</view>
								</view>
							</view>
						</view>
					</view>
					<!-- <uni-load-more :status="loadingType"></uni-load-more> -->
				</view>
			</view>
			<view style="margin-top: 20rpx;">
				<view class="h2title">课程推荐</view>
				<view class="content" >
					<view class="list-box">
						<view class="item-box lp-flex-column" v-for="(item, index) in hotList" :key="index"
							@click="navToDetailPage2(item)">
							<view class="top-box lp-flex">
								<view class="cover-box lp-flex-center" >
									<image class="cover" :src="item.picture"></image>

								</view>
						
								<view class="info-box lp-flex-column">
									<view class="title">{{item.title}}</view>
									<!-- <view class="des">{{item.des}}</view> -->
								<!-- 	<view class="end"><text
											style="text-align: right;float: right;">点击报名</text></view> -->
								</view>
							</view>
						</view>
					</view>
					<!-- <uni-load-more :status="loadingType"></uni-load-more> -->
				</view>
			</view>
		</view>

		<!-- 自定义底部导航 -->
		<custom-tabbar />
	</gui-page>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import CustomTabbar from '@/components/custom-tabbar.vue';
	export default {
		components: {
			uniLoadMore,
			CustomTabbar
		},
		data() {
			return {
				cateMaskState: 0, //分类面板展开状态
				headerPosition: "fixed",
				headerTop: "0px",
				loadingType: 'more', //加载更多状态
				filterIndex: 0,
				cateId: 0, //已选三级分类id
				priceOrder: 0, //1 价格从低到高 2价格从高到低
				cateList: [],
				goodsList: [],
				title: '',
				loading: false,
				hotList:[],
				num:0
			};
		},

		onLoad(options) {
			// #ifdef H5
			this.headerTop = document.getElementsByTagName('uni-page-head')[0].offsetHeight + 'px';
			// #endif
			this.cateId = options.id;
			this.title = options.title;
			console.log(this.title);
			// 动态设置标题
			uni.setNavigationBarTitle({
				title: this.title
			});
			//this.loadCateList(options.fid, options.sid);
			
			this.loadHotData();
			

		},
		onPageScroll(e) {
			//兼容iOS端下拉时顶部漂移
			if (e.scrollTop >= 0) {
				this.headerPosition = "fixed";
			} else {
				this.headerPosition = "absolute";
			}
		},
		//下拉刷新
		onPullDownRefresh() {
			//this.loadData('refresh');
		},
		//加载更多
		onReachBottom() {
			//this.loadData();
		},
		onShow() {
			this.user = this.$store.state.user;

			if (!this.$store.state.user.token) {
				uni.showModal({
					title: '请先登录',
					content: '登录后才能进行操作',
					success: function(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: "/pages/login/login?type=wx"
							})
						} else if (res.cancel) {
							/* uni.switchtab({
								url: "/pages/index/index"
							}) */
						}
					}
				});
			} else {
				this.$store.dispatch('refreshUserMember');
				this.loadData();
			}
		},
		methods: {
			
			//加载商品 ，带下拉刷新和上滑加载
			async loadData(type = 'add', loading) {
				this.apiGetCourseList().then(pagination => {
					this.goodsList = pagination.data;
					
				})
			},
			//加载商品 ，带下拉刷新和上滑加载
			async loadHotData(type = 'add', loading) {
				this.$http.get("/v1/course/getCommend", {
					params: {
				
					}
				}).then(res => {
					console.log(res)
					if (res.data.code == 0) {
						this.hotList = res.data.data
					}
				});
			},
			apiGetCourseList: function() {
				return this.$http.get('/v1/course/userOrder', {
					params: {
					
					}
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			},
			//详情页
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.good_id;
				uni.navigateTo({
					url: `/pages/course/course?id=${id}`
				});
			},
			navToDetailPage2(item) {
				//测试数据没有写id，用title代替
				let id = item.id;
				uni.navigateTo({
					url: `/pages/course/course?id=${id}`
				});
			},
		},
	}
</script>

<style lang="scss">
	page,
	.content {
		background-color: #fff;
	}

	.content {
		// padding-top: 20upx;
		padding: 30rpx;
	}

	.navbar {
		position: fixed;
		left: 0;
		top: var(--window-top);
		display: flex;
		width: 100%;
		height: 80upx;
		background: #fff;
		box-shadow: 0 2upx 10upx rgba(0, 0, 0, .06);
		z-index: 10;

		.nav-item {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			font-size: 30upx;
			color: $font-color-dark;
			position: relative;

			&.current {
				color: $base-color;

				&:after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translateX(-50%);
					width: 120upx;
					height: 0;
					border-bottom: 4upx solid $base-color;
				}
			}
		}

		.p-box {
			display: flex;
			flex-direction: column;

			.yticon {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30upx;
				height: 14upx;
				line-height: 1;
				margin-left: 4upx;
				font-size: 26upx;
				color: #888;

				&.active {
					color: $base-color;
				}
			}

			.xia {
				transform: scaleY(-1);
			}
		}

		.cate-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			width: 80upx;
			position: relative;
			font-size: 44upx;

			&:after {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				border-left: 1px solid #ddd;
				width: 0;
				height: 36upx;
			}
		}
	}

	/* 分类 */
	.cate-mask {
		position: fixed;
		left: 0;
		top: var(--window-top);
		bottom: 0;
		width: 100%;
		background: rgba(0, 0, 0, 0);
		z-index: 95;
		transition: .3s;

		.cate-content {
			width: 630upx;
			height: 100%;
			background: #fff;
			float: right;
			transform: translateX(100%);
			transition: .3s;
		}

		&.none {
			display: none;
		}

		&.show {
			background: rgba(0, 0, 0, .4);

			.cate-content {
				transform: translateX(0);
			}
		}
	}

	.cate-list {
		display: flex;
		flex-direction: column;
		height: 100%;

		.cate-item {
			display: flex;
			align-items: center;
			height: 90upx;
			padding-left: 30upx;
			font-size: 28upx;
			color: #555;
			position: relative;
		}

		.two {
			height: 64upx;
			color: #303133;
			font-size: 30upx;
			background: #f8f8f8;
		}

		.active {
			color: $base-color;
		}
	}

	/* 商品列表 */
	.goods-list {
		display: flex;
		flex-wrap: wrap;
		padding: 0 30upx;
		background: #fff;

		.goods-item {
			display: flex;
			flex-direction: column;
			width: 48%;
			padding-bottom: 40upx;

			&:nth-child(2n+1) {
				margin-right: 4%;
			}
		}

		.image-wrapper {
			width: 100%;
			height: 180upx;
			border-radius: 3px;
			overflow: hidden;

			image {
				width: 100%;
				height: 100%;
				opacity: 1;
			}
		}

		.title {
			font-size: $font-lg;
			color: $font-color-dark;
			font-size: 28upx;
			line-height: 45upx;
		}

		.price-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-right: 10upx;
			font-size: 22upx;
			color: $font-color-light;
		}

		.price {
			font-size: $font-lg;
			color: $uni-color-primary;
			line-height: 1;

			&:before {
				content: '￥';
				font-size: 26upx;
			}
		}
	}

	.list-box {
		// padding-bottom: 20rpx;
	
		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding: 10rpx 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
			border-bottom: 1rpx solid #ebebeb;
	
	
			.top-box {
				position: relative;
	
				.cover-box-hot {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
	
					.cover :after {
						background-color: red;
						border-radius: 10rpx;
						color: #fff;
						content: "hot";
						font-size: 25rpx;
						line-height: 1;
						padding: 2rpx 6rpx;
						position: absolute;
						left: 5rpx;
						top: 5rpx;
					}
	
					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
	
				.cover-box {
					width: 25%;
					height: auto;
					min-height: 120rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
						min-height: 150rpx;
					}
	
					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
	
				.cover-large-box {
					width: 100%;
					height: auto;
					height: 200rpx;
					position: relative;
				
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
				
					.button {
						position: absolute;
						bottom: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: rgba(0, 0, 0, .5) !important;
						color: white;
						padding: 15rpx 20rpx;
						font-size: 20rpx;
					}
				}
	
				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;
	
					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}
	
					.lang-box {
						color: $uni-text-color-grey;
						font-size: 24rpx;
					}
	
					.title {
						font-weight: bold;
						font-size: 26rpx;
						color: #666666;
					}
	
					.end-date {
						font-size: 20rpx;
						color: #999999;
					}
	
					.total {
						font-size: 20rpx;
						color: #39b54a;
					}
	
					.des {
						font-size: 22rpx;
						color: #8f8f94;
	
	
					}
	
					.price {
						font-size: 24rpx;
						color: red;
						float: right;
					}
	
					.end {
						font-size: 24rpx;
						color: #0070C0;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}
	
			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;
	
				.text-sm {
					font-size: 24upx;
				}
	
				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}
	
				.uni-row {
					flex-direction: row;
				}
	
				.align-center {
					align-items: center;
				}
	
				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}
	
		:last-child {
			// border-bottom: 1rpx solid #fff;
		}
	}
	/* 空白页 */
	.empty {
		// position: fixed;
		// left: 0;
		// top: 0;
		width: 100%;
		// height: 100vh;
		padding-bottom: 100rpx;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;
		background: #fff;

		image {
			width: 300rpx;
			height: 300rpx;
			margin-bottom: 20rpx;
		}

		.empty-tips {
			display: flex;
			font-size: $font-sm+2upx;
			color: $font-color-disabled;

			.navigator {
				color: $uni-color-primary;
				margin-left: 16upx;
			}
		}
	}

	.first {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		background-color: rgb(250, 250, 243);
		width: 700rpx;
		margin: 20rpx auto;
		height: 100rpx;
		color: #000;
		font-size: 40rpx;
		font-weight: bold;
	}

	.h2title {
		color: #000;
		display: block;
		font-size: 35rpx;
		height: 60rpx;
		line-height: 60rpx;
		margin-left: 40rpx;
		font-weight: bold;
	}

	/* 自定义tabBar适配 */
	.gui-page {
		padding-bottom: 120rpx; /* 为自定义tabBar留出空间 */
	}
</style>
