﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>数据库管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="optimizeDatabase" :loading="optimizing">
          <el-icon><Tools /></el-icon>
          优化数据库
        </el-button>
        <el-button @click="showSqlDialog = true" type="primary">
          <el-icon><EditPen /></el-icon>
          执行SQL
        </el-button>
      </div>
    </div>

    <!-- 数据库概览 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="数据库大小" :value="dbStats.size" suffix="MB" />
          <div class="stats-extra">
            <el-icon><Coin /></el-icon>
            <span>存储空间</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="数据表数量" :value="dbStats.tableCount" />
          <div class="stats-extra">
            <el-icon><Grid /></el-icon>
            <span>个数据表</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总记录数" :value="dbStats.recordCount" />
          <div class="stats-extra">
            <el-icon><Document /></el-icon>
            <span>条记录</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="连接数" :value="dbStats.connections" />
          <div class="stats-extra">
            <el-icon><Link /></el-icon>
            <span>个连接</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>数据表管理</span>
          <el-input
            v-model="tableSearch"
            placeholder="搜索数据表"
            style="width: 200px"
            clearable
            @input="filterTables"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </template>

      <el-table :data="filteredTables" v-loading="loading" stripe>
        <el-table-column prop="name" label="表名" min-width="150" />
        <el-table-column prop="comment" label="描述" min-width="200" />
        <el-table-column prop="engine" label="存储引擎" width="120" />
        <el-table-column prop="rows" label="记录数" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.rows) }}
          </template>
        </el-table-column>
        <el-table-column prop="dataSize" label="数据大小" width="100" align="right">
          <template #default="{ row }">
            {{ formatSize(row.dataSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="indexSize" label="索引大小" width="100" align="right">
          <template #default="{ row }">
            {{ formatSize(row.indexSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="viewTableStructure(row)">
              <el-icon><View /></el-icon>
              结构
            </el-button>
            <el-button link size="small" @click="viewTableData(row)">
              <el-icon><Document /></el-icon>
              数据
            </el-button>
            <el-button link size="small" @click="optimizeTable(row)">
              <el-icon><Tools /></el-icon>
              优化
            </el-button>
            <el-button link size="small" @click="truncateTable(row)" class="danger-button">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- SQL执行对话框 -->
    <el-dialog
      title="执行SQL语句"
      v-model="showSqlDialog"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="sql-editor">
        <el-input
          v-model="sqlQuery"
          type="textarea"
          :rows="8"
          placeholder="请输入SQL语句..."
          class="sql-textarea"
        />
        <div class="sql-actions">
          <el-button @click="clearSql">清空</el-button>
          <el-button @click="formatSql">格式化</el-button>
          <el-button type="primary" @click="executeSql" :loading="executing">
            <el-icon><CaretRight /></el-icon>
            执行
          </el-button>
        </div>
      </div>

      <!-- SQL执行结果 -->
      <div v-if="sqlResult" class="sql-result">
        <el-divider>执行结果</el-divider>
        <div v-if="sqlResult.success" class="result-success">
          <el-alert type="success" :title="sqlResult.message" show-icon />
          <div v-if="sqlResult.data && sqlResult.data.length > 0" class="result-table">
            <el-table :data="sqlResult.data" size="small" max-height="300">
              <el-table-column
                v-for="column in sqlResult.columns"
                :key="column"
                :prop="column"
                :label="column"
                min-width="100"
              />
            </el-table>
          </div>
          <div v-else-if="sqlResult.affectedRows !== undefined" class="result-info">
            <p>影响行数: {{ sqlResult.affectedRows }}</p>
            <p>执行时间: {{ sqlResult.executionTime }}ms</p>
          </div>
        </div>
        <div v-else class="result-error">
          <el-alert type="error" :title="sqlResult.message" show-icon />
        </div>
      </div>
    </el-dialog>

    <!-- 表结构对话框 -->
    <el-dialog
      :title="`${selectedTable?.name} - 表结构`"
      v-model="showStructureDialog"
      width="900px"
    >
      <el-table :data="tableStructure" v-loading="structureLoading">
        <el-table-column prop="field" label="字段名" width="150" />
        <el-table-column prop="type" label="数据类型" width="120" />
        <el-table-column prop="null" label="允许NULL" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.null === 'YES' ? 'warning' : 'success'" size="small">
              {{ row.null }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="key" label="键" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.key === 'PRI'" type="danger" size="small">主键</el-tag>
            <el-tag v-else-if="row.key === 'UNI'" type="warning" size="small">唯一</el-tag>
            <el-tag v-else-if="row.key === 'MUL'" type="info" size="small">索引</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="default" label="默认值" width="120" />
        <el-table-column prop="extra" label="额外" width="120" />
        <el-table-column prop="comment" label="注释" min-width="200" />
      </el-table>
    </el-dialog>

    <!-- 表数据对话框 -->
    <el-dialog
      :title="`${selectedTable?.name} - 数据预览`"
      v-model="showDataDialog"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="table-data-toolbar">
        <el-button @click="refreshTableData" :loading="dataLoading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-pagination
          v-model:current-page="dataPagination.page"
          v-model:page-size="dataPagination.size"
          :total="dataPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="sizes, prev, pager, next"
          @size-change="loadTableData"
          @current-change="loadTableData"
          small
        />
      </div>

      <el-table :data="tableData" v-loading="dataLoading" size="small" max-height="400">
        <el-table-column
          v-for="column in dataColumns"
          :key="column"
          :prop="column"
          :label="column"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { get, post, put } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Tools,
  EditPen,
  Coin,
  Grid,
  Document,
  Link,
  Search,
  View,
  Delete,
  CaretRight
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const optimizing = ref(false)
const executing = ref(false)
const structureLoading = ref(false)
const dataLoading = ref(false)
const showSqlDialog = ref(false)
const showStructureDialog = ref(false)
const showDataDialog = ref(false)
const tableSearch = ref('')
const sqlQuery = ref('')
const sqlResult = ref(null)
const selectedTable = ref(null)
const tables = ref([])
const tableStructure = ref([])
const tableData = ref([])
const dataColumns = ref([])

// 数据库统计
const dbStats = reactive({
  size: 0,
  tableCount: 0,
  recordCount: 0,
  connections: 0
})

// 数据分页
const dataPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性
const filteredTables = computed(() => {
  if (!tableSearch.value) return tables.value
  return tables.value.filter(table =>
    table.name.toLowerCase().includes(tableSearch.value.toLowerCase()) ||
    table.comment.toLowerCase().includes(tableSearch.value.toLowerCase())
  )
})

// 加载数据库信息
const loadDatabaseInfo = async () => {
  loading.value = true
  try {
    const response = await get('/api/database/info')
    if (response.success) {
      Object.assign(dbStats, response.data.stats || {})
      tables.value = response.data.tables || []
    }
  } catch (error) {
    console.error('加载数据库信息失败:', error)
    ElMessage.error('加载数据库信息失败')

    // 使用模拟数据
    generateMockData()
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  Object.assign(dbStats, {
    size: 256.8,
    tableCount: 15,
    recordCount: 125680,
    connections: 8
  })

  tables.value = [
    {
      name: 'users',
      comment: '用户表',
      engine: 'InnoDB',
      rows: 1250,
      dataSize: 2048000,
      indexSize: 512000,
      createdAt: new Date('2024-01-15')
    },
    {
      name: 'courses',
      comment: '课程表',
      engine: 'InnoDB',
      rows: 156,
      dataSize: 1024000,
      indexSize: 256000,
      createdAt: new Date('2024-01-20')
    },
    {
      name: 'groups',
      comment: '小组表',
      engine: 'InnoDB',
      rows: 89,
      dataSize: 512000,
      indexSize: 128000,
      createdAt: new Date('2024-02-01')
    },
    {
      name: 'assignments',
      comment: '作业表',
      engine: 'InnoDB',
      rows: 234,
      dataSize: 768000,
      indexSize: 192000,
      createdAt: new Date('2024-02-10')
    },
    {
      name: 'learning_records',
      comment: '学习记录表',
      engine: 'InnoDB',
      rows: 15680,
      dataSize: 8192000,
      indexSize: 2048000,
      createdAt: new Date('2024-01-25')
    }
  ]
}

// 优化数据库
const optimizeDatabase = async () => {
  try {
    await ElMessageBox.confirm('确定要优化整个数据库吗？此操作可能需要较长时间。', '确认优化', {
      type: 'warning'
    })

    optimizing.value = true
    await post('/api/database/optimize')
    ElMessage.success('数据库优化完成')
    await loadDatabaseInfo()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('优化数据库失败:', error)
      ElMessage.error('优化数据库失败')
    }
  } finally {
    optimizing.value = false
  }
}

// 执行SQL
const executeSql = async () => {
  if (!sqlQuery.value.trim()) {
    ElMessage.warning('请输入SQL语句')
    return
  }

  executing.value = true
  try {
    const response = await post('/api/database/execute', {
      sql: sqlQuery.value
    })

    sqlResult.value = response.data

    if (response.success) {
      ElMessage.success('SQL执行成功')
    } else {
      ElMessage.error('SQL执行失败')
    }
  } catch (error) {
    console.error('执行SQL失败:', error)
    sqlResult.value = {
      success: false,
      message: error.message || 'SQL执行失败'
    }
  } finally {
    executing.value = false
  }
}

// 查看表结构
const viewTableStructure = async (table) => {
  selectedTable.value = table
  showStructureDialog.value = true

  structureLoading.value = true
  try {
    const response = await get(`/api/database/tables/${table.name}/structure`)
    if (response.success) {
      tableStructure.value = response.data.structure || []
    }
  } catch (error) {
    console.error('获取表结构失败:', error)
    ElMessage.error('获取表结构失败')

    // 使用模拟数据
    tableStructure.value = [
      {
        field: 'id',
        type: 'int(11)',
        null: 'NO',
        key: 'PRI',
        default: null,
        extra: 'auto_increment',
        comment: '主键ID'
      },
      {
        field: 'name',
        type: 'varchar(255)',
        null: 'NO',
        key: '',
        default: null,
        extra: '',
        comment: '名称'
      },
      {
        field: 'created_at',
        type: 'timestamp',
        null: 'NO',
        key: '',
        default: 'CURRENT_TIMESTAMP',
        extra: '',
        comment: '创建时间'
      }
    ]
  } finally {
    structureLoading.value = false
  }
}

// 查看表数据
const viewTableData = async (table) => {
  selectedTable.value = table
  showDataDialog.value = true
  dataPagination.page = 1
  await loadTableData()
}

// 加载表数据
const loadTableData = async () => {
  if (!selectedTable.value) return

  dataLoading.value = true
  try {
    const response = await get(`/api/database/tables/${selectedTable.value.name}/data`, {
      page: dataPagination.page,
      limit: dataPagination.size
    })

    if (response.success) {
      tableData.value = response.data.data || []
      dataColumns.value = response.data.columns || []
      dataPagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取表数据失败:', error)
    ElMessage.error('获取表数据失败')

    // 使用模拟数据
    tableData.value = [
      { id: 1, name: '示例数据1', created_at: '2024-01-01 10:00:00' },
      { id: 2, name: '示例数据2', created_at: '2024-01-02 11:00:00' }
    ]
    dataColumns.value = ['id', 'name', 'created_at']
    dataPagination.total = 100
  } finally {
    dataLoading.value = false
  }
}

// 刷新表数据
const refreshTableData = () => {
  loadTableData()
}

// 优化表
const optimizeTable = async (table) => {
  try {
    await ElMessageBox.confirm(`确定要优化表"${table.name}"吗？`, '确认优化', {
      type: 'warning'
    })

    await post(`/api/database/tables/${table.name}/optimize`)
    ElMessage.success('表优化完成')
    await loadDatabaseInfo()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('优化表失败:', error)
      ElMessage.error('优化表失败')
    }
  }
}

// 清空表
const truncateTable = async (table) => {
  try {
    await ElMessageBox.confirm(`确定要清空表"${table.name}"的所有数据吗？此操作不可恢复！`, '确认清空', {
      type: 'error'
    })

    await post(`/api/database/tables/${table.name}/truncate`)
    ElMessage.success('表数据清空完成')
    await loadDatabaseInfo()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空表失败:', error)
      ElMessage.error('清空表失败')
    }
  }
}

// 工具函数
const formatNumber = (num) => {
  return new Intl.NumberFormat().format(num)
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const filterTables = () => {
  // 筛选逻辑已在计算属性中实现
}

const clearSql = () => {
  sqlQuery.value = ''
  sqlResult.value = null
}

const formatSql = () => {
  // 简单的SQL格式化
  sqlQuery.value = sqlQuery.value
    .replace(/\s+/g, ' ')
    .replace(/,/g, ',\n  ')
    .replace(/FROM/gi, '\nFROM')
    .replace(/WHERE/gi, '\nWHERE')
    .replace(/ORDER BY/gi, '\nORDER BY')
    .replace(/GROUP BY/gi, '\nGROUP BY')
    .trim()
}

const refreshData = () => {
  loadDatabaseInfo()
}

// 组件挂载时加载数据
onMounted(() => {
  loadDatabaseInfo()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  .stats-extra {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}

// SQL编辑器样式
.sql-editor {
  .sql-textarea {
    :deep(.el-textarea__inner) {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }

  .sql-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
  }
}

.sql-result {
  margin-top: 20px;

  .result-success {
    .result-table {
      margin-top: 16px;
    }

    .result-info {
      margin-top: 16px;
      padding: 12px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;

      p {
        margin: 4px 0;
        font-size: 14px;
      }
    }
  }

  .result-error {
    margin-top: 16px;
  }
}

// 表数据工具栏
.table-data-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
}

// 表格样式优化
.el-table {
  .el-table__cell {
    padding: 8px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-row {
    .el-col {
      margin-bottom: 16px;
    }
  }

  .table-data-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
