/**
 * 缓存管理工具
 */

class CacheManager {
  constructor() {
    this.memoryCache = new Map()
    this.storagePrefix = 'gst_cache_'
    this.defaultTTL = 5 * 60 * 1000 // 5分钟
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} value 缓存值
   * @param {number} ttl 过期时间（毫秒）
   * @param {string} storage 存储类型：memory, localStorage, sessionStorage
   */
  set(key, value, ttl = this.defaultTTL, storage = 'memory') {
    const expireTime = Date.now() + ttl
    const cacheItem = {
      value,
      expireTime,
      createTime: Date.now()
    }

    switch (storage) {
      case 'memory':
        this.memoryCache.set(key, cacheItem)
        break
      case 'localStorage':
        try {
          localStorage.setItem(
            this.storagePrefix + key,
            JSON.stringify(cacheItem)
          )
        } catch (e) {
          console.warn('localStorage set failed:', e)
        }
        break
      case 'sessionStorage':
        try {
          sessionStorage.setItem(
            this.storagePrefix + key,
            JSON.stringify(cacheItem)
          )
        } catch (e) {
          console.warn('sessionStorage set failed:', e)
        }
        break
    }
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {string} storage 存储类型
   * @returns {any} 缓存值，如果不存在或已过期返回null
   */
  get(key, storage = 'memory') {
    let cacheItem = null

    switch (storage) {
      case 'memory':
        cacheItem = this.memoryCache.get(key)
        break
      case 'localStorage':
        try {
          const item = localStorage.getItem(this.storagePrefix + key)
          cacheItem = item ? JSON.parse(item) : null
        } catch (e) {
          console.warn('localStorage get failed:', e)
        }
        break
      case 'sessionStorage':
        try {
          const item = sessionStorage.getItem(this.storagePrefix + key)
          cacheItem = item ? JSON.parse(item) : null
        } catch (e) {
          console.warn('sessionStorage get failed:', e)
        }
        break
    }

    if (!cacheItem) {
      return null
    }

    // 检查是否过期
    if (Date.now() > cacheItem.expireTime) {
      this.delete(key, storage)
      return null
    }

    return cacheItem.value
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   * @param {string} storage 存储类型
   */
  delete(key, storage = 'memory') {
    switch (storage) {
      case 'memory':
        this.memoryCache.delete(key)
        break
      case 'localStorage':
        localStorage.removeItem(this.storagePrefix + key)
        break
      case 'sessionStorage':
        sessionStorage.removeItem(this.storagePrefix + key)
        break
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key 缓存键
   * @param {string} storage 存储类型
   * @returns {boolean}
   */
  has(key, storage = 'memory') {
    return this.get(key, storage) !== null
  }

  /**
   * 清除所有缓存
   * @param {string} storage 存储类型
   */
  clear(storage = 'memory') {
    switch (storage) {
      case 'memory':
        this.memoryCache.clear()
        break
      case 'localStorage':
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith(this.storagePrefix)) {
            localStorage.removeItem(key)
          }
        })
        break
      case 'sessionStorage':
        Object.keys(sessionStorage).forEach(key => {
          if (key.startsWith(this.storagePrefix)) {
            sessionStorage.removeItem(key)
          }
        })
        break
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    const stats = {
      memory: {
        count: this.memoryCache.size,
        keys: Array.from(this.memoryCache.keys())
      },
      localStorage: {
        count: 0,
        keys: []
      },
      sessionStorage: {
        count: 0,
        keys: []
      }
    }

    // 统计localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.storagePrefix)) {
        stats.localStorage.count++
        stats.localStorage.keys.push(key.replace(this.storagePrefix, ''))
      }
    })

    // 统计sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith(this.storagePrefix)) {
        stats.sessionStorage.count++
        stats.sessionStorage.keys.push(key.replace(this.storagePrefix, ''))
      }
    })

    return stats
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()

    // 清理内存缓存
    for (const [key, item] of this.memoryCache) {
      if (now > item.expireTime) {
        this.memoryCache.delete(key)
      }
    }

    // 清理localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.storagePrefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key))
          if (item && now > item.expireTime) {
            localStorage.removeItem(key)
          }
        } catch (e) {
          // 删除无效的缓存项
          localStorage.removeItem(key)
        }
      }
    })

    // 清理sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith(this.storagePrefix)) {
        try {
          const item = JSON.parse(sessionStorage.getItem(key))
          if (item && now > item.expireTime) {
            sessionStorage.removeItem(key)
          }
        } catch (e) {
          // 删除无效的缓存项
          sessionStorage.removeItem(key)
        }
      }
    })
  }

  /**
   * 缓存装饰器
   * @param {string} key 缓存键
   * @param {number} ttl 过期时间
   * @param {string} storage 存储类型
   * @returns {function} 装饰器函数
   */
  cached(key, ttl = this.defaultTTL, storage = 'memory') {
    return (target, propertyName, descriptor) => {
      const originalMethod = descriptor.value

      descriptor.value = async function (...args) {
        const cacheKey = typeof key === 'function' ? key(...args) : key
        const cached = cacheManager.get(cacheKey, storage)

        if (cached !== null) {
          return cached
        }

        const result = await originalMethod.apply(this, args)
        cacheManager.set(cacheKey, result, ttl, storage)
        return result
      }

      return descriptor
    }
  }
}

// 创建全局实例
const cacheManager = new CacheManager()

// 定期清理过期缓存
setInterval(() => {
  cacheManager.cleanup()
}, 60000) // 每分钟清理一次

// 页面卸载时清理内存缓存
window.addEventListener('beforeunload', () => {
  cacheManager.clear('memory')
})

// 导出常用方法
export const setCache = (key, value, ttl, storage) => cacheManager.set(key, value, ttl, storage)
export const getCache = (key, storage) => cacheManager.get(key, storage)
export const deleteCache = (key, storage) => cacheManager.delete(key, storage)
export const hasCache = (key, storage) => cacheManager.has(key, storage)
export const clearCache = (storage) => cacheManager.clear(storage)
export const getCacheStats = () => cacheManager.getStats()
export const cleanupCache = () => cacheManager.cleanup()
export const cached = (key, ttl, storage) => cacheManager.cached(key, ttl, storage)

export default cacheManager
