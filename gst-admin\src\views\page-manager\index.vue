﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>页面管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建页面
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="页面名称">
          <el-input v-model="filters.name" placeholder="输入页面名称" clearable @change="loadPages" />
        </el-form-item>
        <el-form-item label="页面类型">
          <el-select v-model="filters.type" placeholder="选择页面类型" clearable @change="loadPages">
            <el-option label="首页" value="home" />
            <el-option label="分类页" value="category" />
            <el-option label="课程详情" value="course" />
            <el-option label="个人中心" value="profile" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadPages">
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已下线" value="offline" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 页面列表 -->
    <el-card>
      <el-table :data="pages" v-loading="loading" stripe>
        <el-table-column prop="name" label="页面名称" min-width="150" />
        <el-table-column prop="path" label="页面路径" min-width="200" />
        <el-table-column prop="type" label="页面类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="visitCount" label="访问量" width="100" align="center">
          <template #default="{ row }">
            <el-statistic :value="row.visitCount || 0" />
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建者" width="120" />
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="previewPage(row)">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button link size="small" @click="editPage(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link size="small" @click="designPage(row)">
              <el-icon><Brush /></el-icon>
              设计
            </el-button>
            <el-button link size="small" @click="copyPage(row)">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
            <el-button
              link
              size="small"
              @click="toggleStatus(row)"
              :type="row.status === 'published' ? 'warning' : 'success'"
            >
              <el-icon><Switch /></el-icon>
              {{ row.status === 'published' ? '下线' : '发布' }}
            </el-button>
            <el-button link size="small" @click="deletePage(row)" class="danger-button">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadPages"
          @current-change="loadPages"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="editingPage ? '编辑页面' : '创建页面'"
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form ref="pageFormRef" :model="pageForm" :rules="pageFormRules" label-width="100px">
        <el-form-item label="页面名称" prop="name">
          <el-input v-model="pageForm.name" placeholder="请输入页面名称" />
        </el-form-item>
        <el-form-item label="页面路径" prop="path">
          <el-input v-model="pageForm.path" placeholder="请输入页面路径，如：/home" />
        </el-form-item>
        <el-form-item label="页面类型" prop="type">
          <el-select v-model="pageForm.type" placeholder="选择页面类型" style="width: 100%">
            <el-option label="首页" value="home" />
            <el-option label="分类页" value="category" />
            <el-option label="课程详情" value="course" />
            <el-option label="个人中心" value="profile" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="页面描述" prop="description">
          <el-input
            v-model="pageForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入页面描述"
          />
        </el-form-item>
        <el-form-item label="页面配置" prop="config">
          <el-input
            v-model="pageForm.config"
            type="textarea"
            :rows="6"
            placeholder="请输入页面配置（JSON格式）"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="pageForm.status">
            <el-radio label="draft">草稿</el-radio>
            <el-radio label="published">已发布</el-radio>
            <el-radio label="offline">已下线</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="savePage" :loading="saving">
          {{ editingPage ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      title="页面预览"
      v-model="showPreviewDialog"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="preview-container">
        <div class="preview-phone">
          <div class="preview-header">
            <div class="preview-status-bar"></div>
            <div class="preview-title">{{ previewPage?.name }}</div>
          </div>
          <div class="preview-content">
            <div v-if="previewPageConfig" class="preview-components">
              <!-- 这里渲染页面组件 -->
              <div class="preview-placeholder">
                <el-empty description="页面预览" :image-size="60" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  View,
  Edit,
  Delete,
  Switch,
  Brush,
  CopyDocument
} from '@element-plus/icons-vue'
import router from '@/router'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const pages = ref([])
const total = ref(0)
const showCreateDialog = ref(false)
const showPreviewDialog = ref(false)
const editingPage = ref(null)
const previewPageData = ref(null)
const previewPageConfig = ref(null)

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 筛选器
const filters = reactive({
  name: '',
  type: '',
  status: ''
})

// 表单数据
const pageForm = reactive({
  name: '',
  path: '',
  type: 'custom',
  description: '',
  config: '{}',
  status: 'draft'
})

const pageFormRules = {
  name: [
    { required: true, message: '请输入页面名称', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请输入页面路径', trigger: 'blur' },
    { pattern: /^\//, message: '页面路径必须以/开头', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择页面类型', trigger: 'change' }
  ]
}

// 加载页面数据
const loadPages = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size,
      ...filters
    }

    const response = await get('/api/pages', params)
    if (response.success) {
      pages.value = response.data.pages || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载页面数据失败:', error)
    ElMessage.error('加载页面数据失败')

    // 使用模拟数据
    pages.value = generateMockPages()
    total.value = pages.value.length
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockPages = () => {
  return [
    {
      id: 1,
      name: '首页',
      path: '/home',
      type: 'home',
      status: 'published',
      visitCount: 1250,
      creatorName: '管理员',
      updatedAt: new Date(),
      description: '小程序首页',
      config: JSON.stringify({
        banner: true,
        categories: true,
        courses: true
      })
    },
    {
      id: 2,
      name: '课程分类',
      path: '/category',
      type: 'category',
      status: 'published',
      visitCount: 890,
      creatorName: '管理员',
      updatedAt: new Date(),
      description: '课程分类页面',
      config: JSON.stringify({
        layout: 'grid',
        columns: 2
      })
    },
    {
      id: 3,
      name: '个人中心',
      path: '/profile',
      type: 'profile',
      status: 'draft',
      visitCount: 0,
      creatorName: '开发者',
      updatedAt: new Date(),
      description: '用户个人中心页面',
      config: JSON.stringify({
        showAvatar: true,
        showStats: true
      })
    }
  ]
}

// 保存页面
const savePage = async () => {
  const pageFormRef = ref()
  try {
    await pageFormRef.value.validate()
  } catch (error) {
    return
  }

  // 验证JSON格式
  try {
    JSON.parse(pageForm.config)
  } catch (error) {
    ElMessage.error('页面配置必须是有效的JSON格式')
    return
  }

  saving.value = true
  try {
    const data = { ...pageForm }

    if (editingPage.value) {
      await put(`/api/pages/${editingPage.value.id}`, data)
      ElMessage.success('页面更新成功')
    } else {
      await post('/api/pages', data)
      ElMessage.success('页面创建成功')
    }

    showCreateDialog.value = false
    resetForm()
    await loadPages()
  } catch (error) {
    console.error('保存页面失败:', error)
    ElMessage.error('保存页面失败')
  } finally {
    saving.value = false
  }
}

// 编辑页面
const editPage = (page) => {
  editingPage.value = page
  Object.assign(pageForm, {
    name: page.name,
    path: page.path,
    type: page.type,
    description: page.description || '',
    config: page.config || '{}',
    status: page.status
  })
  showCreateDialog.value = true
}

// 删除页面
const deletePage = async (page) => {
  try {
    await ElMessageBox.confirm('确定要删除这个页面吗？', '确认删除', {
      type: 'warning'
    })

    await del(`/api/pages/${page.id}`)
    ElMessage.success('页面删除成功')
    await loadPages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除页面失败:', error)
      ElMessage.error('删除页面失败')
    }
  }
}

// 预览页面
const previewPage = (page) => {
  previewPageData.value = page
  try {
    previewPageConfig.value = JSON.parse(page.config || '{}')
  } catch (error) {
    previewPageConfig.value = {}
  }
  showPreviewDialog.value = true
}

// 设计页面
const designPage = (page) => {
  router.push(`/visual-editor?pageId=${page.id}`)
}

// 复制页面
const copyPage = async (page) => {
  const newPage = {
    name: `${page.name} - 副本`,
    path: `${page.path}-copy`,
    type: page.type,
    description: page.description,
    config: page.config,
    status: 'draft'
  }

  try {
    await post('/api/pages', newPage)
    ElMessage.success('页面复制成功')
    await loadPages()
  } catch (error) {
    console.error('复制页面失败:', error)
    ElMessage.error('复制页面失败')
  }
}

// 切换状态
const toggleStatus = async (page) => {
  const newStatus = page.status === 'published' ? 'offline' : 'published'
  const action = newStatus === 'published' ? '发布' : '下线'

  try {
    await ElMessageBox.confirm(`确定要${action}这个页面吗？`, `确认${action}`, {
      type: 'warning'
    })

    await put(`/api/pages/${page.id}`, { status: newStatus })
    ElMessage.success(`页面${action}成功`)
    await loadPages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}页面失败:`, error)
      ElMessage.error(`${action}页面失败`)
    }
  }
}

// 工具函数
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getTypeColor = (type) => {
  const colors = {
    home: 'success',
    category: 'primary',
    course: 'warning',
    profile: 'info',
    custom: ''
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    home: '首页',
    category: '分类页',
    course: '课程详情',
    profile: '个人中心',
    custom: '自定义'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'info',
    published: 'success',
    offline: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    offline: '已下线'
  }
  return texts[status] || status
}

const resetForm = () => {
  editingPage.value = null
  Object.assign(pageForm, {
    name: '',
    path: '',
    type: 'custom',
    description: '',
    config: '{}',
    status: 'draft'
  })
}

const refreshData = () => {
  loadPages()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPages()
})
</script>

<style lang="scss" scoped>
.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}

// 预览对话框样式
.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.preview-phone {
  width: 320px;
  height: 568px;
  background: #333;
  border-radius: 20px;
  padding: 10px;
  position: relative;

  .preview-header {
    height: 40px;
    background: #000;
    border-radius: 10px 10px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .preview-status-bar {
      position: absolute;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #666;
      border-radius: 2px;
    }

    .preview-title {
      color: white;
      font-size: 14px;
      font-weight: 500;
      margin-top: 10px;
    }
  }

  .preview-content {
    height: calc(100% - 40px);
    background: white;
    border-radius: 0 0 10px 10px;
    overflow: auto;

    .preview-components {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .preview-placeholder {
      text-align: center;
    }
  }
}

// 表格样式优化
.el-table {
  .el-statistic {
    .el-statistic__content {
      font-size: 14px;
    }
  }
}

// 表单样式
.el-form {
  .el-textarea {
    .el-textarea__inner {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
    }
  }
}
</style>
