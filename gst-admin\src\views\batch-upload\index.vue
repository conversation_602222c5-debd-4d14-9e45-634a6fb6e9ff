<template>
  <div class="page-container">
    <div class="page-header">
      <h1>批量上传音频课程</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 上传区域 -->
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <span>批量上传音频文件</span>
          <el-tag type="info">支持MP3、WAV、M4A等格式，最多50个文件</el-tag>
        </div>
      </template>

      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" label-width="120px">
        <el-form-item label="课程分类" prop="categoryId">
          <el-select v-model="uploadForm.categoryId" placeholder="选择课程分类" style="width: 300px">
            <el-option 
              v-for="category in categories" 
              :key="category.id" 
              :label="category.name" 
              :value="category.id" 
            />
          </el-select>
        </el-form-item>

        <el-form-item label="难度等级" prop="level">
          <el-select v-model="uploadForm.level" placeholder="选择难度等级" style="width: 200px">
            <el-option label="N5" value="N5" />
            <el-option label="N4" value="N4" />
            <el-option label="N3" value="N3" />
            <el-option label="N2" value="N2" />
            <el-option label="N1" value="N1" />
          </el-select>
        </el-form-item>

        <el-form-item label="课程类型" prop="courseType">
          <el-select v-model="uploadForm.courseType" placeholder="选择课程类型" style="width: 200px">
            <el-option label="听力" value="listening" />
            <el-option label="口语" value="speaking" />
            <el-option label="语法" value="grammar" />
            <el-option label="词汇" value="vocabulary" />
          </el-select>
        </el-form-item>

        <el-form-item label="音频文件">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :headers="uploadHeaders"
            :data="uploadForm"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-progress="handleUploadProgress"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :auto-upload="false"
            multiple
            accept="audio/*"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将音频文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持MP3、WAV、M4A等音频格式，单个文件不超过500MB，最多50个文件
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="startUpload" :loading="uploading" :disabled="fileList.length === 0">
            <el-icon><Upload /></el-icon>
            开始批量上传 ({{ fileList.length }} 个文件)
          </el-button>
          <el-button @click="clearFiles" :disabled="uploading">
            <el-icon><Delete /></el-icon>
            清空文件列表
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 上传进度 -->
    <el-card v-if="uploadProgress.show" class="progress-card">
      <template #header>
        <div class="card-header">
          <span>上传进度</span>
          <el-tag :type="getProgressType(uploadProgress.status)">
            {{ getProgressText(uploadProgress.status) }}
          </el-tag>
        </div>
      </template>

      <div class="progress-content">
        <el-progress 
          :percentage="uploadProgress.percentage" 
          :status="uploadProgress.status"
          :stroke-width="20"
        />
        <div class="progress-info">
          <p>总文件数: {{ uploadProgress.total }}</p>
          <p>已完成: {{ uploadProgress.completed }}</p>
          <p>失败: {{ uploadProgress.failed }}</p>
          <p v-if="uploadProgress.currentFile">当前处理: {{ uploadProgress.currentFile }}</p>
        </div>
      </div>

      <!-- 错误列表 -->
      <div v-if="uploadProgress.errors.length > 0" class="error-list">
        <h4>失败文件列表:</h4>
        <el-table :data="uploadProgress.errors" size="small">
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column prop="error" label="错误信息" />
        </el-table>
      </div>
    </el-card>

    <!-- 上传结果 -->
    <el-card v-if="uploadResult.show" class="result-card">
      <template #header>
        <div class="card-header">
          <span>上传结果</span>
          <el-tag :type="uploadResult.success > 0 ? 'success' : 'danger'">
            成功: {{ uploadResult.success }} / 失败: {{ uploadResult.failed }}
          </el-tag>
        </div>
      </template>

      <div class="result-content">
        <el-alert
          :title="`批量上传完成: 成功 ${uploadResult.success} 个，失败 ${uploadResult.failed} 个`"
          :type="uploadResult.failed === 0 ? 'success' : 'warning'"
          show-icon
          :closable="false"
        />

        <!-- 成功列表 -->
        <div v-if="uploadResult.courses.length > 0" class="success-list">
          <h4>成功创建的课程:</h4>
          <el-table :data="uploadResult.courses" size="small">
            <el-table-column prop="title" label="课程名称" />
            <el-table-column prop="fileName" label="原始文件名" />
            <el-table-column prop="courseId" label="课程ID" width="100" />
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button link size="small" @click="viewCourse(row.courseId)">
                  查看课程
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Upload, Delete, Refresh } from '@element-plus/icons-vue'
import router from '@/router'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const categories = ref([])
const fileList = ref([])

// 上传表单
const uploadForm = reactive({
  categoryId: '',
  level: 'N5',
  courseType: 'listening'
})

const uploadRules = {
  categoryId: [
    { required: true, message: '请选择课程分类', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],
  courseType: [
    { required: true, message: '请选择课程类型', trigger: 'change' }
  ]
}

// 上传配置
const uploadAction = '/api/batch/upload-audio-courses'
const uploadHeaders = {
  'Authorization': `Bearer ${authStore.token}`
}

// 上传进度
const uploadProgress = reactive({
  show: false,
  percentage: 0,
  status: 'success',
  total: 0,
  completed: 0,
  failed: 0,
  currentFile: '',
  errors: []
})

// 上传结果
const uploadResult = reactive({
  show: false,
  success: 0,
  failed: 0,
  courses: [],
  errors: []
})

// 加载分类数据
const loadCategories = async () => {
  try {
    const response = await get('/api/categories')
    if (response.success) {
      categories.value = response.data.categories || []
    }
  } catch (error) {
    console.error('加载分类数据失败:', error)
    ElMessage.error('加载分类数据失败')
  }
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isAudio = file.type.startsWith('audio/')
  const isLt500M = file.size / 1024 / 1024 < 500

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt500M) {
    ElMessage.error('音频文件大小不能超过 500MB!')
    return false
  }
  return true
}

// 开始上传
const startUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }

  // 表单验证
  const uploadFormRef = ref()
  try {
    await uploadFormRef.value.validate()
  } catch (error) {
    return
  }

  uploading.value = true
  uploadProgress.show = true
  uploadProgress.percentage = 0
  uploadProgress.status = 'success'
  uploadProgress.total = fileList.value.length
  uploadProgress.completed = 0
  uploadProgress.failed = 0
  uploadProgress.errors = []
  uploadResult.show = false

  // 开始上传
  uploadRef.value.submit()
}

// 上传成功回调
const handleUploadSuccess = (response, file, fileList) => {
  uploading.value = false
  uploadProgress.show = false
  
  if (response.success) {
    uploadResult.show = true
    uploadResult.success = response.data.success
    uploadResult.failed = response.data.failed
    uploadResult.courses = response.data.courses
    uploadResult.errors = response.data.errors

    ElMessage.success(`批量上传完成: 成功 ${response.data.success} 个，失败 ${response.data.failed} 个`)
    
    // 清空文件列表
    clearFiles()
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

// 上传失败回调
const handleUploadError = (error, file, fileList) => {
  uploading.value = false
  uploadProgress.show = false
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请重试')
}

// 上传进度回调
const handleUploadProgress = (event, file, fileList) => {
  uploadProgress.percentage = Math.round(event.percent)
  uploadProgress.currentFile = file.name
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
  uploadRef.value.clearFiles()
}

// 查看课程
const viewCourse = (courseId) => {
  router.push(`/courses/${courseId}`)
}

// 获取进度状态类型
const getProgressType = (status) => {
  const types = {
    success: 'success',
    exception: 'danger',
    warning: 'warning'
  }
  return types[status] || 'info'
}

// 获取进度状态文本
const getProgressText = (status) => {
  const texts = {
    success: '上传中',
    exception: '上传失败',
    warning: '部分失败'
  }
  return texts[status] || '处理中'
}

// 刷新数据
const refreshData = () => {
  loadCategories()
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})
</script>

<style lang="scss" scoped>
.upload-card, .progress-card, .result-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-demo {
  width: 100%;
}

.progress-content {
  .progress-info {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    
    p {
      margin: 0;
      padding: 8px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
  }
}

.error-list, .success-list {
  margin-top: 20px;
  
  h4 {
    margin-bottom: 10px;
    color: var(--el-text-color-primary);
  }
}

.result-content {
  .el-alert {
    margin-bottom: 20px;
  }
}
</style>
