{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?985d", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?0448", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?2fd8", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?4838", "uni-app:///uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?d7d3", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue?d8e6"], "names": ["name", "mixins", "data", "safeAreaBottomHeight", "isNvue", "computed", "style", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAwoB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACU5pB;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,eAUA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MAKA;IACA;EACA;EACAC,6BAKA;AACA;AAAA,2B;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAmuC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACAvvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-safe-bottom.vue?vue&type=template&id=40b3d0de&scoped=true&\"\nvar renderjs\nimport script from \"./u-safe-bottom.vue?vue&type=script&lang=js&\"\nexport * from \"./u-safe-bottom.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-safe-bottom.vue?vue&type=style&index=0&id=40b3d0de&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"40b3d0de\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=template&id=40b3d0de&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.style])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-safe-bottom\"\r\n\t\t:style=\"[style]\"\r\n\t\t:class=\"[!isNvue && 'u-safe-area-inset-bottom']\"\r\n\t>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from \"./props.js\";\r\n\t/**\r\n\t * SafeBottom 底部安全区\r\n\t * @description 这个适配，主要是针对IPhone X等一些底部带指示条的机型，指示条的操作区域与页面底部存在重合，容易导致用户误操作，因此我们需要针对这些机型进行底部安全区适配。\r\n\t * @tutorial https://www.uviewui.com/components/safeAreaInset.html\r\n\t * @property {type}\t\tprop_name\r\n\t * @property {Object}\tcustomStyle\t定义需要用到的外部样式\r\n\t *\r\n\t * @event {Function()}\r\n\t * @example <u-status-bar></u-status-bar>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-safe-bottom\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsafeAreaBottomHeight: 0,\r\n\t\t\t\tisNvue: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstyle() {\r\n\t\t\t\tconst style = {};\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// nvue下，高度使用js计算填充\r\n\t\t\t\tstyle.height = uni.$u.addUnit(uni.$u.sys().safeAreaInsets.bottom, 'px');\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\r\n\t\t\t},\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\t// 标识为是否nvue\r\n\t\t\tthis.isNvue = true;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.u-safe-bottom {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=style&index=0&id=40b3d0de&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-safe-bottom.vue?vue&type=style&index=0&id=40b3d0de&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039749482\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}