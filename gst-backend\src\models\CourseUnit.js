const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
const CourseUnit = sequelize.define('CourseUnit', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  courseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'course_id',
    references: {
      model: 'courses',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '单元标题'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '单元描述'
  },
  content: {
    type: DataTypes.TEXT,
    comment: '单元内容'
  },
  orderNum: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'order_num',
    comment: '排序号'
  },
  duration: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '时长(分钟)'
  },
  videoUrl: {
    type: DataTypes.STRING(500),
    field: 'video_url',
    comment: '视频URL'
  },
  audioUrl: {
    type: DataTypes.STRING(500),
    field: 'audio_url',
    comment: '音频URL'
  },
  videoId: {
    type: DataTypes.STRING(100),
    field: 'video_id',
    comment: '阿里云视频ID'
  },
  playId: {
    type: DataTypes.STRING(255),
    field: 'play_id',
    comment: '录制地址/播放ID'
  },
  audioId: {
    type: DataTypes.STRING(100),
    field: 'audio_id',
    comment: '阿里云音频ID'
  },
  attachments: {
    type: DataTypes.TEXT,
    comment: '附件列表(JSON格式)'
  },
  status: {
    type: DataTypes.ENUM('draft', 'published', 'archived'),
    defaultValue: 'draft',
    comment: '状态'
  },
  isRequired: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_required',
    comment: '是否必修'
  },
  isFree: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_free',
    comment: '是否免费试听'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    field: 'updated_at'
  }
}, {
  tableName: 'course_units',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  comment: '课程单元表'
});

// 定义关联关系
CourseUnit.associate = function(models) {
  CourseUnit.belongsTo(models.Course, {
    foreignKey: 'courseId',
    as: 'course'
  });
  CourseUnit.belongsTo(models.User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });
  CourseUnit.hasMany(models.CourseMedia, {
    foreignKey: 'unitId',
    as: 'media'
  });
};

return CourseUnit;
};
