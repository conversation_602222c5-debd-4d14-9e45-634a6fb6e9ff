/*
 简化的课程分类测试数据
 只包含基本字段，避免外键约束问题
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for courses_classify
-- ----------------------------
DROP TABLE IF EXISTS `courses_classify`;
CREATE TABLE `courses_classify`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` tinyint(4) NOT NULL DEFAULT 1,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of courses_classify
-- ----------------------------
INSERT INTO `courses_classify` VALUES (1, '日语学习', '基础日语学习课程分类', 1, 1);
INSERT INTO `courses_classify` VALUES (2, '日语考级', 'JLPT考级相关课程', 2, 1);
INSERT INTO `courses_classify` VALUES (3, '实用口语', '日常生活口语练习', 3, 1);
INSERT INTO `courses_classify` VALUES (4, '升学考试', '升学相关日语考试', 4, 1);
INSERT INTO `courses_classify` VALUES (5, 'N5语法', 'N5级别语法课程', 5, 1);

SET FOREIGN_KEY_CHECKS = 1;
