const { sequelize } = require('../src/models');

async function verifyImport() {
  try {
    console.log('🔍 开始验证导入数据的完整性...');

    // 1. 基础数据统计
    console.log('\n📊 基础数据统计:');
    
    const [courseStats] = await sequelize.query(`SELECT COUNT(*) as total FROM courses`);
    const [categoryStats] = await sequelize.query(`SELECT COUNT(*) as total FROM categories`);
    const [unitStats] = await sequelize.query(`SELECT COUNT(*) as total FROM course_units`);
    let taskStats = [{ total: 0 }];
    try {
      [taskStats] = await sequelize.query(`SELECT COUNT(*) as total FROM import_tasks`);
    } catch (error) {
      // import_tasks表不存在
    }

    console.log(`  - 课程数量: ${courseStats[0].total}`);
    console.log(`  - 分类数量: ${categoryStats[0].total}`);
    console.log(`  - 课程单元数量: ${unitStats[0].total}`);
    console.log(`  - 导入任务数量: ${taskStats[0].total}`);

    // 2. 检查数据完整性
    console.log('\n🔍 检查数据完整性...');

    // 检查课程分类关联
    const [coursesWithCategory] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses WHERE category_id IS NOT NULL
    `);
    
    const [coursesWithoutCategory] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses WHERE category_id IS NULL
    `);

    console.log(`  - 有分类的课程: ${coursesWithCategory[0].count}`);
    console.log(`  - 无分类的课程: ${coursesWithoutCategory[0].count}`);

    if (coursesWithoutCategory[0].count > 0) {
      console.log('⚠️  发现无分类的课程，需要检查分类导入是否完整');
      
      const [orphanCourses] = await sequelize.query(`
        SELECT id, title FROM courses WHERE category_id IS NULL LIMIT 5
      `);
      
      console.log('无分类课程示例:');
      orphanCourses.forEach(course => {
        console.log(`    - ${course.title} (ID: ${course.id})`);
      });
    }

    // 检查分类是否有效
    const [invalidCategories] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.category_id IS NOT NULL AND cat.id IS NULL
    `);

    if (invalidCategories[0].count > 0) {
      console.log(`⚠️  发现 ${invalidCategories[0].count} 个课程的分类ID无效`);
    }

    // 检查课程单元关联
    const [unitsWithValidCourse] = await sequelize.query(`
      SELECT COUNT(*) as count FROM course_units cu
      INNER JOIN courses c ON cu.course_id = c.id
    `);
    
    const [orphanUnits] = await sequelize.query(`
      SELECT COUNT(*) as count FROM course_units cu
      LEFT JOIN courses c ON cu.course_id = c.id
      WHERE c.id IS NULL
    `);

    console.log(`  - 有效关联的课程单元: ${unitsWithValidCourse[0].count}`);
    console.log(`  - 孤立的课程单元: ${orphanUnits[0].count}`);

    if (orphanUnits[0].count > 0) {
      console.log('⚠️  发现孤立的课程单元，需要检查课程导入是否完整');
      
      const [orphanUnitsList] = await sequelize.query(`
        SELECT cu.id, cu.title, cu.course_id
        FROM course_units cu
        LEFT JOIN courses c ON cu.course_id = c.id
        WHERE c.id IS NULL
        LIMIT 5
      `);
      
      console.log('孤立课程单元示例:');
      orphanUnitsList.forEach(unit => {
        console.log(`    - ${unit.title} (课程ID: ${unit.course_id})`);
      });
    }

    // 3. 检查分类层级结构
    console.log('\n🔍 检查分类层级结构...');
    
    const [topCategories] = await sequelize.query(`
      SELECT COUNT(*) as count FROM categories WHERE pid = 0
    `);
    
    const [subCategories] = await sequelize.query(`
      SELECT COUNT(*) as count FROM categories WHERE pid != 0
    `);

    console.log(`  - 一级分类数量: ${topCategories[0].count}`);
    console.log(`  - 二级分类数量: ${subCategories[0].count}`);

    // 检查分类层级关系是否有效
    const [invalidHierarchy] = await sequelize.query(`
      SELECT COUNT(*) as count FROM categories c
      LEFT JOIN categories p ON c.pid = p.id
      WHERE c.pid != 0 AND p.id IS NULL
    `);

    if (invalidHierarchy[0].count > 0) {
      console.log(`⚠️  发现 ${invalidHierarchy[0].count} 个分类的父分类不存在`);
    }

    // 4. 显示分类分布
    console.log('\n📋 分类分布统计:');
    
    const [categoryDistribution] = await sequelize.query(`
      SELECT 
        c.id,
        c.name,
        c.pid,
        COUNT(co.id) as course_count
      FROM categories c
      LEFT JOIN courses co ON c.id = co.category_id
      GROUP BY c.id, c.name, c.pid
      ORDER BY c.pid, course_count DESC
    `);

    const topCats = categoryDistribution.filter(cat => cat.pid === 0);
    const subCats = categoryDistribution.filter(cat => cat.pid !== 0);

    console.log('一级分类:');
    topCats.forEach(cat => {
      console.log(`  - ${cat.name} (ID: ${cat.id}): ${cat.course_count} 个课程`);
      
      // 显示子分类
      const children = subCats.filter(sub => sub.pid === cat.id);
      children.forEach(child => {
        console.log(`    └── ${child.name} (ID: ${child.id}): ${child.course_count} 个课程`);
      });
    });

    // 5. 检查导入任务状态
    console.log('\n📋 导入任务状态:');

    try {
      const [importTasks] = await sequelize.query(`
        SELECT
          filename,
          type,
          status,
          total_records,
          success_records,
          error_records,
          created_at
        FROM import_tasks
        ORDER BY created_at DESC
        LIMIT 10
      `);

      if (importTasks.length > 0) {
        importTasks.forEach(task => {
          const successRate = task.total_records > 0 ?
            ((task.success_records / task.total_records) * 100).toFixed(1) : 0;

          console.log(`  - ${task.filename} (${task.type}):`);
          console.log(`    状态: ${task.status}, 成功率: ${successRate}%`);
          console.log(`    记录: ${task.success_records}/${task.total_records} 成功, ${task.error_records} 失败`);
          console.log(`    时间: ${task.created_at}`);
          console.log('');
        });
      } else {
        console.log('  没有找到导入任务记录');
      }
    } catch (error) {
      console.log('  import_tasks表不存在，跳过导入任务检查');
    }

    // 6. 数据质量检查
    console.log('\n🔍 数据质量检查:');
    
    // 检查必填字段
    const [coursesWithoutTitle] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses WHERE title IS NULL OR title = ''
    `);
    
    const [unitsWithoutTitle] = await sequelize.query(`
      SELECT COUNT(*) as count FROM course_units WHERE title IS NULL OR title = ''
    `);

    console.log(`  - 无标题的课程: ${coursesWithoutTitle[0].count}`);
    console.log(`  - 无标题的课程单元: ${unitsWithoutTitle[0].count}`);

    // 检查重复数据
    const [duplicateCourses] = await sequelize.query(`
      SELECT title, COUNT(*) as count
      FROM courses
      GROUP BY title
      HAVING COUNT(*) > 1
      LIMIT 5
    `);

    if (duplicateCourses.length > 0) {
      console.log('⚠️  发现重复的课程标题:');
      duplicateCourses.forEach(course => {
        console.log(`    - "${course.title}": ${course.count} 个重复`);
      });
    } else {
      console.log('✅ 没有发现重复的课程标题');
    }

    // 7. 总结验证结果
    console.log('\n📊 验证结果总结:');
    
    const totalIssues = coursesWithoutCategory[0].count + 
                       invalidCategories[0].count + 
                       orphanUnits[0].count + 
                       invalidHierarchy[0].count +
                       coursesWithoutTitle[0].count +
                       unitsWithoutTitle[0].count;

    if (totalIssues === 0) {
      console.log('✅ 数据导入完全成功！所有数据关联正确，没有发现问题。');
    } else {
      console.log(`⚠️  发现 ${totalIssues} 个数据问题，需要检查和修复：`);
      
      if (coursesWithoutCategory[0].count > 0) {
        console.log(`  - ${coursesWithoutCategory[0].count} 个课程没有分类`);
      }
      if (invalidCategories[0].count > 0) {
        console.log(`  - ${invalidCategories[0].count} 个课程的分类ID无效`);
      }
      if (orphanUnits[0].count > 0) {
        console.log(`  - ${orphanUnits[0].count} 个课程单元是孤立的`);
      }
      if (invalidHierarchy[0].count > 0) {
        console.log(`  - ${invalidHierarchy[0].count} 个分类的父分类不存在`);
      }
      if (coursesWithoutTitle[0].count > 0) {
        console.log(`  - ${coursesWithoutTitle[0].count} 个课程没有标题`);
      }
      if (unitsWithoutTitle[0].count > 0) {
        console.log(`  - ${unitsWithoutTitle[0].count} 个课程单元没有标题`);
      }
    }

    // 8. 提供修复建议
    if (totalIssues > 0) {
      console.log('\n💡 修复建议:');
      console.log('1. 如果有数据问题，可以重新导入对应的数据文件');
      console.log('2. 确保导入顺序正确：分类 → 课程 → 课程单元');
      console.log('3. 检查原始数据文件中的字段映射是否正确');
      console.log('4. 运行数据修复脚本：node scripts/fix-data-relations.js');
    }

    console.log('\n✅ 验证完成！');

  } catch (error) {
    console.error('❌ 验证失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  verifyImport().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = verifyImport;
