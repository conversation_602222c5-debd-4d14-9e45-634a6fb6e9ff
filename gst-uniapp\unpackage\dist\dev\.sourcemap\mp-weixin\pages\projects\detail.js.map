{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/projects/detail.vue?b9a9", "webpack:///D:/gst/gst-uniapp/pages/projects/detail.vue?6421", "webpack:///D:/gst/gst-uniapp/pages/projects/detail.vue?4b90", "webpack:///D:/gst/gst-uniapp/pages/projects/detail.vue?ab1c", "uni-app:///pages/projects/detail.vue", "webpack:///D:/gst/gst-uniapp/pages/projects/detail.vue?b9c9", "webpack:///D:/gst/gst-uniapp/pages/projects/detail.vue?96ec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniLoadMore", "empty", "u_line_progress", "data", "tabCurrentIndex", "navList", "state", "text", "loadingType", "orderList", "current_page", "project_id", "specClass", "specList", "created", "uni", "title", "content", "success", "url", "onLoad", "methods", "loadData", "navItem", "item", "console", "page", "changeTab", "tabClick", "orderStateExp", "stateTipColor", "stateTip", "apiGetCourseList", "params", "id", "navToDetailPage", "link", "datas", "cid", "token", "toggleSpec", "setTimeout", "starttimes", "endtimes", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2FvnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;UACA;YACAH;cACAI;YACA;UACA;YACA;AACA;AACA;UAFA;QAIA;MACA;IACA;EAEA;EACAC;IACA;AACA;AACA;AACA;IACA;IACA;IAKA;MACA;IACA;EAGA;EAEAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UACAC;UACA;UACA;YACA;YACA;UACA;UACA;QACA;QACA;QACAC;QACAhB;UACAc;QACA;QACA;QACA;;QAEA;QACAA;QAEAG;QACA;MACA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAC;MACA;QACA;UACAC;UAAA;QACA;UACAA;UAAA;QACA;UACAA;UAAA;QACA;UACAA;UACAD;UACA;;QAEA;MAAA;;MAEA;QAAAC;QAAAD;MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACAE;MACA;QACAC;UACAC;UACAR;QACA;MACA;QACA;MACA;IACA;IACA;IACAS;MACA;MACA;MACA;MACAV;MACA;MACA;MACA;;MAEA;QACAW;QACA;QACA;QACAC;UACAH;UACAI;UACAC;QACA;MACA;MACAxB;QACAI;MACA;IACA;IACA;IACAqB;MAAA;MAEA;QACA;QACAC;UACA;QACA;MACA;QAEA;UACA;UACAC;UACA;UACA;UACA;UACAC;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACA5B;cACAC;cACA4B;YACA;YACA;UACA;UACA;YACA7B;cACAC;cACA4B;YACA;YACA;UACA;UACA;UACAnB;UACA;QACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnWA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/projects/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/projects/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=1026391d&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/projects/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=1026391d&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.navList, function (tabItem, tabIndex) {\n    var $orig = _vm.__get_orig(tabItem)\n    var g0 = tabItem.loaded === true && tabItem.orderList.length === 0\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"navbar\">\r\n\t\t\t<view \r\n\t\t\t\tv-for=\"(item, index) in navList\" :key=\"index\" \r\n\t\t\t\tclass=\"nav-item\" \r\n\t\t\t\t:class=\"{current: tabCurrentIndex === index}\"\r\n\t\t\t\t@click=\"tabClick(index)\"\r\n\t\t\t>\r\n\t\t\t\t{{item.text}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<swiper :current=\"tabCurrentIndex\" class=\"swiper-box\" duration=\"300\" @change=\"changeTab\">\r\n\t\t\t<swiper-item class=\"tab-content\" v-for=\"(tabItem,tabIndex) in navList\" :key=\"tabIndex\">\r\n\t\t\t\t<scroll-view \r\n\t\t\t\t\tclass=\"list-scroll-content\" \r\n\t\t\t\t\tscroll-y\r\n\t\t\t\t\t@scrolltolower=\"loadData\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<!-- 空白页 -->\r\n\t\t\t\t\t<empty v-if=\"tabItem.loaded === true && tabItem.orderList.length === 0\"></empty>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 订单列表 -->\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(item,index) in tabItem.orderList\" :key=\"index\"\r\n\t\t\t\t\t\tclass=\"order-item\"  @tap=\"toggleSpec(item)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"i-top b-b\">\r\n\t\t\t\t\t\t\t<text class=\"time\">{{item.start_time}}</text>\r\n\t\t\t\t\t\t\t<text class=\"state\" :style=\"{color: item.stateTipColor}\">{{item.stateTip}}</text>\r\n\t\t\t\t\t\t\t<!-- <text \r\n\t\t\t\t\t\t\t\tv-if=\"item.state===9\" \r\n\t\t\t\t\t\t\t\tclass=\"del-btn yticon icon-iconfontshanchu1\"\r\n\t\t\t\t\t\t\t\t@click=\"deleteOrder(index)\"\r\n\t\t\t\t\t\t\t></text> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"goods-box-single\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<!-- <image class=\"goods-img\" :src=\"goodsItem.image\" mode=\"aspectFill\"></image> -->\r\n\t\t\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t\t\t<text class=\"title clamp\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t<!-- <text class=\"attr-box\">{{goodsItem.attr}}  x {{goodsItem.number}}</text> -->\r\n\t\t\t\t\t\t\t\t<!-- <text class=\"price\">{{goodsItem.price}}</text> -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"price-box\">\r\n\t\t\t\t\t\t\t<view class=\"uni-row align-center\">\r\n\t\t\t\t\t\t\t\t<text class=\"text-sm\">观看进度：</text>\r\n\t\t\t\t\t\t\t\t<u_line_progress inactive-color=\"#e6eaf2\" active-color=\"#19be6b\"\r\n\t\t\t\t\t\t\t\t\t:percent=\"item.progress\" height=\"20\"></u_line_progress>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-box b-t\" >\r\n\t\t\t\t\t\t\t<!-- <button class=\"action-btn\" @click=\"cancelOrder(item)\">取消订单</button> -->\r\n\t\t\t\t\t\t\t<button class=\"action-btn recom\">立即学习</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t \r\n\t\t\t\t\t<uni-load-more :status=\"tabItem.loadingType\"></uni-load-more>\r\n\t\t\t\t\t\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view\r\n\t\t\tclass=\"popup spec\" \r\n\t\t\t:class=\"specClass\"\r\n\t\t\************************=\"stopPrevent\"\r\n\t\t\t@click=\"toggleSpec\"\r\n\t\t>\r\n\t\t\t<!-- 遮罩层 -->\r\n\t\t\t<view class=\"mask\"></view>\r\n\t\t\t<view class=\"layer attr-content\" @click.stop=\"stopPrevent\">\r\n\t\t\t\t<view style=\"padding: 20rpx;color: #999999;font-size: 25rpx; background-color: #f1f1f1;text-align: center;border-radius: 20rpx 20rpx 0 0; \">\r\n\t\t\t\t\t<view style=\"font-weight: bold;color: #000;font-size: 35rpx;\">课程目录</view>\r\n\t\t\t\t\t<view>(点击课程名称进入学习)</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-for=\"(item,index) in specList.list\" :key=\"index\" class=\"attr-list\" style=\"padding: 20rpx \">\r\n\t\t\t\t\t<text style=\"padding: 20rpx;font-size: 30rpx; \" @click=\"navToDetailPage(item)\">任务{{index+1}}:{{item.title}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template> \r\n\r\n<script>\r\n\timport uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';\r\n\timport empty from \"@/components/empty\";\r\n\timport Json from '@/Json';\r\n\timport u_line_progress from '@/components/uview-ui/components/u-line-progress/u-line-progress.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniLoadMore,\r\n\t\t\tempty,\r\n\t\t\tu_line_progress\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabCurrentIndex: 0,\r\n\t\t\t\tnavList: [{\r\n\t\t\t\t\t\tstate: 0,\r\n\t\t\t\t\t\ttext: '全部',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page:1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \tstate: 1,\r\n\t\t\t\t\t// \ttext: '待完成',\r\n\t\t\t\t\t// \tloadingType: 'more',\r\n\t\t\t\t\t// \torderList: [],\r\n\t\t\t\t\t// \tcurrent_page:1\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstate: 3,\r\n\t\t\t\t\t\ttext: '已完成',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page:1\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tproject_id:1,\r\n\t\t\t\tspecClass: 'none',\r\n\t\t\t\tspecList:{}\r\n\t\t\t\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/login/login\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\r\n\t\t},\r\n\t\tonLoad(options){\r\n\t\t\t/**\r\n\t\t\t * 修复app端点击除全部订单外的按钮进入时不加载数据的问题\r\n\t\t\t * 替换onLoad下代码即可\r\n\t\t\t */\r\n\t\t\tthis.project_id = options.project_id\r\n\t\t\tthis.tabCurrentIndex = +options.state;\r\n\t\t\t// #ifndef MP\r\n\t\t\tthis.loadData()\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP\r\n\t\t\tif(options.state == 0){\r\n\t\t\t\tthis.loadData()\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t},\r\n\t\t \r\n\t\tmethods: {\r\n\t\t\t//获取订单列表\r\n\t\t\tloadData(source){\r\n\t\t\t\t//这里是将订单挂载到tab列表下\r\n\t\t\t\tlet index = this.tabCurrentIndex;\r\n\t\t\t\tlet navItem = this.navList[index];\r\n\t\t\t\tlet state = navItem.state;\r\n\t\t\t\tlet page = navItem.current_page;\r\n\t\t\t\t\r\n\t\t\t\tif(source === 'tabChange' && navItem.loaded === true){\r\n\t\t\t\t\t//tab切换只有第一次需要加载数据\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif(navItem.loadingType === 'loading'){\r\n\t\t\t\t\t//防止重复加载\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tnavItem.loadingType = 'loading';\r\n\t\t\t\tthis.apiGetCourseList(this.project_id,page).then(pagination => {\r\n\t\t\t\t\tthis.empty = false;\r\n\t\t\t\t\tthis.current_page = pagination.current_page;\r\n\t\t\t\t\tthis.total_page = pagination.last_page;\r\n\t\t\t\t\tthis.total = pagination.total;\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tlet orderList = pagination.data.filter(item=>{\r\n\t\t\t\t\t\t//添加不同状态下订单的表现形式\r\n\t\t\t\t\t\titem = Object.assign(item, this.orderStateExp(item.state));\r\n\t\t\t\t\t\t//演示数据所以自己进行状态筛选\r\n\t\t\t\t\t\tif(state === 0){\r\n\t\t\t\t\t\t\t//0为全部订单\r\n\t\t\t\t\t\t\treturn item;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn item.state === state\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//let orderList = pagination.data;\r\n\t\t\t\t\tconsole.log(orderList)\r\n\t\t\t\t\torderList.forEach(item=>{\r\n\t\t\t\t\t\tnavItem.orderList.push(item);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页\r\n\t\t\t\t\tthis.$set(navItem, 'loaded', true);\r\n\t\t\t\t\t\r\n\t\t\t\t\t//判断是否还有数据， 有改为 more， 没有改为noMore \r\n\t\t\t\t\tnavItem.loadingType = 'noMore';\r\n\t\t\t\t\t\r\n\t\t\t\t\tpage++;\r\n\t\t\t\t\tthis.navList[index].current_page = page;\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t// \tlet orderList = Json.orderList.filter(item=>{\r\n\t\t\t\t// \t\t//添加不同状态下订单的表现形式\r\n\t\t\t\t// \t\titem = Object.assign(item, this.orderStateExp(item.state));\r\n\t\t\t\t// \t\t//演示数据所以自己进行状态筛选\r\n\t\t\t\t// \t\tif(state === 0){\r\n\t\t\t\t// \t\t\t//0为全部订单\r\n\t\t\t\t// \t\t\treturn item;\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t\treturn item.state === state\r\n\t\t\t\t// \t});\r\n\t\t\t\t// \torderList.forEach(item=>{\r\n\t\t\t\t// \t\tnavItem.orderList.push(item);\r\n\t\t\t\t// \t})\r\n\t\t\t\t// \t//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页\r\n\t\t\t\t// \tthis.$set(navItem, 'loaded', true);\r\n\t\t\t\t\t\r\n\t\t\t\t// \t//判断是否还有数据， 有改为 more， 没有改为noMore \r\n\t\t\t\t// \tnavItem.loadingType = 'more';\r\n\t\t\t\t// }, 600);\t\r\n\t\t\t}, \r\n\r\n\t\t\t//swiper 切换\r\n\t\t\tchangeTab(e){\r\n\t\t\t\tthis.tabCurrentIndex = e.target.current;\r\n\t\t\t\tthis.loadData('tabChange');\r\n\t\t\t},\r\n\t\t\t//顶部tab点击\r\n\t\t\ttabClick(index){\r\n\t\t\t\tthis.tabCurrentIndex = index;\r\n\t\t\t},\r\n\t\t\r\n\t\t\t//订单状态文字和颜色\r\n\t\t\torderStateExp(state){\r\n\t\t\t\tlet stateTip = '',\r\n\t\t\t\t\tstateTipColor = '#fa436a';\r\n\t\t\t\tswitch(+state){\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\tstateTip = '已完成'; break;\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\tstateTip = '待发货'; break;\r\n\t\t\t\t\tcase 3:\r\n\t\t\t\t\t\tstateTip = '已完成'; break;\r\n\t\t\t\t\tcase 9:\r\n\t\t\t\t\t\tstateTip = '订单已关闭'; \r\n\t\t\t\t\t\tstateTipColor = '#909399';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t//更多自定义\r\n\t\t\t\t}\r\n\t\t\t\treturn {stateTip, stateTipColor};\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tapiGetCourseList: function(id,page) {\r\n\t\t\t\treturn this.$http.get('/v1/member/projectDetail', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid,\r\n\t\t\t\t\t\tpage\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情页\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tlet cid = this.specList.id\r\n\t\t\t\tconsole.log(id)\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t// \turl: `/pages/projects/course?id=${cid}&cid=${id}`\r\n\t\t\t\t// })\r\n\t\t\t\t\r\n\t\t\t\tlet obj = {\r\n\t\t\t\t\tlink:'https://practice.jpworld.cn/course/',\r\n\t\t\t\t\t// link:'https://skytest2.utools.club/signup/',\r\n\t\t\t\t\t// haveData: true,\r\n\t\t\t\t\tdatas: JSON.stringify({\r\n\t\t\t\t\t\tid: cid,\r\n\t\t\t\t\t\tcid: id,\r\n\t\t\t\t\t\ttoken: this.$store.state.user.token\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/upOpus/webUpOpus?data=\" + encodeURIComponent(JSON.stringify(obj))\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//规格弹窗开关\r\n\t\t\ttoggleSpec(item) {\r\n\t\t\t\t\r\n\t\t\t\tif(this.specClass === 'show'){\r\n\t\t\t\t\tthis.specClass = 'hide';\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.specClass = 'none';\r\n\t\t\t\t\t}, 250);\r\n\t\t\t\t}else if(this.specClass === 'none'){\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(item){\r\n\t\t\t\t\t\tlet starttimes = item.start_time;\r\n\t\t\t\t\t\tstarttimes = starttimes.replace(/-/g,'/')\r\n\t\t\t\t\t\tlet startDt = new Date(starttimes);\r\n\t\t\t\t\t\tlet startTime = startDt.getTime();\r\n\t\t\t\t\t\tlet endtimes = item.end_time;\r\n\t\t\t\t\t\tendtimes = endtimes.replace(/-/g,'/')\r\n\t\t\t\t\t\tlet endDt = new Date(endtimes);\r\n\t\t\t\t\t\tlet endTime = endDt.getTime();\r\n\t\t\t\t\t\tlet newDt = new Date();\r\n\t\t\t\t\t\tlet newTime = newDt.getTime();\r\n\t\t\t\t\t\tlet starttime = startTime-newTime;\r\n\t\t\t\t\t\tlet endtime = endTime-newTime;\r\n\t\t\t\t\t\tif(starttime>0){\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"课程还没有开始！\",\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(endtime<0){\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"课程已结束！\",\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.specList = item\r\n\t\t\t\t\t\tconsole.log(this.specList)\r\n\t\t\t\t\t\tthis.specClass = 'show';\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage, .content{\r\n\t\tbackground: $page-color-base;\r\n\t\theight: 100%;\r\n\t}\r\n\t\r\n\t.swiper-box{\r\n\t\theight: calc(100% - 40px);\r\n\t}\r\n\t.list-scroll-content{\r\n\t\theight: 100%;\r\n\t}\r\n\t\r\n\t.navbar{\r\n\t\tdisplay: flex;\r\n\t\theight: 40px;\r\n\t\tpadding: 0 5px;\r\n\t\tbackground: #fff;\r\n\t\tbox-shadow: 0 1px 5px rgba(0,0,0,.06);\r\n\t\tposition: relative;\r\n\t\tz-index: 10;\r\n\t\t.nav-item{\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\t\t\t&.current{\r\n\t\t\t\tcolor: $base-color;\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 44px;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-bottom: 2px solid $base-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.uni-swiper-item{\r\n\t\theight: auto;\r\n\t}\r\n\t.order-item{\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding-left: 30upx;\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 16upx;\r\n\t\t.i-top{\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 80upx;\r\n\t\t\tpadding-right:30upx;\r\n\t\t\tfont-size: $font-base;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\t\t\t.time{\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\t\t\t.state{\r\n\t\t\t\tcolor: $base-color;\r\n\t\t\t}\r\n\t\t\t.del-btn{\r\n\t\t\t\tpadding: 10upx 0 10upx 36upx;\r\n\t\t\t\tfont-size: $font-lg;\r\n\t\t\t\tcolor: $font-color-light;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 0;\r\n\t\t\t\t\theight: 30upx;\r\n\t\t\t\t\tborder-left: 1px solid $border-color-dark;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 20upx;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t/* 多条商品 */\r\n\t\t.goods-box{\r\n\t\t\theight: 160upx;\r\n\t\t\tpadding: 20upx 0;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\t.goods-item{\r\n\t\t\t\twidth: 120upx;\r\n\t\t\t\theight: 120upx;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tmargin-right: 24upx;\r\n\t\t\t}\r\n\t\t\t.goods-img{\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t/* 单条商品 */\r\n\t\t.goods-box-single{\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 20upx 0;\r\n\t\t\t.goods-img{\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 120upx;\r\n\t\t\t\theight: 120upx;\r\n\t\t\t}\r\n\t\t\t.right{\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tpadding: 0 30upx 0 24upx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t.title{\r\n\t\t\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t}\r\n\t\t\t\t.attr-box{\r\n\t\t\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\t\t\tcolor: $font-color-light;\r\n\t\t\t\t\tpadding: 10upx 12upx;\r\n\t\t\t\t}\r\n\t\t\t\t.price{\r\n\t\t\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t\t\t&:before{\r\n\t\t\t\t\t\tcontent: '￥';\r\n\t\t\t\t\t\tfont-size: $font-sm;\r\n\t\t\t\t\t\tmargin: 0 2upx 0 8upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.price-box{\r\n\t\t\tdisplay: flex;\r\n\t\t\t// justify-content: flex-end;\r\n\t\t\talign-items: baseline;\r\n\t\t\tpadding: 20upx 30upx;\r\n\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\tcolor: $font-color-light;\r\n\t\t\t.num{\r\n\t\t\t\tmargin: 0 8upx;\r\n\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t}\r\n\t\t\t.price{\r\n\t\t\t\tfont-size: $font-lg;\r\n\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t\t&:before{\r\n\t\t\t\t\tcontent: '￥';\r\n\t\t\t\t\tfont-size: $font-sm;\r\n\t\t\t\t\tmargin: 0 2upx 0 8upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.action-box{\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100upx;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding-right: 30upx;\r\n\t\t}\r\n\t\t.action-btn{\r\n\t\t\twidth: 160upx;\r\n\t\t\theight: 60upx;\r\n\t\t\tmargin: 0;\r\n\t\t\tmargin-left: 24upx;\r\n\t\t\tpadding: 0;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 60upx;\r\n\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 100px;\r\n\t\t\t&:after{\r\n\t\t\t\tborder-radius: 100px;\r\n\t\t\t}\r\n\t\t\t&.recom{\r\n\t\t\t\tbackground: #fff9f9;\r\n\t\t\t\tcolor: $base-color;\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tborder-color: #f7bcc8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t\r\n\t/* load-more */\r\n\t.uni-load-more {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\theight: 80upx;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center\r\n\t}\r\n\t\r\n\t.uni-load-more__text {\r\n\t\tfont-size: 28upx;\r\n\t\tcolor: #999\r\n\t}\r\n\t\r\n\t.uni-load-more__img {\r\n\t\theight: 24px;\r\n\t\twidth: 24px;\r\n\t\tmargin-right: 10px\r\n\t}\r\n\t\r\n\t.uni-load-more__img>view {\r\n\t\tposition: absolute\r\n\t}\r\n\t\r\n\t.uni-load-more__img>view view {\r\n\t\twidth: 6px;\r\n\t\theight: 2px;\r\n\t\tborder-top-left-radius: 1px;\r\n\t\tborder-bottom-left-radius: 1px;\r\n\t\tbackground: #999;\r\n\t\tposition: absolute;\r\n\t\topacity: .2;\r\n\t\ttransform-origin: 50%;\r\n\t\tanimation: load 1.56s ease infinite\r\n\t}\r\n\t\r\n\t.uni-load-more__img>view view:nth-child(1) {\r\n\t\ttransform: rotate(90deg);\r\n\t\ttop: 2px;\r\n\t\tleft: 9px\r\n\t}\r\n\t\r\n\t.uni-load-more__img>view view:nth-child(2) {\r\n\t\ttransform: rotate(180deg);\r\n\t\ttop: 11px;\r\n\t\tright: 0\r\n\t}\r\n\t\r\n\t.uni-load-more__img>view view:nth-child(3) {\r\n\t\ttransform: rotate(270deg);\r\n\t\tbottom: 2px;\r\n\t\tleft: 9px\r\n\t}\r\n\t\r\n\t.uni-load-more__img>view view:nth-child(4) {\r\n\t\ttop: 11px;\r\n\t\tleft: 0\r\n\t}\r\n\t\r\n\t.load1,\r\n\t.load2,\r\n\t.load3 {\r\n\t\theight: 24px;\r\n\t\twidth: 24px\r\n\t}\r\n\t\r\n\t.load2 {\r\n\t\ttransform: rotate(30deg)\r\n\t}\r\n\t\r\n\t.load3 {\r\n\t\ttransform: rotate(60deg)\r\n\t}\r\n\t\r\n\t.load1 view:nth-child(1) {\r\n\t\tanimation-delay: 0s\r\n\t}\r\n\t\r\n\t.load2 view:nth-child(1) {\r\n\t\tanimation-delay: .13s\r\n\t}\r\n\t\r\n\t.load3 view:nth-child(1) {\r\n\t\tanimation-delay: .26s\r\n\t}\r\n\t\r\n\t.load1 view:nth-child(2) {\r\n\t\tanimation-delay: .39s\r\n\t}\r\n\t\r\n\t.load2 view:nth-child(2) {\r\n\t\tanimation-delay: .52s\r\n\t}\r\n\t\r\n\t.load3 view:nth-child(2) {\r\n\t\tanimation-delay: .65s\r\n\t}\r\n\t\r\n\t.load1 view:nth-child(3) {\r\n\t\tanimation-delay: .78s\r\n\t}\r\n\t\r\n\t.load2 view:nth-child(3) {\r\n\t\tanimation-delay: .91s\r\n\t}\r\n\t\r\n\t.load3 view:nth-child(3) {\r\n\t\tanimation-delay: 1.04s\r\n\t}\r\n\t\r\n\t.load1 view:nth-child(4) {\r\n\t\tanimation-delay: 1.17s\r\n\t}\r\n\t\r\n\t.load2 view:nth-child(4) {\r\n\t\tanimation-delay: 1.3s\r\n\t}\r\n\t\r\n\t.load3 view:nth-child(4) {\r\n\t\tanimation-delay: 1.43s\r\n\t}\r\n\t\r\n\t@-webkit-keyframes load {\r\n\t\t0% {\r\n\t\t\topacity: 1\r\n\t\t}\r\n\t\r\n\t\t100% {\r\n\t\t\topacity: .2\r\n\t\t}\r\n\t}\r\n\t/*  弹出层 */\r\n\t.popup {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 99;\r\n\t\t\r\n\t\t&.show {\r\n\t\t\tdisplay: block;\r\n\t\t\t.mask{\r\n\t\t\t\tanimation: showPopup 0.2s linear both;\r\n\t\t\t}\r\n\t\t\t.layer {\r\n\t\t\t\tanimation: showLayer 0.2s linear both;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.hide {\r\n\t\t\t.mask{\r\n\t\t\t\tanimation: hidePopup 0.2s linear both;\r\n\t\t\t}\r\n\t\t\t.layer {\r\n\t\t\t\tanimation: hideLayer 0.2s linear both;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.none {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t\t.mask{\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tz-index: 1;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\t\t}\r\n\t\t.layer {\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 99;\r\n\t\t\ttop: 20%;\r\n\t\t\twidth: 80%;\r\n\t\t\tmargin-left: 10%;\r\n\t\t\tmin-height: 10vh;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t.btn{\r\n\t\t\t\theight: 66upx;\r\n\t\t\t\tline-height: 66upx;\r\n\t\t\t\tborder-radius: 100upx;\r\n\t\t\t\tbackground: $uni-color-primary;\r\n\t\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin: 30upx auto 20upx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes showPopup {\r\n\t\t\t0% {\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes hidePopup {\r\n\t\t\t0% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes showLayer {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: translateY(120%);\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: translateY(0%);\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes hideLayer {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: translateY(0);\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: translateY(120%);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.uni-row {\r\n\t\tflex-direction: row;\r\n\t\tflex: 1;\r\n\t}\r\n\t.text-sm {\r\n\t\tfont-size: 24upx;\r\n\t}\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689563841\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}