<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${group?.name} - 课程管理`"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div class="group-courses-container">
      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button @click="loadGroupCourses" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="primary" @click="showAddCourseDialog = true">
            <el-icon><Plus /></el-icon>
            添加课程
          </el-button>
          <el-button @click="batchRemoveCourses" :disabled="selectedCourses.length === 0">
            <el-icon><Delete /></el-icon>
            批量移除
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索课程"
            clearable
            style="width: 200px"
            @input="filterCourses"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 课程列表 -->
      <div class="courses-section">
        <h3>已分配课程 ({{ filteredCourses.length }})</h3>
        <el-table
          :data="filteredCourses"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="title" label="课程名称" min-width="200">
            <template #default="{ row }">
              <div class="course-info">
                <img 
                  v-if="row.picture" 
                  :src="row.picture" 
                  class="course-image"
                  @error="handleImageError"
                />
                <div class="course-placeholder" v-else>
                  <el-icon><Document /></el-icon>
                </div>
                <div class="course-details">
                  <div class="course-title">{{ row.title }}</div>
                  <div class="course-meta">
                    <el-tag size="small" :type="getLevelType(row.level)">{{ row.level }}</el-tag>
                    <span class="course-category">{{ row.category }}</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="100" align="right">
            <template #default="{ row }">
              <span class="price">¥{{ row.price || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderNum" label="排序" width="80" align="center">
            <template #default="{ row }">
              <el-input-number
                v-model="row.orderNum"
                :min="0"
                :max="999"
                size="small"
                @change="updateCourseOrder(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-select
                v-model="row.status"
                size="small"
                @change="updateCourseStatus(row)"
              >
                <el-option label="待开始" value="pending" />
                <el-option label="进行中" value="active" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="isRequired" label="必修" width="80" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.isRequired"
                @change="updateCourseRequired(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="开始时间" width="120">
            <template #default="{ row }">
              <el-date-picker
                v-model="row.startDate"
                type="date"
                size="small"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="updateCourseDate(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="endDate" label="结束时间" width="120">
            <template #default="{ row }">
              <el-date-picker
                v-model="row.endDate"
                type="date"
                size="small"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="updateCourseDate(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button link size="small" @click="viewCourseUnits(row)">
                <el-icon><List /></el-icon>
                单元
              </el-button>
              <el-button link size="small" @click="removeCourse(row)" class="danger-button">
                <el-icon><Delete /></el-icon>
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-empty 
          v-if="!loading && filteredCourses.length === 0" 
          description="暂无课程"
          :image-size="100"
        >
          <el-button type="primary" @click="showAddCourseDialog = true">
            添加第一个课程
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 添加课程对话框 -->
    <el-dialog
      v-model="showAddCourseDialog"
      title="添加课程到小组"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="add-course-content">
        <el-form :model="addCourseForm" label-width="80px">
          <el-form-item label="选择课程">
            <el-select
              v-model="addCourseForm.courseIds"
              multiple
              placeholder="请选择要添加的课程"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="course in availableCourses"
                :key="course.id"
                :label="course.title"
                :value="course.id"
              >
                <div class="course-option">
                  <span class="course-title">{{ course.title }}</span>
                  <el-tag size="small" :type="getLevelType(course.level)">{{ course.level }}</el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否必修">
            <el-switch v-model="addCourseForm.isRequired" />
          </el-form-item>
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="addCourseForm.startDate"
              type="date"
              placeholder="选择开始时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="addCourseForm.endDate"
              type="date"
              placeholder="选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showAddCourseDialog = false">取消</el-button>
        <el-button type="primary" @click="addCoursesToGroup" :loading="adding">
          确定添加
        </el-button>
      </template>
    </el-dialog>

    <!-- 课程单元对话框 -->
    <el-dialog
      v-model="showUnitsDialog"
      :title="`${selectedCourse?.title} - 课程单元`"
      width="800px"
    >
      <div class="course-units" v-loading="unitsLoading">
        <div 
          v-for="unit in courseUnits" 
          :key="unit.id"
          class="unit-item"
        >
          <div class="unit-info">
            <div class="unit-title">{{ unit.title }}</div>
            <div class="unit-meta">
              <span class="unit-duration">{{ unit.duration }}分钟</span>
              <el-tag size="small" :type="unit.status === 'published' ? 'success' : 'warning'">
                {{ unit.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </div>
          </div>
          <div class="unit-actions">
            <el-button link size="small" v-if="unit.videoUrl">
              <el-icon><VideoPlay /></el-icon>
              播放
            </el-button>
          </div>
        </div>
        
        <el-empty 
          v-if="!unitsLoading && courseUnits.length === 0" 
          description="暂无课程单元"
          :image-size="80"
        />
      </div>
    </el-dialog>

    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Plus, Delete, Search, Document, List, VideoPlay
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: Boolean,
  group: Object
})

// Emits
const emit = defineEmits(['update:modelValue'])

// Stores
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const adding = ref(false)
const unitsLoading = ref(false)
const showAddCourseDialog = ref(false)
const showUnitsDialog = ref(false)
const searchKeyword = ref('')
const selectedCourses = ref([])
const selectedCourse = ref(null)
const groupCourses = ref([])
const availableCourses = ref([])
const courseUnits = ref([])

// 添加课程表单
const addCourseForm = reactive({
  courseIds: [],
  isRequired: true,
  startDate: '',
  endDate: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredCourses = computed(() => {
  if (!searchKeyword.value) return groupCourses.value
  return groupCourses.value.filter(course =>
    course.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 监听对话框显示
watch(dialogVisible, (visible) => {
  if (visible && props.group) {
    loadGroupCourses()
    loadAvailableCourses()
  }
})

// 加载小组课程
const loadGroupCourses = async () => {
  if (!props.group) return
  
  loading.value = true
  try {
    const response = await get(`/api/groups/${props.group.id}/courses`)
    if (response.success) {
      groupCourses.value = response.data.courses || []
    }
  } catch (error) {
    console.error('加载小组课程失败:', error)
    ElMessage.error('加载小组课程失败')
    
    // 使用模拟数据
    groupCourses.value = [
      {
        id: 1,
        title: 'N5基础日语',
        level: 'N5',
        category: 'grammar',
        price: 199,
        picture: '',
        orderNum: 1,
        status: 'active',
        isRequired: true,
        startDate: '2024-01-15',
        endDate: '2024-03-15'
      },
      {
        id: 2,
        title: 'N5词汇训练',
        level: 'N5',
        category: 'vocabulary',
        price: 159,
        picture: '',
        orderNum: 2,
        status: 'pending',
        isRequired: true,
        startDate: '2024-02-01',
        endDate: '2024-04-01'
      }
    ]
  } finally {
    loading.value = false
  }
}

// 加载可用课程
const loadAvailableCourses = async () => {
  try {
    const response = await get('/api/courses', {
      status: 'published',
      limit: 100
    })
    if (response.success) {
      availableCourses.value = response.data.courses || []
    }
  } catch (error) {
    console.error('加载可用课程失败:', error)
    // 使用模拟数据
    availableCourses.value = [
      { id: 3, title: 'N4进阶语法', level: 'N4' },
      { id: 4, title: 'N4听力训练', level: 'N4' },
      { id: 5, title: 'N3中级日语', level: 'N3' }
    ]
  }
}

// 工具函数
const getLevelType = (level) => {
  const types = {
    'N5': 'success',
    'N4': 'primary',
    'N3': 'warning',
    'N2': 'danger',
    'N1': 'info'
  }
  return types[level] || 'info'
}

const handleImageError = (e) => {
  e.target.style.display = 'none'
}

const handleSelectionChange = (selection) => {
  selectedCourses.value = selection
}

const filterCourses = () => {
  // 筛选逻辑已在计算属性中实现
}

// 课程操作
const addCoursesToGroup = async () => {
  if (addCourseForm.courseIds.length === 0) {
    ElMessage.warning('请选择要添加的课程')
    return
  }

  adding.value = true
  try {
    await post(`/api/groups/${props.group.id}/courses`, {
      courseIds: addCourseForm.courseIds,
      isRequired: addCourseForm.isRequired,
      startDate: addCourseForm.startDate,
      endDate: addCourseForm.endDate
    })
    
    ElMessage.success('课程添加成功')
    showAddCourseDialog.value = false
    
    // 重置表单
    Object.assign(addCourseForm, {
      courseIds: [],
      isRequired: true,
      startDate: '',
      endDate: ''
    })
    
    await loadGroupCourses()
  } catch (error) {
    console.error('添加课程失败:', error)
    ElMessage.error('添加课程失败')
  } finally {
    adding.value = false
  }
}

const removeCourse = async (course) => {
  try {
    await ElMessageBox.confirm(`确定要从小组中移除课程"${course.title}"吗？`, '确认移除', {
      type: 'warning'
    })
    
    await del(`/api/groups/${props.group.id}/courses/${course.id}`)
    ElMessage.success('课程移除成功')
    await loadGroupCourses()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除课程失败:', error)
      ElMessage.error('移除课程失败')
    }
  }
}

const batchRemoveCourses = async () => {
  if (selectedCourses.value.length === 0) {
    ElMessage.warning('请选择要移除的课程')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要移除选中的 ${selectedCourses.value.length} 个课程吗？`, '确认批量移除', {
      type: 'warning'
    })
    
    const courseIds = selectedCourses.value.map(course => course.id)
    await del(`/api/groups/${props.group.id}/courses/batch`, { courseIds })
    
    ElMessage.success('课程批量移除成功')
    selectedCourses.value = []
    await loadGroupCourses()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量移除课程失败:', error)
      ElMessage.error('批量移除课程失败')
    }
  }
}

const updateCourseOrder = async (course) => {
  try {
    await put(`/api/groups/${props.group.id}/courses/${course.id}`, {
      orderNum: course.orderNum
    })
    ElMessage.success('排序更新成功')
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('更新排序失败')
  }
}

const updateCourseStatus = async (course) => {
  try {
    await put(`/api/groups/${props.group.id}/courses/${course.id}`, {
      status: course.status
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  }
}

const updateCourseRequired = async (course) => {
  try {
    await put(`/api/groups/${props.group.id}/courses/${course.id}`, {
      isRequired: course.isRequired
    })
    ElMessage.success('必修状态更新成功')
  } catch (error) {
    console.error('更新必修状态失败:', error)
    ElMessage.error('更新必修状态失败')
  }
}

const updateCourseDate = async (course) => {
  try {
    await put(`/api/groups/${props.group.id}/courses/${course.id}`, {
      startDate: course.startDate,
      endDate: course.endDate
    })
    ElMessage.success('时间更新成功')
  } catch (error) {
    console.error('更新时间失败:', error)
    ElMessage.error('更新时间失败')
  }
}

const viewCourseUnits = async (course) => {
  selectedCourse.value = course
  showUnitsDialog.value = true
  
  unitsLoading.value = true
  try {
    const response = await get(`/api/course-units?courseId=${course.id}`)
    if (response.success) {
      courseUnits.value = response.data.units || []
    }
  } catch (error) {
    console.error('加载课程单元失败:', error)
    // 使用模拟数据
    courseUnits.value = [
      {
        id: 1,
        title: '第1课：五十音图',
        duration: 30,
        status: 'published',
        videoUrl: 'https://example.com/video1.mp4'
      },
      {
        id: 2,
        title: '第2课：基础问候',
        duration: 25,
        status: 'published',
        videoUrl: 'https://example.com/video2.mp4'
      }
    ]
  } finally {
    unitsLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.group-courses-container {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;

    .toolbar-left {
      display: flex;
      gap: 8px;
    }
  }

  .courses-section {
    h3 {
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
  }
}

.course-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .course-image {
    width: 50px;
    height: 35px;
    object-fit: cover;
    border-radius: 4px;
  }

  .course-placeholder {
    width: 50px;
    height: 35px;
    background: var(--el-fill-color-light);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
  }

  .course-details {
    flex: 1;

    .course-title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .course-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

.price {
  color: var(--el-color-danger);
  font-weight: 600;
}

.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}

.add-course-content {
  .course-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .course-title {
      flex: 1;
    }
  }
}

.course-units {
  .unit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    margin-bottom: 8px;

    .unit-info {
      flex: 1;

      .unit-title {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .unit-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: var(--el-text-color-secondary);

        .unit-duration {
          color: var(--el-color-primary);
        }
      }
    }

    .unit-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch !important;

    .toolbar-left {
      justify-content: center;
    }
  }

  .course-info {
    .course-image,
    .course-placeholder {
      width: 40px;
      height: 28px;
    }
  }
}
</style>
