(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/projects/course"],{

/***/ 287:
/*!**********************************************************************!*\
  !*** D:/gst/gst-uniapp/main.js?{"page":"pages%2Fprojects%2Fcourse"} ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _course = _interopRequireDefault(__webpack_require__(/*! ./pages/projects/course.vue */ 288));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_course.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 288:
/*!***************************************************!*\
  !*** D:/gst/gst-uniapp/pages/projects/course.vue ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./course.vue?vue&type=template&id=3b3be832&scoped=true& */ 289);
/* harmony import */ var _course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./course.vue?vue&type=script&lang=js& */ 291);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./course.vue?vue&type=style&index=0&id=3b3be832&lang=less&scoped=true& */ 296);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "3b3be832",
  null,
  false,
  _course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/projects/course.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 289:
/*!**********************************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/projects/course.vue?vue&type=template&id=3b3be832&scoped=true& ***!
  \**********************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=template&id=3b3be832&scoped=true& */ 290);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_template_id_3b3be832_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 290:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/projects/course.vue?vue&type=template&id=3b3be832&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 291:
/*!****************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/projects/course.vue?vue&type=script&lang=js& ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=script&lang=js& */ 292);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 292:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/projects/course.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _courses = __webpack_require__(/*! @/request/courses.js */ 293);
var _checkUserinfo = __webpack_require__(/*! @/request/checkUserinfo */ 295);
var uParse = function uParse() {
  Promise.all(/*! require.ensure | components/gaoyia-parse/parse */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/gaoyia-parse/parse")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/gaoyia-parse/parse.vue */ 613));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var innerAudioContext = uni.getBackgroundAudioManager();
var _default = {
  components: {
    uParse: uParse
  },
  data: function data() {
    return {
      kechengList: ['课程介绍', '目录'],
      btnnum: 0,
      currentTime: '',
      coursemenus: [],
      menuinfo: [],
      teacher: [],
      courses: [],
      comment: [],
      HOST_URL: uni.HOST_URL,
      videostate: 'video',
      durationTime: "",
      //音频的总时长
      coursehour: 0,
      // 课时
      audio_paused: true,
      audio_progress: 0,
      gaosi: false,
      t1: '',
      t2: '',
      //video计时器id
      t3: '',
      //audio计时器id
      videosrc: '',
      free_time: '',
      video_controls: true,
      videofullscreen: false,
      menuid: '',
      sonid: '',
      videoStudyTime: 0,
      //秒
      audioStudyTime: 0,
      //秒
      is_free: false,
      son_is_pay: false,
      uid: '',
      is_dingyue: false,
      videoContext: {},
      creditinfo: {},
      action: '',
      seckillinfo: {},
      pintuaninfo: {},
      project: {},
      videoId: 1
    };
  },
  onShareAppMessage: function onShareAppMessage(res) {
    var path = getCurrentPages();
    var path_share = path[0].$page.fullPath;
    var path_title = path[0].data.title;
    var userinfo = uni.getStorageSync('userinfo');
    var base_set = uni.getStorageSync('base_set');
    if (userinfo.uid == '' || !userinfo.uid) {
      uni.navigateTo({
        url: '../login/login'
      });
      return {
        title: '请先登录后再分享给好友',
        path: ''
      };
    } else {
      if (res.from === 'button') {}
      return {
        title: this.menuinfo.menuname,
        path: "".concat(path_share, "?pid=").concat(userinfo.uid),
        imageUrl: this.HOST_URL + this.menuinfo.thumb
      };
    }
  },
  onReady: function onReady(res) {
    this.videoContext = uni.createVideoContext('myVideo');
  },
  onLoad: function onLoad(e) {
    var _this = this;
    this.apiGetCourseList(e.id, e.cid).then(function (pagination) {
      console.log('pagination' + pagination.course);
      _this.project = pagination;
      _this.menuinfo = pagination.course[0];
      _this.videosrc = pagination.course[0].li[0].url;
      _this.videoId = pagination.course[0].li[0].id;
    });
  },
  onShow: function onShow() {
    var _this2 = this;
    //checkUserinfo()
    var audio = innerAudioContext;
    audio.onTimeUpdate(function (e) {
      _this2.durationTime = _this2.format(audio.duration);
      _this2.currentTime = _this2.format(audio.currentTime);
      var progress = (audio.currentTime / audio.duration).toFixed(2);
      _this2.audio_progress = progress * 100;
      // if(!this.is_free){
      // 	if(!this.son_is_pay){
      // 		if(audio.currentTime>=this.free_time){
      // 			audio.pause()
      // 			this.currentTime='00:00'
      // 			audio.seek(0)
      // 			this.audio_progress=0
      // 			uni.showModal({
      // 				title: '试听结束',
      // 				content: '需要解锁该课程吗?',
      // 				success: (res) =>{
      // 					var that=this
      // 					if (res.confirm) {
      // 						this.pay()
      // 					} else if (res.cancel) {
      // 						// that.currentTime='00:00'
      // 						// audio.seek(0)
      // 						// that.audio_progress=0
      // 					}
      // 				}
      // 			});
      // 		}
      // 	}
      // }
      // console.log(this.currentTime)
    });

    audio.onSeeked(function (e) {
      _this2.currentTime = _this2.format(audio.currentTime);
    });
    audio.onPlay(function (e) {
      _this2.videoContext.pause();
      _this2.audio_paused = false;
      _this2.gaosi_ani();
      _this2.gaosi = true;
      _this2.countAtime();
    });
    audio.onPause(function (e) {
      _this2.audio_paused = true;
      clearInterval(_this2.t1);
      clearInterval(_this2.t3);
      _this2.gaosi = false;
      _this2.saveAtime();
    });
    audio.onStop(function (e) {
      _this2.audio_paused = true;
      clearInterval(_this2.t1);
      clearInterval(_this2.t3);
      _this2.saveAtime();
    });
    audio.onEnded(function (e) {
      clearInterval(_this2.t1);
      clearInterval(_this2.t3);
      _this2.audio_paused = true;
      _this2.currentTime = '00:00';
      audio.seek(0);
      _this2.saveAtime();
    });
  },
  onUnload: function onUnload() {
    clearInterval(this.t1);
    clearInterval(this.t2);
    clearInterval(this.t3);
    this.menuid = '';
    this.sonid = '';
    this.saveAtime();
    this.saveVtime();
  },
  methods: {
    jsdetail: function jsdetail() {
      uni.navigateTo({
        url: '../tutor-introduced/tutor-introduced?tid=' + this.teacher.id
      });
    },
    get_creditinfo: function get_creditinfo(id, goodstype) {
      var _this3 = this;
      var BASE_URL = uni.BASE_URL;
      uni.request({
        url: BASE_URL + 'index/credit/creditinfo',
        data: {
          id: id,
          goodstype: goodstype
        },
        method: 'POST',
        success: function success(res) {
          console.log(res.data);
          _this3.creditinfo = res.data.data;
        },
        fail: function fail(res) {
          console.log(res.data);
        }
      });
    },
    get_seckillinfo: function get_seckillinfo(id, goodstype) {
      var _this4 = this;
      var BASE_URL = uni.BASE_URL;
      uni.request({
        url: BASE_URL + 'index/seckill/seckillinfo',
        data: {
          id: id,
          goodstype: goodstype
        },
        method: 'POST',
        success: function success(res) {
          console.log(res.data);
          _this4.seckillinfo = res.data.data;
        },
        fail: function fail(res) {
          console.log(res.data);
        }
      });
    },
    get_pintuaninfo: function get_pintuaninfo(id, goodstype) {
      var _this5 = this;
      var BASE_URL = uni.BASE_URL;
      uni.request({
        url: BASE_URL + 'index/pintuan/pintuaninfo',
        data: {
          id: id,
          goodstype: goodstype
        },
        method: 'POST',
        success: function success(res) {
          console.log(res.data);
          _this5.pintuaninfo = res.data.data;
        },
        fail: function fail(res) {
          console.log(res.data);
        }
      });
    },
    saveStudytime: function saveStudytime(studytime, media) {
      var BASE_URL = uni.BASE_URL;
      uni.request({
        url: BASE_URL + 'index/user/save_studytime',
        method: 'POST',
        data: {
          uid: this.uid,
          media: media,
          studytime: studytime
        },
        success: function success(res) {
          console.log(res.data);
        },
        fail: function fail(res) {
          console.log(res.data);
        }
      });
    },
    saveAtime: function saveAtime() {
      var atime = uni.getStorageSync('atime');
      var data = {};
      if (atime) {
        data.atime = atime.atime + this.audioStudyTime;
        data.nowtime = new Date().getTime();
        uni.setStorageSync('atime', data);
        this.saveStudytime(data.atime, 'audio');
      } else {
        data.atime = this.audioStudyTime;
        data.nowtime = new Date().getTime();
        uni.setStorageSync('atime', data);
        this.saveStudytime(data.atime, 'audio');
      }
    },
    saveVtime: function saveVtime() {
      var vtime = uni.getStorageSync('vtime');
      var data = {};
      if (vtime) {
        data.vtime = vtime.vtime + this.videoStudyTime;
        data.nowtime = new Date().getTime();
        uni.setStorageSync('vtime', data);
        this.saveStudytime(data.vtime, 'video');
      } else {
        data.vtime = this.videoStudyTime;
        data.nowtime = new Date().getTime();
        uni.setStorageSync('vtime', data);
        this.saveStudytime(data.vtime, 'video');
      }
    },
    countVtime: function countVtime() {
      var _this6 = this;
      this.t2 = setInterval(function () {
        _this6.videoStudyTime++;
        //console.log(this.videoStudyTime)
      }, 1000);
    },
    countAtime: function countAtime() {
      var _this7 = this;
      this.t3 = setInterval(function () {
        _this7.audioStudyTime++;
        //console.log(this.audioStudyTime)
      }, 1000);
    },
    dingyue: function dingyue() {
      var _this8 = this;
      var BASE_URL = uni.BASE_URL;
      uni.request({
        url: BASE_URL + 'index/courses/dingyue',
        method: 'POST',
        data: {
          uid: this.uid,
          media: this.menuinfo.media,
          menuid: this.menuid,
          goodstype: 'course'
        },
        success: function success(res) {
          console.log(res.data);
          _this8.is_dingyue = res.data.data == 1 ? true : false;
        },
        fail: function fail(res) {
          console.log(res.data);
        }
      });
    },
    pay: function pay() {
      if (this.sonid == '') {
        uni.navigateTo({
          url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&goodstype=course'
        });
      } else {
        uni.navigateTo({
          url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&sonid=' + this.sonid + '&goodstype=course'
        });
      }
    },
    pay1: function pay1() {
      uni.navigateTo({
        url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&goodstype=course'
      });
    },
    exchange: function exchange() {
      uni.navigateTo({
        url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&goodstype=course&action=credit'
      });
    },
    seckill: function seckill() {
      uni.navigateTo({
        url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&goodstype=course&action=seckill'
      });
    },
    pintuan: function pintuan() {
      uni.navigateTo({
        url: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&goodstype=course&action=pintuan'
      });
    },
    fullscreen: function fullscreen(e) {
      this.videofullscreen = e.detail.fullScreen;
    },
    video_onplay: function video_onplay() {
      this.sign();
      this.countVtime();
      var audio = innerAudioContext;
      audio.pause();
    },
    video_onpause: function video_onpause() {
      clearInterval(this.t2);
      this.saveVtime();
    },
    video_onend: function video_onend() {
      clearInterval(this.t2);
      this.saveVtime();
    },
    video_timeUpdate: function video_timeUpdate(e) {
      var video_currentTime = e.detail.currentTime;
      var video_duration = e.detail.duration;
      // if(!this.is_free || !this.son_is_pay){
      // 	if(!this.son_is_pay){
      // 		if(video_currentTime>=this.free_time){
      // 			if(this.videofullscreen){
      // 				this.videoContext.exitFullScreen()
      // 			}
      // 			this.videoContext.pause()
      // 			uni.showModal({
      // 				title: '试看结束',
      // 				content: '需要解锁该课程吗?',
      // 				success: function (res) {
      // 					if (res.confirm) {
      // 						this.pay()
      // 					} else if (res.cancel) {
      // 						this.videoContext.seek(0)
      // 					}
      // 				}
      // 			});
      // 		}
      // 	}
      // }
    },
    gaosi_ani: function gaosi_ani() {
      if (this.gaosi == true) {
        this.t1 = setInterval(function () {
          this.gaosi = false;
          if (this.gaosi == false) {
            this.gaosi = true;
          }
        }, 4000);
      }
    },
    progress_text: function progress_text(e) {
      var audio = innerAudioContext;
      var progress = e.detail.value;
      this.currentTime = this.format(progress / 100 * audio.duration);
    },
    progress_change: function progress_change(e) {
      var audio = innerAudioContext;
      var progress = e.detail.value;
      this.currentTime = this.format(progress / 100 * audio.duration);
      audio.seek(progress / 100 * audio.duration);
    },
    change_slide: function change_slide(e) {
      var audio = innerAudioContext;
      if (e == 1) {
        audio.seek(audio.currentTime + 15);
      } else {
        audio.seek(audio.currentTime - 15);
      }
    },
    play: function play() {
      var audio = innerAudioContext;
      if (audio.paused) {
        audio.play();
      } else {
        audio.pause();
      }
    },
    muluchange: function muluchange(e) {
      this.btnnum = e;
    },
    videoErrorCallback: function videoErrorCallback(e) {
      uni.showModal({
        content: e.target.errMsg,
        showCancel: false
      });
    },
    openvideo: function openvideo(item) {
      console.log(item);
      this.sonid = item.id;
      this.son_is_pay = true;
      this.videostate = 'video';
      this.videosrc = item.url;
      this.videoId = item.id;
      this.videoContext.pause();
      this.videoContext.play();
      //this.video_onplay()
      //this.sign()
      // var menu_free=this.is_free || item.is_pay==1?1:0
      // if(this.is_free || item.is_pay==1){
      // 	this.son_is_pay=true
      // 	if(item.media === 'video') {
      // 		this.videostate = 'video'
      // 		this.videosrc = item.src
      // 	} else if (item.media === 'audio') {
      // 		this.videostate = 'audio'
      // 		if(item.src){
      // 			this.gaosi=true
      // 			let audio=innerAudioContext;
      //

      //
      // 			audio.title = item.coursename;
      // 			audio.singer = this.teacher.imgname;
      // 			audio.coverImgUrl = this.HOST_URL+this.menuinfo.thumb;
      // 			audio.src = item.src;
      //
      // 		}
      // 	} else {
      // 		uni.navigateTo({
      // 			url: '/pages/tuwen/tuwen?twid='+item.id+'&is_free='+menu_free,
      // 		})
      // 	}
      // }else{
      // 	this.son_is_pay=false
      // 	if(item.is_sk==1){
      // 		this.free_time=item.freesecond
      // 		if(item.media === 'video') {
      // 			this.videostate = 'video'
      // 			this.videosrc = item.src
      // 		} else if (item.media === 'audio') {
      // 			this.videostate = 'audio'
      // 			if(item.src){
      // 				this.gaosi=true
      // 				let audio=innerAudioContext;
      //

      //
      // 				audio.title = item.coursename;
      // 				audio.singer = this.teacher.imgname;
      // 				audio.coverImgUrl = this.HOST_URL+this.menuinfo.thumb;
      // 				audio.src = item.src;
      //
      // 			}
      // 		} else {
      // 			uni.navigateTo({
      // 				url: '/pages/tuwen/tuwen?twid='+item.id+'&is_free='+menu_free,
      // 			})
      // 		}
      // 	}else{
      // 		uni.showModal({
      // 			title: '提示',
      // 			content: '要解锁单个课程吗?',
      // 			success: function (res) {
      // 				if (res.confirm) {
      // 					this.pay()
      // 				} else if (res.cancel) {
      // 					console.log('用户点击取消');
      // 				}
      // 			}
      // 		});
      // 	}
      // }
    },
    //格式化时长
    format: function format(num) {
      return '0'.repeat(2 - String(Math.floor(num / 60)).length) + Math.floor(num / 60) + ':' + '0'.repeat(2 - String(Math.floor(num % 60)).length) + Math.floor(num % 60);
    },
    //-----------------------------------------------------------------------------------------------
    //
    // api
    //
    //-----------------------------------------------------------------------------------------------
    apiGetCourseList: function apiGetCourseList(id, cid) {
      return this.$http.get('/v1/member/projectCourseDetail', {
        params: {
          id: id,
          cid: cid
        }
      }).then(function (res) {
        return Promise.resolve(res.data.data);
      });
    },
    sign: function sign() {
      var _this9 = this;
      setTimeout(function () {
        _this9.$http.post("v1/member/doSign", {
          id: _this9.project.pid,
          pid: _this9.project.id,
          cid: _this9.videoId
        }).then(function (res) {});
      }, 5000);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 296:
/*!*************************************************************************************************************!*\
  !*** D:/gst/gst-uniapp/pages/projects/course.vue?vue&type=style&index=0&id=3b3be832&lang=less&scoped=true& ***!
  \*************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=style&index=0&id=3b3be832&lang=less&scoped=true& */ 297);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_course_vue_vue_type_style_index_0_id_3b3be832_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 297:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!./node_modules/postcss-loader/src??ref--10-oneOf-1-3!./node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/gst/gst-uniapp/pages/projects/course.vue?vue&type=style&index=0&id=3b3be832&lang=less&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[287,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/projects/course.js.map