<template>
	<view class="lesson-player">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
			</view>
			<view class="nav-center">
				<text class="nav-title">{{lessonInfo.title}}</text>
			</view>
			<view class="nav-right">
				<text class="nav-icon" @click="showMenu">⋯</text>
			</view>
		</view>

		<!-- 播放器容器 -->
		<view class="player-container">
			<!-- 视频播放器 -->
			<view v-if="lessonInfo.type === 'video'" class="video-player-wrapper">
				<video
					id="lessonVideo"
					:src="lessonInfo.mediaUrl"
					:poster="lessonInfo.poster"
					:controls="true"
					:autoplay="false"
					:show-center-play-btn="true"
					:enable-play-gesture="true"
					:object-fit="'contain'"
					@play="onPlay"
					@pause="onPause"
					@ended="onEnded"
					@timeupdate="onTimeUpdate"
					@error="onError"
					@fullscreenchange="onFullscreenChange"
					class="video-player"
				/>
				<view class="player-overlay" v-if="showOverlay">
					<view class="play-button" @click="togglePlay">
						<text class="play-icon">{{isPlaying ? '⏸' : '▶'}}</text>
					</view>
				</view>
			</view>

			<!-- 音频播放器 -->
			<view v-else-if="lessonInfo.type === 'audio'" class="audio-player-wrapper">
				<view class="audio-cover">
					<image :src="lessonInfo.poster || '/static/imgs/audio-default.png'" mode="aspectFit" />
					<view class="audio-play-btn" @click="toggleAudioPlay">
						<text class="audio-play-icon">{{isPlaying ? '⏸' : '▶'}}</text>
					</view>
				</view>
				<view class="audio-controls">
					<view class="audio-progress">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
							<view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
						</view>
						<view class="time-display">
							<text class="current-time">{{formatTime(currentTime)}}</text>
							<text class="total-time">{{formatTime(totalTime)}}</text>
						</view>
					</view>
					<view class="audio-actions">
						<view class="speed-control" @click="changeSpeed">
							<text class="speed-text">{{playbackRate}}x</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view v-else class="loading-wrapper">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 课程信息 -->
		<view class="lesson-info">
			<view class="lesson-header">
				<view class="lesson-meta">
					<text class="lesson-category">{{lessonInfo.categoryName}}</text>
					<text class="lesson-number">第{{lessonInfo.lessonNumber}}课</text>
				</view>
				<view class="lesson-status" :class="lessonInfo.completed ? 'completed' : 'pending'">
					<text class="status-text">{{lessonInfo.completed ? '已完成' : '学习中'}}</text>
				</view>
			</view>
			<text class="lesson-title">{{lessonInfo.title}}</text>
			<text class="lesson-subtitle">{{lessonInfo.subtitle}}</text>
			<view class="lesson-stats">
				<text class="stat-item">⏱ {{formatTime(lessonInfo.duration)}}</text>
				<text class="stat-item">👁 {{lessonInfo.viewCount || 0}}次播放</text>
				<text class="stat-item">❤ {{lessonInfo.likeCount || 0}}点赞</text>
			</view>
		</view>

		<!-- 学习笔记 -->
		<view class="notes-section">
			<view class="section-header">
				<text class="section-title">学习笔记</text>
				<text class="add-note" @click="addNote">+ 添加笔记</text>
			</view>
			<view class="notes-list" v-if="notes.length > 0">
				<view class="note-item" v-for="(note, index) in notes" :key="index">
					<view class="note-time">{{formatTime(note.time)}}</view>
					<text class="note-content">{{note.content}}</text>
				</view>
			</view>
			<view v-else class="empty-notes">
				<text class="empty-text">暂无学习笔记</text>
			</view>
		</view>

		<!-- 相关课程 -->
		<view class="related-lessons">
			<view class="section-header">
				<text class="section-title">相关课程</text>
			</view>
			<scroll-view class="related-list" scroll-x="true">
				<view class="related-item" v-for="(item, index) in relatedLessons" :key="index" @click="playRelated(item)">
					<image class="related-thumb" :src="item.poster" mode="aspectFit" />
					<text class="related-title">{{item.title}}</text>
					<text class="related-subtitle">{{item.subtitle}}</text>
				</view>
			</scroll-view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="action-btn" @click="toggleFavorite">
				<text class="action-icon">{{isFavorite ? '❤' : '♡'}}</text>
				<text class="action-text">收藏</text>
			</view>
			<view class="action-btn" @click="shareLesson">
				<text class="action-icon">📤</text>
				<text class="action-text">分享</text>
			</view>
			<view class="action-btn" @click="downloadLesson">
				<text class="action-icon">⬇</text>
				<text class="action-text">下载</text>
			</view>
			<view class="action-btn primary" @click="markComplete">
				<text class="action-text">{{lessonInfo.completed ? '已完成' : '标记完成'}}</text>
			</view>
		</view>

		<!-- 菜单弹窗 -->
		<view class="menu-overlay" v-if="showMenuPopup" @click="hideMenu">
			<view class="menu-popup" @click.stop>
				<view class="menu-item" @click="adjustSpeed">
					<text class="menu-icon">⚡</text>
					<text class="menu-text">播放速度</text>
				</view>
				<view class="menu-item" @click="adjustQuality">
					<text class="menu-icon">🎬</text>
					<text class="menu-text">画质设置</text>
				</view>
				<view class="menu-item" @click="reportIssue">
					<text class="menu-icon">⚠</text>
					<text class="menu-text">反馈问题</text>
				</view>
			</view>
		</view>

		<!-- 音频播放器（隐藏） -->
		<audio
			v-if="lessonInfo.type === 'audio'"
			id="lessonAudio"
			:src="lessonInfo.mediaUrl"
			@play="onAudioPlay"
			@pause="onAudioPause"
			@ended="onAudioEnded"
			@timeupdate="onAudioTimeUpdate"
			@error="onAudioError"
			style="display: none;"
		/>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			lessonInfo: {
				id: 1,
				title: '第一课',
				subtitle: '基础问候语',
				categoryName: '口语',
				lessonNumber: 1,
				type: 'video', // 'video' 或 'audio'
				mediaUrl: '',
				poster: '/static/imgs/lesson-default.png',
				duration: 300, // 秒
				completed: false,
				viewCount: 128,
				likeCount: 23
			},
			isPlaying: false,
			showOverlay: true,
			currentTime: 0,
			totalTime: 0,
			playbackRate: 1.0,
			isFavorite: false,
			showMenuPopup: false,
			notes: [],
			relatedLessons: [
				{
					id: 2,
					title: '第二课',
					subtitle: '自我介绍',
					poster: '/static/imgs/lesson-2.png'
				},
				{
					id: 3,
					title: '第三课',
					subtitle: '日常对话',
					poster: '/static/imgs/lesson-3.png'
				}
			]
		};
	},
	computed: {
		progressPercent() {
			if (this.totalTime === 0) return 0;
			return (this.currentTime / this.totalTime) * 100;
		}
	},
	onLoad(options) {
		// 获取系统信息
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 获取课程信息
		if (options.lessonId) {
			this.loadLessonInfo(options.lessonId);
		}
		if (options.categoryId) {
			this.lessonInfo.categoryName = options.categoryName || '未知分类';
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 显示菜单
		showMenu() {
			this.showMenuPopup = true;
		},
		
		// 隐藏菜单
		hideMenu() {
			this.showMenuPopup = false;
		},
		
		// 加载课程信息
		loadLessonInfo(lessonId) {
			// 这里可以调用API获取课程信息
			console.log('加载课程信息:', lessonId);
			
			// 模拟数据
			this.lessonInfo = {
				...this.lessonInfo,
				id: lessonId,
				mediaUrl: 'https://example.com/lesson.mp4', // 实际的媒体URL
				type: Math.random() > 0.5 ? 'video' : 'audio' // 随机类型用于演示
			};
		},
		
		// 视频播放事件
		onPlay() {
			this.isPlaying = true;
			this.showOverlay = false;
		},
		
		onPause() {
			this.isPlaying = false;
			this.showOverlay = true;
		},
		
		onEnded() {
			this.isPlaying = false;
			this.showOverlay = true;
			this.markComplete();
		},
		
		onTimeUpdate(e) {
			this.currentTime = e.detail.currentTime;
			this.totalTime = e.detail.duration;
		},
		
		onError(e) {
			console.error('视频播放错误:', e);
			uni.showToast({
				title: '播放失败',
				icon: 'none'
			});
		},
		
		onFullscreenChange(e) {
			console.log('全屏状态改变:', e.detail.fullScreen);
		},
		
		// 音频播放事件
		onAudioPlay() {
			this.isPlaying = true;
		},
		
		onAudioPause() {
			this.isPlaying = false;
		},
		
		onAudioEnded() {
			this.isPlaying = false;
			this.markComplete();
		},
		
		onAudioTimeUpdate(e) {
			this.currentTime = e.detail.currentTime;
			this.totalTime = e.detail.duration;
		},
		
		onAudioError(e) {
			console.error('音频播放错误:', e);
			uni.showToast({
				title: '播放失败',
				icon: 'none'
			});
		},
		
		// 切换播放状态
		togglePlay() {
			const video = uni.createVideoContext('lessonVideo', this);
			if (this.isPlaying) {
				video.pause();
			} else {
				video.play();
			}
		},
		
		// 切换音频播放状态
		toggleAudioPlay() {
			const audio = uni.createInnerAudioContext();
			audio.src = this.lessonInfo.mediaUrl;
			
			if (this.isPlaying) {
				audio.pause();
			} else {
				audio.play();
			}
		},
		
		// 改变播放速度
		changeSpeed() {
			const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
			const currentIndex = speeds.indexOf(this.playbackRate);
			const nextIndex = (currentIndex + 1) % speeds.length;
			this.playbackRate = speeds[nextIndex];
			
			// 设置播放速度
			if (this.lessonInfo.type === 'video') {
				const video = uni.createVideoContext('lessonVideo', this);
				video.playbackRate(this.playbackRate);
			}
		},
		
		// 格式化时间
		formatTime(seconds) {
			if (!seconds) return '00:00';
			const mins = Math.floor(seconds / 60);
			const secs = Math.floor(seconds % 60);
			return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
		},
		
		// 添加笔记
		addNote() {
			uni.showModal({
				title: '添加学习笔记',
				editable: true,
				placeholderText: '请输入学习笔记...',
				success: (res) => {
					if (res.confirm && res.content) {
						this.notes.push({
							time: this.currentTime,
							content: res.content,
							timestamp: new Date().getTime()
						});
						
						uni.showToast({
							title: '笔记添加成功',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 切换收藏状态
		toggleFavorite() {
			this.isFavorite = !this.isFavorite;
			uni.showToast({
				title: this.isFavorite ? '已收藏' : '已取消收藏',
				icon: 'success'
			});
		},
		
		// 分享课程
		shareLesson() {
			uni.showActionSheet({
				itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
				success: (res) => {
					const actions = ['微信', '朋友圈', '复制链接'];
					uni.showToast({
						title: `分享到${actions[res.tapIndex]}`,
						icon: 'success'
					});
				}
			});
		},
		
		// 下载课程
		downloadLesson() {
			uni.showModal({
				title: '下载课程',
				content: '是否下载此课程到本地？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '开始下载',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 标记完成
		markComplete() {
			this.lessonInfo.completed = true;
			uni.showToast({
				title: '课程已完成',
				icon: 'success'
			});
		},
		
		// 播放相关课程
		playRelated(lesson) {
			uni.navigateTo({
				url: `/pages/groups/lesson-player?lessonId=${lesson.id}`
			});
		},
		
		// 调整播放速度
		adjustSpeed() {
			this.hideMenu();
			this.changeSpeed();
		},
		
		// 调整画质
		adjustQuality() {
			this.hideMenu();
			uni.showActionSheet({
				itemList: ['自动', '高清', '标清', '流畅'],
				success: (res) => {
					const qualities = ['自动', '高清', '标清', '流畅'];
					uni.showToast({
						title: `已切换到${qualities[res.tapIndex]}`,
						icon: 'success'
					});
				}
			});
		},
		
		// 反馈问题
		reportIssue() {
			this.hideMenu();
			uni.showModal({
				title: '反馈问题',
				content: '请描述您遇到的问题',
				editable: true,
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '反馈已提交',
							icon: 'success'
						});
					}
				}
			});
		}
	}
};
</script>
