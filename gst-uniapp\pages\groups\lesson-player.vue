<template>
	<view class="lesson-player">
		

		<!-- 播放器容器 -->
		<view class="player-container">
			<!-- 视频播放器 -->
			<view  class="video-player-wrapper">
				<video
					id="lessonVideo"
					:src="lessonInfo.url"
					:poster="lessonInfo.poster"
					:controls="true"
					:autoplay="false"
					:show-center-play-btn="true"
					:enable-play-gesture="true"
					:object-fit="'contain'"
					@play="onPlay"
					@pause="onPause"
					@ended="onEnded"
					@timeupdate="onTimeUpdate"
					@error="onError"
					@fullscreenchange="onFullscreenChange"
					class="video-player"
				/>
				<view class="player-overlay" v-if="showOverlay">
					<view class="play-button" @click="togglePlay">
						<text class="play-icon">{{isPlaying ? '⏸' : '▶'}}</text>
					</view>
				</view>
			</view>

			<!-- 音频播放器 -->
			<!-- <view v-else-if="lessonInfo.type === 'audio'" class="audio-player-wrapper">
				<view class="audio-cover">
					<image :src="lessonInfo.poster || '/static/imgs/audio-default.png'" mode="aspectFit" />
					<view class="audio-play-btn" @click="toggleAudioPlay">
						<text class="audio-play-icon">{{isPlaying ? '⏸' : '▶'}}</text>
					</view>
				</view>
				<view class="audio-controls">
					<view class="audio-progress">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
							<view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
						</view>
						<view class="time-display">
							<text class="current-time">{{formatTime(currentTime)}}</text>
							<text class="total-time">{{formatTime(totalTime)}}</text>
						</view>
					</view>
					<view class="audio-actions">
						<view class="speed-control" @click="changeSpeed">
							<text class="speed-text">{{playbackRate}}x</text>
						</view>
					</view>
				</view>
			</view> -->

			<!-- 加载状态 -->
			<view v-else class="loading-wrapper">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 课程信息 -->
		<view class="lesson-info">
			<view class="lesson-header">
				<view class="lesson-meta">
					<!-- <text class="lesson-category">{{lessonInfo.categoryName}}</text> -->
					<text class="lesson-number">{{lessonInfo.title}}</text>
				</view>
				<!-- <view class="lesson-status" :class="lessonInfo.completed ? 'completed' : 'pending'">
					<text class="status-text">{{lessonInfo.completed ? '已完成' : '学习中'}}</text>
				</view> -->
			</view>
			<text class="lesson-title">{{lessonInfo.course.title}}</text>
			<!-- <text class="lesson-subtitle">{{lessonInfo.subtitle}}</text> -->
			<!-- <view class="lesson-stats">
				<text class="stat-item">⏱ {{formatTime(lessonInfo.duration)}}</text>
				<text class="stat-item">👁 {{lessonInfo.viewCount || 0}}次播放</text>
				<text class="stat-item">❤ {{lessonInfo.likeCount || 0}}点赞</text>
			</view> -->
		</view>

		<!-- 学习笔记 -->
		<!-- <view class="notes-section">
			<view class="section-header">
				<text class="section-title">学习笔记</text>
				<text class="add-note" @click="addNote">+ 添加笔记</text>
			</view>
			<view class="notes-list" v-if="notes.length > 0">
				<view class="note-item" v-for="(note, index) in notes" :key="index">
					<view class="note-time">{{formatTime(note.time)}}</view>
					<text class="note-content">{{note.content}}</text>
				</view>
			</view>
			<view v-else class="empty-notes">
				<text class="empty-text">暂无学习笔记</text>
			</view>
		</view> -->

		<!-- 相关课程 -->
		<!-- <view class="related-lessons">
			<view class="section-header">
				<text class="section-title">相关课程</text>
			</view>
			<scroll-view class="related-list" scroll-x="true">
				<view class="related-item" v-for="(item, index) in relatedLessons" :key="index" @click="playRelated(item)">
					<image class="related-thumb" :src="item.poster" mode="aspectFit" />
					<text class="related-title">{{item.title}}</text>
					<text class="related-subtitle">{{item.subtitle}}</text>
				</view>
			</scroll-view>
		</view> -->

		<!-- 底部操作栏 -->
		<!-- <view class="bottom-actions">
			<view class="action-btn" @click="toggleFavorite">
				<text class="action-icon">{{isFavorite ? '❤' : '♡'}}</text>
				<text class="action-text">收藏</text>
			</view>
			<view class="action-btn" @click="shareLesson">
				<text class="action-icon">📤</text>
				<text class="action-text">分享</text>
			</view>
			<view class="action-btn" @click="downloadLesson">
				<text class="action-icon">⬇</text>
				<text class="action-text">下载</text>
			</view>
			<view class="action-btn primary" @click="markComplete">
				<text class="action-text">{{lessonInfo.completed ? '已完成' : '标记完成'}}</text>
			</view>
		</view> -->

		<!-- 菜单弹窗 -->
		<view class="menu-overlay" v-if="showMenuPopup" @click="hideMenu">
			<view class="menu-popup" @click.stop>
				<view class="menu-item" @click="adjustSpeed">
					<text class="menu-icon">⚡</text>
					<text class="menu-text">播放速度</text>
				</view>
				<view class="menu-item" @click="adjustQuality">
					<text class="menu-icon">🎬</text>
					<text class="menu-text">画质设置</text>
				</view>
				<view class="menu-item" @click="reportIssue">
					<text class="menu-icon">⚠</text>
					<text class="menu-text">反馈问题</text>
				</view>
			</view>
		</view>

		<!-- 音频播放器（隐藏） -->
		<audio
			v-if="lessonInfo.type === 'audio'"
			id="lessonAudio"
			:src="lessonInfo.mediaUrl"
			@play="onAudioPlay"
			@pause="onAudioPause"
			@ended="onAudioEnded"
			@timeupdate="onAudioTimeUpdate"
			@error="onAudioError"
			style="display: none;"
		/>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			lessonInfo: {
				id: 1,
				title: '第一课',
				subtitle: '基础问候语',
				categoryName: '口语',
				lessonNumber: 1,
				type: 'video', // 'video' 或 'audio'
				mediaUrl: '',
				poster: '/static/imgs/lesson-default.png',
				duration: 300, // 秒
				completed: false,
				viewCount: 128,
				likeCount: 23
			},
			isPlaying: false,
			showOverlay: true,
			currentTime: 0,
			totalTime: 0,
			playbackRate: 1.0,
			isFavorite: false,
			showMenuPopup: false,
			notes: [],
			relatedLessons: [
				{
					id: 2,
					title: '第二课',
					subtitle: '自我介绍',
					poster: '/static/imgs/lesson-2.png'
				},
				{
					id: 3,
					title: '第三课',
					subtitle: '日常对话',
					poster: '/static/imgs/lesson-3.png'
				}
			]
		};
	},
	computed: {
		progressPercent() {
			if (this.totalTime === 0) return 0;
			return (this.currentTime / this.totalTime) * 100;
		}
	},
	onLoad(options) {
		// 获取系统信息
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 获取课程信息
		if (options.lessonId) {
			this.loadLessonInfo(options.lessonId);
		}
		if (options.categoryId) {
			this.lessonInfo.categoryName = options.categoryName || '未知分类';
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 显示菜单
		showMenu() {
			this.showMenuPopup = true;
		},
		
		// 隐藏菜单
		hideMenu() {
			this.showMenuPopup = false;
		},
		
		// 加载课程信息
		loadLessonInfo(lessonId) {
			// 这里可以调用API获取课程信息
			console.log('加载课程信息:', lessonId);
			
			// 模拟数据
			// this.lessonInfo = {
			// 	...this.lessonInfo,
			// 	id: lessonId,
			// 	mediaUrl: 'https://example.com/lesson.mp4', // 实际的媒体URL
			// 	type: Math.random() > 0.5 ? 'video' : 'audio' // 随机类型用于演示
			// };
			
			this.$http.get("v1/course/groups_course_detail", {
					params: {
						id:lessonId
		
					}
				}).then(res => {
				if (res.data.code == 0) {
					this.lessonInfo = res.data.data
				} 
			});
		},
		
		// 视频播放事件
		onPlay() {
			this.isPlaying = true;
			this.showOverlay = false;
		},
		
		onPause() {
			this.isPlaying = false;
			this.showOverlay = true;
		},
		
		onEnded() {
			this.isPlaying = false;
			this.showOverlay = true;
			this.markComplete();
		},
		
		onTimeUpdate(e) {
			this.currentTime = e.detail.currentTime;
			this.totalTime = e.detail.duration;
		},
		
		onError(e) {
			console.error('视频播放错误:', e);
			uni.showToast({
				title: '播放失败',
				icon: 'none'
			});
		},
		
		onFullscreenChange(e) {
			console.log('全屏状态改变:', e.detail.fullScreen);
		},
		
		// 音频播放事件
		onAudioPlay() {
			this.isPlaying = true;
		},
		
		onAudioPause() {
			this.isPlaying = false;
		},
		
		onAudioEnded() {
			this.isPlaying = false;
			this.markComplete();
		},
		
		onAudioTimeUpdate(e) {
			this.currentTime = e.detail.currentTime;
			this.totalTime = e.detail.duration;
		},
		
		onAudioError(e) {
			console.error('音频播放错误:', e);
			uni.showToast({
				title: '播放失败',
				icon: 'none'
			});
		},
		
		// 切换播放状态
		togglePlay() {
			const video = uni.createVideoContext('lessonVideo', this);
			if (this.isPlaying) {
				video.pause();
			} else {
				video.play();
			}
		},
		
		// 切换音频播放状态
		toggleAudioPlay() {
			const audio = uni.createInnerAudioContext();
			audio.src = this.lessonInfo.mediaUrl;
			
			if (this.isPlaying) {
				audio.pause();
			} else {
				audio.play();
			}
		},
		
		// 改变播放速度
		changeSpeed() {
			const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
			const currentIndex = speeds.indexOf(this.playbackRate);
			const nextIndex = (currentIndex + 1) % speeds.length;
			this.playbackRate = speeds[nextIndex];
			
			// 设置播放速度
			if (this.lessonInfo.type === 'video') {
				const video = uni.createVideoContext('lessonVideo', this);
				video.playbackRate(this.playbackRate);
			}
		},
		
		// 格式化时间
		formatTime(seconds) {
			if (!seconds) return '00:00';
			const mins = Math.floor(seconds / 60);
			const secs = Math.floor(seconds % 60);
			return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
		},
		
		// 添加笔记
		addNote() {
			uni.showModal({
				title: '添加学习笔记',
				editable: true,
				placeholderText: '请输入学习笔记...',
				success: (res) => {
					if (res.confirm && res.content) {
						this.notes.push({
							time: this.currentTime,
							content: res.content,
							timestamp: new Date().getTime()
						});
						
						uni.showToast({
							title: '笔记添加成功',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 切换收藏状态
		toggleFavorite() {
			this.isFavorite = !this.isFavorite;
			uni.showToast({
				title: this.isFavorite ? '已收藏' : '已取消收藏',
				icon: 'success'
			});
		},
		
		// 分享课程
		shareLesson() {
			uni.showActionSheet({
				itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
				success: (res) => {
					const actions = ['微信', '朋友圈', '复制链接'];
					uni.showToast({
						title: `分享到${actions[res.tapIndex]}`,
						icon: 'success'
					});
				}
			});
		},
		
		// 下载课程
		downloadLesson() {
			uni.showModal({
				title: '下载课程',
				content: '是否下载此课程到本地？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '开始下载',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 标记完成
		markComplete() {
			this.lessonInfo.completed = true;
			uni.showToast({
				title: '课程已完成',
				icon: 'success'
			});
		},
		
		// 播放相关课程
		playRelated(lesson) {
			uni.navigateTo({
				url: `/pages/groups/lesson-player?lessonId=${lesson.id}`
			});
		},
		
		// 调整播放速度
		adjustSpeed() {
			this.hideMenu();
			this.changeSpeed();
		},
		
		// 调整画质
		adjustQuality() {
			this.hideMenu();
			uni.showActionSheet({
				itemList: ['自动', '高清', '标清', '流畅'],
				success: (res) => {
					const qualities = ['自动', '高清', '标清', '流畅'];
					uni.showToast({
						title: `已切换到${qualities[res.tapIndex]}`,
						icon: 'success'
					});
				}
			});
		},
		
		// 反馈问题
		reportIssue() {
			this.hideMenu();
			uni.showModal({
				title: '反馈问题',
				content: '请描述您遇到的问题',
				editable: true,
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '反馈已提交',
							icon: 'success'
						});
					}
				}
			});
		}
	}
};
</script>

<style scoped>
.lesson-player {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 状态栏 */
.status-bar {
	background: #000;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	background: #fff;
	padding: 0 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-center {
	flex: 1;
	text-align: center;
}

.nav-icon {
	font-size: 32rpx;
	color: #333;
}

.nav-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

/* 播放器容器 */
.player-container {
	position: relative;
	background: #000;
}

/* 视频播放器 */
.video-player-wrapper {
	position: relative;
	width: 100%;
	height: 420rpx;
}

.video-player {
	width: 100%;
	height: 100%;
}

.player-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
}

.play-button {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.play-icon {
	font-size: 48rpx;
	color: #333;
}

/* 音频播放器 */
.audio-player-wrapper {
	padding: 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.audio-cover {
	position: relative;
	width: 300rpx;
	height: 300rpx;
	margin: 0 auto 40rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
}

.audio-cover image {
	width: 100%;
	height: 100%;
}

.audio-play-btn {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.audio-play-icon {
	font-size: 32rpx;
	color: #333;
}

.audio-controls {
	color: #fff;
}

.audio-progress {
	margin-bottom: 30rpx;
}

.progress-bar {
	position: relative;
	height: 6rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 3rpx;
	margin-bottom: 20rpx;
}

.progress-fill {
	height: 100%;
	background: #fff;
	border-radius: 3rpx;
	transition: width 0.3s ease;
}

.progress-thumb {
	position: absolute;
	top: -8rpx;
	width: 22rpx;
	height: 22rpx;
	background: #fff;
	border-radius: 50%;
	transform: translateX(-50%);
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}

.time-display {
	display: flex;
	justify-content: space-between;
	font-size: 24rpx;
}

.audio-actions {
	display: flex;
	justify-content: center;
}

.speed-control {
	padding: 12rpx 24rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.speed-text {
	font-size: 24rpx;
	color: #fff;
}

/* 加载状态 */
.loading-wrapper {
	height: 420rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #000;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	border-top: 4rpx solid #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: #fff;
	font-size: 28rpx;
}

/* 课程信息 */
.lesson-info {
	background: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.lesson-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.lesson-meta {
	display: flex;
	gap: 20rpx;
}

.lesson-category {
	font-size: 24rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.lesson-number {
	font-size: 24rpx;
	color: #666;
	background: #f0f0f0;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.lesson-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.lesson-status.completed {
	background: rgba(82, 196, 26, 0.1);
	color: #52c41a;
}

.lesson-status.pending {
	background: rgba(255, 193, 7, 0.1);
	color: #ffc107;
}

.lesson-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 12rpx;
}

.lesson-subtitle {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.lesson-stats {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	font-size: 24rpx;
	color: #999;
}

/* 学习笔记 */
.notes-section {
	background: #fff;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.add-note {
	font-size: 28rpx;
	color: #667eea;
}

.notes-list {
	padding: 0 30rpx;
}

.note-item {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.note-item:last-child {
	border-bottom: none;
}

.note-time {
	width: 120rpx;
	font-size: 24rpx;
	color: #999;
	margin-right: 20rpx;
}

.note-content {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	line-height: 1.5;
}

.empty-notes {
	padding: 60rpx 30rpx;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 相关课程 */
.related-lessons {
	background: #fff;
	margin-bottom: 120rpx;
}

.related-list {
	padding: 20rpx 30rpx;
	white-space: nowrap;
}

.related-item {
	display: inline-block;
	width: 200rpx;
	margin-right: 20rpx;
	vertical-align: top;
}

.related-thumb {
	width: 100%;
	height: 120rpx;
	border-radius: 12rpx;
	background: #f0f0f0;
	margin-bottom: 12rpx;
}

.related-title {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.related-subtitle {
	font-size: 22rpx;
	color: #666;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background: #fff;
	padding: 20rpx;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	safe-area-inset-bottom: env(safe-area-inset-bottom);
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx;
	margin: 0 8rpx;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn.primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.action-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.action-text {
	font-size: 24rpx;
}

.action-btn.primary .action-text {
	color: #fff;
}

/* 菜单弹窗 */
.menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-start;
	justify-content: flex-end;
	padding: 100rpx 20rpx 0 0;
	z-index: 1000;
}

.menu-popup {
	background: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
	min-width: 200rpx;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-item:active {
	background: #f8f9fa;
}

.menu-icon {
	font-size: 28rpx;
	margin-right: 16rpx;
}

.menu-text {
	font-size: 28rpx;
	color: #333;
}
</style>
