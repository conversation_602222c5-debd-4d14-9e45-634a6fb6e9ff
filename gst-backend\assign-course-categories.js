const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'gst_japanese_training.sqlite');

console.log('📍 为课程分配分类...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 连接到SQLite数据库成功');
});

// 分类映射规则
const categoryMappings = [
  {
    keywords: ['商务', '职场', '工作', '面试', '商业'],
    categoryId: 1, // 商务日语
    categoryName: '商务日语'
  },
  {
    keywords: ['口语', '会话', '对话', '交流', '外教'],
    categoryId: 2, // 日语口语
    categoryName: '日语口语'
  },
  {
    keywords: ['升学', '考试', '入学', '大学'],
    categoryId: 4, // 升学考试
    categoryName: '升学考试'
  },
  {
    keywords: ['翻译', '翻译'],
    categoryId: 6, // 日语翻译
    categoryName: '日语翻译'
  },
  {
    keywords: ['留学', '出国', '海外'],
    categoryId: 7, // 留学日语
    categoryName: '留学日语'
  },
  {
    keywords: ['考证', 'N1', 'N2', 'N3', 'N4', 'N5', 'JLPT'],
    categoryId: 8, // 日语考证
    categoryName: '日语考证'
  },
  {
    keywords: ['文化', '传统', '习俗', '社会'],
    categoryId: 9, // 日本文化
    categoryName: '日本文化'
  },
  {
    keywords: ['文学', '作家', '小说', '诗歌', '村上', '东野', '川端'],
    categoryId: 10, // 日本文学
    categoryName: '日本文学'
  },
  {
    keywords: ['动漫', '娱乐', '游戏', '漫画'],
    categoryId: 11, // 动漫娱乐
    categoryName: '动漫娱乐'
  },
  {
    keywords: ['时事', '热点', '新闻', '社会'],
    categoryId: 12, // 时事热点
    categoryName: '时事热点'
  }
];

// 获取所有课程
db.all("SELECT id, title, description FROM courses WHERE category_id IS NULL", [], (err, courses) => {
  if (err) {
    console.error('❌ 查询课程失败:', err.message);
    return;
  }

  console.log(`\n📊 需要分配分类的课程数: ${courses.length}`);
  
  let assignedCount = 0;
  let processedCount = 0;

  courses.forEach((course) => {
    const text = (course.title + ' ' + (course.description || '')).toLowerCase();
    let assignedCategoryId = null;
    let assignedCategoryName = null;

    // 查找匹配的分类
    for (const mapping of categoryMappings) {
      for (const keyword of mapping.keywords) {
        if (text.includes(keyword.toLowerCase())) {
          assignedCategoryId = mapping.categoryId;
          assignedCategoryName = mapping.categoryName;
          break;
        }
      }
      if (assignedCategoryId) break;
    }

    processedCount++;

    if (assignedCategoryId) {
      // 更新课程分类
      db.run(
        "UPDATE courses SET category_id = ? WHERE id = ?",
        [assignedCategoryId, course.id],
        function(err) {
          if (err) {
            console.error(`❌ 更新课程 ${course.id} 分类失败:`, err.message);
          } else {
            assignedCount++;
            console.log(`✅ 课程 "${course.title}" 分配到分类: ${assignedCategoryName}`);
          }

          // 检查是否处理完所有课程
          if (processedCount === courses.length) {
            console.log(`\n📈 分类分配完成:`);
            console.log(`- 处理课程数: ${processedCount}`);
            console.log(`- 成功分配: ${assignedCount}`);
            console.log(`- 未分配: ${processedCount - assignedCount}`);
            console.log(`- 分配成功率: ${((assignedCount / processedCount) * 100).toFixed(1)}%`);

            // 关闭数据库连接
            db.close((err) => {
              if (err) {
                console.error('❌ 关闭数据库失败:', err.message);
              } else {
                console.log('\n✅ 数据库连接已关闭');
              }
            });
          }
        }
      );
    } else {
      console.log(`⚠️ 课程 "${course.title}" 未找到匹配的分类`);
      
      // 检查是否处理完所有课程
      if (processedCount === courses.length) {
        console.log(`\n📈 分类分配完成:`);
        console.log(`- 处理课程数: ${processedCount}`);
        console.log(`- 成功分配: ${assignedCount}`);
        console.log(`- 未分配: ${processedCount - assignedCount}`);
        console.log(`- 分配成功率: ${((assignedCount / processedCount) * 100).toFixed(1)}%`);

        // 关闭数据库连接
        db.close((err) => {
          if (err) {
            console.error('❌ 关闭数据库失败:', err.message);
          } else {
            console.log('\n✅ 数据库连接已关闭');
          }
        });
      }
    }
  });

  if (courses.length === 0) {
    console.log('✅ 所有课程都已有分类');
    db.close();
  }
});
