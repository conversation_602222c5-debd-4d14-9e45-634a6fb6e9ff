<template>
  <div class="loading-container" :class="{ fullscreen: fullscreen }">
    <div class="loading-content">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <div class="loading-text" v-if="text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  text: {
    type: String,
    default: '加载中...'
  },
  fullscreen: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    z-index: 9999;
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  
  .spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    
    &:nth-child(1) {
      animation-delay: -0.45s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.3s;
      width: 80%;
      height: 80%;
      top: 10%;
      left: 10%;
      border-top-color: var(--success-color);
    }
    
    &:nth-child(3) {
      animation-delay: -0.15s;
      width: 60%;
      height: 60%;
      top: 20%;
      left: 20%;
      border-top-color: var(--warning-color);
    }
    
    &:nth-child(4) {
      width: 40%;
      height: 40%;
      top: 30%;
      left: 30%;
      border-top-color: var(--danger-color);
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: 500;
}
</style>
