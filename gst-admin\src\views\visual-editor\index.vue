﻿<template>
  <div class="visual-editor">
    <div class="editor-header">
      <div class="header-left">
        <h1>可视化编辑器</h1>
        <el-tag type="info">小程序页面设计</el-tag>
      </div>
      <div class="header-actions">
        <el-button @click="previewPage" :disabled="!currentPage">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button @click="savePage" type="primary" :loading="saving" :disabled="!currentPage">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button @click="publishPage" type="success" :disabled="!currentPage">
          <el-icon><Upload /></el-icon>
          发布
        </el-button>
      </div>
    </div>

    <div class="editor-body">
      <!-- 左侧组件库 -->
      <div class="components-panel">
        <div class="panel-header">
          <h3>组件库</h3>
        </div>
        <div class="component-groups">
          <el-collapse v-model="activeGroups">
            <el-collapse-item title="基础组件" name="basic">
              <div class="component-list">
                <div
                  v-for="component in basicComponents"
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart(component)"
                >
                  <el-icon>
                    <component :is="component.icon" />
                  </el-icon>
                  <span>{{ component.name }}</span>
                </div>
              </div>
            </el-collapse-item>

            <el-collapse-item title="布局组件" name="layout">
              <div class="component-list">
                <div
                  v-for="component in layoutComponents"
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart(component)"
                >
                  <el-icon>
                    <component :is="component.icon" />
                  </el-icon>
                  <span>{{ component.name }}</span>
                </div>
              </div>
            </el-collapse-item>

            <el-collapse-item title="业务组件" name="business">
              <div class="component-list">
                <div
                  v-for="component in businessComponents"
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart(component)"
                >
                  <el-icon>
                    <component :is="component.icon" />
                  </el-icon>
                  <span>{{ component.name }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-panel">
        <div class="canvas-toolbar">
          <el-button-group>
            <el-button
              v-for="device in devices"
              :key="device.type"
              :type="currentDevice === device.type ? 'primary' : ''"
              @click="switchDevice(device.type)"
            >
              <el-icon>
                <component :is="device.icon" />
              </el-icon>
              {{ device.name }}
            </el-button>
          </el-button-group>

          <div class="canvas-actions">
            <el-button @click="clearCanvas" size="small">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
            <el-button @click="undoAction" size="small" :disabled="!canUndo">
              <el-icon><RefreshLeft /></el-icon>
              撤销
            </el-button>
            <el-button @click="redoAction" size="small" :disabled="!canRedo">
              <el-icon><RefreshRight /></el-icon>
              重做
            </el-button>
          </div>
        </div>

        <div class="canvas-container">
          <div
            class="canvas"
            :class="[`device-${currentDevice}`]"
            @drop="handleDrop"
            @dragover="handleDragOver"
          >
            <div v-if="pageComponents.length === 0" class="canvas-placeholder">
              <el-empty description="拖拽组件到此处开始设计" :image-size="80" />
            </div>

            <div
              v-for="(component, index) in pageComponents"
              :key="component.id"
              class="canvas-component"
              :class="{ active: selectedComponent?.id === component.id }"
              @click="selectComponent(component)"
            >
              <component
                :is="getComponentRenderer(component.type)"
                :config="component.config"
                :data="component.data"
              />
              <div class="component-actions" v-if="selectedComponent?.id === component.id">
                <el-button size="small" @click="moveUp(index)" :disabled="index === 0">
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
                <el-button size="small" @click="moveDown(index)" :disabled="index === pageComponents.length - 1">
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <el-button size="small" @click="duplicateComponent(component)">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
                <el-button size="small" @click="deleteComponent(index)" type="danger">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h3>属性设置</h3>
        </div>

        <div v-if="!selectedComponent" class="no-selection">
          <el-empty description="请选择一个组件" :image-size="60" />
        </div>

        <div v-else class="property-form">
          <el-form :model="selectedComponent.config" label-width="80px" size="small">
            <!-- 基础属性 -->
            <el-form-item label="组件ID">
              <el-input v-model="selectedComponent.id" disabled />
            </el-form-item>

            <el-form-item label="组件类型">
              <el-input v-model="selectedComponent.type" disabled />
            </el-form-item>

            <!-- 样式属性 -->
            <el-divider>样式设置</el-divider>

            <el-form-item label="宽度">
              <el-input v-model="selectedComponent.config.width" placeholder="auto" />
            </el-form-item>

            <el-form-item label="高度">
              <el-input v-model="selectedComponent.config.height" placeholder="auto" />
            </el-form-item>

            <el-form-item label="边距">
              <el-input v-model="selectedComponent.config.margin" placeholder="0" />
            </el-form-item>

            <el-form-item label="内边距">
              <el-input v-model="selectedComponent.config.padding" placeholder="0" />
            </el-form-item>

            <!-- 动态属性 -->
            <el-divider>组件属性</el-divider>

            <component
              :is="getPropertyEditor(selectedComponent.type)"
              v-model="selectedComponent.config"
              :component="selectedComponent"
            />
          </el-form>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      title="页面预览"
      v-model="showPreview"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="preview-container">
        <div class="preview-phone">
          <div class="preview-content">
            <div
              v-for="component in pageComponents"
              :key="component.id"
              class="preview-component"
            >
              <component
                :is="getComponentRenderer(component.type)"
                :config="component.config"
                :data="component.data"
                preview
              />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  View, Document, Upload, Delete, RefreshLeft, RefreshRight,
  ArrowUp, ArrowDown, CopyDocument, Iphone, Monitor, Computer,
  Picture, EditPen, List, Grid, Menu
} from '@element-plus/icons-vue'

// 响应式数据
const saving = ref(false)
const showPreview = ref(false)
const currentDevice = ref('mobile')
const currentPage = ref(null)
const selectedComponent = ref(null)
const activeGroups = ref(['basic', 'layout', 'business'])

// 页面组件列表
const pageComponents = ref([])

// 历史记录
const history = ref([])
const historyIndex = ref(-1)

// 设备类型
const devices = [
  { type: 'mobile', name: '手机', icon: 'Iphone' },
  { type: 'tablet', name: '平板', icon: 'Monitor' },
  { type: 'desktop', name: '桌面', icon: 'Computer' }
]

// 基础组件
const basicComponents = [
  { type: 'text', name: '文本', icon: 'EditPen' },
  { type: 'image', name: '图片', icon: 'Picture' },
  { type: 'button', name: '按钮', icon: 'Grid' },
  { type: 'input', name: '输入框', icon: 'EditPen' },
  { type: 'divider', name: '分割线', icon: 'Menu' }
]

// 布局组件
const layoutComponents = [
  { type: 'container', name: '容器', icon: 'Grid' },
  { type: 'row', name: '行', icon: 'Menu' },
  { type: 'column', name: '列', icon: 'List' },
  { type: 'grid', name: '网格', icon: 'Grid' }
]

// 业务组件
const businessComponents = [
  { type: 'banner', name: '轮播图', icon: 'Picture' },
  { type: 'category', name: '分类', icon: 'Grid' },
  { type: 'course-list', name: '课程列表', icon: 'List' },
  { type: 'user-info', name: '用户信息', icon: 'User' }
]

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 拖拽处理
const handleDragStart = (component) => {
  event.dataTransfer.setData('component', JSON.stringify(component))
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDrop = (event) => {
  event.preventDefault()
  const componentData = JSON.parse(event.dataTransfer.getData('component'))
  addComponent(componentData)
}

// 添加组件
const addComponent = (componentData) => {
  const newComponent = {
    id: `${componentData.type}_${Date.now()}`,
    type: componentData.type,
    config: getDefaultConfig(componentData.type),
    data: {}
  }

  pageComponents.value.push(newComponent)
  selectedComponent.value = newComponent
  saveToHistory()
}

// 获取默认配置
const getDefaultConfig = (type) => {
  const configs = {
    text: {
      content: '文本内容',
      fontSize: '14px',
      color: '#333',
      textAlign: 'left'
    },
    image: {
      src: 'https://via.placeholder.com/300x200',
      alt: '图片',
      width: '100%',
      height: 'auto'
    },
    button: {
      text: '按钮',
      type: 'primary',
      size: 'medium'
    },
    banner: {
      images: [],
      autoplay: true,
      interval: 3000
    },
    category: {
      columns: 4,
      items: []
    }
  }

  return {
    width: 'auto',
    height: 'auto',
    margin: '0',
    padding: '0',
    ...configs[type] || {}
  }
}

// 选择组件
const selectComponent = (component) => {
  selectedComponent.value = component
}

// 删除组件
const deleteComponent = (index) => {
  pageComponents.value.splice(index, 1)
  selectedComponent.value = null
  saveToHistory()
}

// 复制组件
const duplicateComponent = (component) => {
  const newComponent = {
    ...component,
    id: `${component.type}_${Date.now()}`
  }
  pageComponents.value.push(newComponent)
  saveToHistory()
}

// 移动组件
const moveUp = (index) => {
  if (index > 0) {
    const temp = pageComponents.value[index]
    pageComponents.value[index] = pageComponents.value[index - 1]
    pageComponents.value[index - 1] = temp
    saveToHistory()
  }
}

const moveDown = (index) => {
  if (index < pageComponents.value.length - 1) {
    const temp = pageComponents.value[index]
    pageComponents.value[index] = pageComponents.value[index + 1]
    pageComponents.value[index + 1] = temp
    saveToHistory()
  }
}

// 切换设备
const switchDevice = (device) => {
  currentDevice.value = device
}

// 清空画布
const clearCanvas = async () => {
  try {
    await ElMessageBox.confirm('确定要清空画布吗？', '确认清空', {
      type: 'warning'
    })

    pageComponents.value = []
    selectedComponent.value = null
    saveToHistory()
  } catch (error) {
    // 用户取消
  }
}

// 撤销/重做
const undoAction = () => {
  if (canUndo.value) {
    historyIndex.value--
    pageComponents.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
    selectedComponent.value = null
  }
}

const redoAction = () => {
  if (canRedo.value) {
    historyIndex.value++
    pageComponents.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
    selectedComponent.value = null
  }
}

// 保存到历史记录
const saveToHistory = () => {
  const currentState = JSON.parse(JSON.stringify(pageComponents.value))

  // 如果当前不在历史记录的末尾，删除后面的记录
  if (historyIndex.value < history.value.length - 1) {
    history.value = history.value.slice(0, historyIndex.value + 1)
  }

  history.value.push(currentState)
  historyIndex.value = history.value.length - 1

  // 限制历史记录数量
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

// 预览页面
const previewPage = () => {
  showPreview.value = true
}

// 保存页面
const savePage = async () => {
  saving.value = true
  try {
    // 这里应该调用API保存页面数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    ElMessage.success('页面保存成功')
  } catch (error) {
    ElMessage.error('页面保存失败')
  } finally {
    saving.value = false
  }
}

// 发布页面
const publishPage = async () => {
  try {
    await ElMessageBox.confirm('确定要发布页面吗？', '确认发布', {
      type: 'warning'
    })

    // 这里应该调用API发布页面
    ElMessage.success('页面发布成功')
  } catch (error) {
    // 用户取消
  }
}

// 获取组件渲染器
const getComponentRenderer = (type) => {
  // 这里应该返回对应的组件渲染器
  return 'div'
}

// 获取属性编辑器
const getPropertyEditor = (type) => {
  // 这里应该返回对应的属性编辑器
  return 'div'
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化历史记录
  saveToHistory()
})
</script>

<style lang="scss" scoped>
.visual-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.editor-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧组件库
.components-panel {
  width: 280px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .component-groups {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .component-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 8px 0;
  }

  .component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    &:active {
      cursor: grabbing;
    }

    .el-icon {
      font-size: 24px;
      margin-bottom: 4px;
      color: #666;
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }
}

// 中间画布区域
.canvas-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;

  .canvas-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .canvas-actions {
      display: flex;
      gap: 8px;
    }
  }

  .canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    overflow: auto;
  }

  .canvas {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-height: 600px;
    position: relative;
    overflow: hidden;

    &.device-mobile {
      width: 375px;
    }

    &.device-tablet {
      width: 768px;
    }

    &.device-desktop {
      width: 1200px;
    }

    .canvas-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 400px;
    }
  }

  .canvas-component {
    position: relative;
    border: 2px solid transparent;
    transition: all 0.2s;

    &:hover {
      border-color: #409eff;
    }

    &.active {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    .component-actions {
      position: absolute;
      top: -40px;
      right: 0;
      display: flex;
      gap: 4px;
      background: white;
      padding: 4px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      z-index: 10;
    }
  }
}

// 右侧属性面板
.properties-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .no-selection {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .property-form {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }
}

// 预览对话框
.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.preview-phone {
  width: 320px;
  height: 568px;
  background: #333;
  border-radius: 20px;
  padding: 20px 10px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #666;
    border-radius: 2px;
  }

  .preview-content {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 10px;
    overflow: auto;
  }

  .preview-component {
    // 预览组件样式
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .components-panel {
    width: 240px;
  }

  .properties-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .editor-body {
    flex-direction: column;
  }

  .components-panel,
  .properties-panel {
    width: 100%;
    height: 200px;
  }

  .canvas-panel {
    flex: 1;
  }
}
</style>
