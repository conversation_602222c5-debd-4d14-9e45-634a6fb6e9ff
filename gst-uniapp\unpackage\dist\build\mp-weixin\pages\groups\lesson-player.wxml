<view class="lesson-player data-v-335db27b"><view class="player-container data-v-335db27b"><view class="video-player-wrapper data-v-335db27b"><video class="video-player data-v-335db27b" id="lessonVideo" src="{{lessonInfo.url}}" poster="{{lessonInfo.poster}}" controls="{{true}}" autoplay="{{false}}" show-center-play-btn="{{true}}" enable-play-gesture="{{true}}" object-fit="contain" data-event-opts="{{[['play',[['onPlay',['$event']]]],['pause',[['onPause',['$event']]]],['ended',[['onEnded',['$event']]]],['timeupdate',[['onTimeUpdate',['$event']]]],['error',[['onError',['$event']]]],['fullscreenchange',[['onFullscreenChange',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" binderror="__e" bindfullscreenchange="__e"></video><block wx:if="{{showOverlay}}"><view class="player-overlay data-v-335db27b"><view data-event-opts="{{[['tap',[['togglePlay',['$event']]]]]}}" class="play-button data-v-335db27b" bindtap="__e"><text class="play-icon data-v-335db27b">{{isPlaying?'⏸':'▶'}}</text></view></view></block></view></view><view class="lesson-info data-v-335db27b"><view class="lesson-header data-v-335db27b"><view class="lesson-meta data-v-335db27b"><text class="lesson-number data-v-335db27b">{{lessonInfo.title}}</text></view></view><text class="lesson-title data-v-335db27b">{{lessonInfo.course.title}}</text></view><block wx:if="{{showMenuPopup}}"><view data-event-opts="{{[['tap',[['hideMenu',['$event']]]]]}}" class="menu-overlay data-v-335db27b" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="menu-popup data-v-335db27b" catchtap="__e"><view data-event-opts="{{[['tap',[['adjustSpeed',['$event']]]]]}}" class="menu-item data-v-335db27b" bindtap="__e"><text class="menu-icon data-v-335db27b">⚡</text><text class="menu-text data-v-335db27b">播放速度</text></view><view data-event-opts="{{[['tap',[['adjustQuality',['$event']]]]]}}" class="menu-item data-v-335db27b" bindtap="__e"><text class="menu-icon data-v-335db27b">🎬</text><text class="menu-text data-v-335db27b">画质设置</text></view><view data-event-opts="{{[['tap',[['reportIssue',['$event']]]]]}}" class="menu-item data-v-335db27b" bindtap="__e"><text class="menu-icon data-v-335db27b">⚠</text><text class="menu-text data-v-335db27b">反馈问题</text></view></view></view></block><block wx:if="{{lessonInfo.type==='audio'}}"><audio style="display:none;" id="lessonAudio" src="{{lessonInfo.mediaUrl}}" data-event-opts="{{[['play',[['onAudioPlay',['$event']]]],['pause',[['onAudioPause',['$event']]]],['ended',[['onAudioEnded',['$event']]]],['timeupdate',[['onAudioTimeUpdate',['$event']]]],['error',[['onAudioError',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" binderror="__e" class="data-v-335db27b"></audio></block></view>