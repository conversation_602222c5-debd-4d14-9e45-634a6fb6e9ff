<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑小组' : '创建小组'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="large"
    >
      <el-form-item label="小组名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入小组名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="等级" prop="level">
        <el-select v-model="form.level" placeholder="选择等级" style="width: 100%">
          <el-option label="N5 - 入门级" value="N5" />
          <el-option label="N4 - 初级" value="N4" />
          <el-option label="N3 - 中级" value="N3" />
          <el-option label="N2 - 中高级" value="N2" />
          <el-option label="N1 - 高级" value="N1" />
        </el-select>
      </el-form-item>

      <el-form-item label="最大人数" prop="maxMembers">
        <el-input-number
          v-model="form.maxMembers"
          :min="1"
          :max="100"
          :step="1"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="指导老师" prop="teacherId" v-if="authStore.isAdmin">
        <el-select
          v-model="form.teacherId"
          placeholder="选择指导老师"
          style="width: 100%"
          filterable
          loading-text="加载中..."
        >
          <el-option
            v-for="teacher in teachers"
            :key="teacher.id"
            :label="teacher.realName || teacher.username"
            :value="teacher.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="小组描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入小组描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="学习目标">
        <el-input
          v-model="form.goals"
          type="textarea"
          :rows="3"
          placeholder="请输入学习目标（可选）"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="状态" prop="status" v-if="isEdit && authStore.isAdmin">
        <el-radio-group v-model="form.status">
          <el-radio label="pending">待审核</el-radio>
          <el-radio label="active">活跃</el-radio>
          <el-radio label="completed">已完成</el-radio>
          <el-radio label="cancelled">已取消</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put } from '@/utils/request'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const authStore = useAuthStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const teachers = ref([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.group?.id)

// 表单数据
const form = reactive({
  name: '',
  level: '',
  maxMembers: 30,
  teacherId: '',
  description: '',
  goals: '',
  status: 'pending'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入小组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '小组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择等级', trigger: 'change' }
  ],
  maxMembers: [
    { required: true, message: '请输入最大人数', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '人数范围在 1 到 100 之间', trigger: 'blur' }
  ],
  teacherId: [
    { required: true, message: '请选择指导老师', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 加载教师列表
const loadTeachers = async () => {
  try {
    const response = await get('/api/users', { role: 'teacher' })
    if (response.success) {
      teachers.value = response.data.users || []
    }
  } catch (error) {
    console.error('加载教师列表失败:', error)
    // 使用模拟数据
    teachers.value = [
      { id: 1, username: 'teacher1', realName: '张老师' },
      { id: 2, username: 'teacher2', realName: '李老师' },
      { id: 3, username: 'teacher3', realName: '王老师' }
    ]
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    level: '',
    maxMembers: 30,
    teacherId: authStore.isTeacher ? authStore.user.id : '',
    description: '',
    goals: '',
    status: 'pending'
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 填充表单数据
const fillForm = (group) => {
  if (group) {
    Object.assign(form, {
      name: group.name || '',
      level: group.level || '',
      maxMembers: group.maxMembers || 30,
      teacherId: group.teacher?.id || group.teacherId || '',
      description: group.description || '',
      goals: group.goals || '',
      status: group.status || 'pending'
    })
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    const data = { ...form }
    let response
    
    if (isEdit.value) {
      response = await put(`/api/groups/${props.group.id}`, data, { showSuccess: true })
    } else {
      response = await post('/api/groups', data, { showSuccess: true })
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    if (props.group) {
      fillForm(props.group)
    } else {
      resetForm()
    }
  }
})

// 组件挂载时加载教师列表
onMounted(() => {
  if (authStore.isAdmin) {
    loadTeachers()
  }
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
