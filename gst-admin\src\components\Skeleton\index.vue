<template>
  <div class="skeleton-container">
    <!-- 表格骨架屏 -->
    <div v-if="type === 'table'" class="skeleton-table">
      <div class="skeleton-header">
        <div class="skeleton-item skeleton-title"></div>
        <div class="skeleton-actions">
          <div class="skeleton-item skeleton-button"></div>
          <div class="skeleton-item skeleton-button"></div>
        </div>
      </div>
      
      <div class="skeleton-filters">
        <div class="skeleton-item skeleton-filter" v-for="i in 4" :key="i"></div>
      </div>
      
      <div class="skeleton-table-content">
        <div class="skeleton-table-row" v-for="i in rows" :key="i">
          <div class="skeleton-item skeleton-cell" v-for="j in columns" :key="j"></div>
        </div>
      </div>
    </div>
    
    <!-- 卡片列表骨架屏 -->
    <div v-else-if="type === 'cards'" class="skeleton-cards">
      <div class="skeleton-header">
        <div class="skeleton-item skeleton-title"></div>
        <div class="skeleton-actions">
          <div class="skeleton-item skeleton-button"></div>
          <div class="skeleton-item skeleton-button"></div>
        </div>
      </div>
      
      <div class="skeleton-filters">
        <div class="skeleton-item skeleton-filter" v-for="i in 3" :key="i"></div>
      </div>
      
      <div class="skeleton-cards-grid">
        <div class="skeleton-card" v-for="i in cards" :key="i">
          <div class="skeleton-card-header">
            <div class="skeleton-item skeleton-avatar"></div>
            <div class="skeleton-card-info">
              <div class="skeleton-item skeleton-name"></div>
              <div class="skeleton-item skeleton-meta"></div>
            </div>
          </div>
          <div class="skeleton-card-content">
            <div class="skeleton-item skeleton-text" v-for="j in 3" :key="j"></div>
          </div>
          <div class="skeleton-card-footer">
            <div class="skeleton-item skeleton-tag" v-for="j in 2" :key="j"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 表单骨架屏 -->
    <div v-else-if="type === 'form'" class="skeleton-form">
      <div class="skeleton-form-header">
        <div class="skeleton-item skeleton-title"></div>
      </div>
      
      <div class="skeleton-form-content">
        <div class="skeleton-form-row" v-for="i in formRows" :key="i">
          <div class="skeleton-form-item" v-for="j in 2" :key="j">
            <div class="skeleton-item skeleton-label"></div>
            <div class="skeleton-item skeleton-input"></div>
          </div>
        </div>
        
        <div class="skeleton-form-textarea">
          <div class="skeleton-item skeleton-label"></div>
          <div class="skeleton-item skeleton-textarea"></div>
        </div>
      </div>
      
      <div class="skeleton-form-footer">
        <div class="skeleton-item skeleton-button"></div>
        <div class="skeleton-item skeleton-button primary"></div>
      </div>
    </div>
    
    <!-- 仪表板骨架屏 -->
    <div v-else-if="type === 'dashboard'" class="skeleton-dashboard">
      <div class="skeleton-stats">
        <div class="skeleton-stat-card" v-for="i in 4" :key="i">
          <div class="skeleton-item skeleton-stat-icon"></div>
          <div class="skeleton-stat-content">
            <div class="skeleton-item skeleton-stat-value"></div>
            <div class="skeleton-item skeleton-stat-label"></div>
          </div>
        </div>
      </div>
      
      <div class="skeleton-charts">
        <div class="skeleton-chart">
          <div class="skeleton-item skeleton-chart-title"></div>
          <div class="skeleton-item skeleton-chart-content"></div>
        </div>
        <div class="skeleton-chart">
          <div class="skeleton-item skeleton-chart-title"></div>
          <div class="skeleton-item skeleton-chart-content"></div>
        </div>
      </div>
      
      <div class="skeleton-recent">
        <div class="skeleton-item skeleton-section-title"></div>
        <div class="skeleton-recent-item" v-for="i in 5" :key="i">
          <div class="skeleton-item skeleton-avatar"></div>
          <div class="skeleton-recent-content">
            <div class="skeleton-item skeleton-recent-title"></div>
            <div class="skeleton-item skeleton-recent-meta"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 默认骨架屏 -->
    <div v-else class="skeleton-default">
      <div class="skeleton-item skeleton-title"></div>
      <div class="skeleton-item skeleton-text" v-for="i in 3" :key="i"></div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['table', 'cards', 'form', 'dashboard', 'default'].includes(value)
  },
  rows: {
    type: Number,
    default: 5
  },
  columns: {
    type: Number,
    default: 6
  },
  cards: {
    type: Number,
    default: 6
  },
  formRows: {
    type: Number,
    default: 3
  }
})
</script>

<style lang="scss" scoped>
.skeleton-container {
  padding: var(--spacing-lg);
}

.skeleton-item {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--border-radius-base);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 表格骨架屏
.skeleton-table {
  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    .skeleton-title {
      width: 200px;
      height: 32px;
    }
    
    .skeleton-actions {
      display: flex;
      gap: var(--spacing-sm);
      
      .skeleton-button {
        width: 80px;
        height: 32px;
      }
    }
  }
  
  .skeleton-filters {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    .skeleton-filter {
      width: 120px;
      height: 32px;
    }
  }
  
  .skeleton-table-content {
    .skeleton-table-row {
      display: flex;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      
      .skeleton-cell {
        flex: 1;
        height: 40px;
      }
    }
  }
}

// 卡片骨架屏
.skeleton-cards {
  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    .skeleton-title {
      width: 200px;
      height: 32px;
    }
    
    .skeleton-actions {
      display: flex;
      gap: var(--spacing-sm);
      
      .skeleton-button {
        width: 80px;
        height: 32px;
      }
    }
  }
  
  .skeleton-filters {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    .skeleton-filter {
      width: 120px;
      height: 32px;
    }
  }
  
  .skeleton-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    
    .skeleton-card {
      padding: var(--spacing-lg);
      border: 1px solid var(--border-base);
      border-radius: var(--border-radius-base);
      
      .skeleton-card-header {
        display: flex;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        
        .skeleton-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
        
        .skeleton-card-info {
          flex: 1;
          
          .skeleton-name {
            width: 120px;
            height: 20px;
            margin-bottom: var(--spacing-xs);
          }
          
          .skeleton-meta {
            width: 80px;
            height: 16px;
          }
        }
      }
      
      .skeleton-card-content {
        margin-bottom: var(--spacing-md);
        
        .skeleton-text {
          height: 16px;
          margin-bottom: var(--spacing-xs);
          
          &:nth-child(1) { width: 100%; }
          &:nth-child(2) { width: 80%; }
          &:nth-child(3) { width: 60%; }
        }
      }
      
      .skeleton-card-footer {
        display: flex;
        gap: var(--spacing-sm);
        
        .skeleton-tag {
          width: 60px;
          height: 24px;
        }
      }
    }
  }
}

// 表单骨架屏
.skeleton-form {
  .skeleton-form-header {
    margin-bottom: var(--spacing-xl);
    
    .skeleton-title {
      width: 200px;
      height: 32px;
    }
  }
  
  .skeleton-form-content {
    margin-bottom: var(--spacing-xl);
    
    .skeleton-form-row {
      display: flex;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      
      .skeleton-form-item {
        flex: 1;
        
        .skeleton-label {
          width: 80px;
          height: 20px;
          margin-bottom: var(--spacing-sm);
        }
        
        .skeleton-input {
          width: 100%;
          height: 40px;
        }
      }
    }
    
    .skeleton-form-textarea {
      .skeleton-label {
        width: 80px;
        height: 20px;
        margin-bottom: var(--spacing-sm);
      }
      
      .skeleton-textarea {
        width: 100%;
        height: 120px;
      }
    }
  }
  
  .skeleton-form-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    
    .skeleton-button {
      width: 80px;
      height: 40px;
      
      &.primary {
        width: 100px;
      }
    }
  }
}

// 仪表板骨架屏
.skeleton-dashboard {
  .skeleton-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .skeleton-stat-card {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-lg);
      border: 1px solid var(--border-base);
      border-radius: var(--border-radius-base);
      
      .skeleton-stat-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius-base);
      }
      
      .skeleton-stat-content {
        flex: 1;
        
        .skeleton-stat-value {
          width: 60px;
          height: 24px;
          margin-bottom: var(--spacing-xs);
        }
        
        .skeleton-stat-label {
          width: 80px;
          height: 16px;
        }
      }
    }
  }
  
  .skeleton-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .skeleton-chart {
      padding: var(--spacing-lg);
      border: 1px solid var(--border-base);
      border-radius: var(--border-radius-base);
      
      .skeleton-chart-title {
        width: 120px;
        height: 24px;
        margin-bottom: var(--spacing-lg);
      }
      
      .skeleton-chart-content {
        width: 100%;
        height: 200px;
      }
    }
  }
  
  .skeleton-recent {
    .skeleton-section-title {
      width: 120px;
      height: 24px;
      margin-bottom: var(--spacing-lg);
    }
    
    .skeleton-recent-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-md);
      
      .skeleton-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
      
      .skeleton-recent-content {
        flex: 1;
        
        .skeleton-recent-title {
          width: 200px;
          height: 16px;
          margin-bottom: var(--spacing-xs);
        }
        
        .skeleton-recent-meta {
          width: 120px;
          height: 14px;
        }
      }
    }
  }
}

// 默认骨架屏
.skeleton-default {
  .skeleton-title {
    width: 200px;
    height: 32px;
    margin-bottom: var(--spacing-lg);
  }
  
  .skeleton-text {
    height: 16px;
    margin-bottom: var(--spacing-sm);
    
    &:nth-child(2) { width: 100%; }
    &:nth-child(3) { width: 80%; }
    &:nth-child(4) { width: 60%; }
  }
}

@media (max-width: 768px) {
  .skeleton-cards-grid {
    grid-template-columns: 1fr !important;
  }
  
  .skeleton-charts {
    grid-template-columns: 1fr !important;
  }
  
  .skeleton-form-row {
    flex-direction: column !important;
  }
}
</style>
