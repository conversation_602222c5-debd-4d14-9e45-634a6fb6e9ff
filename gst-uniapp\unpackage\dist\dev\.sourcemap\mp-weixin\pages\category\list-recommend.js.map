{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/category/list-recommend.vue?d7a2", "webpack:///D:/gst/gst-uniapp/pages/category/list-recommend.vue?a4e5", "webpack:///D:/gst/gst-uniapp/pages/category/list-recommend.vue?58eb", "webpack:///D:/gst/gst-uniapp/pages/category/list-recommend.vue?6ed3", "uni-app:///pages/category/list-recommend.vue", "webpack:///D:/gst/gst-uniapp/pages/category/list-recommend.vue?179b", "webpack:///D:/gst/gst-uniapp/pages/category/list-recommend.vue?195f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniLoadMore", "empty", "empty1", "data", "cateMaskState", "headerPosition", "headerTop", "loadingType", "filterIndex", "cateId", "priceOrder", "cateList", "goodsList", "title", "loading", "listtabs1", "status", "text", "orderList", "current_page", "listtabs2", "currenttabs1", "mainHeight", "style", "courseList", "onLoad", "console", "uni", "grace<PERSON>s", "ref", "onPageScroll", "onPullDownRefresh", "onReachBottom", "methods", "loadCateList", "loadData", "navItem", "url", "page", "header", "success", "item", "orderStateExp", "stateTipColor", "stateTip", "tabClick", "changeTab", "toggleCateMask", "setTimeout", "changeCate", "duration", "scrollTop", "navToDetailPage", "stopPrevent", "navToWeb", "filePath", "showMenu", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA2mB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;AC6C/nB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAV;QACAW;QACAC;MACA,GACA;QACAH;QACAC;QACAV;QACAW;QACAC;MACA,GACA;QACAH;QACAC;QACAV;QACAW;QACAC;MACA,GACA;QACAH;QACAC;QACAV;QACAW;QACAC;MACA,EACA;MACAC;QACAJ;QACAC;QACAV;QACAW;QACAC;MACA,GACA;QACAH;QACAC;QACAV;QACAW;QACAC;MACA,GACA;QACAH;QACAC;QACAV;QACAW;QACAC;MACA,EACA;MACAE;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;IAIA;IACA;IACA;IACAC;IACA;IACAC;MACAd;IACA;IACAe;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EAGA;EACAC;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACA;EACAC;IACA;EAAA,CACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAR;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MAAA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MAEAC;MACAT;QACAU;QAAA;QACAlC;UACAmC;QACA;QACAC;UACA;QACA;;QACAC;UACAd;UACA;UACA;UACA;UACA;UACA;UAEA;YACA;YACAe;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;UACA;UACA;UACAf;UACAR;YACAkB;UACA;UACA;UACA;;UAEA;UACAA;UAEAE;UACA;UACAZ;QACA;MACA;IAEA;IAEA;IACAgB;MACA;QACAC;MACA;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACAD;UACA;;QAEA;MAAA;;MAEA;QACAC;QACAD;MACA;IACA;IAEAE;MACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAtB;QACAuB;QACAC;MACA;MACA;MACAxB;QACAd;MACA;IACA;IACA;IACAuC;MACA;MACA;MACAzB;QACAU;MACA;IACA;IACAgB;IACAC;MACA;QACA;UAAA;UACA3B;YACAU;UACA;UACA;QACA;UAAA;UACAV;YACAU;UACA;UACA;QACA;UAAA;UACA;UACA;UACA;UACA;QACA;UAAA;UACA;UACA;QACA;UAAA;UACA;YACAA;YACAG;cACA;cACAb;gBACA4B;gBACAC;gBACAhB;kBACAd;gBACA;gBACA+B;kBACA/B;gBACA;cACA;YACA;UACA;UACA;MAAA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtXA;AAAA;AAAA;AAAA;AAAspC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA1qC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/list-recommend.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/category/list-recommend.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list-recommend.vue?vue&type=template&id=c041abd6&\"\nvar renderjs\nimport script from \"./list-recommend.vue?vue&type=script&lang=js&\"\nexport * from \"./list-recommend.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-recommend.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/list-recommend.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-recommend.vue?vue&type=template&id=c041abd6&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.listtabs1, function (tabItem, tabIndex) {\n    var $orig = _vm.__get_orig(tabItem)\n    var g0 = tabItem.loaded === true && tabItem.orderList.length === 0\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-recommend.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-recommend.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#fff;\">\r\n\t\t\t<view class=\"navbar\">\r\n\t\t\t\t<view v-for=\"(item, index) in listtabs1\" :key=\"index\" class=\"nav-item\"\r\n\t\t\t\t\t:class=\"{current: currenttabs1 === index}\" @click=\"tabClick(index)\">\r\n\t\t\t\t\t{{item.text}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--科目考题 结束-->\r\n\t\t\t<swiper :current=\"currenttabs1\" class=\"swiper-box\" duration=\"300\" @change=\"changeTab\"\r\n\t\t\t\t:style=\"{height:mainHeight+'px'}\">\r\n\t\t\t\t<swiper-item class=\"tab-content\" v-for=\"(tabItem,tabIndex) in listtabs1\" :key=\"tabIndex\">\r\n\t\t\t\t\t<scroll-view class=\"list-scroll-content\" scroll-y>\r\n\t\t\t\t\t\t<!-- 空白页 -->\r\n\t\t\t\t\t\t<!-- <empty v-if=\"tabItem.loaded === true && tabItem.orderList.length === 0 && style!=4\"></empty> -->\r\n\t\t\t\t\t\t<empty1 v-if=\"tabItem.loaded === true && tabItem.orderList.length === 0\"></empty1>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item1, index1) in tabItem.orderList\"\r\n\t\t\t\t\t\t\t\t\t:key=\"index1\" @tap=\"navToWeb(item1)\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item1.thumb\"></image>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"title\">{{item1.title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"des\">{{item1.description}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"end\"><text style=\"text-align: right;float: right;\">详情</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\timport uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';\r\n\tvar graceJs = require('@/GraceUI5/js/grace.js');\r\n\timport empty from \"@/components/empty\";\r\n\timport empty1 from \"@/components/null\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniLoadMore,\r\n\t\t\tempty,\r\n\t\t\tempty1\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcateMaskState: 0, //分类面板展开状态\r\n\t\t\t\theaderPosition: \"fixed\",\r\n\t\t\t\theaderTop: \"0px\",\r\n\t\t\t\tloadingType: 'more', //加载更多状态\r\n\t\t\t\tfilterIndex: 0,\r\n\t\t\t\tcateId: 0, //已选三级分类id\r\n\t\t\t\tpriceOrder: 0, //1 价格从低到高 2价格从高到低\r\n\t\t\t\tcateList: [],\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tloading: false,\r\n\t\t\t\tlisttabs1: [{\r\n\t\t\t\t\t\tstatus: 'word',\r\n\t\t\t\t\t\ttext: '热词对译',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 'current',\r\n\t\t\t\t\t\ttext: '时政资料',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 'course',\r\n\t\t\t\t\t\ttext: '公开讲座',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t},\t\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 'study',\r\n\t\t\t\t\t\ttext: '学习资料',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t},\t\t\t\t\t\r\n\t\t\t\t],\r\n\t\t\t\tlisttabs2: [{\r\n\t\t\t\t\t\tstatus: 0,\r\n\t\t\t\t\t\ttext: '正在报名',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 1,\r\n\t\t\t\t\t\ttext: '现已开课',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 2,\r\n\t\t\t\t\t\ttext: '往期课程',\r\n\t\t\t\t\t\tloadingType: 'more',\r\n\t\t\t\t\t\torderList: [],\r\n\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcurrenttabs1: 0,\r\n\t\t\t\tmainHeight: 200,\r\n\t\t\t\tstyle: 1,\r\n\t\t\t\tcourseList: []\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.headerTop = document.getElementsByTagName('uni-page-head')[0].offsetHeight + 'px';\r\n\t\t\t// #endif\r\n\t\t\tthis.cateId = options.id;\r\n\t\t\tthis.style = options.style;\r\n\t\t\tthis.title = options.title;\r\n\t\t\tconsole.log(this.title);\r\n\t\t\t// 动态设置标题\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.title\r\n\t\t\t});\r\n\t\t\tgraceJs.getRefs('guiPage', this, 0, (ref) => {\r\n\t\t\t\tref.getDomSize('guiPageBody', (e) => {\r\n\t\t\t\t\t// 主体高度 = 页面高度 - 自定义区域高度\r\n\t\t\t\t\t// graceJs.select('#myheader', (e2) => {\r\n\t\t\t\t\t// 如果自定义区域有 margin 尺寸获取不到请加上这个值，可以利用 uni.upx2px() 转换\r\n\t\t\t\t\tthis.mainHeight = e.height;\r\n\t\t\t\t\tthis.pageLoading = false;\r\n\t\t\t\t\t// 第一次加载数据\r\n\t\t\t\t\tthis.loadCateList();\r\n\t\t\t\t\tthis.loadData();\r\n\t\t\t\t\t// });\r\n\t\t\t\t});\r\n\t\t\t});\r\n\r\n\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\t//兼容iOS端下拉时顶部漂移\r\n\t\t\tif (e.scrollTop >= 0) {\r\n\t\t\t\tthis.headerPosition = \"fixed\";\r\n\t\t\t} else {\r\n\t\t\t\tthis.headerPosition = \"absolute\";\r\n\t\t\t}\r\n\t\t},\r\n\t\t//下拉刷新\r\n\t\tonPullDownRefresh() {\r\n\t\t\t// this.loadData('refresh');\r\n\t\t},\r\n\t\t//加载更多\r\n\t\tonReachBottom() {\r\n\t\t\t// this.loadData();\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t\t//加载分类\r\n\t\t\tasync loadCateList(fid, sid) {\r\n\t\t\t\tthis.$http.get(\"v1/index\").then(res => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.courseList = res.data.data.course;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tloadData: function(source) {\r\n\t\t\t\t//这里是将订单挂载到tab列表下\r\n\t\t\t\tlet index = this.currenttabs1;\r\n\t\t\t\tlet navItem = this.listtabs1[index];\r\n\t\t\t\tlet status = navItem.status;\r\n\t\t\t\tlet page = navItem.current_page;\r\n\r\n\t\t\t\tif (source === 'tabChange' && navItem.loaded === true) {\r\n\t\t\t\t\t//tab切换只有第一次需要加载数据\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (navItem.loadingType === 'loading') {\r\n\t\t\t\t\t//防止重复加载\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnavItem.loadingType = 'loading';\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: 'https://practice.jpworld.cn/api/v1/getRecommend', //仅为示例，并非真实接口地址。\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tpage: page,\r\n\t\t\t\t\t},\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'custom-header': 'hello' //自定义请求头信息\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data.data)\r\n\t\t\t\t\t\tthis.empty = false;\r\n\t\t\t\t\t\t// this.current_page = res.data.data.list.current_page;\r\n\t\t\t\t\t\t// this.total_page = res.data.data.list.last_page;\r\n\t\t\t\t\t\t// this.total = res.data.data.list.total;\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tlet orderList = res.data.data.filter(item => {\r\n\t\t\t\t\t\t\t//添加不同状态下订单的表现形式\r\n\t\t\t\t\t\t\titem = Object.assign(item, this.orderStateExp(item.group_name));\r\n\t\t\t\t\t\t\t//演示数据所以自己进行状态筛选\r\n\t\t\t\t\t\t\t// if (status === 0) {\r\n\t\t\t\t\t\t\t// \tif (item.status === 0 || item.status === 1) {\r\n\t\t\t\t\t\t\t// \t\t//0为全部订单\r\n\t\t\t\t\t\t\t// \t\treturn item;\r\n\t\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t\treturn item.group_name === status\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t//let orderList = pagination.data;\r\n\t\t\t\t\t\tconsole.log(orderList)\r\n\t\t\t\t\t\torderList.forEach(item => {\r\n\t\t\t\t\t\t\tnavItem.orderList.push(item);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页\r\n\t\t\t\t\t\tthis.$set(navItem, 'loaded', true);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t//判断是否还有数据， 有改为 more， 没有改为noMore \r\n\t\t\t\t\t\tnavItem.loadingType = 'noMore';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tpage++;\r\n\t\t\t\t\t\tthis.listtabs1[index].current_page = page;\r\n\t\t\t\t\t\tconsole.log(this.listtabs1)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t},\r\n\r\n\t\t\t//订单状态文字和颜色\r\n\t\t\torderStateExp(status) {\r\n\t\t\t\tlet stateTip = '',\r\n\t\t\t\t\tstateTipColor = '#fa436a';\r\n\t\t\t\tswitch (+status) {\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\tstateTip = '现已开课';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\tstateTip = '往期课程';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 3:\r\n\t\t\t\t\t\tstateTip = '已完成';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 9:\r\n\t\t\t\t\t\tstateTip = '订单已关闭';\r\n\t\t\t\t\t\tstateTipColor = '#909399';\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t//更多自定义\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\tstateTip,\r\n\t\t\t\t\tstateTipColor\r\n\t\t\t\t};\r\n\t\t\t},\r\n\r\n\t\t\ttabClick(index) {\r\n\t\t\t\tthis.currenttabs1 = index;\r\n\r\n\t\t\t},\r\n\t\t\tchangeTab(e) {\r\n\t\t\t\tthis.currenttabs1 = e.target.current;\r\n\t\t\t\tthis.loadData('tabChange');\r\n\t\t\t},\r\n\r\n\t\t\t//筛选点击\r\n\t\t\t// tabClick(index) {\r\n\t\t\t// \tif (this.filterIndex === index && index !== 2) {\r\n\t\t\t// \t\treturn;\r\n\t\t\t// \t}\r\n\t\t\t// \tthis.filterIndex = index;\r\n\t\t\t// \tif (index === 2) {\r\n\t\t\t// \t\tthis.priceOrder = this.priceOrder === 1 ? 2 : 1;\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tthis.priceOrder = 0;\r\n\t\t\t// \t}\r\n\t\t\t// \tuni.pageScrollTo({\r\n\t\t\t// \t\tduration: 300,\r\n\t\t\t// \t\tscrollTop: 0\r\n\t\t\t// \t})\r\n\t\t\t// \tthis.loadData('refresh', 1);\r\n\t\t\t// \tuni.showLoading({\r\n\t\t\t// \t\ttitle: '正在加载'\r\n\t\t\t// \t})\r\n\t\t\t// },\r\n\t\t\t//显示分类面板\r\n\t\t\ttoggleCateMask(type) {\r\n\t\t\t\tlet timer = type === 'show' ? 10 : 300;\r\n\t\t\t\tlet state = type === 'show' ? 1 : 0;\r\n\t\t\t\tthis.cateMaskState = 2;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.cateMaskState = state;\r\n\t\t\t\t}, timer)\r\n\t\t\t},\r\n\t\t\t//分类点击\r\n\t\t\tchangeCate(item) {\r\n\t\t\t\tthis.cateId = item.id;\r\n\t\t\t\tthis.toggleCateMask();\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tduration: 300,\r\n\t\t\t\t\tscrollTop: 0\r\n\t\t\t\t})\r\n\t\t\t\tthis.loadData('refresh', 1);\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/product/product?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tstopPrevent() {},\r\n\t\t\tnavToWeb(item) {\r\n\t\t\t\tswitch (item.type) {\r\n\t\t\t\t\tcase \"web\": // 项目外部跳转，需要使用web-view跳转外部H5页面\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: \"/pages/webView/webView?data=\" + encodeURIComponent(JSON.stringify(item))\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"mini_app\": // 项目内部跳转\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: item.link\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"popu\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tthis.module = \"show\";\r\n\t\t\t\t\t\tthis.moduleTitle = item.title;\r\n\t\t\t\t\t\tthis.moduleContent = item.description;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"other_mini\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tthis.getUrl(item.app_id, item.link)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"file\": // 当前页内部弹窗，不跳转\r\n\t\t\t\t\t\tlet downloadTask = uni.downloadFile({\r\n\t\t\t\t\t\t\turl:item.file,\r\n\t\t\t\t\t\t\tsuccess:function(res){\r\n\t\t\t\t\t\t\t\tvar filePath = res.tempFilePath;\r\n\t\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\t\tfilePath:filePath,\r\n\t\t\t\t\t\t\t\t\tshowMenu:true,\r\n\t\t\t\t\t\t\t\t\tsuccess:function(res){\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('打开成功');\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail:function(err){\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('打开失败：',err);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage,\r\n\t.content {\r\n\t\t// background: $page-color-base;\r\n\t\t// height: 100%;\r\n\t}\r\n\r\n\t.swiper-box {\r\n\t\t// height: 100%;\r\n\t}\r\n\r\n\t.list-scroll-content {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.navbar {\r\n\t\tdisplay: flex;\r\n\t\theight: 40px;\r\n\t\tpadding: 0 5px;\r\n\t\tbackground: #fff;\r\n\t\tbox-shadow: 0 1px 5px rgba(0, 0, 0, .06);\r\n\t\tposition: relative;\r\n\t\tz-index: 10;\r\n\r\n\t\t.nav-item {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&.current {\r\n\t\t\t\tcolor: #0070C0;\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 44px;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-bottom: 2px solid #0070C0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.list-box {\r\n\t\t// padding-bottom: 20rpx;\r\n\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\r\n\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 20rpx;\r\n\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.cover-large-box {\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, .5) !important;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.des {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.end {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #0070C0;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t:last-child {\r\n\t\t\t// border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.service-main {\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.h2title {\r\n\t\tcolor: #000;\r\n\t\tdisplay: block;\r\n\t\tfont-size: 35rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-left: 40rpx;\r\n\t}\r\n\r\n\t.listbox {\r\n\t\tbackground: #fff;\r\n\t\tborder: 1rpx solid #ebebeb;\r\n\t\tborder-radius: 8rpx;\r\n\t\t// margin: 40rpx 40rpx 0;\r\n\t\t// padding-bottom: 20rpx;\r\n\t}\r\n\r\n\t.titlebox {\r\n\t\talign-items: center;\r\n\t\tcolor: #3888ff;\r\n\t\tdisplay: flex;\r\n\t\theight: 60rpx;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding-bottom: 18rpx;\r\n\t\tpadding-top: 36rpx;\r\n\t}\r\n\r\n\t.service-list {\r\n\t\tbackground-color: initial !important;\r\n\t\tborder: 1rpx solid transparent !important;\r\n\t\tbox-shadow: none !important;\r\n\t\tmargin-top: 0 !important;\r\n\t}\r\n\r\n\t.service-list-title {\r\n\t\tpadding-left: 0rpx !important;\r\n\t}\r\n\r\n\t.viewtitle {\r\n\t\tfont-weight: 700;\r\n\t}\r\n\r\n\t.service-main .service-hot-title {\r\n\t\talign-items: center;\r\n\t\tcolor: #3888ff;\r\n\t\tdisplay: inline-flex;\r\n\t\tfont-size: 30rpx;\r\n\t\theight: 40rpx;\r\n\t\tjustify-content: space-between;\r\n\t\tline-height: 40rpx;\r\n\t\twidth: 133rpx;\r\n\t}\r\n\r\n\t.content.service-hot-list {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\t// margin-top: 20rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-title .refresh-icon {\r\n\t\theight: 27rpx;\r\n\t\twidth: 30rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item {\r\n\t\talign-items: center;\r\n\t\tbox-shadow: inset 0 -1rpx 0 0 #ebebeb;\r\n\t\tdisplay: flex;\r\n\t\tmargin: 0 40rpx;\r\n\t\tpadding: 36rpx 0;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item .title {\r\n\t\tcolor: #000;\r\n\t\tfont-family: PingFangSC-Regular;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tmax-width: 540rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item .tag {\r\n\t\tbackground: rgba(66, 147, 244, .1);\r\n\t\tborder-radius: 4rpx;\r\n\t\tcolor: #4293f4;\r\n\t\tdisplay: inline-block;\r\n\t\tfont-family: PingFangSC-Regular;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 700;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-left: 12rpx;\r\n\t\tpadding: 0 12rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item .arrow {\r\n\t\theight: 24rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\twidth: 14rpx;\r\n\t}\r\n\r\n\t.service-main .service-hot-list .service-hot-item:last-child {\r\n\t\tbox-shadow: none;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-recommend.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list-recommend.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689563470\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}