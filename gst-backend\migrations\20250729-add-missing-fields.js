'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 检查 study_groups 表是否存在 is_public 字段
      const studyGroupsTableInfo = await queryInterface.describeTable('study_groups');
      
      if (!studyGroupsTableInfo.is_public) {
        console.log('添加 study_groups.is_public 字段...');
        await queryInterface.addColumn('study_groups', 'is_public', {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
          comment: '是否为公共小组'
        }, { transaction });
      }

      // 检查 courses 表是否存在 is_public 字段
      const coursesTableInfo = await queryInterface.describeTable('courses');
      
      if (!coursesTableInfo.is_public) {
        console.log('添加 courses.is_public 字段...');
        await queryInterface.addColumn('courses', 'is_public', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          comment: '是否为公共课程'
        }, { transaction });
      }

      // 检查 course_units 表是否存在 is_free 字段
      if (!coursesTableInfo.is_free) {
        console.log('添加 course_units.is_free 字段...');
        await queryInterface.addColumn('course_units', 'is_free', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          comment: '是否免费试听'
        }, { transaction });
      }

      // 检查 course_units 表是否存在 video_id 字段
      const courseUnitsTableInfo = await queryInterface.describeTable('course_units');
      
      if (!courseUnitsTableInfo.video_id) {
        console.log('添加 course_units.video_id 字段...');
        await queryInterface.addColumn('course_units', 'video_id', {
          type: Sequelize.STRING,
          allowNull: true,
          comment: '视频ID'
        }, { transaction });
      }

      if (!courseUnitsTableInfo.play_id) {
        console.log('添加 course_units.play_id 字段...');
        await queryInterface.addColumn('course_units', 'play_id', {
          type: Sequelize.STRING,
          allowNull: true,
          comment: '播放ID'
        }, { transaction });
      }

      await transaction.commit();
      console.log('✅ 数据库字段添加完成');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 数据库迁移失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 删除添加的字段
      await queryInterface.removeColumn('study_groups', 'is_public', { transaction });
      await queryInterface.removeColumn('courses', 'is_public', { transaction });
      await queryInterface.removeColumn('course_units', 'is_free', { transaction });
      await queryInterface.removeColumn('course_units', 'video_id', { transaction });
      await queryInterface.removeColumn('course_units', 'play_id', { transaction });
      
      await transaction.commit();
      console.log('✅ 数据库字段回滚完成');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 数据库回滚失败:', error);
      throw error;
    }
  }
};
