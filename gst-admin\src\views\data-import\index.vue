﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>数据导入</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="downloadTemplate">
          <el-icon><Download /></el-icon>
          下载模板
        </el-button>
      </div>
    </div>

    <!-- 导入类型选择 -->
    <div class="import-types">
      <el-row :gutter="24">
        <el-col :span="8" v-for="type in importTypes" :key="type.key">
          <el-card
            class="import-type-card"
            :class="{ active: selectedType === type.key }"
            @click="selectType(type.key)"
            shadow="hover"
          >
            <div class="type-icon">
              <el-icon :size="32" :color="type.color">
                <component :is="type.icon" />
              </el-icon>
            </div>
            <div class="type-content">
              <h3>{{ type.title }}</h3>
              <p>{{ type.description }}</p>
              <div class="type-meta">
                <el-tag size="small">{{ type.format }}</el-tag>
                <span class="file-size">最大{{ type.maxSize }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section" v-if="selectedType">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>上传{{ getTypeTitle(selectedType) }}文件</h3>
            <el-button link @click="clearFiles">
              <el-icon><Delete /></el-icon>
              清空文件
            </el-button>
          </div>
        </template>

        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :on-exceed="handleExceed"
            :limit="1"
            drag
            multiple
            :accept="getAcceptTypes(selectedType)"
          >
            <div class="upload-content">
              <el-icon class="upload-icon" :size="48">
                <UploadFilled />
              </el-icon>
              <div class="upload-text">
                <p>将文件拖到此处，或<em>点击上传</em></p>
                <p class="upload-hint">
                  支持 {{ getAcceptTypes(selectedType) }} 格式，文件大小不超过 {{ getMaxSize(selectedType) }}
                </p>
              </div>
            </div>
          </el-upload>
        </div>

        <!-- 上传进度 -->
        <div class="upload-progress" v-if="uploading">
          <el-progress
            :percentage="uploadProgress"
            :status="uploadStatus"
            :stroke-width="8"
          />
          <p class="progress-text">{{ uploadProgressText }}</p>
        </div>

        <!-- 文件预览 -->
        <div class="file-preview" v-if="fileList.length > 0">
          <h4>已选择文件</h4>
          <div class="file-list">
            <div
              class="file-item"
              v-for="file in fileList"
              :key="file.uid"
            >
              <div class="file-info">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">
                    <span>{{ formatFileSize(file.size) }}</span>
                    <span>{{ formatDate(file.lastModified) }}</span>
                  </div>
                </div>
              </div>
              <div class="file-actions">
                <el-button
                  link
                  size="small"
                  @click="previewFile(file)"
                  v-if="canPreview(file)"
                >
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
                <el-button
                  link
                  size="small"
                  @click="removeFile(file)"
                  class="danger-button"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 导入选项 -->
        <div class="import-options" v-if="fileList.length > 0">
          <h4>导入选项</h4>
          <el-form :model="importConfig" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据处理方式">
                  <el-radio-group v-model="importConfig.mode">
                    <el-radio label="insert">仅插入新数据</el-radio>
                    <el-radio label="update">更新已存在数据</el-radio>
                    <el-radio label="replace">替换所有数据</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="错误处理">
                  <el-radio-group v-model="importConfig.errorHandling">
                    <el-radio label="skip">跳过错误行</el-radio>
                    <el-radio label="stop">遇错停止</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="是否发送通知">
              <el-switch v-model="importConfig.sendNotification" />
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作按钮 -->
        <div class="upload-actions" v-if="fileList.length > 0">
          <el-button @click="clearFiles">取消</el-button>
          <el-button
            type="primary"
            @click="startImport"
            :loading="importing"
            :disabled="fileList.length === 0"
          >
            <el-icon><Upload /></el-icon>
            {{ importing ? '导入中...' : '开始导入' }}
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 导入历史 -->
    <div class="import-history">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>导入历史</h3>
            <el-button link @click="loadImportHistory">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>

        <el-table :data="importHistory" v-loading="historyLoading">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ getTypeTitle(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fileName" label="文件名" min-width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalRows" label="总行数" width="100" />
          <el-table-column prop="successRows" label="成功" width="100" />
          <el-table-column prop="errorRows" label="失败" width="100" />
          <el-table-column prop="createdAt" label="导入时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button link size="small" @click="viewImportDetail(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
              <el-button
                link
                size="small"
                @click="downloadErrorLog(row)"
                v-if="row.errorRows > 0"
              >
                <el-icon><Download /></el-icon>
                错误日志
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper" v-if="historyTotal > 0">
          <el-pagination
            v-model:current-page="historyPagination.page"
            v-model:page-size="historyPagination.size"
            :page-sizes="[10, 20, 50]"
            :total="historyTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadImportHistory"
            @current-change="loadImportHistory"
          />
        </div>
      </el-card>
    </div>

    <!-- 文件预览对话框 -->
    <FilePreview
      v-model="showPreviewDialog"
      :file="previewFile"
    />

    <!-- 导入详情对话框 -->
    <ImportDetail
      v-model="showDetailDialog"
      :import-record="selectedImport"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, uploadFile } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import FilePreview from './components/FilePreview.vue'
import ImportDetail from './components/ImportDetail.vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const importing = ref(false)
const historyLoading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadProgressText = ref('')
const selectedType = ref('')
const fileList = ref([])
const importHistory = ref([])
const historyTotal = ref(0)
const showPreviewDialog = ref(false)
const showDetailDialog = ref(false)
const previewFile = ref(null)
const selectedImport = ref(null)
const uploadRef = ref()

// 导入配置
const importConfig = reactive({
  mode: 'insert',
  errorHandling: 'skip',
  sendNotification: true
})

// 历史记录分页
const historyPagination = reactive({
  page: 1,
  size: 20
})

// 导入类型配置
const importTypes = [
  {
    key: 'users',
    title: '用户数据',
    description: '批量导入用户信息，包括学生、教师等',
    icon: 'User',
    color: '#409eff',
    format: 'Excel/CSV',
    maxSize: '10MB',
    accept: '.xlsx,.xls,.csv'
  },
  {
    key: 'courses',
    title: '课程数据',
    description: '批量导入课程信息和教学内容',
    icon: 'Reading',
    color: '#67c23a',
    format: 'Excel/CSV',
    maxSize: '20MB',
    accept: '.xlsx,.xls,.csv'
  },
  {
    key: 'groups',
    title: '小组数据',
    description: '批量导入学习小组和成员关系',
    icon: 'UserFilled',
    color: '#e6a23c',
    format: 'Excel/CSV',
    maxSize: '5MB',
    accept: '.xlsx,.xls,.csv'
  }
]

// 上传配置
const uploadUrl = computed(() => `/api/import/${selectedType.value}`)
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

// 选择导入类型
const selectType = (type) => {
  selectedType.value = type
  clearFiles()
}

// 获取类型标题
const getTypeTitle = (type) => {
  const typeConfig = importTypes.find(t => t.key === type)
  return typeConfig ? typeConfig.title : '未知类型'
}

// 获取接受的文件类型
const getAcceptTypes = (type) => {
  const typeConfig = importTypes.find(t => t.key === type)
  return typeConfig ? typeConfig.accept : '*'
}

// 获取最大文件大小
const getMaxSize = (type) => {
  const typeConfig = importTypes.find(t => t.key === type)
  return typeConfig ? typeConfig.maxSize : '10MB'
}

// 文件上传前检查
const beforeUpload = (file) => {
  const typeConfig = importTypes.find(t => t.key === selectedType.value)
  if (!typeConfig) return false

  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const acceptTypes = typeConfig.accept.split(',')
  const isValidType = acceptTypes.some(type => fileName.endsWith(type.trim()))

  if (!isValidType) {
    ElMessage.error(`只支持 ${typeConfig.accept} 格式的文件`)
    return false
  }

  // 检查文件大小
  const maxSizeMap = { '5MB': 5, '10MB': 10, '20MB': 20 }
  const maxSize = maxSizeMap[typeConfig.maxSize] || 10
  const isLtMaxSize = file.size / 1024 / 1024 < maxSize

  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${typeConfig.maxSize}`)
    return false
  }

  uploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = 'active'
  uploadProgressText.value = '准备上传...'

  return true
}

// 文件上传成功
const handleUploadSuccess = (response, file) => {
  uploading.value = false
  uploadProgress.value = 100
  uploadStatus.value = 'success'
  uploadProgressText.value = '上传完成'

  if (response.success) {
    ElMessage.success('文件上传成功')
    // 更新文件列表中的文件信息
    const fileIndex = fileList.value.findIndex(f => f.uid === file.uid)
    if (fileIndex > -1) {
      fileList.value[fileIndex] = {
        ...fileList.value[fileIndex],
        response: response.data,
        status: 'success'
      }
    }
  } else {
    uploadStatus.value = 'exception'
    uploadProgressText.value = '上传失败'
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = (error, file) => {
  uploading.value = false
  uploadProgress.value = 0
  uploadStatus.value = 'exception'
  uploadProgressText.value = '上传失败'

  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败，请重试')
}

// 文件移除
const handleFileRemove = (file) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 文件数量超限
const handleExceed = (files, fileList) => {
  ElMessage.warning('每次只能上传一个文件，请先删除已选择的文件')
}

// 清空文件
const clearFiles = () => {
  fileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  uploading.value = false
  uploadProgress.value = 0
}

// 移除文件
const removeFile = (file) => {
  handleFileRemove(file)
}

// 预览文件
const previewFile = (file) => {
  if (canPreview(file)) {
    previewFile.value = file
    showPreviewDialog.value = true
  }
}

// 检查是否可以预览
const canPreview = (file) => {
  const previewableTypes = ['.xlsx', '.xls', '.csv', '.txt']
  return previewableTypes.some(type => file.name.toLowerCase().endsWith(type))
}

// 开始导入
const startImport = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    await ElMessageBox.confirm('确定要开始导入数据吗？', '确认导入', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    importing.value = true

    const file = fileList.value[0]
    const importData = {
      type: selectedType.value,
      fileName: file.name,
      fileId: file.response?.id,
      config: importConfig
    }

    const response = await post('/api/import/start', importData, {
      showLoading: true,
      showSuccess: true
    })

    if (response.success) {
      ElMessage.success('导入任务已启动，请稍后查看导入结果')
      clearFiles()
      selectedType.value = ''
      loadImportHistory()
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('导入失败:', error)
    }
  } finally {
    importing.value = false
  }
}

// 下载模板
const downloadTemplate = () => {
  if (!selectedType.value) {
    ElMessage.warning('请先选择导入类型')
    return
  }

  const link = document.createElement('a')
  link.href = `/api/import/template/${selectedType.value}`
  link.download = `${getTypeTitle(selectedType.value)}导入模板.xlsx`
  link.click()

  ElMessage.success('模板下载已开始')
}

// 加载导入历史
const loadImportHistory = async () => {
  historyLoading.value = true
  try {
    const params = {
      page: historyPagination.page,
      size: historyPagination.size
    }

    const response = await get('/api/import/history', params)
    if (response.success) {
      importHistory.value = response.data.records || []
      historyTotal.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载导入历史失败:', error)
    // 使用模拟数据
    importHistory.value = [
      {
        id: 1,
        type: 'users',
        fileName: '用户数据.xlsx',
        status: 'completed',
        totalRows: 150,
        successRows: 148,
        errorRows: 2,
        createdAt: new Date('2024-07-28T10:30:00')
      },
      {
        id: 2,
        type: 'courses',
        fileName: '课程数据.xlsx',
        status: 'processing',
        totalRows: 50,
        successRows: 30,
        errorRows: 0,
        createdAt: new Date('2024-07-28T14:15:00')
      }
    ]
    historyTotal.value = importHistory.value.length
  } finally {
    historyLoading.value = false
  }
}

// 查看导入详情
const viewImportDetail = (record) => {
  selectedImport.value = record
  showDetailDialog.value = true
}

// 下载错误日志
const downloadErrorLog = (record) => {
  const link = document.createElement('a')
  link.href = `/api/import/${record.id}/error-log`
  link.download = `导入错误日志_${record.id}.xlsx`
  link.click()

  ElMessage.success('错误日志下载已开始')
}

// 刷新数据
const refreshData = () => {
  loadImportHistory()
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'info',
    processing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || '未知'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadImportHistory()
})
</script>

<style lang="scss" scoped>
.import-types {
  margin-bottom: var(--spacing-xl);

  .import-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--box-shadow-light);
    }

    &.active {
      border-color: var(--primary-color);
      box-shadow: var(--box-shadow-light);
    }

    .type-icon {
      text-align: center;
      margin-bottom: var(--spacing-md);
    }

    .type-content {
      text-align: center;

      h3 {
        margin: 0 0 var(--spacing-sm) 0;
        font-size: var(--font-size-medium);
        font-weight: 600;
        color: var(--text-primary);
      }

      p {
        margin: 0 0 var(--spacing-md) 0;
        font-size: var(--font-size-small);
        color: var(--text-secondary);
        line-height: 1.5;
      }

      .type-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .file-size {
          font-size: var(--font-size-small);
          color: var(--text-secondary);
        }
      }
    }
  }
}

.upload-section {
  margin-bottom: var(--spacing-xl);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: var(--font-size-medium);
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .upload-area {
    margin-bottom: var(--spacing-lg);

    .upload-dragger {
      width: 100%;

      :deep(.el-upload-dragger) {
        width: 100%;
        height: 200px;
        border: 2px dashed var(--border-base);
        border-radius: var(--border-radius-base);
        background: var(--bg-color-page);
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--primary-color);
          background: rgba(64, 158, 255, 0.05);
        }
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .upload-icon {
          color: var(--text-secondary);
          margin-bottom: var(--spacing-md);
        }

        .upload-text {
          text-align: center;

          p {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: var(--font-size-base);
            color: var(--text-primary);

            em {
              color: var(--primary-color);
              font-style: normal;
            }
          }

          .upload-hint {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
          }
        }
      }
    }
  }

  .upload-progress {
    margin-bottom: var(--spacing-lg);

    .progress-text {
      margin: var(--spacing-sm) 0 0 0;
      font-size: var(--font-size-small);
      color: var(--text-secondary);
      text-align: center;
    }
  }

  .file-preview {
    margin-bottom: var(--spacing-lg);

    h4 {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--text-primary);
    }

    .file-list {
      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-md);
        background: var(--bg-color-page);
        border-radius: var(--border-radius-base);
        margin-bottom: var(--spacing-sm);

        &:last-child {
          margin-bottom: 0;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: var(--spacing-md);

          .file-icon {
            font-size: 24px;
            color: var(--primary-color);
          }

          .file-details {
            .file-name {
              font-size: var(--font-size-base);
              font-weight: 500;
              color: var(--text-primary);
              margin-bottom: var(--spacing-xs);
            }

            .file-meta {
              display: flex;
              gap: var(--spacing-md);
              font-size: var(--font-size-small);
              color: var(--text-secondary);
            }
          }
        }

        .file-actions {
          display: flex;
          gap: var(--spacing-sm);

          .danger-button {
            color: var(--danger-color);

            &:hover {
              background: var(--danger-color);
              color: white;
            }
          }
        }
      }
    }
  }

  .import-options {
    margin-bottom: var(--spacing-lg);

    h4 {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .upload-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-base);
  }
}

.import-history {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: var(--font-size-medium);
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .import-types {
    .el-row {
      .el-col {
        margin-bottom: var(--spacing-lg);
      }
    }
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: var(--spacing-md);

    .file-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .upload-actions {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}
</style>
