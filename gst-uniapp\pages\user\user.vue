<template>
	<view>

		<view class="user-section">
			<image class="bg" src="/static/user-bg.jpg"></image>
			<view class="user-info-box" @tap="login()">
				<view class="portrait-box">
					<image class="portrait" :src="user.userInfo.avatar || '/static/missing-face.png'"></image>
				</view>
				<view class="info-box" style="margin-left: 40rpx;text-align: left;">
					<view class="username">{{user.userInfo.name || '暂未登录'}}</view>
					<view class="sign" v-if="!is_login">点击立即登录</view>
					<view class="vip-box">
						<text class="vip" v-if="user.member && user.member.status==1">VIP <text
								style="font-size: 20rpx;"> 到期日期 {{user.member.end_date}}</text> </text>
						<!-- <text class="vip" v-else @tap="goAuth">报名成为会员</text> -->
					</view>
				</view>
			</view>
		</view>

		<view class="cover-container" :style="[{
				transform: coverTransform,
				transition: coverTransition
			}]" @touchstart="coverTouchstart" @touchmove="coverTouchmove" @touchend="coverTouchend">
			<image class="arc" src="/static/arc.png"></image>
			<!-- <view class="tj-sction">
				<view class="tj-item">
					<text class="num">{{user.member ? user.member.course_num : 0}}</text>
					<text>已学课程数</text>
				</view>
				<view class="tj-item">
					<text class="num">{{user.member ? user.member.trans_num : 0}}</text>
					<text>已翻译数</text>
				</view> -->
			<!-- <view class="tj-item">
					<text class="num">20</text>
					<text>代金券</text>
				</view> -->
			<!-- </view> -->
			<!-- 浏览历史 -->
			<view class="history-section icon">
				<list-cell icon="icon-gouwuche_" iconColor="#e07472" title="我的订单"
					@eventClick="navTo('/pages/order/list')"></list-cell>
				<!-- <list-cell icon="icon-iconfontweixin" iconColor="#e07472" title="我的课程" @eventClick="navTo('/pages/projects/list')"></list-cell> -->
				<!-- <list-cell icon="icon-iLinkapp-" iconColor="#5fcda2" title="我的作业" @eventClick="navTo('/pages/article/list')"></list-cell> -->
				<!-- <list-cell icon="icon-saomiao" iconColor="#e07472" title="证书下载" @eventClick="navTo('/pages/certificate/certificate')"></list-cell> -->
				<list-cell icon="icon-share" iconColor="#9789f7" title="我的兑换"
					@eventClick="navTo('/pages/my-exchange/my-exchange')"></list-cell>
				<!-- <list-cell icon="icon-saomiao" iconColor="#9789f7" title="我的推广" @eventClick="navTo('/pages/popularize/popularize')"></list-cell>
				 -->
				<list-cell icon="icon-shoucang" iconColor="#e07472" title="我的收藏"
					@eventClick="navTo('/pages/order/collect')"></list-cell>
				<!-- <list-cell icon=".icon-dianhua-copy" iconColor="#9789f7" title="客服中心" @eventClick="navTo('/pages/service/service')"></list-cell> -->
				<list-cell icon="icon-pinglun-copy" iconColor="#e07472" title="联系我们"
					@eventClick="navTo('/pages/about-us/about-us')"></list-cell>
				<list-cell icon="icon-huifu" iconColor="#e07472" title="退出登录" @tap="out()"></list-cell>
			</view>
		</view>


		<!-- 自定义底部导航 -->
		<custom-tabbar />
	</view>
</template>
<script>
	import listCell from '@/components/mix-list-cell';
	import CustomTabbar from '@/components/custom-tabbar.vue';
	import {
		mapState
	} from 'vuex';
	let startY = 0,
		moveY = 0,
		pageAtTop = true;
	export default {
		components: {
			listCell,
			CustomTabbar
		},
		data() {
			return {
				ver: this.$store.state.ver,
				coverTransform: 'translateY(0px)',
				coverTransition: '0s',
				moving: false,
				user: null,
				is_login: false,
			}
		},
		onLoad() {},
		// #ifndef MP
		onNavigationBarButtonTap(e) {
			const index = e.index;
			if (index === 0) {
				this.navTo('/pages/set/set');
			} else if (index === 1) {
				// #ifdef APP-PLUS
				const pages = getCurrentPages();
				const page = pages[pages.length - 1];
				const currentWebview = page.$getAppWebview();
				currentWebview.hideTitleNViewButtonRedDot({
					index
				});
				// #endif
				uni.navigateTo({
					url: '/pages/notice/notice'
				})
			}
		},
		// #endif
		onShow() {
			this.user = this.$store.state.user;

			if (!this.$store.state.user.token) {
				uni.showModal({
					title: '请先登录',
					content: '登录后才能进行操作',
					success: function(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: "/pages/login/login?type=wx"
							})
						} else if (res.cancel) {
							/* uni.switchtab({
								url: "/pages/index/index"
							}) */
						}
					}
				});
			} else {
				this.is_login = true;
				this.$store.dispatch('refreshUserMember');
			}
		},
		methods: {

			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			navTo(url) {
				uni.navigateTo({
					url
				})
			},

			/**
			 *  会员卡下拉和回弹
			 *  1.关闭bounce避免ios端下拉冲突
			 *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
			 *    transition设置0.1秒延迟，让css来过渡这段空窗期
			 *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
			 */
			coverTouchstart(e) {
				if (pageAtTop === false) {
					return;
				}
				this.coverTransition = 'transform .1s linear';
				startY = e.touches[0].clientY;
			},
			coverTouchmove(e) {
				moveY = e.touches[0].clientY;
				let moveDistance = moveY - startY;
				if (moveDistance < 0) {
					this.moving = false;
					return;
				}
				this.moving = true;
				if (moveDistance >= 80 && moveDistance < 100) {
					moveDistance = 80;
				}

				if (moveDistance > 0 && moveDistance <= 80) {
					this.coverTransform = `translateY(${moveDistance}px)`;
				}
			},
			coverTouchend() {
				if (this.moving === false) {
					return;
				}
				this.moving = false;
				this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)';
				this.coverTransform = 'translateY(0px)';
			},
			login() {
				if (!this.$store.state.user.token) {
					uni.showModal({
						title: '请先登录',
						content: '登录后才能进行操作',
						success: function(res) {
							if (res.confirm) {
								self.reload = true;
								uni.navigateTo({
									url: "/pages/login/login?type=wx"
								})
							} else if (res.cancel) {
								uni.navigateBack();
							}
						}
					});
				} else {
					uni.navigateTo({
						url: "/pages/userinfo/userinfo",
					});
				}
			},
			out() {
				var _this = this;
				uni.showModal({
					title: '退出登录',
					content: '请确认是否退出登录当前账号',
					success: function(res) {
						if (res.confirm) {
							// 使用store的清理方法，完整清理所有用户数据
							_this.$store.commit('clearUserData');

							// 显示退出成功提示
							uni.showToast({
								title: '退出成功',
								icon: 'success',
								duration: 1500
							});

							// 延迟重载页面，让用户看到提示
							setTimeout(() => {
								_this.reload();
							}, 1500);
						} else if (res.cancel) {

						}
					}
				});
			},
			reload() {
				// 页面重载
				const pages = getCurrentPages()
				// 声明一个pages使用getCurrentPages方法
				const curPage = pages[pages.length - 1]
				console.log(curPage);
				// 声明一个当前页面
				curPage.onLoad(curPage.options) // 传入参数
				curPage.onShow()
				curPage.onReady()
				// 执行刷新
			},

		}
	}
</script>
<style lang='scss'>
	page {
		background-color: #f8f8f8;
	}

	%flex-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	%section {
		display: flex;
		justify-content: space-around;
		align-content: center;
		background: #fff;
		border-radius: 10upx;
	}

	.user-section {
		height: 520upx;
		padding: 100upx 30upx 0;
		position: relative;

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			filter: blur(1px);
			opacity: .7;
		}
	}

	.user-info-box {
		height: 180upx;
		display: flex;
		align-items: center;
		position: relative;
		z-index: 1;

		.portrait {
			width: 130upx;
			height: 130upx;
			border: 5upx solid #fff;
			border-radius: 50%;
		}

		.username {
			font-size: $font-lg + 6upx;
			color: $font-color-dark;
			margin-left: 20upx;
		}
	}

	.vip-card-box {
		display: flex;
		flex-direction: column;
		color: #f7d680;
		height: 240upx;
		background: linear-gradient(left, rgba(0, 0, 0, .7), rgba(0, 0, 0, .8));
		border-radius: 16upx 16upx 0 0;
		overflow: hidden;
		position: relative;
		padding: 20upx 24upx;

		.card-bg {
			position: absolute;
			top: 20upx;
			right: 0;
			width: 380upx;
			height: 260upx;
		}

		.b-btn {
			position: absolute;
			right: 20upx;
			top: 16upx;
			width: 132upx;
			height: 40upx;
			text-align: center;
			line-height: 40upx;
			font-size: 22upx;
			color: #36343c;
			border-radius: 20px;
			background: linear-gradient(left, #f9e6af, #ffd465);
			z-index: 1;
		}

		.tit {
			font-size: $font-base+2upx;
			color: #f7d680;
			margin-bottom: 28upx;

			.yticon {
				color: #f6e5a3;
				margin-right: 16upx;
			}
		}

		.e-b {
			font-size: $font-sm;
			color: #d8cba9;
			margin-top: 10upx;
		}
	}

	.cover-container {
		background: $page-color-base;
		margin-top: -150upx;
		padding: 0 30upx;
		position: relative;
		height: 100%;
		/* background: #f8f8f8; */
		padding-bottom: 20upx;

		.arc {
			position: absolute;
			left: 0;
			top: -34upx;
			width: 100%;
			height: 36upx;
		}
	}

	.tj-sction {
		@extend %section;

		.tj-item {
			@extend %flex-center;
			flex-direction: column;
			height: 140upx;
			font-size: $font-sm;
			color: #75787d;
		}

		.num {
			font-size: $font-lg;
			color: $font-color-dark;
			margin-bottom: 8upx;
		}
	}

	.order-section {
		@extend %section;
		padding: 28upx 0;
		margin-top: 20upx;

		.order-item {
			@extend %flex-center;
			width: 120upx;
			height: 120upx;
			border-radius: 10upx;
			font-size: $font-sm;
			color: $font-color-dark;
		}

		.yticon {
			font-size: 48upx;
			margin-bottom: 18upx;
			color: #fa436a;
		}

		.icon-shouhoutuikuan {
			font-size: 44upx;
		}
	}

	.history-section {
		padding: 30upx 0 0;
		margin-top: 20upx;
		background: #fff;
		border-radius: 10upx;

		.sec-header {
			display: flex;
			align-items: center;
			font-size: $font-base;
			color: $font-color-dark;
			line-height: 40upx;
			margin-left: 30upx;

			.yticon {
				font-size: 44upx;
				color: #5eba8f;
				margin-right: 16upx;
				line-height: 40upx;
			}
		}

		.h-list {
			white-space: nowrap;
			padding: 30upx 30upx 0;

			image {
				display: inline-block;
				width: 160upx;
				height: 160upx;
				margin-right: 20upx;
				border-radius: 10upx;
			}
		}
	}

	.vip-box {}

	.vip {
		position: absolute;
		font-size: 28rpx;
		background: #F59A23;
		color: #fff;
		padding: 5rpx 10rpx;
		border-radius: 10rpx
	}

	/* 自定义tabBar适配 */
	.page {
		padding-bottom: 120rpx; /* 为自定义tabBar留出空间 */
	}
</style>