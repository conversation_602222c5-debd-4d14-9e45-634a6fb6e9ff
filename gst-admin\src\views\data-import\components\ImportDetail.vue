<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入详情"
    width="900px"
    :close-on-click-modal="false"
  >
    <div class="import-detail" v-if="importRecord">
      <div class="detail-placeholder">
        <el-empty description="导入详情功能开发中..." :image-size="80">
          <p>导入ID：{{ importRecord.id }}</p>
          <p>文件名：{{ importRecord.fileName }}</p>
          <p>状态：{{ importRecord.status }}</p>
        </el-empty>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  importRecord: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style lang="scss" scoped>
.detail-placeholder {
  padding: var(--spacing-xl);
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}
</style>
