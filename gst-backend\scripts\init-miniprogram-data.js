const { sequelize } = require('../src/models');

async function initMiniprogramData() {
  try {
    console.log('开始初始化小程序数据...');

    const { Course, Category, StudyGroup, Banner, CourseUnit } = require('../src/models');

    // 1. 创建课程分类
    const categories = await Category.bulkCreate([
      {
        name: '新概念日语',
        description: '从零开始学习日语，系统掌握基础知识',
        image: '/images/categories/new-concept.jpg',
        sort: 1,
        status: 'active',
        courseCount: 0
      },
      {
        name: '日语语法',
        description: '系统学习日语语法规则和应用',
        image: '/images/categories/grammar.jpg',
        sort: 2,
        status: 'active',
        courseCount: 0
      },
      {
        name: '日语词汇',
        description: '扩充日语词汇量，提高表达能力',
        image: '/images/categories/vocabulary.jpg',
        sort: 3,
        status: 'active',
        courseCount: 0
      },
      {
        name: '日语听力',
        description: '提高日语听力理解能力',
        image: '/images/categories/listening.jpg',
        sort: 4,
        status: 'active',
        courseCount: 0
      },
      {
        name: '日语口语',
        description: '练习日语口语表达和发音',
        image: '/images/categories/speaking.jpg',
        sort: 5,
        status: 'active',
        courseCount: 0
      },
      {
        name: '商务日语',
        description: '学习商务场景下的日语应用',
        image: '/images/categories/business.jpg',
        sort: 6,
        status: 'active',
        courseCount: 0
      }
    ], { ignoreDuplicates: true });

    console.log('课程分类创建完成');

    // 2. 创建公共课程
    const courses = await Course.bulkCreate([
      {
        title: '新概念日语第一册',
        description: '适合零基础学员，从五十音图开始，系统学习日语基础知识',
        content: '本课程包含五十音图、基础语法、日常会话等内容，是日语学习的入门必修课程。',
        level: 'N5',
        categoryId: categories[0].id,
        category: 'grammar',
        duration: 1200, // 20小时
        difficulty: 1,
        price: 299.00,
        picture: '/images/courses/new-concept-1.jpg',
        isPublic: true,
        sort: 1,
        rating: 4.8,
        studentCount: 1250,
        status: 'published',
        createdBy: 1
      },
      {
        title: '新概念日语第二册',
        description: '进阶课程，深入学习日语语法和表达方式',
        content: '在第一册基础上，学习更复杂的语法结构和表达方式，提高日语综合能力。',
        level: 'N4',
        categoryId: categories[0].id,
        category: 'grammar',
        duration: 1500, // 25小时
        difficulty: 2,
        price: 399.00,
        picture: '/images/courses/new-concept-2.jpg',
        isPublic: true,
        sort: 2,
        rating: 4.7,
        studentCount: 890,
        status: 'published',
        createdBy: 1
      },
      {
        title: 'N5基础语法精讲',
        description: 'N5级别语法点详细讲解，配合大量例句和练习',
        content: '系统讲解N5级别的所有语法点，通过例句和练习帮助学员掌握基础语法。',
        level: 'N5',
        categoryId: categories[1].id,
        category: 'grammar',
        duration: 800, // 13.3小时
        difficulty: 1,
        price: 199.00,
        picture: '/images/courses/n5-grammar.jpg',
        isPublic: true,
        sort: 3,
        rating: 4.6,
        studentCount: 1560,
        status: 'published',
        createdBy: 1
      },
      {
        title: 'N5核心词汇1000',
        description: '掌握N5级别核心词汇，为日语学习打下坚实基础',
        content: '精选N5级别最重要的1000个词汇，通过科学的记忆方法帮助学员快速掌握。',
        level: 'N5',
        categoryId: categories[2].id,
        category: 'vocabulary',
        duration: 600, // 10小时
        difficulty: 1,
        price: 159.00,
        picture: '/images/courses/n5-vocabulary.jpg',
        isPublic: true,
        sort: 4,
        rating: 4.5,
        studentCount: 2100,
        status: 'published',
        createdBy: 1
      },
      {
        title: '日语听力入门',
        description: '从基础听力开始，逐步提高日语听力理解能力',
        content: '通过大量的听力练习，帮助学员适应日语语音语调，提高听力理解能力。',
        level: 'N5',
        categoryId: categories[3].id,
        category: 'listening',
        duration: 900, // 15小时
        difficulty: 2,
        price: 229.00,
        picture: '/images/courses/listening-basic.jpg',
        isPublic: true,
        sort: 5,
        rating: 4.4,
        studentCount: 780,
        status: 'published',
        createdBy: 1
      },
      {
        title: '日语口语发音纠正',
        description: '专业外教指导，纠正发音问题，提高口语表达',
        content: '由专业外教授课，针对中国学员常见的发音问题进行纠正和指导。',
        level: 'N5',
        categoryId: categories[4].id,
        category: 'speaking',
        duration: 720, // 12小时
        difficulty: 2,
        price: 359.00,
        picture: '/images/courses/pronunciation.jpg',
        isPublic: true,
        sort: 6,
        rating: 4.9,
        studentCount: 650,
        status: 'published',
        createdBy: 1
      }
    ], { ignoreDuplicates: true });

    console.log('公共课程创建完成');

    // 3. 为新概念日语第一册创建课程单元
    const newConceptUnits = [];
    const lessons = [
      '第1课：五十音图（あ行）',
      '第2课：五十音图（か行）',
      '第3课：五十音图（さ行）',
      '第4课：五十音图（た行）',
      '第5课：五十音图（な行）',
      '第6课：五十音图（は行）',
      '第7课：五十音图（ま行）',
      '第8课：五十音图（や行）',
      '第9课：五十音图（ら行）',
      '第10课：五十音图（わ行）',
      '第11课：基础问候语',
      '第12课：自我介绍',
      '第13课：数字和时间',
      '第14课：家族称呼',
      '第15课：日常用品',
      '第16课：颜色和形状',
      '第17课：方位和场所',
      '第18课：交通工具',
      '第19课：食物和饮料',
      '第20课：综合复习'
    ];

    lessons.forEach((title, index) => {
      newConceptUnits.push({
        courseId: courses[0].id,
        title,
        content: `${title}的详细内容讲解，包括发音、词汇、语法等。`,
        orderNum: index + 1,
        duration: 60, // 60分钟
        type: 'video',
        status: 'published',
        isRequired: true,
        isFree: index < 3, // 前3课免费试听
        videoId: `video_${courses[0].id}_${index + 1}`,
        playId: `play_${courses[0].id}_${index + 1}`,
        createdBy: 1
      });
    });

    await CourseUnit.bulkCreate(newConceptUnits, { ignoreDuplicates: true });

    console.log('新概念日语课程单元创建完成');

    // 4. 创建公共学习小组
    const groups = await StudyGroup.bulkCreate([
      {
        name: '新概念日语学习小组',
        description: '一起学习新概念日语，从零基础到入门',
        level: 'N5',
        status: 'active',
        maxMembers: 50,
        currentMembers: 23,
        isPublic: true,
        avatar: '/images/groups/new-concept-group.jpg',
        tags: JSON.stringify(['新概念', '零基础', '入门']),
        teacherId: 1,
        startDate: new Date(),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90天后
        createdBy: 1
      },
      {
        name: 'N5语法突破小组',
        description: '专注N5语法学习，系统掌握基础语法',
        level: 'N5',
        status: 'active',
        maxMembers: 30,
        currentMembers: 18,
        isPublic: true,
        avatar: '/images/groups/n5-grammar-group.jpg',
        tags: JSON.stringify(['N5', '语法', '基础']),
        teacherId: 1,
        startDate: new Date(),
        endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60天后
        createdBy: 1
      },
      {
        name: '日语口语练习小组',
        description: '每日口语练习，提高日语表达能力',
        level: 'N5',
        status: 'active',
        maxMembers: 20,
        currentMembers: 15,
        isPublic: true,
        avatar: '/images/groups/speaking-group.jpg',
        tags: JSON.stringify(['口语', '练习', '交流']),
        teacherId: 1,
        startDate: new Date(),
        endDate: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000), // 120天后
        createdBy: 1
      }
    ], { ignoreDuplicates: true });

    console.log('学习小组创建完成');

    // 5. 创建轮播图
    await Banner.bulkCreate([
      {
        title: '新概念日语课程上线',
        description: '零基础学日语，从新概念开始',
        image: '/images/banners/new-concept-banner.jpg',
        link: '/courses/1',
        sort: 1,
        status: 'active',
        startTime: new Date(),
        endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
        createdBy: 1
      },
      {
        title: '加入学习小组',
        description: '和小伙伴一起学习日语',
        image: '/images/banners/group-banner.jpg',
        link: '/groups',
        sort: 2,
        status: 'active',
        startTime: new Date(),
        endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        createdBy: 1
      }
    ], { ignoreDuplicates: true });

    console.log('轮播图创建完成');

    // 6. 更新分类的课程数量
    for (const category of categories) {
      const courseCount = await Course.count({
        where: { categoryId: category.id, status: 'published' }
      });
      await category.update({ courseCount });
    }

    console.log('分类课程数量更新完成');
    console.log('小程序数据初始化完成！');

  } catch (error) {
    console.error('初始化小程序数据失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initMiniprogramData().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error(error);
    process.exit(1);
  });
}

module.exports = initMiniprogramData;
