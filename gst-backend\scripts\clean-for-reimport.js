const { sequelize } = require('../src/models');
const readline = require('readline');

async function askConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function cleanForReimport() {
  try {
    console.log('🧹 准备清理数据以重新导入...');

    // 1. 显示当前数据统计
    console.log('\n📊 当前数据统计:');
    
    const [courseStats] = await sequelize.query(`SELECT COUNT(*) as total FROM courses`);
    const [categoryStats] = await sequelize.query(`SELECT COUNT(*) as total FROM categories`);
    const [unitStats] = await sequelize.query(`SELECT COUNT(*) as total FROM course_units`);
    let taskStats = [{ total: 0 }];
    try {
      [taskStats] = await sequelize.query(`SELECT COUNT(*) as total FROM import_tasks`);
    } catch (error) {
      // import_tasks表不存在
    }

    console.log(`  - 课程数量: ${courseStats[0].total}`);
    console.log(`  - 分类数量: ${categoryStats[0].total}`);
    console.log(`  - 课程单元数量: ${unitStats[0].total}`);
    console.log(`  - 导入任务数量: ${taskStats[0].total}`);

    // 2. 确认清理操作
    console.log('\n⚠️  警告：此操作将删除以下数据：');
    console.log('  - 所有课程数据');
    console.log('  - 所有课程单元数据');
    console.log('  - 所有分类数据');
    console.log('  - 所有导入任务记录');
    console.log('  - 相关的关联数据');

    const confirmed = await askConfirmation('\n确定要继续清理数据吗？(y/N): ');
    
    if (!confirmed) {
      console.log('❌ 操作已取消');
      return;
    }

    // 3. 开始清理数据
    console.log('\n🔄 开始清理数据...');

    // 禁用外键约束检查（SQLite）
    await sequelize.query('PRAGMA foreign_keys = OFF');

    try {
      // 清理顺序很重要，先清理依赖表，再清理主表
      
      console.log('📋 清理课程单元数据...');
      await sequelize.query('DELETE FROM course_units');
      console.log('✅ 课程单元数据已清理');

      console.log('📋 清理课程数据...');
      await sequelize.query('DELETE FROM courses');
      console.log('✅ 课程数据已清理');

      console.log('📋 清理分类数据...');
      await sequelize.query('DELETE FROM categories');
      console.log('✅ 分类数据已清理');

      console.log('📋 清理导入任务记录...');
      try {
        await sequelize.query('DELETE FROM import_tasks');
        console.log('✅ 导入任务记录已清理');
      } catch (error) {
        console.log('⚠️  import_tasks表不存在，跳过清理');
      }

      // 重置自增ID（SQLite）
      console.log('📋 重置自增ID...');
      await sequelize.query('DELETE FROM sqlite_sequence WHERE name IN ("courses", "course_units", "categories")');
      console.log('✅ 自增ID已重置');

      // 清理可能的孤立数据
      console.log('📋 清理其他相关数据...');
      
      // 清理可能的学习记录
      try {
        await sequelize.query('DELETE FROM learning_progress WHERE course_id NOT IN (SELECT id FROM courses)');
        console.log('✅ 孤立的学习记录已清理');
      } catch (error) {
        console.log('⚠️  learning_progress表不存在或已清理');
      }

      // 清理可能的收藏记录
      try {
        await sequelize.query('DELETE FROM favorites WHERE course_id NOT IN (SELECT id FROM courses)');
        console.log('✅ 孤立的收藏记录已清理');
      } catch (error) {
        console.log('⚠️  favorites表不存在或已清理');
      }

      // 清理可能的评论记录
      try {
        await sequelize.query('DELETE FROM comments WHERE course_id NOT IN (SELECT id FROM courses)');
        console.log('✅ 孤立的评论记录已清理');
      } catch (error) {
        console.log('⚠️  comments表不存在或已清理');
      }

    } finally {
      // 重新启用外键约束检查
      await sequelize.query('PRAGMA foreign_keys = ON');
    }

    // 4. 验证清理结果
    console.log('\n📊 清理后数据统计:');
    
    const [newCourseStats] = await sequelize.query(`SELECT COUNT(*) as total FROM courses`);
    const [newCategoryStats] = await sequelize.query(`SELECT COUNT(*) as total FROM categories`);
    const [newUnitStats] = await sequelize.query(`SELECT COUNT(*) as total FROM course_units`);
    let newTaskStats = [{ total: 0 }];
    try {
      [newTaskStats] = await sequelize.query(`SELECT COUNT(*) as total FROM import_tasks`);
    } catch (error) {
      // import_tasks表不存在
    }

    console.log(`  - 课程数量: ${newCourseStats[0].total}`);
    console.log(`  - 分类数量: ${newCategoryStats[0].total}`);
    console.log(`  - 课程单元数量: ${newUnitStats[0].total}`);
    console.log(`  - 导入任务数量: ${newTaskStats[0].total}`);

    // 5. 提供下一步指导
    console.log('\n✅ 数据清理完成！');
    console.log('\n📋 下一步操作指南:');
    console.log('1. 准备原始数据文件:');
    console.log('   - courses_classify.xlsx (分类数据)');
    console.log('   - courses.xlsx (课程数据)');
    console.log('   - courses_item.xlsx (课程单元数据)');
    console.log('');
    console.log('2. 按顺序导入数据:');
    console.log('   a) 登录管理后台');
    console.log('   b) 进入数据导入页面');
    console.log('   c) 先导入分类数据 (categories)');
    console.log('   d) 再导入课程数据 (courses)');
    console.log('   e) 最后导入课程单元数据 (course_units)');
    console.log('');
    console.log('3. 导入完成后验证:');
    console.log('   node scripts/verify-import.js');

    console.log('\n💡 重要提醒:');
    console.log('- 确保按正确顺序导入，先分类，再课程，最后课程单元');
    console.log('- 每次导入完成后检查是否有错误');
    console.log('- 如果导入失败，可以重新运行此清理脚本');

  } catch (error) {
    console.error('❌ 数据清理失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanForReimport().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = cleanForReimport;
