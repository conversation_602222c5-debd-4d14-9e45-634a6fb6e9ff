const { Course, Category, CourseUnit } = require('../src/models');

async function fixDataRelations() {
  try {
    console.log('🔄 开始修复数据关联...');

    // 1. 检查并修复课程分类关联
    console.log('📋 检查课程分类关联...');
    
    // 获取所有分类ID
    const categories = await Category.findAll({ attributes: ['id', 'name'] });
    const validCategoryIds = categories.map(c => c.id);
    console.log('有效分类ID:', validCategoryIds);

    // 获取所有课程
    const courses = await Course.findAll({ attributes: ['id', 'title', 'categoryId'] });
    console.log(`总课程数: ${courses.length}`);

    let fixedCourses = 0;
    let nullCategoryCount = 0;
    let invalidCategoryCount = 0;

    // 找一个默认分类（第一个分类）
    const defaultCategory = categories[0];
    console.log(`默认分类: ${defaultCategory.name} (ID: ${defaultCategory.id})`);

    for (const course of courses) {
      let needUpdate = false;
      let newCategoryId = course.categoryId;

      if (course.categoryId === null) {
        // 分类为空的课程，分配到默认分类
        newCategoryId = defaultCategory.id;
        needUpdate = true;
        nullCategoryCount++;
      } else if (!validCategoryIds.includes(course.categoryId)) {
        // 分类ID无效的课程，分配到默认分类
        newCategoryId = defaultCategory.id;
        needUpdate = true;
        invalidCategoryCount++;
      }

      if (needUpdate) {
        await Course.update(
          { categoryId: newCategoryId },
          { where: { id: course.id } }
        );
        fixedCourses++;
        console.log(`✅ 修复课程: ${course.title} -> 分类ID: ${newCategoryId}`);
      }
    }

    console.log(`📊 课程分类修复统计:`);
    console.log(`  - 分类为空的课程: ${nullCategoryCount}`);
    console.log(`  - 分类ID无效的课程: ${invalidCategoryCount}`);
    console.log(`  - 总共修复的课程: ${fixedCourses}`);

    // 2. 检查课程单元关联
    console.log('\n📋 检查课程单元关联...');
    
    const courseUnits = await CourseUnit.findAll({ 
      attributes: ['id', 'title', 'courseId'],
      limit: 20
    });
    
    console.log(`课程单元样本数: ${courseUnits.length}`);
    
    if (courseUnits.length > 0) {
      console.log('课程单元示例:');
      courseUnits.slice(0, 5).forEach(unit => {
        console.log(`  - ${unit.title} (课程ID: ${unit.courseId})`);
      });
    }

    // 3. 验证修复结果
    console.log('\n📋 验证修复结果...');
    
    const coursesAfterFix = await Course.findAll({
      include: [{
        model: Category,
        as: 'courseCategory',
        attributes: ['id', 'name']
      }],
      attributes: ['id', 'title', 'categoryId'],
      limit: 10
    });

    console.log('修复后的课程示例:');
    coursesAfterFix.forEach(course => {
      console.log(`  - ${course.title} -> ${course.courseCategory?.name || '未知分类'} (ID: ${course.categoryId})`);
    });

    // 4. 统计信息
    const totalCourses = await Course.count();
    const coursesWithCategory = await Course.count({
      where: { categoryId: { [require('sequelize').Op.not]: null } }
    });
    const totalUnits = await CourseUnit.count();

    console.log('\n📊 最终统计:');
    console.log(`  - 总课程数: ${totalCourses}`);
    console.log(`  - 有分类的课程: ${coursesWithCategory}`);
    console.log(`  - 总课程单元数: ${totalUnits}`);

    console.log('🎉 数据关联修复完成！');

  } catch (error) {
    console.error('❌ 数据关联修复失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixDataRelations().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = fixDataRelations;
