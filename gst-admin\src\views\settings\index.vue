﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>系统设置</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="saveAllSettings" :loading="saving">
          <el-icon><Check /></el-icon>
          保存设置
        </el-button>
      </div>
    </div>

    <div class="settings-container">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="6">
          <el-card class="settings-menu">
            <el-menu
              v-model:default-active="activeTab"
              class="settings-menu-list"
              @select="handleMenuSelect"
            >
              <el-menu-item index="basic">
                <el-icon><Setting /></el-icon>
                <span>基础设置</span>
              </el-menu-item>
              <el-menu-item index="app">
                <el-icon><Iphone /></el-icon>
                <span>小程序设置</span>
              </el-menu-item>
              <el-menu-item index="upload">
                <el-icon><Upload /></el-icon>
                <span>上传设置</span>
              </el-menu-item>
              <el-menu-item index="email">
                <el-icon><Message /></el-icon>
                <span>邮件设置</span>
              </el-menu-item>
              <el-menu-item index="sms">
                <el-icon><ChatDotRound /></el-icon>
                <span>短信设置</span>
              </el-menu-item>
              <el-menu-item index="payment">
                <el-icon><CreditCard /></el-icon>
                <span>支付设置</span>
              </el-menu-item>
              <el-menu-item index="security">
                <el-icon><Lock /></el-icon>
                <span>安全设置</span>
              </el-menu-item>
            </el-menu>
          </el-card>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="18">
          <!-- 基础设置 -->
          <el-card v-show="activeTab === 'basic'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>基础设置</span>
              </div>
            </template>
            <el-form :model="basicSettings" label-width="120px">
              <el-form-item label="网站名称">
                <el-input v-model="basicSettings.siteName" placeholder="请输入网站名称" />
              </el-form-item>
              <el-form-item label="网站描述">
                <el-input
                  v-model="basicSettings.siteDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入网站描述"
                />
              </el-form-item>
              <el-form-item label="网站关键词">
                <el-input v-model="basicSettings.siteKeywords" placeholder="请输入网站关键词，用逗号分隔" />
              </el-form-item>
              <el-form-item label="网站Logo">
                <el-upload
                  class="logo-uploader"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :show-file-list="false"
                  :on-success="handleLogoSuccess"
                  :before-upload="beforeImageUpload"
                  accept="image/*"
                >
                  <img v-if="basicSettings.siteLogo" :src="basicSettings.siteLogo" class="logo-image" />
                  <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
              <el-form-item label="ICP备案号">
                <el-input v-model="basicSettings.icpNumber" placeholder="请输入ICP备案号" />
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input v-model="basicSettings.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item label="联系邮箱">
                <el-input v-model="basicSettings.contactEmail" placeholder="请输入联系邮箱" />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 小程序设置 -->
          <el-card v-show="activeTab === 'app'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>小程序设置</span>
              </div>
            </template>
            <el-form :model="appSettings" label-width="120px">
              <el-form-item label="小程序名称">
                <el-input v-model="appSettings.appName" placeholder="请输入小程序名称" />
              </el-form-item>
              <el-form-item label="AppID">
                <el-input v-model="appSettings.appId" placeholder="请输入小程序AppID" />
              </el-form-item>
              <el-form-item label="AppSecret">
                <el-input v-model="appSettings.appSecret" type="password" placeholder="请输入小程序AppSecret" />
              </el-form-item>
              <el-form-item label="版本号">
                <el-input v-model="appSettings.version" placeholder="请输入当前版本号" />
              </el-form-item>
              <el-form-item label="更新提示">
                <el-switch v-model="appSettings.updateNotice" />
              </el-form-item>
              <el-form-item label="维护模式">
                <el-switch v-model="appSettings.maintenanceMode" />
              </el-form-item>
              <el-form-item label="维护提示">
                <el-input
                  v-model="appSettings.maintenanceMessage"
                  type="textarea"
                  :rows="3"
                  placeholder="维护模式下显示的提示信息"
                />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 上传设置 -->
          <el-card v-show="activeTab === 'upload'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>上传设置</span>
              </div>
            </template>
            <el-form :model="uploadSettings" label-width="120px">
              <el-form-item label="存储方式">
                <el-radio-group v-model="uploadSettings.storageType">
                  <el-radio label="local">本地存储</el-radio>
                  <el-radio label="qiniu">七牛云</el-radio>
                  <el-radio label="aliyun">阿里云OSS</el-radio>
                  <el-radio label="tencent">腾讯云COS</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="最大文件大小">
                <el-input-number v-model="uploadSettings.maxFileSize" :min="1" :max="100" />
                <span style="margin-left: 8px;">MB</span>
              </el-form-item>
              <el-form-item label="允许的文件类型">
                <el-checkbox-group v-model="uploadSettings.allowedTypes">
                  <el-checkbox label="image">图片</el-checkbox>
                  <el-checkbox label="video">视频</el-checkbox>
                  <el-checkbox label="audio">音频</el-checkbox>
                  <el-checkbox label="document">文档</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item v-if="uploadSettings.storageType !== 'local'" label="访问域名">
                <el-input v-model="uploadSettings.domain" placeholder="请输入CDN域名" />
              </el-form-item>
              <el-form-item v-if="uploadSettings.storageType !== 'local'" label="Access Key">
                <el-input v-model="uploadSettings.accessKey" placeholder="请输入Access Key" />
              </el-form-item>
              <el-form-item v-if="uploadSettings.storageType !== 'local'" label="Secret Key">
                <el-input v-model="uploadSettings.secretKey" type="password" placeholder="请输入Secret Key" />
              </el-form-item>
              <el-form-item v-if="uploadSettings.storageType !== 'local'" label="存储桶/区域">
                <el-input v-model="uploadSettings.bucket" placeholder="请输入存储桶名称或区域" />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 邮件设置 -->
          <el-card v-show="activeTab === 'email'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>邮件设置</span>
              </div>
            </template>
            <el-form :model="emailSettings" label-width="120px">
              <el-form-item label="SMTP服务器">
                <el-input v-model="emailSettings.smtpHost" placeholder="请输入SMTP服务器地址" />
              </el-form-item>
              <el-form-item label="SMTP端口">
                <el-input-number v-model="emailSettings.smtpPort" :min="1" :max="65535" />
              </el-form-item>
              <el-form-item label="发送邮箱">
                <el-input v-model="emailSettings.fromEmail" placeholder="请输入发送邮箱" />
              </el-form-item>
              <el-form-item label="邮箱密码">
                <el-input v-model="emailSettings.password" type="password" placeholder="请输入邮箱密码或授权码" />
              </el-form-item>
              <el-form-item label="发送者名称">
                <el-input v-model="emailSettings.fromName" placeholder="请输入发送者名称" />
              </el-form-item>
              <el-form-item label="启用SSL">
                <el-switch v-model="emailSettings.ssl" />
              </el-form-item>
              <el-form-item>
                <el-button @click="testEmail" :loading="testingEmail">测试邮件发送</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 短信设置 -->
          <el-card v-show="activeTab === 'sms'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>短信设置</span>
              </div>
            </template>
            <el-form :model="smsSettings" label-width="120px">
              <el-form-item label="短信服务商">
                <el-radio-group v-model="smsSettings.provider">
                  <el-radio label="aliyun">阿里云</el-radio>
                  <el-radio label="tencent">腾讯云</el-radio>
                  <el-radio label="huawei">华为云</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="Access Key">
                <el-input v-model="smsSettings.accessKey" placeholder="请输入Access Key" />
              </el-form-item>
              <el-form-item label="Secret Key">
                <el-input v-model="smsSettings.secretKey" type="password" placeholder="请输入Secret Key" />
              </el-form-item>
              <el-form-item label="签名">
                <el-input v-model="smsSettings.signName" placeholder="请输入短信签名" />
              </el-form-item>
              <el-form-item label="验证码模板">
                <el-input v-model="smsSettings.codeTemplate" placeholder="请输入验证码模板ID" />
              </el-form-item>
              <el-form-item>
                <el-button @click="testSms" :loading="testingSms">测试短信发送</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 支付设置 -->
          <el-card v-show="activeTab === 'payment'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>支付设置</span>
              </div>
            </template>
            <el-form :model="paymentSettings" label-width="120px">
              <el-form-item label="微信支付">
                <el-switch v-model="paymentSettings.wechatEnabled" />
              </el-form-item>
              <el-form-item v-if="paymentSettings.wechatEnabled" label="微信AppID">
                <el-input v-model="paymentSettings.wechatAppId" placeholder="请输入微信AppID" />
              </el-form-item>
              <el-form-item v-if="paymentSettings.wechatEnabled" label="微信商户号">
                <el-input v-model="paymentSettings.wechatMchId" placeholder="请输入微信商户号" />
              </el-form-item>
              <el-form-item v-if="paymentSettings.wechatEnabled" label="微信商户密钥">
                <el-input v-model="paymentSettings.wechatKey" type="password" placeholder="请输入微信商户密钥" />
              </el-form-item>
              <el-form-item label="支付宝支付">
                <el-switch v-model="paymentSettings.alipayEnabled" />
              </el-form-item>
              <el-form-item v-if="paymentSettings.alipayEnabled" label="支付宝AppID">
                <el-input v-model="paymentSettings.alipayAppId" placeholder="请输入支付宝AppID" />
              </el-form-item>
              <el-form-item v-if="paymentSettings.alipayEnabled" label="支付宝私钥">
                <el-input
                  v-model="paymentSettings.alipayPrivateKey"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入支付宝应用私钥"
                />
              </el-form-item>
              <el-form-item v-if="paymentSettings.alipayEnabled" label="支付宝公钥">
                <el-input
                  v-model="paymentSettings.alipayPublicKey"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入支付宝公钥"
                />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 安全设置 -->
          <el-card v-show="activeTab === 'security'" class="settings-content">
            <template #header>
              <div class="card-header">
                <span>安全设置</span>
              </div>
            </template>
            <el-form :model="securitySettings" label-width="120px">
              <el-form-item label="登录验证码">
                <el-switch v-model="securitySettings.loginCaptcha" />
              </el-form-item>
              <el-form-item label="注册验证码">
                <el-switch v-model="securitySettings.registerCaptcha" />
              </el-form-item>
              <el-form-item label="密码最小长度">
                <el-input-number v-model="securitySettings.minPasswordLength" :min="6" :max="20" />
              </el-form-item>
              <el-form-item label="登录失败限制">
                <el-input-number v-model="securitySettings.maxLoginAttempts" :min="3" :max="10" />
                <span style="margin-left: 8px;">次</span>
              </el-form-item>
              <el-form-item label="锁定时间">
                <el-input-number v-model="securitySettings.lockoutDuration" :min="5" :max="60" />
                <span style="margin-left: 8px;">分钟</span>
              </el-form-item>
              <el-form-item label="会话超时">
                <el-input-number v-model="securitySettings.sessionTimeout" :min="30" :max="1440" />
                <span style="margin-left: 8px;">分钟</span>
              </el-form-item>
              <el-form-item label="IP白名单">
                <el-input
                  v-model="securitySettings.ipWhitelist"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入IP白名单，每行一个IP或IP段"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put } from '@/utils/request'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Check,
  Setting,
  Iphone,
  Upload,
  Message,
  ChatDotRound,
  CreditCard,
  Lock,
  Plus
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const testingEmail = ref(false)
const testingSms = ref(false)
const activeTab = ref('basic')

// 上传配置
const uploadAction = '/api/media/upload'
const uploadHeaders = {
  'Authorization': `Bearer ${authStore.token}`
}

// 基础设置
const basicSettings = reactive({
  siteName: 'GST日语培训系统',
  siteDescription: '专业的日语在线学习平台',
  siteKeywords: '日语学习,在线教育,日语培训',
  siteLogo: '',
  icpNumber: '',
  contactPhone: '',
  contactEmail: ''
})

// 小程序设置
const appSettings = reactive({
  appName: 'GST日语',
  appId: '',
  appSecret: '',
  version: '1.0.0',
  updateNotice: true,
  maintenanceMode: false,
  maintenanceMessage: '系统维护中，请稍后再试'
})

// 上传设置
const uploadSettings = reactive({
  storageType: 'local',
  maxFileSize: 10,
  allowedTypes: ['image', 'video', 'audio'],
  domain: '',
  accessKey: '',
  secretKey: '',
  bucket: ''
})

// 邮件设置
const emailSettings = reactive({
  smtpHost: '',
  smtpPort: 587,
  fromEmail: '',
  password: '',
  fromName: '',
  ssl: true
})

// 短信设置
const smsSettings = reactive({
  provider: 'aliyun',
  accessKey: '',
  secretKey: '',
  signName: '',
  codeTemplate: ''
})

// 支付设置
const paymentSettings = reactive({
  wechatEnabled: false,
  wechatAppId: '',
  wechatMchId: '',
  wechatKey: '',
  alipayEnabled: false,
  alipayAppId: '',
  alipayPrivateKey: '',
  alipayPublicKey: ''
})

// 安全设置
const securitySettings = reactive({
  loginCaptcha: true,
  registerCaptcha: true,
  minPasswordLength: 8,
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  sessionTimeout: 120,
  ipWhitelist: ''
})

// 加载设置数据
const loadSettings = async () => {
  loading.value = true
  try {
    const response = await get('/api/settings')
    if (response.success) {
      const settings = response.data.settings || {}

      // 更新各个设置对象
      Object.assign(basicSettings, settings.basic || {})
      Object.assign(appSettings, settings.app || {})
      Object.assign(uploadSettings, settings.upload || {})
      Object.assign(emailSettings, settings.email || {})
      Object.assign(smsSettings, settings.sms || {})
      Object.assign(paymentSettings, settings.payment || {})
      Object.assign(securitySettings, settings.security || {})
    }
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.error('加载设置失败')
  } finally {
    loading.value = false
  }
}

// 保存所有设置
const saveAllSettings = async () => {
  saving.value = true
  try {
    const allSettings = {
      basic: basicSettings,
      app: appSettings,
      upload: uploadSettings,
      email: emailSettings,
      sms: smsSettings,
      payment: paymentSettings,
      security: securitySettings
    }

    await put('/api/settings', { settings: allSettings })
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

// 菜单选择
const handleMenuSelect = (key) => {
  activeTab.value = key
}

// Logo上传成功
const handleLogoSuccess = (response) => {
  if (response.success) {
    basicSettings.siteLogo = response.data.url
    ElMessage.success('Logo上传成功')
  } else {
    ElMessage.error('Logo上传失败')
  }
}

// 图片上传前检查
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 测试邮件发送
const testEmail = async () => {
  testingEmail.value = true
  try {
    await post('/api/settings/test-email', {
      to: emailSettings.fromEmail,
      subject: '邮件配置测试',
      content: '这是一封测试邮件，如果您收到此邮件，说明邮件配置正确。'
    })
    ElMessage.success('测试邮件发送成功，请检查邮箱')
  } catch (error) {
    console.error('测试邮件发送失败:', error)
    ElMessage.error('测试邮件发送失败')
  } finally {
    testingEmail.value = false
  }
}

// 测试短信发送
const testSms = async () => {
  testingSms.value = true
  try {
    await post('/api/settings/test-sms', {
      phone: '13800138000', // 这里应该让用户输入测试手机号
      message: '【GST日语】您的验证码是123456，5分钟内有效。'
    })
    ElMessage.success('测试短信发送成功')
  } catch (error) {
    console.error('测试短信发送失败:', error)
    ElMessage.error('测试短信发送失败')
  } finally {
    testingSms.value = false
  }
}

const refreshData = () => {
  loadSettings()
}

// 组件挂载时加载数据
onMounted(() => {
  loadSettings()
})
</script>

<style lang="scss" scoped>
.settings-container {
  margin-top: 20px;
}

.settings-menu {
  .settings-menu-list {
    border: none;

    .el-menu-item {
      border-radius: 6px;
      margin-bottom: 4px;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      &.is-active {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }
    }
  }
}

.settings-content {
  min-height: 600px;

  .card-header {
    font-size: 16px;
    font-weight: 600;
  }

  .el-form {
    padding: 20px 0;

    .el-form-item {
      margin-bottom: 24px;

      .el-form-item__label {
        font-weight: 500;
      }
    }
  }
}

// Logo上传样式
.logo-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--el-color-primary);
    }
  }

  .logo-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    display: block;
  }

  .logo-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }
}

// 表单样式优化
.el-radio-group {
  .el-radio {
    margin-right: 20px;
    margin-bottom: 8px;
  }
}

.el-checkbox-group {
  .el-checkbox {
    margin-right: 20px;
    margin-bottom: 8px;
  }
}

// 文本域样式
.el-textarea {
  .el-textarea__inner {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-container {
    .el-col {
      width: 100% !important;
      margin-bottom: 20px;
    }
  }

  .settings-menu {
    .settings-menu-list {
      display: flex;
      overflow-x: auto;

      .el-menu-item {
        white-space: nowrap;
        margin-right: 8px;
      }
    }
  }
}
</style>
