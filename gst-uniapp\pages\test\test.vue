<template>
	<view class="test-page">
		<view class="content">
			<text>这是测试页面</text>
			<text>用来验证底部菜单是否能显示</text>
		</view>
		
		<!-- 最简单的底部菜单 -->
		<view class="simple-tabbar">
			<text style="color: white; font-size: 32rpx;">简单底部菜单测试</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TestPage',
	data() {
		return {
			
		};
	}
};
</script>

<style scoped>
.test-page {
	height: 100vh;
	background: #f0f0f0;
}

.content {
	padding: 100rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.simple-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100rpx;
	background: green;
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
