{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/lp-record/record-clock.vue?79c8", "webpack:///D:/gst/gst-uniapp/components/lp-record/record-clock.vue?69ae", "webpack:///D:/gst/gst-uniapp/components/lp-record/record-clock.vue?ed9a", "webpack:///D:/gst/gst-uniapp/components/lp-record/record-clock.vue?ce92", "uni-app:///components/lp-record/record-clock.vue", "webpack:///D:/gst/gst-uniapp/components/lp-record/record-clock.vue?a196", "webpack:///D:/gst/gst-uniapp/components/lp-record/record-clock.vue?c1ea"], "names": ["props", "show", "type", "default", "data", "value", "currentValue", "drawTimer", "angle", "canvasCenter", "width", "height", "innerCenter", "x", "y", "radius", "created", "methods", "setValue", "initDraw", "resetDraw", "startDraw", "_this", "pauseDraw", "clearInterval", "drawBg", "draw"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAymB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCW7nB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAC;IAEA;AACA;AACA;IACAC;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEAC;MACA;MACA;QACA;QACAC;QACAA;QACA;UACAA;UACAA;QACA;MAEA;IACA;IAEAC;MACAC;IACA;IAEAC;MACA;MACA;MACA;MACA;;MAEA;MACAH;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;IACA;IAEAI;MACA;MACA;MACA;MAEA;MACA;MACA;;MAEA;MACAJ;MACAA;MACAA;MACAA;MACAA,kGACA;MACAA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/lp-record/record-clock.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./record-clock.vue?vue&type=template&id=25481e42&scoped=true&\"\nvar renderjs\nimport script from \"./record-clock.vue?vue&type=script&lang=js&\"\nexport * from \"./record-clock.vue?vue&type=script&lang=js&\"\nimport style0 from \"./record-clock.vue?vue&type=style&index=0&id=25481e42&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"25481e42\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/lp-record/record-clock.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-clock.vue?vue&type=template&id=25481e42&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-clock.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-clock.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"root-box\">\n\t\t<canvas class=\"canvas\" canvas-id=\"canvas\" :style=\"{display:show ? 'block' : 'none'}\">\n\t\t\t<view class=\"content\">\n\t\t\t\t<slot></slot>\n\t\t\t</view>\n\t\t</canvas>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tprops: {\n\t\t\tshow: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 目标百分比\n\t\t\t\tvalue: 0,\n\t\t\t\t// 当前值\n\t\t\t\tcurrentValue: 0,\n\t\t\t\t// draw\n\t\t\t\tdrawTimer: null,\n\t\t\t\t// 角度\n\t\t\t\tangle: 0,\n\t\t\t\t// 外圆\n\t\t\t\tcanvasCenter: {\n\t\t\t\t\twidth: 100,\n\t\t\t\t\theight: 100,\n\t\t\t\t},\n\t\t\t\t// 内圆\n\t\t\t\tinnerCenter: {\n\t\t\t\t\tx: 50,\n\t\t\t\t\ty: 50,\n\t\t\t\t\tradius: 44\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\n\t\tcreated() {\n\t\t\t//this.initDraw();\n\t\t\tthis.resetDraw();\n\t\t},\n\n\t\tmethods: {\n\t\t\tsetValue: function(v) {\n\t\t\t\tthis.value = v;\n\t\t\t\tthis.pauseDraw();\n\t\t\t\tthis.startDraw();\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// Draw\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 初始\n\t\t\t */\n\t\t\tinitDraw: function() {},\n\n\t\t\t/**\n\t\t\t * 从置\n\t\t\t */\n\t\t\tresetDraw: function() {\n\t\t\t\tthis.pauseDraw();\n\t\t\t\tthis.currentValue = 0;\n\n\t\t\t\t//清除canvas内容 方式一：不知道为啥 不起作用\n\t\t\t\t//this.canvasObj.clearRect(0,0,this.canvasObj.width,this.canvasObj.height);\n\t\t\t\t//清除canvas内容 方式二：填充canvas为白色\n\t\t\t\tthis.canvasObj = uni.createCanvasContext('canvas', this);\n\t\t\t\tthis.canvasObj.setFillStyle('#fff')\n\t\t\t\tthis.canvasObj.fillRect(0, 0, this.canvasObj.width, this.canvasObj.height)\n\t\t\t\tthis.canvasObj.draw();\n\n\t\t\t\tthis.drawBg();\n\t\t\t},\n\n\t\t\tstartDraw: function() {\n\t\t\t\tconst _this = this;\n\t\t\t\tthis.drawTimer = setInterval(function() {\n\t\t\t\t\tconst start = _this.currentValue;\n\t\t\t\t\t_this.currentValue += (_this.value - _this.currentValue) * 0.1;\n\t\t\t\t\t_this.draw(start, _this.currentValue);\n\t\t\t\t\tif (_this.value - _this.currentValue <= 0.001) {\n\t\t\t\t\t\t_this.currentValue = _this.value;\n\t\t\t\t\t\t_this.pauseDraw();\n\t\t\t\t\t}\n\n\t\t\t\t}, 50);\n\t\t\t},\n\n\t\t\tpauseDraw: function() {\n\t\t\t\tclearInterval(this.drawTimer);\n\t\t\t},\n\n\t\t\tdrawBg: function() {\n\t\t\t\tconst _this = this;\n\t\t\t\tlet centerX = _this.innerCenter.x;\n\t\t\t\tlet centerY = _this.innerCenter.y;\n\t\t\t\tlet radius = _this.innerCenter.radius;\n\n\t\t\t\t// 录音过程圆圈动画的背景园\n\t\t\t\t_this.canvasObj.beginPath();\n\t\t\t\t_this.canvasObj.setStrokeStyle(\"#fe3b54\");\n\t\t\t\t_this.canvasObj.setGlobalAlpha(0.3)\n\t\t\t\t_this.canvasObj.setLineWidth(3);\n\t\t\t\t_this.canvasObj.arc(centerX, centerY, radius, 0, 2 * Math.PI);\n\t\t\t\t_this.canvasObj.stroke();\n\t\t\t\t_this.canvasObj.draw();\n\t\t\t},\n\n\t\t\tdraw: function(startValue, endValue) {\n\t\t\t\tconst _this = this;\n\t\t\t\tconst innerCenter = _this.innerCenter;\n\t\t\t\tconst PI = Math.PI;\n\n\t\t\t\tlet centerX = innerCenter.x;\n\t\t\t\tlet centerY = innerCenter.y;\n\t\t\t\tlet radius = innerCenter.radius;\n\n\t\t\t\t// 录音过程圆圈动画\n\t\t\t\t_this.canvasObj.beginPath();\n\t\t\t\t_this.canvasObj.setStrokeStyle(\"#fe3b54\");\n\t\t\t\t_this.canvasObj.setGlobalAlpha(1)\n\t\t\t\t_this.canvasObj.setLineWidth(3);\n\t\t\t\t_this.canvasObj.arc(centerX, centerY, radius, 2 * PI * startValue - 0.5 * PI, 2 * PI * endValue -\n\t\t\t\t\t0.5 * PI, false);\n\t\t\t\t_this.canvasObj.stroke();\n\t\t\t\t_this.canvasObj.draw(true);\n\t\t\t},\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\tcanvas {\n\t\tmargin: 10rpx 60rpx;\n\t\tposition: relative;\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tz-index: 10;\n\t}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-clock.vue?vue&type=style&index=0&id=25481e42&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record-clock.vue?vue&type=style&index=0&id=25481e42&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039749503\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}