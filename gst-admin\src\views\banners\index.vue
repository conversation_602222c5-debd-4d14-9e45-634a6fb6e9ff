﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>轮播图管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加轮播图
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="标题">
          <el-input v-model="filters.title" placeholder="输入轮播图标题" clearable @change="loadBanners" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadBanners">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接类型">
          <el-select v-model="filters.linkType" placeholder="选择链接类型" clearable @change="loadBanners">
            <el-option label="内部链接" value="internal" />
            <el-option label="外部链接" value="external" />
            <el-option label="无链接" value="none" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 轮播图列表 -->
    <el-card>
      <el-table :data="banners" v-loading="loading" stripe>
        <el-table-column prop="image" label="图片" width="120">
          <template #default="{ row }">
            <el-image
              :src="row.image"
              :alt="row.title"
              style="width: 80px; height: 45px; border-radius: 4px;"
              fit="cover"
              :preview-src-list="[row.image]"
              preview-teleported
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="link" label="跳转链接" min-width="200" show-overflow-tooltip />
        <el-table-column prop="linkType" label="链接类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getLinkTypeColor(row.linkType)">
              {{ getLinkTypeText(row.linkType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderNum" label="排序" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="editBanner(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              link
              size="small"
              @click="toggleStatus(row)"
              :type="row.status === 1 ? 'warning' : 'success'"
            >
              <el-icon><Switch /></el-icon>
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button link size="small" @click="deleteBanner(row)" class="danger-button">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadBanners"
          @current-change="loadBanners"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="editingBanner ? '编辑轮播图' : '添加轮播图'"
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form ref="bannerFormRef" :model="bannerForm" :rules="bannerFormRules" label-width="100px">
        <el-form-item label="轮播图片" prop="image">
          <el-upload
            class="banner-uploader"
            :action="uploadAction"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
            accept="image/*"
          >
            <img v-if="bannerForm.image" :src="bannerForm.image" class="banner-image" />
            <el-icon v-else class="banner-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议尺寸：750x300px，支持JPG、PNG格式，大小不超过2MB</div>
        </el-form-item>

        <el-form-item label="标题" prop="title">
          <el-input v-model="bannerForm.title" placeholder="请输入轮播图标题" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="bannerForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入轮播图描述"
          />
        </el-form-item>

        <el-form-item label="链接类型" prop="linkType">
          <el-radio-group v-model="bannerForm.linkType">
            <el-radio label="none">无链接</el-radio>
            <el-radio label="internal">内部链接</el-radio>
            <el-radio label="external">外部链接</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="bannerForm.linkType !== 'none'"
          label="跳转链接"
          prop="link"
        >
          <el-input
            v-model="bannerForm.link"
            :placeholder="bannerForm.linkType === 'internal' ? '请输入内部页面路径，如：/courses' : '请输入完整的URL地址'"
          />
        </el-form-item>

        <el-form-item
          v-if="bannerForm.linkType === 'internal'"
          label="目标ID"
          prop="targetId"
        >
          <el-input-number
            v-model="bannerForm.targetId"
            :min="0"
            placeholder="目标页面或内容的ID"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="bannerForm.orderNum" :min="0" style="width: 100%" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="bannerForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveBanner" :loading="saving">
          {{ editingBanner ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Edit,
  Delete,
  Switch
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const banners = ref([])
const total = ref(0)
const showCreateDialog = ref(false)
const editingBanner = ref(null)

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 筛选器
const filters = reactive({
  title: '',
  status: '',
  linkType: ''
})

// 表单数据
const bannerForm = reactive({
  title: '',
  description: '',
  image: '',
  link: '',
  linkType: 'none',
  targetId: null,
  orderNum: 0,
  status: 1
})

const bannerFormRules = {
  title: [
    { required: true, message: '请输入轮播图标题', trigger: 'blur' }
  ],
  image: [
    { required: true, message: '请上传轮播图片', trigger: 'change' }
  ],
  link: [
    {
      validator: (rule, value, callback) => {
        if (bannerForm.linkType !== 'none' && !value) {
          callback(new Error('请输入跳转链接'))
        } else if (bannerForm.linkType === 'external' && value && !value.startsWith('http')) {
          callback(new Error('外部链接必须以http://或https://开头'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 上传配置
const uploadAction = '/api/media/upload'
const uploadHeaders = {
  'Authorization': `Bearer ${authStore.token}`
}

// 加载轮播图数据
const loadBanners = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size,
      ...filters
    }

    const response = await get('/api/banners', params)
    if (response.success) {
      banners.value = response.data.banners || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载轮播图数据失败:', error)
    ElMessage.error('加载轮播图数据失败')

    // 使用模拟数据
    banners.value = generateMockBanners()
    total.value = banners.value.length
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockBanners = () => {
  return [
    {
      id: 1,
      title: '日语学习新课程上线',
      description: '全新的日语学习课程，从零基础到N1水平',
      image: 'https://via.placeholder.com/750x300/409EFF/ffffff?text=Banner+1',
      link: '/courses/japanese',
      linkType: 'internal',
      targetId: 1,
      orderNum: 1,
      status: 1,
      createdAt: new Date()
    },
    {
      id: 2,
      title: '限时优惠活动',
      description: '所有课程8折优惠，仅限本月',
      image: 'https://via.placeholder.com/750x300/67C23A/ffffff?text=Banner+2',
      link: 'https://example.com/promotion',
      linkType: 'external',
      targetId: null,
      orderNum: 2,
      status: 1,
      createdAt: new Date()
    },
    {
      id: 3,
      title: '师资介绍',
      description: '认识我们的专业日语教师团队',
      image: 'https://via.placeholder.com/750x300/E6A23C/ffffff?text=Banner+3',
      link: '',
      linkType: 'none',
      targetId: null,
      orderNum: 3,
      status: 0,
      createdAt: new Date()
    }
  ]
}

// 保存轮播图
const saveBanner = async () => {
  const bannerFormRef = ref()
  try {
    await bannerFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    const data = { ...bannerForm }

    if (editingBanner.value) {
      await put(`/api/banners/${editingBanner.value.id}`, data)
      ElMessage.success('轮播图更新成功')
    } else {
      await post('/api/banners', data)
      ElMessage.success('轮播图创建成功')
    }

    showCreateDialog.value = false
    resetForm()
    await loadBanners()
  } catch (error) {
    console.error('保存轮播图失败:', error)
    ElMessage.error('保存轮播图失败')
  } finally {
    saving.value = false
  }
}

// 编辑轮播图
const editBanner = (banner) => {
  editingBanner.value = banner
  Object.assign(bannerForm, {
    title: banner.title,
    description: banner.description || '',
    image: banner.image,
    link: banner.link || '',
    linkType: banner.linkType || 'none',
    targetId: banner.targetId,
    orderNum: banner.orderNum,
    status: banner.status
  })
  showCreateDialog.value = true
}

// 删除轮播图
const deleteBanner = async (banner) => {
  try {
    await ElMessageBox.confirm('确定要删除这个轮播图吗？', '确认删除', {
      type: 'warning'
    })

    await del(`/api/banners/${banner.id}`)
    ElMessage.success('轮播图删除成功')
    await loadBanners()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除轮播图失败')
    }
  }
}

// 切换状态
const toggleStatus = async (banner) => {
  const newStatus = banner.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'

  try {
    await put(`/api/banners/${banner.id}`, { status: newStatus })
    ElMessage.success(`轮播图${action}成功`)
    banner.status = newStatus
  } catch (error) {
    console.error(`${action}轮播图失败:`, error)
    ElMessage.error(`${action}轮播图失败`)
  }
}

// 图片上传成功
const handleImageSuccess = (response) => {
  if (response.success) {
    bannerForm.image = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 图片上传前检查
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 工具函数
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getLinkTypeColor = (type) => {
  const colors = {
    none: 'info',
    internal: 'primary',
    external: 'success'
  }
  return colors[type] || ''
}

const getLinkTypeText = (type) => {
  const texts = {
    none: '无链接',
    internal: '内部链接',
    external: '外部链接'
  }
  return texts[type] || type
}

const resetForm = () => {
  editingBanner.value = null
  Object.assign(bannerForm, {
    title: '',
    description: '',
    image: '',
    link: '',
    linkType: 'none',
    targetId: null,
    orderNum: 0,
    status: 1
  })
}

const refreshData = () => {
  loadBanners()
}

// 组件挂载时加载数据
onMounted(() => {
  loadBanners()
})
</script>

<style lang="scss" scoped>
.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}

// 轮播图上传样式
.banner-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 300px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--el-color-primary);
    }
  }

  .banner-image {
    width: 300px;
    height: 120px;
    object-fit: cover;
    display: block;
  }

  .banner-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  line-height: 1.4;
}

// 表格图片样式
.el-image {
  border: 1px solid var(--el-border-color-lighter);
}

// 表单样式优化
.el-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }
}
</style>
