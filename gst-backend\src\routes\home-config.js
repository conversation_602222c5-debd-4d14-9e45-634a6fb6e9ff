const express = require('express');
const router = express.Router();

// 模拟首页配置数据
let homeConfig = {
  banner: {
    enabled: true,
    autoplay: true,
    interval: 3000,
    indicators: true,
    height: 200
  },
  categories: {
    enabled: true,
    layout: 'grid',
    columns: 4,
    showIcon: true
  },
  courses: {
    enabled: true,
    title: '推荐课程',
    limit: 8,
    sortBy: 'popularity',
    showPrice: true,
    showRating: true
  },
  notice: {
    enabled: true,
    content: '欢迎来到GST日语培训，开启您的日语学习之旅！',
    type: 'scroll',
    backgroundColor: '#f0f9ff',
    textColor: '#1e40af'
  },
  shortcuts: {
    enabled: true,
    items: [
      { name: '我的课程', icon: '📚', path: '/my-courses' },
      { name: '学习记录', icon: '📊', path: '/learning-records' },
      { name: '在线测试', icon: '📝', path: '/tests' },
      { name: '学习社区', icon: '👥', path: '/community' }
    ]
  },
  footer: {
    enabled: true,
    copyright: '© 2024 GST日语培训',
    phone: '************',
    wechat: 'GST_Service'
  }
};

// 获取首页配置
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        config: homeConfig
      }
    });
  } catch (error) {
    console.error('获取首页配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取首页配置失败'
    });
  }
});

// 更新首页配置
router.put('/', (req, res) => {
  try {
    const { config } = req.body;
    
    if (!config) {
      return res.status(400).json({
        success: false,
        message: '配置数据不能为空'
      });
    }
    
    // 验证配置数据结构
    const requiredSections = ['banner', 'categories', 'courses', 'notice', 'shortcuts', 'footer'];
    for (const section of requiredSections) {
      if (!config[section]) {
        return res.status(400).json({
          success: false,
          message: `缺少必要的配置节: ${section}`
        });
      }
    }
    
    // 更新配置
    homeConfig = { ...homeConfig, ...config };
    
    res.json({
      success: true,
      data: {
        config: homeConfig
      },
      message: '首页配置更新成功'
    });
  } catch (error) {
    console.error('更新首页配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新首页配置失败'
    });
  }
});

// 重置首页配置
router.post('/reset', (req, res) => {
  try {
    // 重置为默认配置
    homeConfig = {
      banner: {
        enabled: true,
        autoplay: true,
        interval: 3000,
        indicators: true,
        height: 200
      },
      categories: {
        enabled: true,
        layout: 'grid',
        columns: 4,
        showIcon: true
      },
      courses: {
        enabled: true,
        title: '推荐课程',
        limit: 8,
        sortBy: 'popularity',
        showPrice: true,
        showRating: true
      },
      notice: {
        enabled: true,
        content: '欢迎来到GST日语培训，开启您的日语学习之旅！',
        type: 'scroll',
        backgroundColor: '#f0f9ff',
        textColor: '#1e40af'
      },
      shortcuts: {
        enabled: true,
        items: [
          { name: '我的课程', icon: '📚', path: '/my-courses' },
          { name: '学习记录', icon: '📊', path: '/learning-records' },
          { name: '在线测试', icon: '📝', path: '/tests' },
          { name: '学习社区', icon: '👥', path: '/community' }
        ]
      },
      footer: {
        enabled: true,
        copyright: '© 2024 GST日语培训',
        phone: '************',
        wechat: 'GST_Service'
      }
    };
    
    res.json({
      success: true,
      data: {
        config: homeConfig
      },
      message: '首页配置重置成功'
    });
  } catch (error) {
    console.error('重置首页配置失败:', error);
    res.status(500).json({
      success: false,
      message: '重置首页配置失败'
    });
  }
});

// 获取首页预览数据
router.get('/preview', (req, res) => {
  try {
    // 模拟首页预览数据
    const previewData = {
      banners: [
        {
          id: 1,
          image: '/images/banner1.jpg',
          title: 'N5基础日语课程',
          link: '/courses/1'
        },
        {
          id: 2,
          image: '/images/banner2.jpg',
          title: 'N4进阶日语课程',
          link: '/courses/2'
        }
      ],
      categories: [
        { id: 1, name: '日语N5', icon: 'N5', courseCount: 12 },
        { id: 2, name: '日语N4', icon: 'N4', courseCount: 8 },
        { id: 3, name: '日语N3', icon: 'N3', courseCount: 6 },
        { id: 4, name: '商务日语', icon: 'BIZ', courseCount: 4 },
        { id: 5, name: '日语口语', icon: 'ORAL', courseCount: 10 },
        { id: 6, name: '日语听力', icon: 'LISTEN', courseCount: 7 }
      ],
      courses: [
        {
          id: 1,
          title: 'N5基础日语入门',
          image: '/images/course1.jpg',
          price: 199,
          rating: 4.8,
          students: 1250
        },
        {
          id: 2,
          title: 'N4进阶日语语法',
          image: '/images/course2.jpg',
          price: 299,
          rating: 4.9,
          students: 890
        },
        {
          id: 3,
          title: '商务日语实战',
          image: '/images/course3.jpg',
          price: 399,
          rating: 4.7,
          students: 560
        },
        {
          id: 4,
          title: '日语口语训练',
          image: '/images/course4.jpg',
          price: 259,
          rating: 4.6,
          students: 720
        }
      ],
      notice: {
        content: homeConfig.notice.content,
        type: homeConfig.notice.type
      }
    };
    
    res.json({
      success: true,
      data: {
        config: homeConfig,
        previewData
      }
    });
  } catch (error) {
    console.error('获取首页预览数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取首页预览数据失败'
    });
  }
});

// 更新特定配置节
router.put('/section/:section', (req, res) => {
  try {
    const { section } = req.params;
    const { config } = req.body;
    
    if (!homeConfig[section]) {
      return res.status(404).json({
        success: false,
        message: `配置节 ${section} 不存在`
      });
    }
    
    if (!config) {
      return res.status(400).json({
        success: false,
        message: '配置数据不能为空'
      });
    }
    
    // 更新特定配置节
    homeConfig[section] = { ...homeConfig[section], ...config };
    
    res.json({
      success: true,
      data: {
        section: homeConfig[section]
      },
      message: `${section} 配置更新成功`
    });
  } catch (error) {
    console.error('更新配置节失败:', error);
    res.status(500).json({
      success: false,
      message: '更新配置节失败'
    });
  }
});

module.exports = router;
