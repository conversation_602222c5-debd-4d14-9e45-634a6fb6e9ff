{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/certificate/certificate.vue?1cb4", "webpack:///D:/gst/gst-uniapp/pages/certificate/certificate.vue?1113", "webpack:///D:/gst/gst-uniapp/pages/certificate/certificate.vue?0e61", "webpack:///D:/gst/gst-uniapp/pages/certificate/certificate.vue?9121", "uni-app:///pages/certificate/certificate.vue", "webpack:///D:/gst/gst-uniapp/pages/certificate/certificate.vue?7e7b", "webpack:///D:/gst/gst-uniapp/pages/certificate/certificate.vue?9c05"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "ver", "img", "onLoad", "methods", "loginNow", "name", "console", "savePoster", "uni", "success", "that", "scope", "fail", "title", "icon", "duration", "setTimeout", "saveImageToPhotos", "src", "filePath"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,kLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqC5nB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;MACA;QACAC;MACA;QACAC;QACA;UACA;QACA,QAEA;MAEA;IACA;IACAC;MACA;MACAC;QAAA;QACAC;UACA;YAAA;YACAC;UACA;YACAF;cAAA;cACAG;cACAF;gBACAC;cACA;cACAE;gBACAJ;kBACAK;kBACAC;kBACAC;gBACA;gBACAC;kBACAR;oBAAA;oBACAC;sBACA;oBAAA;kBAEA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAQ;MACA;MACAX;MACAE;QACAU;QACAT;UACAH;UACAE;YACAW;YACAV;cACAD;gBACAK;cACA;YACA;YACAD;cACAJ;gBACAK;cACA;YACA;UACA;QACA;QACAD;UACAN;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAmpC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAvqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/certificate/certificate.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/certificate/certificate.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./certificate.vue?vue&type=template&id=b06f7194&\"\nvar renderjs\nimport script from \"./certificate.vue?vue&type=script&lang=js&\"\nexport * from \"./certificate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./certificate.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/certificate/certificate.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./certificate.vue?vue&type=template&id=b06f7194&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    wsNull: function () {\n      return import(\n        /* webpackChunkName: \"components/ws-null/ws-null\" */ \"@/components/ws-null/ws-null.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./certificate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./certificate.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page>\r\n\r\n\t\t<!-- 页面主体 -->\r\n\t\t<view slot=\"gBody\" style=\"padding:30rpx 30rpx 30rpx 30rpx;\">\r\n\t\t\t<!-- <view class=\"ws-btn-box\" >\r\n\t\t\t\t<form @submit=\"loginNow\" class=\"grace-form\" style=\"margin-top:100rpx;\">\r\n\t\t\t\t\t<view class=\"grace-form-item grace-border-b\">\r\n\r\n\t\t\t\t\t\t<view class=\"grace-form-body\">\r\n\t\t\t\t\t\t\t<input type=\"text\"  class=\"ws-input\" name=\"name\" \r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入姓名\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"grace-margin-top\">\r\n\t\t\t\t\t\t<button form-type=\"submit\" type=\"primary\" class=\"phone-login-btn\">\r\n\t\t\t\t\t\t\t查询\r\n\t\t\t\t\t\t</button>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</form>\r\n\t\t\t</view> -->\r\n\t\t\t<view v-if=\"img != null\" style=\"margin: 20rpx;text-align: center;\">\r\n\t\t\t\t<image show-menu-by-longpress='1' :src=\"img\" mode=\"aspectFill\" style=\"width: 100%;height: 1000rpx;\"></image>\r\n\t\t\t\t<!-- <button type=\"primary\" style = \"width: 600rpx;background-color: #FF3D00;margin-top: 100rpx;\" class=\"phone-login-btn\" @tap=\"savePoster\">\r\n\t\t\t\t\t保存证书\r\n\t\t\t\t</button> -->\r\n\t\t\t\t<view style=\"font-size: 30rpx;color: #f00;margin-top: 40rpx;\">长按上面证书，可保存证书到相册</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else>\r\n\t\t\t\t<ws-null  text=\"查无内容\"></ws-null>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\tvar graceChecker = require(\"@/GraceUI5/js/checker.js\");\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tver: this.$store.state.ver,\r\n\t\t\t\timg: null\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.img = option.img\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tloginNow: function(e) {\r\n\t\t\t\t// 表单验证\r\n\t\t\t\tvar formData = e.detail.value;\r\n\t\t\t\t// 验证通过\r\n\t\t\t\tthis.$http.post(\"v1/member/doCertificate\", {\r\n\t\t\t\t\tname: e.mp.detail.value.name,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res.data)\r\n\t\t\t\t\tif(res.data.data){\r\n\t\t\t\t\t\tthis.img = res.data.data.img\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsavePoster() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getSetting({ //获取用户的当前设置\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.authSetting['scope.writePhotosAlbum']) { //验证用户是否授权可以访问相册\r\n\t\t\t\t\t\t\tthat.saveImageToPhotos();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.authorize({ //如果没有授权，向用户发起请求\r\n\t\t\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tthat.saveImageToPhotos();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"请打开保存相册权限，再点击保存相册分享\",\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.openSetting({ //调起客户端小程序设置界面,让用户开启访问相册\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: (res2) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// console.log(res2.authSetting)\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}, 3000);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsaveImageToPhotos() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconsole.log(that.img)\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\tsrc: that.img,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '保存图片成功！',\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '保存图片失败！',\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tconsole.log(err, 'err')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.root-box {\r\n\t\t.ver {\r\n\t\t\tcolor: #aaa;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.logo-box {\r\n\t\t\theight: 400rpx;\r\n\r\n\t\t\t.gui-icons {\r\n\t\t\t\tfont-size: 200rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.info-box {\r\n\t\t\tpadding: 60rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t}\r\n\t\t\r\n\t\t\r\n\t}\r\n\t\r\n\t.ws-input{\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tfont-size: 26rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 20rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder: 2rpx solid #F5F5F5;\r\n\t\tletter-spacing: 1px;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./certificate.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./certificate.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041066348\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}