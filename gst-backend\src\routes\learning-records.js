const express = require('express');
const router = express.Router();

// 模拟学习记录数据
const learningRecords = [];

// 生成模拟学习记录数据
for (let i = 1; i <= 50; i++) {
  const users = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const courses = [
    { id: 1, title: 'N5基础日语', totalUnits: 20 },
    { id: 2, title: 'N4进阶日语', totalUnits: 25 },
    { id: 3, title: 'N3中级日语', totalUnits: 30 },
    { id: 4, title: '商务日语', totalUnits: 18 },
    { id: 5, title: '日语口语', totalUnits: 22 }
  ];
  
  const course = courses[Math.floor(Math.random() * courses.length)];
  const completedUnits = Math.floor(Math.random() * course.totalUnits);
  const progress = Math.floor((completedUnits / course.totalUnits) * 100);
  const statuses = ['in_progress', 'completed', 'paused'];
  const status = progress === 100 ? 'completed' : statuses[Math.floor(Math.random() * statuses.length)];
  
  learningRecords.push({
    id: i,
    userId: i,
    userName: users[Math.floor(Math.random() * users.length)],
    courseId: course.id,
    courseTitle: course.title,
    progress: progress,
    duration: Math.floor(Math.random() * 3600) + 600, // 10分钟到1小时
    status: status,
    score: status === 'completed' ? Math.floor(Math.random() * 40) + 60 : null,
    startTime: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    lastStudyTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    completedUnits: completedUnits,
    totalUnits: course.totalUnits,
    units: Array.from({ length: course.totalUnits }, (_, index) => ({
      id: index + 1,
      title: `第${index + 1}单元`,
      duration: Math.floor(Math.random() * 1800) + 300,
      status: index < completedUnits ? 'completed' : 'not_started',
      completedAt: index < completedUnits ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : null
    }))
  });
}

// 获取学习记录列表
router.get('/', (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      userName, 
      courseId, 
      status,
      startDate,
      endDate
    } = req.query;
    
    let filteredRecords = [...learningRecords];
    
    // 筛选
    if (userName) {
      filteredRecords = filteredRecords.filter(record => 
        record.userName.includes(userName)
      );
    }
    
    if (courseId) {
      filteredRecords = filteredRecords.filter(record => 
        record.courseId === parseInt(courseId)
      );
    }
    
    if (status) {
      filteredRecords = filteredRecords.filter(record => record.status === status);
    }
    
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      filteredRecords = filteredRecords.filter(record => {
        const recordDate = new Date(record.lastStudyTime);
        return recordDate >= start && recordDate <= end;
      });
    }
    
    // 按最后学习时间倒序排序
    filteredRecords.sort((a, b) => new Date(b.lastStudyTime) - new Date(a.lastStudyTime));
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedRecords = filteredRecords.slice(startIndex, endIndex);
    
    // 统计数据
    const stats = {
      totalUsers: new Set(learningRecords.map(r => r.userId)).size,
      totalDuration: Math.floor(learningRecords.reduce((sum, r) => sum + r.duration, 0) / 3600),
      completedCourses: learningRecords.filter(r => r.status === 'completed').length,
      avgCompletionRate: Math.floor(learningRecords.reduce((sum, r) => sum + r.progress, 0) / learningRecords.length * 100) / 100
    };
    
    res.json({
      success: true,
      data: {
        records: paginatedRecords,
        total: filteredRecords.length,
        page: parseInt(page),
        limit: parseInt(limit),
        stats
      }
    });
  } catch (error) {
    console.error('获取学习记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习记录失败'
    });
  }
});

// 获取单个学习记录详情
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const record = learningRecords.find(r => r.id === parseInt(id));
    
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '学习记录不存在'
      });
    }
    
    res.json({
      success: true,
      data: { record }
    });
  } catch (error) {
    console.error('获取学习记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习记录详情失败'
    });
  }
});

// 获取学习进度统计
router.get('/stats/progress', (req, res) => {
  try {
    const { userId, courseId, days = 30 } = req.query;
    
    let filteredRecords = [...learningRecords];
    
    if (userId) {
      filteredRecords = filteredRecords.filter(r => r.userId === parseInt(userId));
    }
    
    if (courseId) {
      filteredRecords = filteredRecords.filter(r => r.courseId === parseInt(courseId));
    }
    
    // 生成进度趋势数据
    const progressTrend = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const dayRecords = filteredRecords.filter(r => {
        const recordDate = new Date(r.lastStudyTime);
        return recordDate.toDateString() === date.toDateString();
      });
      
      progressTrend.push({
        date: date.toISOString().split('T')[0],
        studyTime: dayRecords.reduce((sum, r) => sum + r.duration, 0) / 3600,
        completedLessons: dayRecords.reduce((sum, r) => sum + r.completedUnits, 0),
        activeUsers: new Set(dayRecords.map(r => r.userId)).size
      });
    }
    
    res.json({
      success: true,
      data: {
        progressTrend,
        summary: {
          totalRecords: filteredRecords.length,
          avgProgress: filteredRecords.reduce((sum, r) => sum + r.progress, 0) / filteredRecords.length,
          totalStudyTime: filteredRecords.reduce((sum, r) => sum + r.duration, 0) / 3600
        }
      }
    });
  } catch (error) {
    console.error('获取学习进度统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习进度统计失败'
    });
  }
});

// 导出学习记录
router.post('/export', (req, res) => {
  try {
    const { format = 'excel', filters = {} } = req.body;
    
    // 这里应该实现学习记录导出逻辑
    res.json({
      success: true,
      data: {
        downloadUrl: '/api/learning-records/download/export_' + Date.now() + '.' + (format === 'excel' ? 'xlsx' : 'csv')
      },
      message: '学习记录导出成功'
    });
  } catch (error) {
    console.error('导出学习记录失败:', error);
    res.status(500).json({
      success: false,
      message: '导出学习记录失败'
    });
  }
});

module.exports = router;
