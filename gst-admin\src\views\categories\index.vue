<template>
  <div class="page-container">
    <div class="page-header">
      <h1>分类管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          v-if="authStore.hasRole(['admin', 'teacher'])"
        >
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
      </div>
    </div>

    <div class="content-placeholder">
      <el-empty description="分类管理功能开发中..." :image-size="120">
        <el-button type="primary" @click="showCreateDialog = true">
          创建分类
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const loading = ref(false)
const showCreateDialog = ref(false)

const refreshData = () => {
  console.log('刷新分类数据')
}
</script>

<style lang="scss" scoped>
.content-placeholder {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-xl);
  text-align: center;
}
</style>
