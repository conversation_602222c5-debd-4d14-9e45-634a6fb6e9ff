<template>
  <div class="page-container">
    <div class="page-header">
      <h1>分类管理</h1>
      <div class="header-actions">
        <el-button @click="loadCategories" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          type="primary"
          @click="showCreateDialog"
          v-if="authStore.hasRole(['admin', 'teacher'])"
        >
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ totalCategories }}</div>
          <div class="stat-label">总分类数</div>
        </div>
        <el-icon class="stat-icon"><FolderOpened /></el-icon>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ rootCategories.length }}</div>
          <div class="stat-label">顶级分类</div>
        </div>
        <el-icon class="stat-icon"><Folder /></el-icon>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ activeCategories }}</div>
          <div class="stat-label">启用分类</div>
        </div>
        <el-icon class="stat-icon"><Check /></el-icon>
      </el-card>
    </div>

    <!-- 分类树形表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分类列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索分类名称"
              style="width: 200px"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>

      <div v-loading="loading">
        <el-table
          :data="filteredCategories"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :default-expand-all="false"
          style="width: 100%"
        >
          <el-table-column prop="name" label="分类名称" min-width="200">
            <template #default="{ row }">
              <div class="category-name">
                <el-tag v-if="row.level > 1" size="small" type="info">
                  L{{ row.level }}
                </el-tag>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

          <el-table-column prop="sort" label="排序" width="80" align="center" />

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="创建时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
                v-if="authStore.hasRole(['admin', 'teacher'])"
              >
                编辑
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="showAddChildDialog(row)"
                v-if="authStore.hasRole(['admin', 'teacher'])"
              >
                添加子分类
              </el-button>
              <el-popconfirm
                title="确定要删除这个分类吗？"
                @confirm="deleteCategory(row)"
                v-if="authStore.hasRole(['admin'])"
              >
                <template #reference>
                  <el-button type="danger" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <el-empty v-if="!loading && categories.length === 0" description="暂无分类数据">
          <el-button type="primary" @click="showCreateDialog">
            创建第一个分类
          </el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 创建/编辑分类对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="父分类" prop="pid">
          <el-select
            v-model="formData.pid"
            placeholder="选择父分类（不选则为顶级分类）"
            clearable
            style="width: 100%"
          >
            <el-option label="顶级分类" :value="0" />
            <el-option
              v-for="category in categories.filter(c => c.pid === 0)"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="999"
            placeholder="排序值，数字越小越靠前"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { get, post, put, del } from '@/utils/request'

// 图标导入
import {
  Refresh,
  Plus,
  Search,
  FolderOpened,
  Folder,
  Check
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const categories = ref([])
const searchText = ref('')
const dialogVisible = ref(false)
const isEditing = ref(false)
const editingId = ref(null)
const formRef = ref(null)

// 表单数据
const formData = ref({
  name: '',
  pid: 0,
  description: '',
  sort: 0,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序值必须在 0-999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 计算属性
const totalCategories = computed(() => categories.value.length)

const rootCategories = computed(() =>
  categories.value.filter(cat => cat.pid === 0)
)

const activeCategories = computed(() =>
  categories.value.filter(cat => cat.status === 'active').length
)

const dialogTitle = computed(() =>
  isEditing.value ? '编辑分类' : '添加分类'
)

const filteredCategories = computed(() => {
  if (!searchText.value) {
    return buildCategoryTree(categories.value)
  }

  const filtered = categories.value.filter(cat =>
    cat.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
  return buildCategoryTree(filtered)
})

// 移除复杂的parentOptions计算属性，简化为基本的顶级分类选择

// 生命周期
onMounted(() => {
  loadCategories()
})

// 方法
const loadCategories = async () => {
  loading.value = true
  try {
    console.log('开始加载分类数据...')
    const response = await get('/api/categories')
    console.log('分类API响应:', response)

    if (response.success) {
      categories.value = response.data.categories || []
      console.log('加载的分类数据:', categories.value.length, '条')
      console.log('前3条分类:', categories.value.slice(0, 3))
    } else {
      console.error('API返回失败:', response)
      ElMessage.error('加载分类数据失败')
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类数据失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEditing.value = false
  editingId.value = null
  formData.value = {
    name: '',
    pid: 0,
    description: '',
    sort: 0,
    status: 'active'
  }
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const showEditDialog = (row) => {
  isEditing.value = true
  editingId.value = row.id
  formData.value = {
    name: row.name,
    pid: row.pid,
    description: row.description || '',
    sort: row.sort,
    status: row.status
  }
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const showAddChildDialog = (row) => {
  isEditing.value = false
  editingId.value = null
  formData.value = {
    name: '',
    pid: row.id,
    description: '',
    sort: 0,
    status: 'active'
  }
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }

  submitting.value = true
  try {
    const url = isEditing.value ? `/api/categories/${editingId.value}` : '/api/categories'
    const method = isEditing.value ? put : post

    const response = await method(url, formData.value)

    if (response.success) {
      ElMessage.success(isEditing.value ? '分类更新成功' : '分类创建成功')
      dialogVisible.value = false
      loadCategories()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const deleteCategory = async (row) => {
  try {
    const response = await del(`/api/categories/${row.id}`)
    if (response.success) {
      ElMessage.success('分类删除成功')
      loadCategories()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除分类失败:', error)
    ElMessage.error('删除失败')
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 工具函数
const buildCategoryTree = (categories, pid = 0) => {
  const tree = []
  const children = categories.filter(cat => cat.pid === pid)

  for (const child of children) {
    const node = {
      ...child,
      children: buildCategoryTree(categories, child.id)
    }
    tree.push(node)
  }

  return tree
}

const isDescendant = (categoryId, ancestorId) => {
  const category = categories.value.find(cat => cat.id === categoryId)
  if (!category || category.pid === 0) return false
  if (category.pid === ancestorId) return true
  return isDescendant(category.pid, ancestorId)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;

  .stat-card {
    .el-card__body {
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .stat-content {
      .stat-number {
        font-size: 28px;
        font-weight: 600;
        color: var(--el-color-primary);
        line-height: 1;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .stat-icon {
      font-size: 32px;
      color: var(--el-color-primary);
      opacity: 0.8;
    }
  }
}

.table-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-tag {
      font-size: 10px;
      height: 18px;
      line-height: 16px;
    }
  }
}

.el-table {
  .el-button {
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 对话框样式
.el-dialog {
  .el-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .el-tree-select {
      width: 100%;
    }

    .el-input-number {
      width: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .header-actions {
      width: 100%;
    }
  }

  .el-table {
    .el-table-column {
      &:not(:first-child):not(:last-child) {
        display: none;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .stat-card {
    .stat-number {
      color: var(--el-color-primary-light-3);
    }
  }
}
</style>
