{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/userinfo/userinfo.vue?6e28", "webpack:///D:/gst/gst-uniapp/pages/userinfo/userinfo.vue?6086", "webpack:///D:/gst/gst-uniapp/pages/userinfo/userinfo.vue?1cf1", "webpack:///D:/gst/gst-uniapp/pages/userinfo/userinfo.vue?26b5", "uni-app:///pages/userinfo/userinfo.vue", "webpack:///D:/gst/gst-uniapp/pages/userinfo/userinfo.vue?ea93", "webpack:///D:/gst/gst-uniapp/pages/userinfo/userinfo.vue?cee1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "uploadOssPolicy", "userInfo", "avatar", "methods", "changeAvatar", "console", "formSubmit", "suffix", "imgRelativePath", "imageType", "title", "e", "uploadData", "name", "key", "policy", "OSSAccessKeyId", "signature", "success_action_status", "uploadImagesData", "url", "filePath", "fileType", "formData", "getOssPolicy", "getUserInfo", "setUserInfo", "uni", "icon", "setTimeout", "getPhoneNumber", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4BznB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBACAF;gBACA;gBACAG;gBACAH;gBACAI;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;kBAAAC;gBAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;kBAAAD;gBAAA;cAAA;gBACA;gBACAE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;gBACA,GACA;gBACAC;kBACAC;kBACAC;kBACAC;kBACAT;kBACAU;gBACA,GACA;gBAAA;gBAAA,OACA;cAAA;gBACAxB;kBACAG;kBACAW;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAGA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAW;MAAA;MACA;QACAnB;QACA;UACA;QACA,QAEA;MACA;IACA;IACAoB;MAAA;MACA;QACApB;QACA;UACA;UACA;QACA,QAEA;MACA;IACA;IACAqB;MACA;QACAzB;MACA;QACAI;QACA;UACAsB;YACAjB;YACAkB;UACA;UACAC;YACAF;UACA;QACA,QAEA;MACA;IACA;IACAG;MACAzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;EAEA;EACA0B;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnLA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/userinfo/userinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/userinfo/userinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userinfo.vue?vue&type=template&id=5cdc0ea6&scoped=true&\"\nvar renderjs\nimport script from \"./userinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./userinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userinfo.vue?vue&type=style&index=0&id=5cdc0ea6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5cdc0ea6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/userinfo/userinfo.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=template&id=5cdc0ea6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gracePage>\r\n\t\t<view slot=\"gHeader\">\r\n\t\t\t<ws-head color=\"black\"></ws-head>\r\n\t\t</view>\r\n\t\t<view slot=\"gBody\" >\n\t<view class=\"info-set\">\n\t\t<button class=\"avatar-btn\" open-type=\"chooseAvatar\" @chooseavatar=\"changeAvatar\">\n\t\t\t<img :src=\"avatar\" class=\"avatar-img\" />\n\t\t</button>\n\t\t<form @submit=\"formSubmit\">\n\t\t\t<view class=\"username-content\">\n\t\t\t\t<view class=\"user-title\">昵称</view>\n\t\t\t\t<input class=\"user-input\" type=\"nickname\" name=\"input\" placeholder=\"请输入昵称\" :value=\"userInfo.name\"/>\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"username-content\">\r\n\t\t\t\t<view class=\"user-title\">手机号</view>\r\n\t\t\t\t<input class=\"user-input\" type=\"phone\" name=\"input\" placeholder=\"请输入手机号\" :value=\"userInfo.mobile\"/>\r\n\t\t\t\t<button class=\"phone-login-btn\" type=\"default\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">修改手机号</button>\r\n\t\t\t</view> -->\n\t\t\t<button class=\"submit-btn\" formType=\"submit\">提交</button>\n\t\t</form>\n\t</view>\r\n\t</view>\n\t</gracePage>\r\n</template>\n\n<script>\nimport { showToast, uploadFile } from '@/common/js/asyncWx.js';\nimport { isHttpOrHttps, randomString } from '@/common/js/replace.js';\n// 此方法是项目获取用户信息方法，根据自己项目进行替换\n// import { getStorageUserInfo, setStorageUserInfo } from '@/utils/storage.js';\n// 此方法是项目获取oss参数接口，根据自己项目进行替换\n// import { getOssPolicy } from '@/api/ossUpload';\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t// 上传oss所需签名\n\t\t\tuploadOssPolicy: {},\n\t\t\tuserInfo: '',\n\t\t\tavatar: ''\n\t\t};\n\t},\n\tmethods: {\n\t\tchangeAvatar(e) {\r\n\t\t\tconsole.log(e)\n\t\t\tthis.avatar = e.detail.avatarUrl;\n\t\t},\n\t\tasync formSubmit(e) {\r\n\t\t\tif(this.avatar.startsWith('http://tmp')||this.avatar.startsWith('wxfile://tmp')){\r\n\t\t\t\t// 后缀名\r\n\t\t\t\tconst suffix = this.avatar.split('.').pop();\r\n\t\t\t\tconsole.log('avatar'+this.avatar)\r\n\t\t\t\t// 图片的正式路径（项目只需要上传半路径，因此此路径不包含基准路径）\r\n\t\t\t\tconst imgRelativePath = this.uploadOssPolicy.dir + '/' + randomString(6, suffix);\r\n\t\t\t\tconsole.log('imgRelativePath'+imgRelativePath)\r\n\t\t\t\tconst imageType = /\\.(jpg|jpeg|png|GIF|JPG|PNG)$/.test(imgRelativePath);\r\n\t\t\t\tif (!this.avatar || !imageType) return showToast({ title: '请上传图片或正确图片' });\r\n\t\t\t\tif (!e.detail.value.input) return showToast({ title: '请输入昵称' });\r\n\t\t\t\t// oss上传参数，根据自己项目进行替换\r\n\t\t\t\tconst uploadData = {\r\n\t\t\t\t\tname: imgRelativePath,\r\n\t\t\t\t\tkey: imgRelativePath,\r\n\t\t\t\t\tpolicy: this.uploadOssPolicy.policy,\r\n\t\t\t\t\tOSSAccessKeyId: this.uploadOssPolicy.accessid,\r\n\t\t\t\t\tsignature: this.uploadOssPolicy.signature,\r\n\t\t\t\t\tsuccess_action_status: '200',\r\n\t\t\t\t\t//callback: this.uploadOssPolicy.callback\r\n\t\t\t\t};\r\n\t\t\t\t// 上传图片所有参数，属性值根据自己项目进行替换\r\n\t\t\t\tconst uploadImagesData = {\r\n\t\t\t\t\turl: this.uploadOssPolicy.host,\r\n\t\t\t\t\tfilePath: this.avatar,\r\n\t\t\t\t\tfileType: 'image',\r\n\t\t\t\t\tname: 'file',\r\n\t\t\t\t\tformData: uploadData\r\n\t\t\t\t};\r\n\t\t\t\t// 由于图片路径为临时路径，因此需要上传至项目服务器转化为正式路径\r\n\t\t\t\tawait uploadFile(uploadImagesData);\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\tavatar: imgRelativePath,\r\n\t\t\t\t\tname: e.detail.value.input\r\n\t\t\t\t};\r\n\t\t\t\tthis.userInfo.avatar = this.uploadOssPolicy.host+imgRelativePath;\r\n\t\t\t}else{\r\n\t\t\t\tthis.userInfo.avatar = this.avatar;\r\n\t\t\t\t\r\n\t\t\t}\n\t\t\tthis.userInfo.name = e.detail.value.input;\n\t\t\t// 发送信息修改请求\n\t\t\tthis.setUserInfo(this.userInfo);\n\t\t},\r\n\t\tgetOssPolicy(){\r\n\t\t\tthis.$http.get(\"v1/user/sign\").then(res => {\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\tthis.uploadOssPolicy = res.data.data;\r\n\t\t\t\t} else {\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetUserInfo(){\r\n\t\t\tthis.$http.get(\"v1/user/getUserInfo\").then(res => {\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\tthis.userInfo = res.data.data\r\n\t\t\t\t\tthis.avatar = res.data.data.avatar\r\n\t\t\t\t} else {\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsetUserInfo(userInfo){\r\n\t\t\tthis.$http.post(\"v1/user/setUserInfo\", {\r\n\t\t\t\tuserInfo: userInfo\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '修改成功',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t} else {\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetPhoneNumber(e) {\r\n\t\t\tconsole.log(e)\r\n\t\t\t// console.log(e.detail.errMsg)\r\n\t\t\t// console.log(e.detail.iv)\r\n\t\t\t// console.log(e.detail.encryptedData)\r\n\t\t\t// console.log('userInfo'+uni.getStorageSync('userInfo'))\r\n\t\t\t// uni.login({\r\n\t\t\t// \tprovider: 'weixin',\r\n\t\t\t// \tsuccess: (res) => {\r\n\t\t\t// \t\tif (res.code) {\r\n\t\t\t// \t\t\tthis.code = res.code;\r\n\t\t\t// \t\t\tthis.$http.post(\"auth/decrypt_phone\", {\r\n\t\t\t// \t\t\t\tcode: this.code,\r\n\t\t\t// \t\t\t\tiv: e.detail.iv,\r\n\t\t\t// \t\t\t\tencryptedData: e.detail.encryptedData,\r\n\t\t\t// \t\t\t\tid:uni.getStorageSync('userInfo').id\r\n\t\t\t// \t\t\t}).then(res => {\r\n\t\t\t// \t\t\t\tif (res.data.code == 0) {\r\n\t\t\t// \t\t\t\t\tthis.$store.commit('setUserInfo', {\r\n\t\t\t// \t\t\t\t\t\tuserInfo: res.data.data,\r\n\t\t\t// \t\t\t\t\t\tsaveStorage: true\r\n\t\t\t// \t\t\t\t\t});\r\n\t\t\t// \t\t\t\t\tuni.switchTab({\r\n\t\t\t// \t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t// \t\t\t\t\t})\r\n\t\t\t// \t\t\t\t} else {\r\n\t\t\t// \t\t\t\t\tuni.showToast({\r\n\t\t\t// \t\t\t\t\t\ttitle: res.data.data.message,\r\n\t\t\t// \t\t\t\t\t\ticon: \"none\"\r\n\t\t\t// \t\t\t\t\t});\r\n\t\t\t// \t\t\t\t}\r\n\t\t\t// \t\t\t\t})\r\n\t\t\t// \t\t} else {\r\n\t\t\t// \t\t\tconsole.log('登录失败！' + res.errMsg)\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\t\t\t// })\r\n\t\t\t\r\n\t\t},\r\n\t\t\n\t},\n\tonLoad() {\n\t\t// getOssPolicy().then(res => {\n\t\t// \tthis.uploadOssPolicy = res.data;\n\t\t// });\r\n\t\tthis.getOssPolicy()\r\n\t\tthis.getUserInfo()\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.info-set {\n\t.avatar-btn {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tmargin: 50rpx auto;\n\t\tpadding: 0;\n\t\tbackground: none;\n\t\tborder-radius: 50%;\n\n\t\t.avatar-img {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t}\n\t}\n\n\t.username-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t\tborder-top: 1px solid #f5f5f5;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\n\t\t.user-title {\n\t\t\twidth: 20%;\n\t\t\theight: 80rpx;\n\t\t\tline-height: 80rpx;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\n\t\t.user-input {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tline-height: 80rpx;\n\t\t\tpadding: 0 28rpx;\n\t\t\tcolor: #333;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\t}\n\n\t.submit-btn {\n\t\tmargin: 80rpx 24rpx 0;\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx;\n\t\tbackground-color: #04BCD6;\n\t\tborder-radius: 4rpx;\n\t\tbox-sizing: border-box;\n\t}\n}\n</style>\n\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=style&index=0&id=5cdc0ea6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=style&index=0&id=5cdc0ea6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699115310\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}