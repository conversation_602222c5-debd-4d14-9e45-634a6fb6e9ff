{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseVideo.vue?8e0b", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseVideo.vue?1397", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseVideo.vue?101a", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseVideo.vue?8754", "uni-app:///components/gaoyia-parse/components/wxParseVideo.vue"], "names": ["name", "props", "node"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;;;AAG3D;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwnB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCQ5oB;EACAA;EACAC;IACAC;EACA;AACA;AAAA,2B", "file": "components/gaoyia-parse/components/wxParseVideo.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxParseVideo.vue?vue&type=template&id=1edf6fd0&\"\nvar renderjs\nimport script from \"./wxParseVideo.vue?vue&type=script&lang=js&\"\nexport * from \"./wxParseVideo.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gaoyia-parse/components/wxParseVideo.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=template&id=1edf6fd0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseVideo.vue?vue&type=script&lang=js&\"", "<template>\r\n  <!--增加video标签支持，并循环添加-->\r\n  <view :class=\"node.classStr\" :style=\"node.styleStr\">\r\n    <video :class=\"node.classStr\" :style=\"node.styleStr\" class=\"video-video\" :src=\"node.attr.src\"></video>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'wxParseVideo',\r\n  props: {\r\n    node: {},\r\n  },\r\n};\r\n</script>\r\n"], "sourceRoot": ""}