{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/login/login.vue?6552", "webpack:///D:/gst/gst-uniapp/pages/login/login.vue?070e", "webpack:///D:/gst/gst-uniapp/pages/login/login.vue?70c8", "webpack:///D:/gst/gst-uniapp/pages/login/login.vue?ba48", "uni-app:///pages/login/login.vue", "webpack:///D:/gst/gst-uniapp/pages/login/login.vue?c221", "webpack:///D:/gst/gst-uniapp/pages/login/login.vue?bc57"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "created", "uni", "provider", "success", "console", "onLoad", "data", "code", "loginType", "userInfo", "token", "pnpre", "pnpres", "vcodeBtnName", "countNum", "countDownTimer", "phoneno", "vCodeKey", "user_id", "canIUseProfile", "methods", "getLogin", "type", "iv", "encryptedData", "saveStorage", "getUserInfo", "title", "mask", "desc", "lang", "that", "fail", "getPhone", "key", "delta", "url", "content", "navTo", "phoneLogin", "clearInterval", "loginWithWx", "icon", "changePre", "loginNow", "name", "checkType", "errorMsg", "checkRule", "mobile", "verification_key", "sms_code", "duration", "getVCode", "params", "countDown", "getPhoneNumber", "open", "withSubscriptions", "tmplIds"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0DtnB;AAAA,eACA;EACAC;IAAA;IACAC;MACAC;MACAC;QACA;UACA;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACAf;QACAgB;QACAC;MACA;QACApB;QACA;QACA;QACA;UACAM;UACAe;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAzB;QACA0B;QACAC;MACA;MACA;MACA;MACAxB;MACA;QACAK;QACAgB;MACA;MACA;MACAxB;QACAC;QACAC;UAEA;UACAC;QACA;MACA;MACA;MACA;QACA;QACA;UACAA;UACA;QACA;MACA;QACA;QACA;QACAH;UACA4B;UACAC;UACA3B;YACAC;YACA2B;UACA;UACAC;YACA5B;UACA;QACA;MAEA;IACA;IACA6B;MAAA;MACA;QACAhC;QACA;UACA;UACA;YACAQ;YACAgB;UACA;UACAxB;YACAiC;YACA5B;UACA;UACA;YACA;YACA;YACA;cACAL;gBACAkC;cACA;YACA;cACAlC;gBACAmC;cACA;YACA;cACAnC;gBACAkC;cACA;YACA;UACA;YACAlC;cACA0B;cACAU;cACAlC;gBACA;kBACA;gBACA;kBACAF;oBACAmC;kBACA;gBACA;cACA;YACA;UACA;QACA,QAEA;MACA;IACA;IACAE;MACArC;QACAmC;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;IACA;IACAC;MACAxC;QACA0B;QACAe;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAE;QACAD;MACA,EACA;MACA;MACA;MACA;MACA;QACA;UACAE;UACAC;UACAC;UACAjC;QACA;UACAd;UACA;YACAH;cACAiC;cACA5B;YACA;YACAL;cACAiC;cACA5B;YACA;YACAL;cACA0B;cACAyB;YACA;YACA;YACA;YACA;YACA;YACA;YACAnD;cACAmC;YACA;UACA;YACAnC;cACA0B;cACAe;YACA;UACA;QACA;MACA;QACAzC;UACA0B;UACAe;QACA;MACA;IACA;IACAW;MAAA;MACA;MACA;QACApD;UACA0B;UACAe;QACA;QACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACAY;UACAL;QACA;MACA;QACA;QACA;UACAhD;YACA0B;YACAe;UACA;UACA;QACA;UACA;UACAzC;YACA0B;YACAe;UACA;UACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA;IAEA;IACAa;MACA;QACAf;QACA;QACA;MACA;MACA;MACA;IACA;IACA;IACA;IACA;IACAgB;MAAA;MACApD;MACA;MACA;MACA;MACAA;MACAH;QACAC;QACAC;UACA;YACA;YACA;cACAI;cACAgB;cACAC;cACAF;YACA;cACA;gBACA;kBACAb;kBACAgB;gBACA;gBACA;gBACA;gBACA;kBACAxB;oBACAkC;kBACA;gBACA;kBACAlC;oBACAmC;kBACA;gBACA;kBACAnC;oBACAkC;kBACA;gBACA;cACA;gBACA/B;gBACAH;kBACA0B;kBACAe;gBACA;cACA;YACA;UACA;YACAtC;UACA;QACA;MACA;IAEA;IACA;IACAqD;MACA;MACA;MACA9D;QACA+D;QAAA;QACAvD;UACA;YAAA;YACAF;cAAA;cACAE;gBACAC;gBACA2B;cACA;YACA;UACA;YAAA;YACA;YACA9B;cACA0D,yDACA,8CACA;cACAxD;gBACAC;gBACA2B;cACA;cACAC;gBACA5B;cACA;YACA;UACA;QAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzbA;AAAA;AAAA;AAAA;AAA64B,CAAgB,43BAAG,EAAC,C;;;;;;;;;;;ACAj6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gracePage>\r\n\t\t<view slot=\"gHeader\">\r\n\t\t\t<ws-head color=\"black\"></ws-head>\r\n\t\t</view>\r\n\t\t<view slot=\"gBody\" style=\"padding:30rpx 30rpx 120rpx 30rpx;\">\r\n\t\t\t<view class=\"ws-logo-box\">\r\n\t\t\t\t<view class=\"logo-title\">日语云课</view>\r\n\t\t\t\t<!-- <view class=\"logo-text\">日语国际翻译大赛</view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ws-btn-box wx-box\" v-if=\"loginType=='wx'\">\r\n\t\t\t\t<!-- <button class=\"wx-login-btn ws-mt\" \r\n\t\t\t\topen-type=\"getUserInfo\"\r\n\t\t\t\t@getuserinfo=\"getuserinfo\">\r\n\t\t\t\t\t微信账号一键登录\r\n\t\t\t\t</button> -->\r\n\t\t\t\t<!-- <button @tap=\"phoneLogin\" class=\"phone-login-btn\">\r\n\t\t\t\t\t手机号登录\r\n\t\t\t\t</button> -->\r\n\t\t\t\t<button class=\"wx-login-btn ws-mt\" v-if=\"canIUseProfile==false\" type=\"primary\" open-type=\"getUserInfo\"\r\n\t\t\t\t\t@getuserinfo=\"getUserInfo\" withCredentials=\"true\">登录</button>\r\n\r\n\t\t\t\t<button class=\"wx-login-btn ws-mt\" v-else type=\"primary\" @tap=\"getUserInfo\">登录</button>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"ws-btn-box\">\r\n\t\t\t\t<form @submit=\"loginNow\" class=\"grace-form\" style=\"margin-top:80rpx;\">\r\n\t\t\t\t\t<!-- <view class=\"grace-form-item grace-border-b\"> -->\r\n\t\t\t\t\t\t<!-- <view class=\"grace-pnper\">\r\n\t\t\t\t\t\t\t<picker :value=\"pnpre\" @change=\"changePre\" :range=\"pnpres\" name=\"pn_pre\" style=\"text-align:left;\">\r\n\t\t\t\t\t\t\t\t<text class=\"grace-text\">+{{pnpres[pnpre]}}</text><text class=\"grace-text grace-icons icon-arrow-down\" style=\"margin-left:10rpx;\"></text>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<!-- <view class=\"grace-form-body\">\r\n\t\t\t\t\t\t\t<input type=\"number\" v-model=\"phoneno\" class=\"grace-form-input\" name=\"pn\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入手机号码\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"grace-form-item grace-border-b\"> -->\r\n\t\t\t\t\t\t<!-- <text class=\"grace-form-label\">短信验证码</text> -->\r\n\t\t\t\t\t\t<!-- <view class=\"grace-form-body\">\r\n\t\t\t\t\t\t\t<input type=\"number\" class=\"grace-form-input\" name=\"yzm\" placeholder=\"请输入验证码\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"login-sendmsg-btn\" @tap=\"getVCode\">{{vcodeBtnName}}</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class=\"grace-margin-top\">\r\n\t\t\t\t\t\t<!-- <button form-type=\"submit\" type=\"primary\" class=\"phone-login-btn\">\r\n\t\t\t\t\t\t\t绑定手机号\r\n\t\t\t\t\t\t</button> -->\r\n\t\t\t\t\t\t<button class=\"phone-login-btn\" type=\"default\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">获取手机号</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</form>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gracePage>\r\n</template>\r\n\r\n\r\n<script>\r\n\tvar graceChecker = require(\"@/GraceUI5/js/checker.js\");\r\n\texport default {\r\n\t\tcreated() {\r\n\t\t\tuni.login({\r\n\t\t\t\tprovider: 'weixin',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\tthis.code = res.code;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('登录失败！' + res.errMsg)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\t// uni.navigateTo({\r\n\t\t\t// \turl:\"/pages/index/index\"\r\n\t\t\t// })\r\n\t\t\tif (option.type) {\r\n\t\t\t\tthis.loginType = option.type;\r\n\t\t\t}\r\n\t\t\tif (wx.getUserProfile) {\r\n\t\t\t\tthis.canIUseProfile = true;\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcode: \"123\",\r\n\t\t\t\tloginType: \"wx\", // 登录方式 -- wx 微信一键登录，其他为 账号密码登录\r\n\t\t\t\tuserInfo: {}, // 用户信息\r\n\t\t\t\ttoken: \"\", // token\r\n\r\n\t\t\t\t// 手机号登录相关变量\r\n\t\t\t\tpnpre: 0,\r\n\t\t\t\tpnpres: ['86', '01', '11', '26', '520'],\r\n\t\t\t\tvcodeBtnName: \"获取验证码\",\r\n\t\t\t\tcountNum: 120,\r\n\t\t\t\tcountDownTimer: null,\r\n\t\t\t\tphoneno: '',\r\n\r\n\t\t\t\tvCodeKey: \"\", // 验证码key\r\n\t\t\t\tuser_id: \"\",\r\n\t\t\t\tcanIUseProfile: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetLogin: function(iv, encryptedData, userInfo) {\r\n\t\t\t\tthis.$http.post(\"auth/wxlogin\", {\r\n\t\t\t\t\ttype:'class',\r\n\t\t\t\t\tcode: this.code,\r\n\t\t\t\t\tiv: iv,\r\n\t\t\t\t\tencryptedData: encryptedData\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res.data.data)\r\n\t\t\t\t\tthis.userInfo = res.data.data;\r\n\t\t\t\t\t// 缓存 token\r\n\t\t\t\t\tthis.$store.commit('setUserToken', {\r\n\t\t\t\t\t\ttoken: res.data.data || '',\r\n\t\t\t\t\t\tsaveStorage: true\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.getPhone(this.userInfo);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetUserInfo(e) {\r\n\t\t\t\t// 等待登录弹窗\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: \"登录中...\",\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t})\r\n\t\t\t\t// 获得用户信息，并异步存储到本地\r\n\t\t\t\tthis.userInfo = e.target.userInfo;\r\n\t\t\t\tconsole.log(this.userInfo)\r\n\t\t\t\tthis.$store.commit('setUserInfo', {\r\n\t\t\t\t\tuserInfo: this.userInfo,\r\n\t\t\t\t\tsaveStorage: true\r\n\t\t\t\t});\r\n\t\t\t\t// 获得code\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (loginRes) => {\r\n\r\n\t\t\t\t\t\tthis.code = loginRes.code\r\n\t\t\t\t\t\tconsole.log('code' + loginRes.code);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t//旧版本方式\r\n\t\t\t\tif (this.canIUseProfile == false) {\r\n\t\t\t\t\t//获取授权信息  \r\n\t\t\t\t\tif (e.target.userInfo) {\r\n\t\t\t\t\t\tconsole.log('用户允许了授权', e.target.userInfo); //1.拿到基本的微信信息!!  \r\n\t\t\t\t\t\tthis.getLogin(e.target.iv, e.target.encryptedData, e.target.userInfo);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t//新版本方式  \r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\tuni.getUserProfile({\r\n\t\t\t\t\t\tdesc: '用于完善用户资料',\r\n\t\t\t\t\t\tlang: 'zh_CN',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tconsole.log('wx.getUserProfile=>用户允许了授权', res);\r\n\t\t\t\t\t\t\tthat.getLogin(res.iv, res.encryptedData, res.userInfo);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\tconsole.log('wx.getUserProfile=>用户拒绝了授权', res);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetPhone() {\r\n\t\t\t\tthis.$http.get(\"auth/me\").then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.userInfo = res.data.data;\r\n\t\t\t\t\t\tthis.$store.commit('setUserInfo', {\r\n\t\t\t\t\t\t\tuserInfo: this.userInfo,\r\n\t\t\t\t\t\t\tsaveStorage: true\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\tkey: \"phone\",\r\n\t\t\t\t\t\t\tdata: res.data.data.phone\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif(res.data.data.phone){\r\n\t\t\t\t\t\t\t//处理兼容，如果没有上一级界面则返回首页\r\n\t\t\t\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\t\t\t\tif (pages.length === 2) {\r\n\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else if (pages.length === 1) {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle:\"是否绑定手机号？\",\r\n\t\t\t\t\t\t\t\tcontent:\"必须绑定手机号才可进入课程\",\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tthis.loginType = \"phone\"\r\n\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\turl:\"/pages/index/index\"\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tnavTo() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tphoneLogin() {\r\n\t\t\t\tthis.loginType = '';\r\n\t\t\t\tthis.pnpre = 0;\r\n\t\t\t\tthis.countNum = 120;\r\n\t\t\t\tthis.phoneno = '';\r\n\t\t\t\tclearInterval(this.countDownTimer);\r\n\t\t\t\tthis.countDownTimer = null;\r\n\t\t\t\tthis.vcodeBtnName = \"获取验证码\";\r\n\t\t\t},\r\n\t\t\tloginWithWx: function() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: \"请完善登录功能\",\r\n\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchangePre: function(e) {\r\n\t\t\t\tthis.pnpre = e.detail.value;\r\n\t\t\t},\r\n\t\t\tloginNow: function(e) {\r\n\t\t\t\t// 表单验证\r\n\t\t\t\tvar rule = [{\r\n\t\t\t\t\t\tname: \"pn\",\r\n\t\t\t\t\t\tcheckType: \"phoneno\",\r\n\t\t\t\t\t\terrorMsg: \"请填写正确的手机号\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"yzm\",\r\n\t\t\t\t\t\tcheckType: \"string\",\r\n\t\t\t\t\t\tcheckRule: \"4,6\",\r\n\t\t\t\t\t\terrorMsg: \"请正确填写短信验证码\"\r\n\t\t\t\t\t},\r\n\t\t\t\t];\r\n\t\t\t\tvar formData = e.detail.value;\r\n\t\t\t\tvar checkRes = graceChecker.check(formData, rule);\r\n\t\t\t\t// 验证通过\r\n\t\t\t\tif (checkRes) {\r\n\t\t\t\t\tthis.$http.post(\"auth/bindphone\", {\r\n\t\t\t\t\t\tmobile: e.mp.detail.value.pn,\r\n\t\t\t\t\t\tverification_key: this.vCodeKey,\r\n\t\t\t\t\t\tsms_code: e.mp.detail.value.yzm,\r\n\t\t\t\t\t\tuser_id: this.user_id\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconsole.log(res.data)\r\n\t\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\t\tkey: \"phone\",\r\n\t\t\t\t\t\t\t\tdata: e.mp.detail.value.pn\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\t\tkey: \"user\",\r\n\t\t\t\t\t\t\t\tdata: res.data.data || ''\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"绑定成功！\",\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// setTimeout(() => {\r\n\t\t\t\t\t\t\t// \tuni.navigateBack({\r\n\t\t\t\t\t\t\t// \t\tdelta:1\r\n\t\t\t\t\t\t\t// \t})\r\n\t\t\t\t\t\t\t// },2000)\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: res.data + \"，建议退出并重新登录后再次尝试\",\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: graceChecker.error,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetVCode: function() {\r\n\t\t\t\tvar myreg = /^[1][0-9]{10}$/;\r\n\t\t\t\tif (!myreg.test(this.phoneno)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请正确填写手机号码',\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t// 手机号码为 :  this.phoneno\r\n\t\t\t\t// vcodeBtnName 可以阻止按钮被多次点击 多次发送 return 会终止函数继续运行\r\n\t\t\t\tif (this.vcodeBtnName != '获取验证码' && this.vcodeBtnName != '重新发送') {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.vcodeBtnName = \"发送中...\";\r\n\t\t\t\t// 与后端 api 交互，发送验证码 【自己写的具体业务代码】\r\n\t\t\t\tthis.$http.get(\"auth/sms\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tmobile: this.phoneno\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\t// console.log(res.data.data.verification_key)\r\n\t\t\t\t\tif (!res.data.data) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '短信发送失败，请稍后再试',\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.vcodeBtnName = \"重新发送\"\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 假设发送成功，给用户提示\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '短信已发送，请注意查收',\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.vCodeKey = res.data.data.verification_key;\r\n\t\t\t\t\t\t// 倒计时\r\n\t\t\t\t\t\tthis.countNum = 120;\r\n\t\t\t\t\t\tthis.countDownTimer = setInterval(function() {\r\n\t\t\t\t\t\t\tthis.countDown();\r\n\t\t\t\t\t\t}.bind(this), 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\t\t\tcountDown: function() {\r\n\t\t\t\tif (this.countNum < 1) {\r\n\t\t\t\t\tclearInterval(this.countDownTimer);\r\n\t\t\t\t\tthis.vcodeBtnName = \"重新发送\";\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.countNum--;\r\n\t\t\t\tthis.vcodeBtnName = this.countNum + '秒重发';\r\n\t\t\t},\r\n\t\t\t// reg : function(){\r\n\t\t\t// \twx.showToast({ title: \"注册页面类似登录，请自行完善 (:\", icon: \"none\" });\r\n\t\t\t// },\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\t// console.log(e.detail.errMsg)\r\n\t\t\t\t// console.log(e.detail.iv)\r\n\t\t\t\t// console.log(e.detail.encryptedData)\r\n\t\t\t\tconsole.log('userInfo'+uni.getStorageSync('userInfo'))\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\t\tthis.code = res.code;\r\n\t\t\t\t\t\t\tthis.$http.post(\"auth/decrypt_phone\", {\r\n\t\t\t\t\t\t\t\tcode: this.code,\r\n\t\t\t\t\t\t\t\tiv: e.detail.iv,\r\n\t\t\t\t\t\t\t\tencryptedData: e.detail.encryptedData,\r\n\t\t\t\t\t\t\t\ttype:'class',\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$store.commit('setUserInfo', {\r\n\t\t\t\t\t\t\t\t\t\tuserInfo: res.data.data,\r\n\t\t\t\t\t\t\t\t\t\tsaveStorage: true\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t//处理兼容，如果没有上一级界面则返回首页\r\n\t\t\t\t\t\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\t\t\t\t\t\tif (pages.length === 2) {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else if (pages.length === 1) {\r\n\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.data.data.message,\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('登录失败！' + res.errMsg)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t//订阅消息\r\n\t\t\topen() {\r\n\t\t\t\tvar that = this\r\n\t\t\t\t// 获取用户的当前设置，判断是否点击了“总是保持以上，不在询问”\r\n\t\t\t\twx.getSetting({\r\n\t\t\t\t\twithSubscriptions: true, //是否获取用户订阅消息的订阅状态，默认false不返回\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tif (res.authSetting['scope.subscribeMessage']) { //用户点击了“总是保持以上，不再询问”\r\n\t\t\t\t\t\t\tuni.openSetting({ // 打开设置页\r\n\t\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res.authSetting)\r\n\t\t\t\t\t\t\t\t\tthat.submit();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息\r\n\t\t\t\t\t\t\t// var templateid = that.setting.templateid.map(item => item.tempid)\r\n\t\t\t\t\t\t\tuni.requestSubscribeMessage({\r\n\t\t\t\t\t\t\t\ttmplIds: ['09VXAeuCPLVlrfdAN5JSCPaSnblK3obw5ynHqtDhsyI',\r\n\t\t\t\t\t\t\t\t\t'KlMjfhtBCQWDZKxuXxEmWy1Xubp5NIccPnIBh4h3QI4'\r\n\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\t\tthat.submit();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.ws-logo-box {\r\n\t\tmargin: 100rpx 0;\r\n\t\ttext-align: center;\r\n\t\t/* display: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center; */\r\n\t}\r\n\r\n\t.ws-logo-img {\r\n\t\tmargin: 0 auto;\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.logo-title {\r\n\t\tfont-size: 44rpx;\r\n\t\tfont-weight: bold;\r\n\t\tletter-spacing: 8rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.logo-text {\r\n\t\tmargin-top: 14rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.ws-btn-box {\r\n\t\ttext-align: center;\r\n\t\tpadding: 0 40rpx;\r\n\t}\r\n\r\n\t.wx-box {\r\n\t\tpadding-top: 100rpx;\r\n\t}\r\n\r\n\t.wx-login-btn,\r\n\t.phone-login-btn {\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tmargin-top: 40rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tbackground-color: #04BCD6;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tletter-spacing: 2px;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t/* .phone-login-btn{\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\tcolor: #04BCD6;\r\n\t\t\tborder: 1px solid #04BCD6;\r\n\t\t} */\r\n\t.noLogin {\r\n\t\tposition: absolute;\r\n\t\tbottom: 80rpx;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tcolor: #04BCD6;\r\n\t}\r\n\r\n\t.marginTop {\r\n\t\tmargin-top: 100rpx;\r\n\t}\r\n\r\n\t.logo {\r\n\t\twidth: 250rpx;\r\n\t\theight: 68rpx\r\n\t}\r\n\r\n\t.login-sendmsg-btn {\r\n\t\theight: 60rpx;\r\n\t\twidth: 200rpx;\r\n\t\tflex-shrink: 0;\r\n\t\tmargin-left: 30rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #04BCD6;\r\n\t\tline-height: 60rpx;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.grace-pnper {\r\n\t\twidth: 168rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.grace-form-label {\r\n\t\twidth: 168rpx;\r\n\t}\r\n\r\n\t.grace-form-input {\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.grace-form-item {\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041063193\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}