{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-image/u-image.vue?a6b3", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-image/u-image.vue?649b", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-image/u-image.vue?676c", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-image/u-image.vue?23cb", "uni-app:///uni_modules/uview-ui/components/u-image/u-image.vue", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-image/u-image.vue?9913", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-image/u-image.vue?ec14"], "names": ["name", "mixins", "data", "isError", "loading", "opacity", "durationTime", "backgroundStyle", "show", "watch", "src", "immediate", "handler", "computed", "wrapStyle", "style", "mounted", "methods", "onClick", "onError<PERSON><PERSON><PERSON>", "onLoadHandler", "removeBgColor", "backgroundColor"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8DtpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACA;MACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAA6tC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAjvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-image/u-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-image.vue?vue&type=template&id=042b391e&scoped=true&\"\nvar renderjs\nimport script from \"./u-image.vue?vue&type=script&lang=js&\"\nexport * from \"./u-image.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-image.vue?vue&type=style&index=0&id=042b391e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"042b391e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-image/u-image.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-image.vue?vue&type=template&id=042b391e&scoped=true&\"", "var components\ntry {\n  components = {\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-transition/u-transition\" */ \"@/uni_modules/uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapStyle, _vm.backgroundStyle])\n  var g0 =\n    !_vm.isError && !(_vm.shape == \"circle\") ? _vm.$u.addUnit(_vm.radius) : null\n  var g1 = !_vm.isError ? _vm.$u.addUnit(_vm.width) : null\n  var g2 = !_vm.isError ? _vm.$u.addUnit(_vm.height) : null\n  var g3 =\n    _vm.showLoading && _vm.loading && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.radius)\n      : null\n  var g4 =\n    _vm.showError && _vm.isError && !_vm.loading && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.radius)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-image.vue?vue&type=script&lang=js&\"", "<template>\n\t<u-transition\n\t\tmode=\"fade\"\n\t\t:show=\"show\"\n\t\t:duration=\"fade ? 1000 : 0\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-image\"\n\t\t\t@tap=\"onClick\"\n\t\t\t:style=\"[wrapStyle, backgroundStyle]\"\n\t\t>\n\t\t\t<image\n\t\t\t\tv-if=\"!isError\"\n\t\t\t\t:src=\"src\"\n\t\t\t\t:mode=\"mode\"\n\t\t\t\t@error=\"onErrorHandler\"\n\t\t\t\t@load=\"onLoadHandler\"\n\t\t\t\t:show-menu-by-longpress=\"showMenuByLongpress\"\n\t\t\t\t:lazy-load=\"lazyLoad\"\n\t\t\t\tclass=\"u-image__image\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tborderRadius: shape == 'circle' ? '10000px' : $u.addUnit(radius),\n\t\t\t\t\twidth: $u.addUnit(width),\n\t\t\t\t\theight: $u.addUnit(height)\n\t\t\t\t}\"\n\t\t\t></image>\n\t\t\t<view\n\t\t\t\tv-if=\"showLoading && loading\"\n\t\t\t\tclass=\"u-image__loading\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(radius),\n\t\t\t\t\tbackgroundColor: this.bgColor\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<slot name=\"loading\">\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\t:name=\"loadingIcon\"\n\t\t\t\t\t\t:width=\"width\"\n\t\t\t\t\t\t:height=\"height\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t\t<view\n\t\t\t\tv-if=\"showError && isError && !loading\"\n\t\t\t\tclass=\"u-image__error\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(radius)\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<slot name=\"error\">\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\t:name=\"errorIcon\"\n\t\t\t\t\t\t:width=\"width\"\n\t\t\t\t\t\t:height=\"height\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t</view>\n\t</u-transition>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * Image 图片\n\t * @description 此组件为uni-app的image组件的加强版，在继承了原有功能外，还支持淡入动画、加载中、加载失败提示、圆角值和形状等。\n\t * @tutorial https://uviewui.com/components/image.html\n\t * @property {String}\t\t\tsrc \t\t\t\t图片地址\n\t * @property {String}\t\t\tmode \t\t\t\t裁剪模式，见官网说明 （默认 'aspectFill' ）\n\t * @property {String | Number}\twidth \t\t\t\t宽度，单位任意，如果为数值，则为px单位 （默认 '300' ）\n\t * @property {String | Number}\theight \t\t\t\t高度，单位任意，如果为数值，则为px单位 （默认 '225' ）\n\t * @property {String}\t\t\tshape \t\t\t\t图片形状，circle-圆形，square-方形 （默认 'square' ）\n\t * @property {String | Number}\tradius\t\t \t\t圆角值，单位任意，如果为数值，则为px单位 （默认 0 ）\n\t * @property {Boolean}\t\t\tlazyLoad\t\t\t是否懒加载，仅微信小程序、App、百度小程序、字节跳动小程序有效 （默认 true ）\n\t * @property {Boolean}\t\t\tshowMenuByLongpress\t是否开启长按图片显示识别小程序码菜单，仅微信小程序有效 （默认 true ）\n\t * @property {String}\t\t\tloadingIcon \t\t加载中的图标，或者小图片 （默认 'photo' ）\n\t * @property {String}\t\t\terrorIcon \t\t\t加载失败的图标，或者小图片 （默认 'error-circle' ）\n\t * @property {Boolean}\t\t\tshowLoading \t\t是否显示加载中的图标或者自定义的slot （默认 true ）\n\t * @property {Boolean}\t\t\tshowError \t\t\t是否显示加载错误的图标或者自定义的slot （默认 true ）\n\t * @property {Boolean}\t\t\tfade \t\t\t\t是否需要淡入效果 （默认 true ）\n\t * @property {Boolean}\t\t\twebp \t\t\t\t只支持网络资源，只对微信小程序有效 （默认 false ）\n\t * @property {String | Number}\tduration \t\t\t搭配fade参数的过渡时间，单位ms （默认 500 ）\n\t * @property {String}\t\t\tbgColor \t\t\t背景颜色，用于深色页面加载图片时，为了和背景色融合  (默认 '#f3f4f6' )\n\t * @property {Object}\t\t\tcustomStyle  \t\t定义需要用到的外部样式\n\t * @event {Function}\tclick\t点击图片时触发\n\t * @event {Function}\terror\t图片加载失败时触发\n\t * @event {Function} load 图片加载成功时触发\n\t * @example <u-image width=\"100%\" height=\"300px\" :src=\"src\"></u-image>\n\t */\n\texport default {\n\t\tname: 'u-image',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 图片是否加载错误，如果是，则显示错误占位图\n\t\t\t\tisError: false,\n\t\t\t\t// 初始化组件时，默认为加载中状态\n\t\t\t\tloading: true,\n\t\t\t\t// 不透明度，为了实现淡入淡出的效果\n\t\t\t\topacity: 1,\n\t\t\t\t// 过渡时间，因为props的值无法修改，故需要一个中间值\n\t\t\t\tdurationTime: this.duration,\n\t\t\t\t// 图片加载完成时，去掉背景颜色，因为如果是png图片，就会显示灰色的背景\n\t\t\t\tbackgroundStyle: {},\n\t\t\t\t// 用于fade模式的控制组件显示与否\n\t\t\t\tshow: false\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\tsrc: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tif (!n) {\n\t\t\t\t\t\t// 如果传入null或者''，或者false，或者undefined，标记为错误状态\n\t\t\t\t\t\tthis.isError = true\n\t\t\t\t\t\tthis.loading = false\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.isError = false\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\twrapStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\t// 如果是显示圆形，设置一个很多的半径值即可\n\t\t\t\tstyle.borderRadius = this.shape == 'circle' ? '10000px' : this.$u.addUnit(this.radius)\n\t\t\t\t// 如果设置圆角，必须要有hidden，否则可能圆角无效\n\t\t\t\tstyle.overflow = this.borderRadius > 0 ? 'hidden' : 'visible'\n\t\t\t\t// if (this.fade) {\n\t\t\t\t// \tstyle.opacity = this.opacity\n\t\t\t\t// \t// nvue下，这几个属性必须要分开写\n\t\t\t\t// \tstyle.transitionDuration = `${this.durationTime}ms`\n\t\t\t\t// \tstyle.transitionTimingFunction = 'ease-in-out'\n\t\t\t\t// \tstyle.transitionProperty = 'opacity'\n\t\t\t\t// }\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\n\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.show = true\n\t\t},\n\t\tmethods: {\n\t\t\t// 点击图片\n\t\t\tonClick() {\n\t\t\t\tthis.$emit('click')\n\t\t\t},\n\t\t\t// 图片加载失败\n\t\t\tonErrorHandler(err) {\n\t\t\t\tthis.loading = false\n\t\t\t\tthis.isError = true\n\t\t\t\tthis.$emit('error', err)\n\t\t\t},\n\t\t\t// 图片加载完成，标记loading结束\n\t\t\tonLoadHandler() {\n\t\t\t\tthis.loading = false\n\t\t\t\tthis.isError = false\n\t\t\t\tthis.$emit('load')\n\t\t\t\tthis.removeBgColor()\n\t\t\t\t// 如果不需要动画效果，就不执行下方代码，同时移除加载时的背景颜色\n\t\t\t\t// 否则无需fade效果时，png图片依然能看到下方的背景色\n\t\t\t\t// if (!this.fade) return this.removeBgColor();\n\t\t\t\t// // 原来opacity为1(不透明，是为了显示占位图)，改成0(透明，意味着该元素显示的是背景颜色，默认的灰色)，再改成1，是为了获得过渡效果\n\t\t\t\t// this.opacity = 0;\n\t\t\t\t// // 这里设置为0，是为了图片展示到背景全透明这个过程时间为0，延时之后延时之后重新设置为duration，是为了获得背景透明(灰色)\n\t\t\t\t// // 到图片展示的过程中的淡入效果\n\t\t\t\t// this.durationTime = 0;\n\t\t\t\t// // 延时50ms，否则在浏览器H5，过渡效果无效\n\t\t\t\t// setTimeout(() => {\n\t\t\t\t// \tthis.durationTime = this.duration;\n\t\t\t\t// \tthis.opacity = 1;\n\t\t\t\t// \tsetTimeout(() => {\n\t\t\t\t// \t\tthis.removeBgColor();\n\t\t\t\t// \t}, this.durationTime);\n\t\t\t\t// }, 50);\n\t\t\t},\n\t\t\t// 移除图片的背景色\n\t\t\tremoveBgColor() {\n\t\t\t\t// 淡入动画过渡完成后，将背景设置为透明色，否则png图片会看到灰色的背景\n\t\t\t\tthis.backgroundStyle = {\n\t\t\t\t\tbackgroundColor: 'transparent'\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import '../../libs/css/components.scss';\n\n\t$u-image-error-top:0px !default;\n\t$u-image-error-left:0px !default;\n\t$u-image-error-width:100% !default;\n\t$u-image-error-hight:100% !default;\n\t$u-image-error-background-color:$u-bg-color !default;\n\t$u-image-error-color:$u-tips-color !default;\n\t$u-image-error-font-size: 46rpx !default;\n\n\t.u-image {\n\t\tposition: relative;\n\t\ttransition: opacity 0.5s ease-in-out;\n\n\t\t&__loading,\n\t\t&__error {\n\t\t\tposition: absolute;\n\t\t\ttop: $u-image-error-top;\n\t\t\tleft: $u-image-error-left;\n\t\t\twidth: $u-image-error-width;\n\t\t\theight: $u-image-error-hight;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground-color: $u-image-error-background-color;\n\t\t\tcolor: $u-image-error-color;\n\t\t\tfont-size: $u-image-error-font-size;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-image.vue?vue&type=style&index=0&id=042b391e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-image.vue?vue&type=style&index=0&id=042b391e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041066835\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}