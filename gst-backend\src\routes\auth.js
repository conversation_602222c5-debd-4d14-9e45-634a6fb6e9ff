const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const { User } = require('../models');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 登录限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 限制每个IP 15分钟内最多5次登录尝试
  message: {
    error: '登录尝试次数过多，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 生成JWT Token
const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      role: user.role
    },
    process.env.JWT_SECRET || 'your-secret-key',
    {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    }
  );
};

// 用户登录
router.post('/login', 
  loginLimiter,
  [
    body('identifier')
      .notEmpty()
      .withMessage('用户名/邮箱/手机号不能为空'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码至少6位')
  ],
  asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { identifier, password } = req.body;

    try {
      // 查找用户并验证密码
      const user = await User.findByCredentials(identifier, password);
      
      // 更新最后登录时间
      await user.updateLastLogin();

      // 生成token
      const token = generateToken(user);

      // 记录登录日志
      logger.info(`用户登录成功: ${user.username} (${user.id})`);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: user.toJSON(),
          token
        }
      });
    } catch (error) {
      logger.warn(`登录失败: ${identifier} - ${error.message}`);
      res.status(401).json({
        success: false,
        message: error.message
      });
    }
  })
);

// 用户注册
router.post('/register',
  [
    body('username')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .isAlphanumeric()
      .withMessage('用户名只能包含字母和数字'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码至少6位'),
    body('email')
      .isEmail()
      .withMessage('邮箱格式不正确'),
    body('realName')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('真实姓名长度必须在2-50个字符之间'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确')
  ],
  asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, password, email, realName, phone } = req.body;

    try {
      // 检查用户名是否已存在
      const existingUser = await User.findOne({
        where: {
          [User.sequelize.Op.or]: [
            { username },
            { email },
            ...(phone ? [{ phone }] : [])
          ]
        }
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: '用户名、邮箱或手机号已存在'
        });
      }

      // 创建用户
      const user = await User.create({
        username,
        password,
        email,
        realName,
        phone,
        role: 'student' // 默认角色为学生
      });

      // 生成token
      const token = generateToken(user);

      // 记录注册日志
      logger.info(`用户注册成功: ${user.username} (${user.id})`);

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user: user.toJSON(),
          token
        }
      });
    } catch (error) {
      logger.error(`用户注册失败: ${error.message}`);
      res.status(500).json({
        success: false,
        message: '注册失败，请稍后重试'
      });
    }
  })
);

// 获取当前用户信息
router.get('/me', 
  require('../middleware/auth').auth,
  asyncHandler(async (req, res) => {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        user: user.toJSON()
      }
    });
  })
);

// 刷新Token
router.post('/refresh',
  require('../middleware/auth').auth,
  asyncHandler(async (req, res) => {
    const user = await User.findByPk(req.user.id);
    
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用'
      });
    }

    const token = generateToken(user);

    res.json({
      success: true,
      message: 'Token刷新成功',
      data: {
        token
      }
    });
  })
);

// 微信小程序登录
router.post('/wxlogin', async (req, res) => {
  try {
    const { code, iv, encryptedData, type } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '登录code不能为空'
      });
    }

    // 模拟微信登录成功
    const mockUser = {
      id: 576,
      openid: 'mock_openid_' + Date.now(),
      unionid: 'mock_unionid_' + Date.now(),
      nickname: '日语学习者',
      avatar: '/static/imgs/placeholder.svg',
      gender: 1,
      city: '北京',
      province: '北京',
      country: '中国',
      language: 'zh_CN'
    };

    // 生成JWT token
    const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvd3d3Lmpwd29ybGQuY25cL2FwaVwvYXV0aFwvd3hsb2dpbiIsImlhdCI6MTc1MzY5MDEzOCwiZXhwIjoyMTEzNjkwMTM4LCJuYmYiOjE3NTM2OTAxMzgsImp0aSI6Im1MeXRUYzN1TE5Gcm5aUmoiLCJzdWIiOjU3NiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.UcGRDnzr2SnX4aLIi1NKLv3O_UeRBMA3Tb5l8cb1rEY';

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: mockUser,
        token: token,
        expires_in: 7200 // 2小时
      }
    });

  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: error.message
    });
  }
});

module.exports = router;
