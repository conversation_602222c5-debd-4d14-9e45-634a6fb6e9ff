const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'gst_japanese_training.sqlite');

console.log('📍 数据库文件路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 连接到SQLite数据库成功');
});

// 查询categories表结构
db.all("PRAGMA table_info(categories)", [], (err, rows) => {
  if (err) {
    console.error('❌ 查询categories表结构失败:', err.message);
  } else {
    console.log('\n📊 categories表结构:');
    rows.forEach((row) => {
      console.log(`- ${row.name}: ${row.type} ${row.notnull ? 'NOT NULL' : 'NULL'} ${row.dflt_value ? `DEFAULT ${row.dflt_value}` : ''}`);
    });
  }
  
  // 查询外键约束
  db.all("PRAGMA foreign_key_list(categories)", [], (err, fkRows) => {
    if (err) {
      console.error('❌ 查询外键约束失败:', err.message);
    } else {
      console.log('\n🔗 categories表外键约束:');
      if (fkRows.length === 0) {
        console.log('- 无外键约束');
      } else {
        fkRows.forEach((fk) => {
          console.log(`- ${fk.from} -> ${fk.table}.${fk.to} (${fk.on_delete})`);
        });
      }
    }
    
    // 查询users表是否存在
    db.all("SELECT COUNT(*) as count FROM users", [], (err, userRows) => {
      if (err) {
        console.error('❌ 查询users表失败:', err.message);
      } else {
        console.log(`\n👥 users表记录数: ${userRows[0].count}`);
      }
      
      // 查询用户ID=1是否存在
      db.all("SELECT id, username FROM users WHERE id = 1", [], (err, user1Rows) => {
        if (err) {
          console.error('❌ 查询用户ID=1失败:', err.message);
        } else {
          if (user1Rows.length > 0) {
            console.log(`✅ 用户ID=1存在: ${user1Rows[0].username}`);
          } else {
            console.log('❌ 用户ID=1不存在');
          }
        }
        
        // 关闭数据库连接
        db.close((err) => {
          if (err) {
            console.error('❌ 关闭数据库失败:', err.message);
          } else {
            console.log('\n✅ 数据库连接已关闭');
          }
        });
      });
    });
  });
});
