const Vod20170321 = require('@alicloud/vod20170321');
const OpenApi = require('@alicloud/openapi-client');
const fs = require('fs');
const path = require('path');

class AlicloudVodService {
  constructor() {
    // 阿里云配置 - 请替换为您的实际配置
    this.config = new OpenApi.Config({
      accessKeyId: process.env.ALICLOUD_ACCESS_KEY_ID || 'your-access-key-id',
      accessKeySecret: process.env.ALICLOUD_ACCESS_KEY_SECRET || 'your-access-key-secret',
      endpoint: 'vod.cn-shanghai.aliyuncs.com',
      regionId: 'cn-shanghai'
    });
    
    this.client = new Vod20170321.default(this.config);
  }

  /**
   * 获取上传凭证和地址
   */
  async getUploadAuth(title, fileName, fileSize) {
    try {
      const request = new Vod20170321.CreateUploadVideoRequest({
        title: title,
        fileName: fileName,
        fileSize: fileSize,
        description: `课程单元媒体文件: ${title}`,
        tags: 'GST日语培训,课程单元',
        coverURL: '', // 可选：封面图片URL
        cateId: 0, // 可选：分类ID
        templateGroupId: '', // 可选：转码模板组ID
        workflowId: '', // 可选：工作流ID
        storageLocation: '', // 可选：存储地址
        appId: 'app-1000000' // 可选：应用ID
      });

      const response = await this.client.createUploadVideo(request);
      
      return {
        success: true,
        data: {
          videoId: response.body.videoId,
          uploadAddress: response.body.uploadAddress,
          uploadAuth: response.body.uploadAuth,
          requestId: response.body.requestId
        }
      };
    } catch (error) {
      console.error('获取上传凭证失败:', error);
      return {
        success: false,
        message: error.message || '获取上传凭证失败'
      };
    }
  }

  /**
   * 刷新上传凭证
   */
  async refreshUploadAuth(videoId) {
    try {
      const request = new Vod20170321.RefreshUploadVideoRequest({
        videoId: videoId
      });

      const response = await this.client.refreshUploadVideo(request);
      
      return {
        success: true,
        data: {
          uploadAddress: response.body.uploadAddress,
          uploadAuth: response.body.uploadAuth,
          requestId: response.body.requestId
        }
      };
    } catch (error) {
      console.error('刷新上传凭证失败:', error);
      return {
        success: false,
        message: error.message || '刷新上传凭证失败'
      };
    }
  }

  /**
   * 获取视频播放信息
   */
  async getPlayInfo(videoId) {
    try {
      const request = new Vod20170321.GetPlayInfoRequest({
        videoId: videoId,
        authTimeout: 3600, // 播放凭证过期时间（秒）
        formats: 'mp4,m3u8', // 播放格式
        definition: 'FD,LD,SD,HD,OD,2K,4K', // 清晰度
        resultType: 'Single', // 返回类型
        streamType: 'video' // 流类型
      });

      const response = await this.client.getPlayInfo(request);
      
      return {
        success: true,
        data: {
          videoBase: response.body.videoBase,
          playInfoList: response.body.playInfoList,
          requestId: response.body.requestId
        }
      };
    } catch (error) {
      console.error('获取播放信息失败:', error);
      return {
        success: false,
        message: error.message || '获取播放信息失败'
      };
    }
  }

  /**
   * 获取视频信息
   */
  async getVideoInfo(videoId) {
    try {
      const request = new Vod20170321.GetVideoInfoRequest({
        videoId: videoId
      });

      const response = await this.client.getVideoInfo(request);
      
      return {
        success: true,
        data: {
          video: response.body.video,
          ai: response.body.ai,
          requestId: response.body.requestId
        }
      };
    } catch (error) {
      console.error('获取视频信息失败:', error);
      return {
        success: false,
        message: error.message || '获取视频信息失败'
      };
    }
  }

  /**
   * 删除视频
   */
  async deleteVideo(videoIds) {
    try {
      const request = new Vod20170321.DeleteVideoRequest({
        videoIds: Array.isArray(videoIds) ? videoIds.join(',') : videoIds
      });

      const response = await this.client.deleteVideo(request);
      
      return {
        success: true,
        data: {
          requestId: response.body.requestId
        }
      };
    } catch (error) {
      console.error('删除视频失败:', error);
      return {
        success: false,
        message: error.message || '删除视频失败'
      };
    }
  }

  /**
   * 批量获取上传凭证（用于批量上传）
   */
  async batchGetUploadAuth(videos) {
    const results = [];
    
    for (const video of videos) {
      const result = await this.getUploadAuth(
        video.title,
        video.fileName,
        video.fileSize
      );
      
      results.push({
        ...result,
        originalFile: video.originalFile,
        index: video.index
      });
      
      // 添加延迟避免API限流
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return results;
  }

  /**
   * 根据文件路径获取文件信息
   */
  getFileInfo(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const fileSize = stats.size;
      
      return {
        fileName,
        fileSize,
        exists: true
      };
    } catch (error) {
      return {
        fileName: path.basename(filePath),
        fileSize: 0,
        exists: false,
        error: error.message
      };
    }
  }
}

module.exports = AlicloudVodService;
