const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');

// 小程序首页数据
router.get('/home', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        banners: [
          {
            id: 1,
            title: '新概念日语课程上线',
            image: '/images/banners/new-concept-banner.jpg',
            link: '/courses/1'
          },
          {
            id: 2,
            title: '加入学习小组',
            image: '/images/banners/group-banner.jpg',
            link: '/groups'
          }
        ],
        categories: [
          {
            id: 1,
            name: '新概念日语',
            description: '从零开始学习日语，系统掌握基础知识',
            image: '/images/categories/new-concept.jpg'
          },
          {
            id: 2,
            name: '日语语法',
            description: '系统学习日语语法规则和应用',
            image: '/images/categories/grammar.jpg'
          },
          {
            id: 3,
            name: '日语词汇',
            description: '扩充日语词汇量，提高表达能力',
            image: '/images/categories/vocabulary.jpg'
          }
        ],
        recommendedCourses: [
          {
            id: 1,
            title: '新概念日语第一册',
            description: '适合零基础学员，从五十音图开始，系统学习日语基础知识',
            level: 'N5',
            duration: 1200,
            price: 299.00,
            picture: '/images/courses/new-concept-1.jpg'
          },
          {
            id: 2,
            title: '新概念日语第二册',
            description: '进阶课程，深入学习日语语法和表达方式',
            level: 'N4',
            duration: 1500,
            price: 399.00,
            picture: '/images/courses/new-concept-2.jpg'
          }
        ],
        popularGroups: [
          {
            id: 1,
            name: '新概念日语学习小组',
            description: '一起学习新概念日语，从零基础到入门',
            level: 'N5',
            maxMembers: 50,
            currentMembers: 23
          },
          {
            id: 2,
            name: 'N5语法突破小组',
            description: '专注N5语法学习，系统掌握基础语法',
            level: 'N5',
            maxMembers: 30,
            currentMembers: 18
          }
        ],
        latestCourses: [
          {
            id: 3,
            title: 'N5基础语法精讲',
            description: 'N5级别语法点详细讲解，配合大量例句和练习',
            level: 'N5',
            duration: 800,
            price: 199.00,
            picture: '/images/courses/n5-grammar.jpg'
          }
        ],
        notice: {
          content: '欢迎来到GST日语学习小程序！',
          type: 'info'
        }
      }
    });

  } catch (error) {
    console.error('获取小程序首页数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取首页数据失败'
    });
  }
});

// 获取课程列表 - 匹配小程序数据结构
router.get('/courses', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      level,
      keyword
    } = req.query;

    // 模拟课程列表数据，匹配小程序期望的数据结构
    const courses = [
      {
        id: 1,
        title: '新概念日语第一册',
        thumb: '/images/courses/new-concept-1.jpg',
        picture: '/images/courses/new-concept-1.jpg',
        Cost: 299,
        price: 299,
        content: '<p>适合零基础学员，从五十音图开始，系统学习日语基础知识。包含五十音图、基础语法、日常会话等内容。</p>',
        level: 'N5',
        count: 20, // 课时数
        viewnum: 1250, // 学习人数
        is_buy: 0, // 是否已购买
        is_collect: 0, // 是否已收藏
        media: 'video',
        li: [ // 课程单元列表
          {
            id: 1,
            title: '第1课：五十音图（あ行）',
            url: 'https://example.com/video1.mp4',
            is_pay: 0,
            is_sk: 1 // 是否可试看
          },
          {
            id: 2,
            title: '第2课：五十音图（か行）',
            url: 'https://example.com/video2.mp4',
            is_pay: 0,
            is_sk: 1
          },
          {
            id: 3,
            title: '第3课：五十音图（さ行）',
            url: 'https://example.com/video3.mp4',
            is_pay: 1,
            is_sk: 0
          }
        ]
      },
      {
        id: 2,
        title: '新概念日语第二册',
        thumb: '/images/courses/new-concept-2.jpg',
        picture: '/images/courses/new-concept-2.jpg',
        Cost: 399,
        price: 399,
        content: '<p>进阶课程，深入学习日语语法和表达方式，在第一册基础上学习更复杂的语法结构。</p>',
        level: 'N4',
        count: 25,
        viewnum: 890,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        li: [
          {
            id: 4,
            title: '第1课：动词变位基础',
            url: 'https://example.com/video4.mp4',
            is_pay: 0,
            is_sk: 1
          },
          {
            id: 5,
            title: '第2课：形容词活用',
            url: 'https://example.com/video5.mp4',
            is_pay: 1,
            is_sk: 0
          }
        ]
      },
      {
        id: 3,
        title: 'N5基础语法精讲',
        thumb: '/images/courses/n5-grammar.jpg',
        picture: '/images/courses/n5-grammar.jpg',
        Cost: 199,
        price: 199,
        content: '<p>N5级别语法点详细讲解，配合大量例句和练习，系统掌握基础语法。</p>',
        level: 'N5',
        count: 15,
        viewnum: 1560,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        li: [
          {
            id: 6,
            title: '第1课：基础句型',
            url: 'https://example.com/video6.mp4',
            is_pay: 0,
            is_sk: 1
          }
        ]
      }
    ];

    // 根据筛选条件过滤
    let filteredCourses = courses;

    if (keyword) {
      filteredCourses = filteredCourses.filter(course =>
        course.title.includes(keyword) || course.content.includes(keyword)
      );
    }

    if (level) {
      filteredCourses = filteredCourses.filter(course => course.level === level);
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedCourses = filteredCourses.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedCourses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredCourses.length,
        pages: Math.ceil(filteredCourses.length / limit)
      }
    });

  } catch (error) {
    console.error('获取课程列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程列表失败'
    });
  }
});

// 获取课程详情 - 完全匹配小程序course.vue页面需求
router.get('/courses/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { uid } = req.query; // 用户ID，用于判断购买状态

    // 模拟课程详情数据，完全匹配小程序期望的数据结构
    const courseDetails = {
      1: {
        id: 1,
        title: '新概念日语第一册',
        thumb: '/images/courses/new-concept-1.jpg',
        picture: '/images/courses/new-concept-1.jpg',
        Cost: 299, // 课程价格
        price: 299,
        content: `<p style="font-size:14px;color:#333;background-color:#fff;">适合零基础学员，从五十音图开始，系统学习日语基础知识。</p>
                 <p style="font-size:14px;color:#333;background-color:#fff;">本课程包含：</p>
                 <ul style="font-size:14px;color:#333;background-color:#fff;">
                   <li>五十音图完整学习</li>
                   <li>基础语法讲解</li>
                   <li>日常会话练习</li>
                   <li>发音纠正指导</li>
                 </ul>`,
        level: 'N5',
        count: 20, // 课时数
        viewnum: 1250, // 学习人数
        is_buy: uid ? 0 : 0, // 是否已购买，根据用户ID判断
        is_collect: 0, // 是否已收藏
        media: 'video',
        jianjie: '从零开始学习日语，系统掌握基础知识',
        li: [ // 课程单元列表
          {
            id: 1,
            title: '第1课：五十音图（あ行）',
            url: 'https://example.com/video1.mp4',
            coursename: '第1课：五十音图（あ行）',
            is_pay: 0,
            is_sk: 1, // 可试看
            freesecond: 300 // 试看时长（秒）
          },
          {
            id: 2,
            title: '第2课：五十音图（か行）',
            url: 'https://example.com/video2.mp4',
            coursename: '第2课：五十音图（か行）',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 3,
            title: '第3课：五十音图（さ行）',
            url: 'https://example.com/video3.mp4',
            coursename: '第3课：五十音图（さ行）',
            is_pay: 1, // 需要付费
            is_sk: 0,
            freesecond: 0
          },
          {
            id: 4,
            title: '第4课：五十音图（た行）',
            url: 'https://example.com/video4.mp4',
            coursename: '第4课：五十音图（た行）',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          },
          {
            id: 5,
            title: '第5课：基础问候语',
            url: 'https://example.com/video5.mp4',
            coursename: '第5课：基础问候语',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          }
        ]
      },
      2: {
        id: 2,
        title: '新概念日语第二册',
        thumb: '/images/courses/new-concept-2.jpg',
        picture: '/images/courses/new-concept-2.jpg',
        Cost: 399,
        price: 399,
        content: `<p style="font-size:14px;color:#333;background-color:#fff;">进阶课程，深入学习日语语法和表达方式。</p>
                 <p style="font-size:14px;color:#333;background-color:#fff;">在第一册基础上，学习更复杂的语法结构和表达方式，提高日语综合能力。</p>`,
        level: 'N4',
        count: 25,
        viewnum: 890,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        jianjie: '进阶课程，深入学习日语语法和表达方式',
        li: [
          {
            id: 6,
            title: '第1课：动词变位基础',
            url: 'https://example.com/video6.mp4',
            coursename: '第1课：动词变位基础',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          },
          {
            id: 7,
            title: '第2课：形容词活用',
            url: 'https://example.com/video7.mp4',
            coursename: '第2课：形容词活用',
            is_pay: 1,
            is_sk: 0,
            freesecond: 0
          }
        ]
      },
      3: {
        id: 3,
        title: 'N5基础语法精讲',
        thumb: '/images/courses/n5-grammar.jpg',
        picture: '/images/courses/n5-grammar.jpg',
        Cost: 199,
        price: 199,
        content: `<p style="font-size:14px;color:#333;background-color:#fff;">N5级别语法点详细讲解，配合大量例句和练习。</p>
                 <p style="font-size:14px;color:#333;background-color:#fff;">系统讲解N5级别的所有语法点，通过例句和练习帮助学员掌握基础语法。</p>`,
        level: 'N5',
        count: 15,
        viewnum: 1560,
        is_buy: 0,
        is_collect: 0,
        media: 'video',
        jianjie: 'N5级别语法点详细讲解，配合大量例句和练习',
        li: [
          {
            id: 8,
            title: '第1课：基础句型',
            url: 'https://example.com/video8.mp4',
            coursename: '第1课：基础句型',
            is_pay: 0,
            is_sk: 1,
            freesecond: 300
          }
        ]
      }
    };

    const course = courseDetails[id];

    if (!course) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 返回完全匹配小程序期望的数据结构
    res.json(course);

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程详情失败'
    });
  }
});

// 获取小组数据 - 匹配小程序groups/index.vue页面
router.get('/groups', async (req, res) => {
  try {
    // 返回完全匹配小程序期望的数据结构
    const groupsData = {
      // 新概念教程分类数据
      conceptTutorial: [
        {
          id: 1,
          name: '五十音图',
          icon: '🔤',
          description: '日语基础发音学习',
          courses: [
            {
              id: 1,
              title: '新概念日语第一册',
              thumb: '/images/courses/new-concept-1.jpg',
              level: 'N5',
              price: 299,
              students: 1250
            }
          ]
        },
        {
          id: 2,
          name: '基础语法',
          icon: '📝',
          description: '日语基础语法规则',
          courses: [
            {
              id: 3,
              title: 'N5基础语法精讲',
              thumb: '/images/courses/n5-grammar.jpg',
              level: 'N5',
              price: 199,
              students: 1560
            }
          ]
        },
        {
          id: 3,
          name: '词汇学习',
          icon: '📚',
          description: '扩充日语词汇量',
          courses: [
            {
              id: 4,
              title: 'N5核心词汇1000',
              thumb: '/images/courses/n5-vocabulary.jpg',
              level: 'N5',
              price: 159,
              students: 2100
            }
          ]
        },
        {
          id: 4,
          name: '听力训练',
          icon: '🎧',
          description: '提高日语听力理解',
          courses: [
            {
              id: 5,
              title: '日语听力入门',
              thumb: '/images/courses/listening-basic.jpg',
              level: 'N5',
              price: 229,
              students: 780
            }
          ]
        },
        {
          id: 5,
          name: '口语练习',
          icon: '🗣️',
          description: '练习日语口语表达',
          courses: [
            {
              id: 6,
              title: '日语口语发音纠正',
              thumb: '/images/courses/pronunciation.jpg',
              level: 'N5',
              price: 359,
              students: 650
            }
          ]
        },
        {
          id: 6,
          name: '进阶课程',
          icon: '🚀',
          description: '更高级别的日语学习',
          courses: [
            {
              id: 2,
              title: '新概念日语第二册',
              thumb: '/images/courses/new-concept-2.jpg',
              level: 'N4',
              price: 399,
              students: 890
            }
          ]
        }
      ],

      // 学习小组列表
      studyGroups: [
        {
          id: 1,
          name: '新概念日语学习小组',
          description: '一起学习新概念日语，从零基础到入门',
          level: 'N5',
          maxMembers: 50,
          currentMembers: 23,
          avatar: '/images/groups/new-concept-group.jpg',
          tags: ['新概念', '零基础', '入门'],
          teacher: {
            name: '田中老师',
            avatar: '/images/teachers/tanaka.jpg'
          },
          status: 'active'
        },
        {
          id: 2,
          name: 'N5语法突破小组',
          description: '专注N5语法学习，系统掌握基础语法',
          level: 'N5',
          maxMembers: 30,
          currentMembers: 18,
          avatar: '/images/groups/n5-grammar-group.jpg',
          tags: ['N5', '语法', '基础'],
          teacher: {
            name: '佐藤老师',
            avatar: '/images/teachers/sato.jpg'
          },
          status: 'active'
        },
        {
          id: 3,
          name: '日语口语练习小组',
          description: '每日口语练习，提高日语表达能力',
          level: 'N5',
          maxMembers: 20,
          currentMembers: 15,
          avatar: '/images/groups/speaking-group.jpg',
          tags: ['口语', '练习', '交流'],
          teacher: {
            name: '山田老师',
            avatar: '/images/teachers/yamada.jpg'
          },
          status: 'active'
        }
      ],

      // 推荐课程
      recommendedCourses: [
        {
          id: 1,
          title: '新概念日语第一册',
          thumb: '/images/courses/new-concept-1.jpg',
          level: 'N5',
          price: 299,
          originalPrice: 399,
          discount: '限时优惠',
          students: 1250,
          rating: 4.8,
          tags: ['零基础', '系统学习']
        },
        {
          id: 2,
          title: '新概念日语第二册',
          thumb: '/images/courses/new-concept-2.jpg',
          level: 'N4',
          price: 399,
          originalPrice: 499,
          discount: '新课上线',
          students: 890,
          rating: 4.7,
          tags: ['进阶', '语法强化']
        }
      ]
    };

    res.json({
      success: true,
      data: groupsData
    });

  } catch (error) {
    console.error('获取小组数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取小组数据失败'
    });
  }
});

// 获取小组详情
router.get('/groups/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { StudyGroup, User, GroupCourse, Course } = require('../models');

    const group = await StudyGroup.findByPk(id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'realName', 'avatar', 'bio']
        },
        {
          model: GroupCourse,
          as: 'groupCourses',
          include: [
            {
              model: Course,
              as: 'course',
              attributes: [
                'id', 'title', 'description', 'level', 'duration',
                'picture', 'price'
              ]
            }
          ],
          order: [['orderNum', 'ASC']]
        }
      ]
    });

    if (!group) {
      return res.status(404).json({
        success: false,
        message: '小组不存在'
      });
    }

    // 检查小组是否公开
    if (!group.isPublic) {
      return res.status(403).json({
        success: false,
        message: '小组暂不可访问'
      });
    }

    res.json({
      success: true,
      data: { group }
    });

  } catch (error) {
    console.error('获取小组详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取小组详情失败'
    });
  }
});

// 获取课程分类
router.get('/categories', async (req, res) => {
  try {
    const { Category } = require('../models');

    const categories = await Category.findAll({
      where: { status: 'active' },
      order: [['sort', 'ASC']],
      attributes: ['id', 'name', 'description', 'image']
    });

    res.json({
      success: true,
      data: { categories }
    });

  } catch (error) {
    console.error('获取课程分类失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程分类失败'
    });
  }
});

// 用户学习进度
router.get('/user/progress', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const { LearningRecord, Course, CourseUnit } = require('../models');

    // 获取用户学习记录
    const learningRecords = await LearningRecord.findAll({
      where: { userId },
      include: [
        {
          model: Course,
          as: 'course',
          attributes: ['id', 'title', 'picture', 'level']
        },
        {
          model: CourseUnit,
          as: 'unit',
          attributes: ['id', 'title', 'duration']
        }
      ],
      order: [['updatedAt', 'DESC']],
      limit: 20
    });

    // 统计学习数据
    const totalCourses = await Course.count({
      where: { status: 'published', isPublic: true }
    });

    const completedCourses = await LearningRecord.count({
      where: {
        userId,
        progress: 100
      },
      distinct: true,
      col: 'courseId'
    });

    const totalStudyTime = await LearningRecord.sum('studyTime', {
      where: { userId }
    }) || 0;

    res.json({
      success: true,
      data: {
        learningRecords,
        statistics: {
          totalCourses,
          completedCourses,
          totalStudyTime: Math.round(totalStudyTime / 60), // 转换为分钟
          completionRate: totalCourses > 0 ? Math.round((completedCourses / totalCourses) * 100) : 0
        }
      }
    });

  } catch (error) {
    console.error('获取学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习进度失败'
    });
  }
});

// 记录学习进度
router.post('/user/progress', async (req, res) => {
  try {
    const { userId, courseId, unitId, progress, studyTime } = req.body;

    if (!userId || !courseId || !unitId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    const { LearningRecord } = require('../models');

    // 更新或创建学习记录
    const [record, created] = await LearningRecord.findOrCreate({
      where: { userId, courseId, unitId },
      defaults: {
        userId,
        courseId,
        unitId,
        progress: progress || 0,
        studyTime: studyTime || 0,
        lastStudyAt: new Date()
      }
    });

    if (!created) {
      await record.update({
        progress: Math.max(record.progress, progress || 0),
        studyTime: (record.studyTime || 0) + (studyTime || 0),
        lastStudyAt: new Date()
      });
    }

    res.json({
      success: true,
      data: { record },
      message: '学习进度已更新'
    });

  } catch (error) {
    console.error('更新学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '更新学习进度失败'
    });
  }
});

// 加入小组
router.post('/groups/:id/join', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const { StudyGroup, GroupMember } = require('../models');

    // 检查小组是否存在
    const group = await StudyGroup.findByPk(id);
    if (!group) {
      return res.status(404).json({
        success: false,
        message: '小组不存在'
      });
    }

    // 检查小组是否已满
    if (group.currentMembers >= group.maxMembers) {
      return res.status(400).json({
        success: false,
        message: '小组人数已满'
      });
    }

    // 检查是否已经加入
    const existingMember = await GroupMember.findOne({
      where: { groupId: id, userId }
    });

    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: '您已经是该小组成员'
      });
    }

    // 加入小组
    await GroupMember.create({
      groupId: id,
      userId,
      role: 'member',
      status: 'active',
      joinedAt: new Date()
    });

    // 更新小组成员数
    await group.increment('currentMembers');

    res.json({
      success: true,
      message: '成功加入小组'
    });

  } catch (error) {
    console.error('加入小组失败:', error);
    res.status(500).json({
      success: false,
      message: '加入小组失败'
    });
  }
});

// 获取用户加入的小组
router.get('/user/groups', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const { GroupMember, StudyGroup, User } = require('../models');

    const userGroups = await GroupMember.findAll({
      where: { userId, status: 'active' },
      include: [
        {
          model: StudyGroup,
          as: 'group',
          include: [
            {
              model: User,
              as: 'teacher',
              attributes: ['id', 'realName', 'avatar']
            }
          ]
        }
      ],
      order: [['joinedAt', 'DESC']]
    });

    res.json({
      success: true,
      data: { groups: userGroups }
    });

  } catch (error) {
    console.error('获取用户小组失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户小组失败'
    });
  }
});

// 搜索功能
router.get('/search', async (req, res) => {
  try {
    const { keyword, type = 'all', page = 1, limit = 10 } = req.query;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }

    const { Course, StudyGroup, Category } = require('../models');
    const results = {};

    const searchCondition = {
      [Op.or]: [
        { title: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ]
    };

    // 搜索课程
    if (type === 'all' || type === 'courses') {
      const courses = await Course.findAll({
        where: {
          ...searchCondition,
          status: 'published',
          isPublic: true
        },
        include: [
          {
            model: Category,
            as: 'categoryInfo',
            attributes: ['name', 'image']
          }
        ],
        limit: parseInt(limit),
        attributes: [
          'id', 'title', 'description', 'level', 'duration',
          'picture', 'price', 'rating'
        ]
      });
      results.courses = courses;
    }

    // 搜索小组
    if (type === 'all' || type === 'groups') {
      const groups = await StudyGroup.findAll({
        where: {
          [Op.or]: [
            { name: { [Op.like]: `%${keyword}%` } },
            { description: { [Op.like]: `%${keyword}%` } }
          ],
          status: 'active',
          isPublic: true
        },
        limit: parseInt(limit),
        attributes: [
          'id', 'name', 'description', 'level', 'maxMembers',
          'currentMembers', 'avatar'
        ]
      });
      results.groups = groups;
    }

    res.json({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('搜索失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索失败'
    });
  }
});

// 获取课程单元详情 - 用于视频播放页面
router.get('/course-units/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 模拟课程单元详情数据
    const unitDetails = {
      1: {
        id: 1,
        title: '第1课：五十音图（あ行）',
        coursename: '第1课：五十音图（あ行）',
        url: 'https://example.com/video1.mp4',
        is_pay: 0,
        is_sk: 1,
        freesecond: 300,
        duration: 1800, // 总时长30分钟
        description: '学习日语基础的五十音图あ行发音，包括あ、い、う、え、お的正确发音方法。'
      },
      2: {
        id: 2,
        title: '第2课：五十音图（か行）',
        coursename: '第2课：五十音图（か行）',
        url: 'https://example.com/video2.mp4',
        is_pay: 0,
        is_sk: 1,
        freesecond: 300,
        duration: 1650,
        description: '学习日语基础的五十音图か行发音，包括か、き、く、け、こ的正确发音方法。'
      }
    };

    const unit = unitDetails[id];
    if (!unit) {
      return res.status(404).json({
        success: false,
        message: '课程单元不存在'
      });
    }

    res.json(unit);

  } catch (error) {
    console.error('获取课程单元详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程单元详情失败'
    });
  }
});

// 用户购买课程
router.post('/purchase', async (req, res) => {
  try {
    const { uid, courseId, paymentMethod } = req.body;

    if (!uid || !courseId) {
      return res.status(400).json({
        success: false,
        message: '用户ID和课程ID不能为空'
      });
    }

    // 模拟购买成功
    res.json({
      success: true,
      data: {
        orderId: `ORDER_${Date.now()}`,
        courseId,
        uid,
        status: 'paid',
        paymentMethod,
        amount: 299,
        purchaseTime: new Date()
      },
      message: '购买成功'
    });

  } catch (error) {
    console.error('购买课程失败:', error);
    res.status(500).json({
      success: false,
      message: '购买课程失败'
    });
  }
});

// 用户收藏/取消收藏课程
router.post('/favorite', async (req, res) => {
  try {
    const { uid, courseId, action } = req.body; // action: 'add' 或 'remove'

    if (!uid || !courseId || !action) {
      return res.status(400).json({
        success: false,
        message: '参数不完整'
      });
    }

    const message = action === 'add' ? '收藏成功' : '取消收藏成功';

    res.json({
      success: true,
      data: {
        uid,
        courseId,
        action,
        timestamp: new Date()
      },
      message
    });

  } catch (error) {
    console.error('收藏操作失败:', error);
    res.status(500).json({
      success: false,
      message: '收藏操作失败'
    });
  }
});

// 获取用户学习记录
router.get('/learning-records', async (req, res) => {
  try {
    const { uid, courseId } = req.query;

    if (!uid) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    // 模拟学习记录数据
    const learningRecords = [
      {
        id: 1,
        uid,
        courseId: 1,
        unitId: 1,
        progress: 100,
        watchTime: 1800,
        lastWatchTime: new Date(),
        completed: true
      },
      {
        id: 2,
        uid,
        courseId: 1,
        unitId: 2,
        progress: 60,
        watchTime: 990,
        lastWatchTime: new Date(),
        completed: false
      }
    ];

    let filteredRecords = learningRecords;
    if (courseId) {
      filteredRecords = learningRecords.filter(record =>
        record.courseId === parseInt(courseId)
      );
    }

    res.json({
      success: true,
      data: filteredRecords
    });

  } catch (error) {
    console.error('获取学习记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习记录失败'
    });
  }
});

// 更新学习进度
router.post('/learning-progress', async (req, res) => {
  try {
    const { uid, courseId, unitId, progress, watchTime } = req.body;

    if (!uid || !courseId || !unitId) {
      return res.status(400).json({
        success: false,
        message: '必要参数不能为空'
      });
    }

    // 模拟更新学习进度
    res.json({
      success: true,
      data: {
        uid,
        courseId,
        unitId,
        progress: progress || 0,
        watchTime: watchTime || 0,
        updateTime: new Date()
      },
      message: '学习进度更新成功'
    });

  } catch (error) {
    console.error('更新学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '更新学习进度失败'
    });
  }
});

module.exports = router;
