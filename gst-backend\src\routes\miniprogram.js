const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');

// 小程序首页数据
router.get('/home', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        banners: [
          {
            id: 1,
            title: '新概念日语课程上线',
            image: '/images/banners/new-concept-banner.jpg',
            link: '/courses/1'
          },
          {
            id: 2,
            title: '加入学习小组',
            image: '/images/banners/group-banner.jpg',
            link: '/groups'
          }
        ],
        categories: [
          {
            id: 1,
            name: '新概念日语',
            description: '从零开始学习日语，系统掌握基础知识',
            image: '/images/categories/new-concept.jpg'
          },
          {
            id: 2,
            name: '日语语法',
            description: '系统学习日语语法规则和应用',
            image: '/images/categories/grammar.jpg'
          },
          {
            id: 3,
            name: '日语词汇',
            description: '扩充日语词汇量，提高表达能力',
            image: '/images/categories/vocabulary.jpg'
          }
        ],
        recommendedCourses: [
          {
            id: 1,
            title: '新概念日语第一册',
            description: '适合零基础学员，从五十音图开始，系统学习日语基础知识',
            level: 'N5',
            duration: 1200,
            price: 299.00,
            picture: '/images/courses/new-concept-1.jpg'
          },
          {
            id: 2,
            title: '新概念日语第二册',
            description: '进阶课程，深入学习日语语法和表达方式',
            level: 'N4',
            duration: 1500,
            price: 399.00,
            picture: '/images/courses/new-concept-2.jpg'
          }
        ],
        popularGroups: [
          {
            id: 1,
            name: '新概念日语学习小组',
            description: '一起学习新概念日语，从零基础到入门',
            level: 'N5',
            maxMembers: 50,
            currentMembers: 23
          },
          {
            id: 2,
            name: 'N5语法突破小组',
            description: '专注N5语法学习，系统掌握基础语法',
            level: 'N5',
            maxMembers: 30,
            currentMembers: 18
          }
        ],
        latestCourses: [
          {
            id: 3,
            title: 'N5基础语法精讲',
            description: 'N5级别语法点详细讲解，配合大量例句和练习',
            level: 'N5',
            duration: 800,
            price: 199.00,
            picture: '/images/courses/n5-grammar.jpg'
          }
        ],
        notice: {
          content: '欢迎来到GST日语学习小程序！',
          type: 'info'
        }
      }
    });

  } catch (error) {
    console.error('获取小程序首页数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取首页数据失败'
    });
  }
});

// 获取课程列表
router.get('/courses', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      category, 
      level, 
      keyword,
      sort = 'latest' 
    } = req.query;

    const { Course, Category, CourseUnit } = require('../models');
    
    const whereCondition = {
      status: 'published',
      isPublic: true
    };

    // 分类筛选
    if (category) {
      whereCondition.categoryId = category;
    }

    // 级别筛选
    if (level) {
      whereCondition.level = level;
    }

    // 关键词搜索
    if (keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 排序条件
    let orderCondition;
    switch (sort) {
      case 'popular':
        orderCondition = [['studentCount', 'DESC']];
        break;
      case 'rating':
        orderCondition = [['rating', 'DESC']];
        break;
      case 'price_low':
        orderCondition = [['price', 'ASC']];
        break;
      case 'price_high':
        orderCondition = [['price', 'DESC']];
        break;
      default:
        orderCondition = [['createdAt', 'DESC']];
    }

    const offset = (page - 1) * limit;

    const { count, rows: courses } = await Course.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Category,
          as: 'categoryInfo',
          attributes: ['name', 'image']
        }
      ],
      order: orderCondition,
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        'id', 'title', 'description', 'level', 'duration',
        'difficulty', 'price', 'picture', 'rating', 'studentCount',
        'createdAt'
      ]
    });

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取课程列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程列表失败'
    });
  }
});

// 获取课程详情
router.get('/courses/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { Course, Category, CourseUnit, User } = require('../models');

    const course = await Course.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'categoryInfo',
          attributes: ['name', 'image']
        },
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'realName', 'avatar']
        },
        {
          model: CourseUnit,
          as: 'units',
          where: { status: 'published' },
          required: false,
          order: [['orderNum', 'ASC']],
          attributes: [
            'id', 'title', 'description', 'duration', 'orderNum',
            'type', 'isFree', 'videoId', 'playId'
          ]
        }
      ]
    });

    if (!course) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      });
    }

    // 检查课程是否公开
    if (!course.isPublic || course.status !== 'published') {
      return res.status(403).json({
        success: false,
        message: '课程暂不可访问'
      });
    }

    res.json({
      success: true,
      data: { course }
    });

  } catch (error) {
    console.error('获取课程详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程详情失败'
    });
  }
});

// 获取小组列表
router.get('/groups', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      level, 
      keyword,
      status = 'active'
    } = req.query;

    const { StudyGroup, User } = require('../models');
    
    const whereCondition = {
      status,
      isPublic: true
    };

    // 级别筛选
    if (level) {
      whereCondition.level = level;
    }

    // 关键词搜索
    if (keyword) {
      whereCondition[Op.or] = [
        { name: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ];
    }

    const offset = (page - 1) * limit;

    const { count, rows: groups } = await StudyGroup.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'realName', 'avatar']
        }
      ],
      order: [['currentMembers', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        'id', 'name', 'description', 'level', 'maxMembers',
        'currentMembers', 'avatar', 'tags', 'startDate', 'endDate'
      ]
    });

    res.json({
      success: true,
      data: {
        groups,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取小组列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取小组列表失败'
    });
  }
});

// 获取小组详情
router.get('/groups/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { StudyGroup, User, GroupCourse, Course } = require('../models');

    const group = await StudyGroup.findByPk(id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'realName', 'avatar', 'bio']
        },
        {
          model: GroupCourse,
          as: 'groupCourses',
          include: [
            {
              model: Course,
              as: 'course',
              attributes: [
                'id', 'title', 'description', 'level', 'duration',
                'picture', 'price'
              ]
            }
          ],
          order: [['orderNum', 'ASC']]
        }
      ]
    });

    if (!group) {
      return res.status(404).json({
        success: false,
        message: '小组不存在'
      });
    }

    // 检查小组是否公开
    if (!group.isPublic) {
      return res.status(403).json({
        success: false,
        message: '小组暂不可访问'
      });
    }

    res.json({
      success: true,
      data: { group }
    });

  } catch (error) {
    console.error('获取小组详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取小组详情失败'
    });
  }
});

// 获取课程分类
router.get('/categories', async (req, res) => {
  try {
    const { Category } = require('../models');

    const categories = await Category.findAll({
      where: { status: 'active' },
      order: [['sort', 'ASC']],
      attributes: ['id', 'name', 'description', 'image']
    });

    res.json({
      success: true,
      data: { categories }
    });

  } catch (error) {
    console.error('获取课程分类失败:', error);
    res.status(500).json({
      success: false,
      message: '获取课程分类失败'
    });
  }
});

// 用户学习进度
router.get('/user/progress', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const { LearningRecord, Course, CourseUnit } = require('../models');

    // 获取用户学习记录
    const learningRecords = await LearningRecord.findAll({
      where: { userId },
      include: [
        {
          model: Course,
          as: 'course',
          attributes: ['id', 'title', 'picture', 'level']
        },
        {
          model: CourseUnit,
          as: 'unit',
          attributes: ['id', 'title', 'duration']
        }
      ],
      order: [['updatedAt', 'DESC']],
      limit: 20
    });

    // 统计学习数据
    const totalCourses = await Course.count({
      where: { status: 'published', isPublic: true }
    });

    const completedCourses = await LearningRecord.count({
      where: {
        userId,
        progress: 100
      },
      distinct: true,
      col: 'courseId'
    });

    const totalStudyTime = await LearningRecord.sum('studyTime', {
      where: { userId }
    }) || 0;

    res.json({
      success: true,
      data: {
        learningRecords,
        statistics: {
          totalCourses,
          completedCourses,
          totalStudyTime: Math.round(totalStudyTime / 60), // 转换为分钟
          completionRate: totalCourses > 0 ? Math.round((completedCourses / totalCourses) * 100) : 0
        }
      }
    });

  } catch (error) {
    console.error('获取学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习进度失败'
    });
  }
});

// 记录学习进度
router.post('/user/progress', async (req, res) => {
  try {
    const { userId, courseId, unitId, progress, studyTime } = req.body;

    if (!userId || !courseId || !unitId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    const { LearningRecord } = require('../models');

    // 更新或创建学习记录
    const [record, created] = await LearningRecord.findOrCreate({
      where: { userId, courseId, unitId },
      defaults: {
        userId,
        courseId,
        unitId,
        progress: progress || 0,
        studyTime: studyTime || 0,
        lastStudyAt: new Date()
      }
    });

    if (!created) {
      await record.update({
        progress: Math.max(record.progress, progress || 0),
        studyTime: (record.studyTime || 0) + (studyTime || 0),
        lastStudyAt: new Date()
      });
    }

    res.json({
      success: true,
      data: { record },
      message: '学习进度已更新'
    });

  } catch (error) {
    console.error('更新学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '更新学习进度失败'
    });
  }
});

// 加入小组
router.post('/groups/:id/join', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const { StudyGroup, GroupMember } = require('../models');

    // 检查小组是否存在
    const group = await StudyGroup.findByPk(id);
    if (!group) {
      return res.status(404).json({
        success: false,
        message: '小组不存在'
      });
    }

    // 检查小组是否已满
    if (group.currentMembers >= group.maxMembers) {
      return res.status(400).json({
        success: false,
        message: '小组人数已满'
      });
    }

    // 检查是否已经加入
    const existingMember = await GroupMember.findOne({
      where: { groupId: id, userId }
    });

    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: '您已经是该小组成员'
      });
    }

    // 加入小组
    await GroupMember.create({
      groupId: id,
      userId,
      role: 'member',
      status: 'active',
      joinedAt: new Date()
    });

    // 更新小组成员数
    await group.increment('currentMembers');

    res.json({
      success: true,
      message: '成功加入小组'
    });

  } catch (error) {
    console.error('加入小组失败:', error);
    res.status(500).json({
      success: false,
      message: '加入小组失败'
    });
  }
});

// 获取用户加入的小组
router.get('/user/groups', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    const { GroupMember, StudyGroup, User } = require('../models');

    const userGroups = await GroupMember.findAll({
      where: { userId, status: 'active' },
      include: [
        {
          model: StudyGroup,
          as: 'group',
          include: [
            {
              model: User,
              as: 'teacher',
              attributes: ['id', 'realName', 'avatar']
            }
          ]
        }
      ],
      order: [['joinedAt', 'DESC']]
    });

    res.json({
      success: true,
      data: { groups: userGroups }
    });

  } catch (error) {
    console.error('获取用户小组失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户小组失败'
    });
  }
});

// 搜索功能
router.get('/search', async (req, res) => {
  try {
    const { keyword, type = 'all', page = 1, limit = 10 } = req.query;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }

    const { Course, StudyGroup, Category } = require('../models');
    const results = {};

    const searchCondition = {
      [Op.or]: [
        { title: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ]
    };

    // 搜索课程
    if (type === 'all' || type === 'courses') {
      const courses = await Course.findAll({
        where: {
          ...searchCondition,
          status: 'published',
          isPublic: true
        },
        include: [
          {
            model: Category,
            as: 'categoryInfo',
            attributes: ['name', 'image']
          }
        ],
        limit: parseInt(limit),
        attributes: [
          'id', 'title', 'description', 'level', 'duration',
          'picture', 'price', 'rating'
        ]
      });
      results.courses = courses;
    }

    // 搜索小组
    if (type === 'all' || type === 'groups') {
      const groups = await StudyGroup.findAll({
        where: {
          [Op.or]: [
            { name: { [Op.like]: `%${keyword}%` } },
            { description: { [Op.like]: `%${keyword}%` } }
          ],
          status: 'active',
          isPublic: true
        },
        limit: parseInt(limit),
        attributes: [
          'id', 'name', 'description', 'level', 'maxMembers',
          'currentMembers', 'avatar'
        ]
      });
      results.groups = groups;
    }

    res.json({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('搜索失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索失败'
    });
  }
});

module.exports = router;
