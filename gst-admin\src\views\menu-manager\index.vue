﻿<template>
  <div class="page-container">
    <div class="page-header">
      <h1>菜单管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加菜单
        </el-button>
      </div>
    </div>

    <!-- 菜单树表格 -->
    <el-card>
      <el-table
        :data="menuTree"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :expand-row-keys="expandedKeys"
        @expand-change="handleExpandChange"
      >
        <el-table-column prop="title" label="菜单名称" min-width="200">
          <template #default="{ row }">
            <div class="menu-title">
              <el-icon v-if="row.icon" class="menu-icon">
                <component :is="row.icon" />
              </el-icon>
              <span>{{ row.title }}</span>
              <el-tag v-if="row.type" size="small" :type="getTypeColor(row.type)">
                {{ getTypeText(row.type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" min-width="150" />
        <el-table-column prop="component" label="组件" min-width="150" show-overflow-tooltip />
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="visible" label="显示" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.visible"
              @change="toggleVisible(row)"
              :loading="row.updating"
            />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="addChild(row)">
              <el-icon><Plus /></el-icon>
              添加子菜单
            </el-button>
            <el-button link size="small" @click="editMenu(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link size="small" @click="deleteMenu(row)" class="danger-button">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑菜单对话框 -->
    <el-dialog
      :title="editingMenu ? '编辑菜单' : '添加菜单'"
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form ref="menuFormRef" :model="menuForm" :rules="menuFormRules" label-width="100px">
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="menuForm.type">
            <el-radio label="menu">菜单</el-radio>
            <el-radio label="button">按钮</el-radio>
            <el-radio label="link">外链</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="菜单名称" prop="title">
          <el-input v-model="menuForm.title" placeholder="请输入菜单名称" />
        </el-form-item>

        <el-form-item label="菜单标识" prop="name">
          <el-input v-model="menuForm.name" placeholder="请输入菜单标识，如：UserManage" />
        </el-form-item>

        <el-form-item v-if="menuForm.type !== 'button'" label="路由路径" prop="path">
          <el-input v-model="menuForm.path" placeholder="请输入路由路径，如：/user/manage" />
        </el-form-item>

        <el-form-item v-if="menuForm.type === 'menu'" label="组件路径" prop="component">
          <el-input v-model="menuForm.component" placeholder="请输入组件路径，如：views/user/index" />
        </el-form-item>

        <el-form-item v-if="menuForm.type === 'link'" label="外链地址" prop="redirect">
          <el-input v-model="menuForm.redirect" placeholder="请输入外链地址" />
        </el-form-item>

        <el-form-item label="父级菜单" prop="parentId">
          <el-tree-select
            v-model="menuForm.parentId"
            :data="menuTreeOptions"
            :props="{ label: 'title', value: 'id' }"
            placeholder="选择父级菜单（可选）"
            clearable
            check-strictly
          />
        </el-form-item>

        <el-form-item label="菜单图标" prop="icon">
          <el-input v-model="menuForm.icon" placeholder="请输入图标名称，如：User">
            <template #append>
              <el-button @click="showIconDialog = true">选择图标</el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="menuForm.sort" :min="0" />
        </el-form-item>

        <el-form-item label="是否显示" prop="visible">
          <el-switch v-model="menuForm.visible" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="menuForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="权限标识" prop="permission">
          <el-input v-model="menuForm.permission" placeholder="请输入权限标识，如：user:view" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="menuForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMenu" :loading="saving">
          {{ editingMenu ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 图标选择对话框 -->
    <el-dialog
      title="选择图标"
      v-model="showIconDialog"
      width="800px"
    >
      <div class="icon-selector">
        <div class="icon-search">
          <el-input
            v-model="iconSearch"
            placeholder="搜索图标"
            clearable
            @input="filterIcons"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="icon-grid">
          <div
            v-for="icon in filteredIcons"
            :key="icon"
            class="icon-item"
            :class="{ active: menuForm.icon === icon }"
            @click="selectIcon(icon)"
          >
            <el-icon>
              <component :is="icon" />
            </el-icon>
            <span>{{ icon }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showIconDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmIcon">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, post, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Edit,
  Delete,
  Search
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const showIconDialog = ref(false)
const menuTree = ref([])
const expandedKeys = ref([])
const editingMenu = ref(null)
const iconSearch = ref('')

// 可用图标列表
const availableIcons = [
  'User', 'UserFilled', 'Users', 'Avatar', 'House', 'HomeFilled',
  'Document', 'DocumentCopy', 'Folder', 'FolderOpened', 'Files',
  'Reading', 'EditPen', 'Edit', 'Delete', 'Plus', 'Minus',
  'Setting', 'Tools', 'Operation', 'Menu', 'List', 'Grid',
  'Picture', 'Camera', 'VideoCamera', 'Headset', 'Microphone',
  'Lock', 'Unlock', 'Key', 'View', 'Hide', 'Search',
  'Refresh', 'Download', 'Upload', 'Share', 'Link', 'Unlink',
  'Bell', 'Message', 'ChatDotRound', 'Phone', 'Cellphone',
  'Monitor', 'Iphone', 'Tablet', 'Laptop', 'Desktop',
  'Star', 'StarFilled', 'Heart', 'HeartFilled', 'Like', 'Unlike',
  'Check', 'Close', 'Warning', 'InfoFilled', 'QuestionFilled',
  'CircleCheck', 'CircleClose', 'CirclePlus', 'Remove',
  'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
  'CaretTop', 'CaretBottom', 'CaretLeft', 'CaretRight',
  'Sort', 'Rank', 'TrendCharts', 'DataBoard', 'PieChart',
  'Histogram', 'LineChart', 'ScaleToOriginal', 'FullScreen',
  'Aim', 'Crop', 'Knife', 'CopyDocument', 'Timer', 'AlarmClock',
  'Calendar', 'Clock', 'Watch', 'Stopwatch', 'Flag', 'Location'
]

// 菜单表单
const menuForm = reactive({
  type: 'menu',
  title: '',
  name: '',
  path: '',
  component: '',
  redirect: '',
  parentId: null,
  icon: '',
  sort: 0,
  visible: true,
  status: 'active',
  permission: '',
  remark: ''
})

const menuFormRules = {
  title: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入菜单标识', trigger: 'blur' },
    { pattern: /^[A-Z][a-zA-Z0-9]*$/, message: '菜单标识必须以大写字母开头，只能包含字母和数字', trigger: 'blur' }
  ],
  path: [
    {
      validator: (rule, value, callback) => {
        if (menuForm.type !== 'button' && !value) {
          callback(new Error('请输入路由路径'))
        } else if (value && !value.startsWith('/')) {
          callback(new Error('路由路径必须以/开头'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  component: [
    {
      validator: (rule, value, callback) => {
        if (menuForm.type === 'menu' && !value) {
          callback(new Error('请输入组件路径'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  redirect: [
    {
      validator: (rule, value, callback) => {
        if (menuForm.type === 'link' && !value) {
          callback(new Error('请输入外链地址'))
        } else if (value && !value.startsWith('http')) {
          callback(new Error('外链地址必须以http://或https://开头'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const menuTreeOptions = computed(() => {
  return buildTreeOptions(menuTree.value)
})

const filteredIcons = computed(() => {
  if (!iconSearch.value) return availableIcons
  return availableIcons.filter(icon =>
    icon.toLowerCase().includes(iconSearch.value.toLowerCase())
  )
})

// 加载菜单树
const loadMenuTree = async () => {
  loading.value = true
  try {
    const response = await get('/api/menus/tree')
    if (response.success) {
      menuTree.value = response.data.tree || []
      expandedKeys.value = getDefaultExpandedKeys(menuTree.value)
    }
  } catch (error) {
    console.error('加载菜单树失败:', error)
    ElMessage.error('加载菜单树失败')

    // 使用模拟数据
    menuTree.value = generateMockMenuTree()
    expandedKeys.value = getDefaultExpandedKeys(menuTree.value)
  } finally {
    loading.value = false
  }
}

// 生成模拟菜单树
const generateMockMenuTree = () => {
  return [
    {
      id: 1,
      title: '仪表板',
      name: 'Dashboard',
      path: '/dashboard',
      component: 'views/dashboard/index',
      icon: 'DataBoard',
      type: 'menu',
      sort: 1,
      visible: true,
      status: 'active',
      permission: 'dashboard:view',
      children: []
    },
    {
      id: 2,
      title: '内容管理',
      name: 'Content',
      path: '/content',
      component: 'Layout',
      icon: 'Document',
      type: 'menu',
      sort: 2,
      visible: true,
      status: 'active',
      permission: 'content:view',
      children: [
        {
          id: 3,
          title: '课程管理',
          name: 'Courses',
          path: '/content/courses',
          component: 'views/courses/index',
          icon: 'Reading',
          type: 'menu',
          sort: 1,
          visible: true,
          status: 'active',
          permission: 'course:view',
          children: [
            {
              id: 4,
              title: '创建课程',
              name: 'CreateCourse',
              path: '',
              component: '',
              icon: 'Plus',
              type: 'button',
              sort: 1,
              visible: true,
              status: 'active',
              permission: 'course:create'
            }
          ]
        },
        {
          id: 5,
          title: '用户管理',
          name: 'Users',
          path: '/content/users',
          component: 'views/users/index',
          icon: 'User',
          type: 'menu',
          sort: 2,
          visible: true,
          status: 'active',
          permission: 'user:view',
          children: []
        }
      ]
    }
  ]
}

// 构建树选择器选项
const buildTreeOptions = (nodes, level = 0) => {
  return nodes.map(node => ({
    id: node.id,
    title: `${'  '.repeat(level)}${node.title}`,
    children: node.children ? buildTreeOptions(node.children, level + 1) : []
  }))
}

// 获取默认展开的键
const getDefaultExpandedKeys = (nodes) => {
  const keys = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        keys.push(node.id)
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return keys
}

// 展开变化处理
const handleExpandChange = (row, expanded) => {
  if (expanded) {
    if (!expandedKeys.value.includes(row.id)) {
      expandedKeys.value.push(row.id)
    }
  } else {
    const index = expandedKeys.value.indexOf(row.id)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    }
  }
}

// 切换显示状态
const toggleVisible = async (menu) => {
  menu.updating = true
  try {
    await put(`/api/menus/${menu.id}`, { visible: menu.visible })
    ElMessage.success('菜单状态更新成功')
  } catch (error) {
    console.error('更新菜单状态失败:', error)
    ElMessage.error('更新菜单状态失败')
    menu.visible = !menu.visible // 回滚状态
  } finally {
    menu.updating = false
  }
}

// 添加子菜单
const addChild = (parent) => {
  menuForm.parentId = parent.id
  showCreateDialog.value = true
}

// 编辑菜单
const editMenu = (menu) => {
  editingMenu.value = menu
  Object.assign(menuForm, {
    type: menu.type,
    title: menu.title,
    name: menu.name,
    path: menu.path,
    component: menu.component,
    redirect: menu.redirect,
    parentId: menu.parentId,
    icon: menu.icon,
    sort: menu.sort,
    visible: menu.visible,
    status: menu.status,
    permission: menu.permission,
    remark: menu.remark
  })
  showCreateDialog.value = true
}

// 删除菜单
const deleteMenu = async (menu) => {
  try {
    await ElMessageBox.confirm(`确定要删除菜单"${menu.title}"吗？`, '确认删除', {
      type: 'warning'
    })

    await del(`/api/menus/${menu.id}`)
    ElMessage.success('菜单删除成功')
    await loadMenuTree()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除菜单失败:', error)
      ElMessage.error('删除菜单失败')
    }
  }
}

// 保存菜单
const saveMenu = async () => {
  const menuFormRef = ref()
  try {
    await menuFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    if (editingMenu.value) {
      await put(`/api/menus/${editingMenu.value.id}`, menuForm)
      ElMessage.success('菜单更新成功')
    } else {
      await post('/api/menus', menuForm)
      ElMessage.success('菜单创建成功')
    }

    showCreateDialog.value = false
    resetForm()
    await loadMenuTree()
  } catch (error) {
    console.error('保存菜单失败:', error)
    ElMessage.error('保存菜单失败')
  } finally {
    saving.value = false
  }
}

// 选择图标
const selectIcon = (icon) => {
  menuForm.icon = icon
}

// 确认图标选择
const confirmIcon = () => {
  showIconDialog.value = false
}

// 过滤图标
const filterIcons = () => {
  // 图标过滤逻辑已在计算属性中实现
}

// 工具函数
const getTypeColor = (type) => {
  const colors = {
    menu: 'primary',
    button: 'success',
    link: 'warning'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    menu: '菜单',
    button: '按钮',
    link: '外链'
  }
  return texts[type] || type
}

const resetForm = () => {
  editingMenu.value = null
  Object.assign(menuForm, {
    type: 'menu',
    title: '',
    name: '',
    path: '',
    component: '',
    redirect: '',
    parentId: null,
    icon: '',
    sort: 0,
    visible: true,
    status: 'active',
    permission: '',
    remark: ''
  })
}

const refreshData = () => {
  loadMenuTree()
}

// 组件挂载时加载数据
onMounted(() => {
  loadMenuTree()
})
</script>

<style lang="scss" scoped>
.menu-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .menu-icon {
    color: var(--el-color-primary);
  }
}

.danger-button {
  color: var(--el-color-danger);

  &:hover {
    color: var(--el-color-danger-light-3);
  }
}

// 图标选择器样式
.icon-selector {
  .icon-search {
    margin-bottom: 20px;
  }

  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 8px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }

      &.active {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }

      .el-icon {
        font-size: 24px;
        margin-bottom: 4px;
        color: var(--el-color-primary);
      }

      span {
        font-size: 12px;
        color: var(--el-text-color-regular);
        text-align: center;
        word-break: break-all;
      }
    }
  }
}

// 表格样式优化
.el-table {
  .el-switch {
    --el-switch-on-color: var(--el-color-success);
  }
}

// 表单样式
.el-form {
  .el-radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .icon-selector {
    .icon-grid {
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 8px;

      .icon-item {
        padding: 8px 4px;

        .el-icon {
          font-size: 20px;
        }

        span {
          font-size: 10px;
        }
      }
    }
  }
}
</style>
