const express = require('express');
const { Banner, HomeConfig, Category, Course, User } = require('../models');
const { asyncHandler } = require('../utils/asyncHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 获取首页数据 - 兼容原始API格式
router.get('/index', asyncHandler(async (req, res) => {
  try {
    // 获取轮播图
    const banners = await Banner.getActiveBanners();
    
    // 获取首页配置数据
    const homeData = await HomeConfig.getHomeData();
    
    // 获取分类数据作为course1和course2
    const categories = await CourseCategory.findAll({
      where: { 
        isActive: true,
        showInHome: true 
      },
      order: [['orderNum', 'ASC'], ['createdAt', 'ASC']]
    });
    
    // 将分类分为两组
    const course1 = categories.slice(0, 10).map(cat => ({
      id: cat.id,
      name: cat.name,
      icon: cat.icon || 'https://statici.jpworld.cn/images/default.png',
      type: 1,
      jump_type: 1,
      jump_pid: cat.id,
      link: `/pages/course/list?categoryId=${cat.id}`,
      remark: cat.description
    }));
    
    const course2 = categories.slice(10, 26).map(cat => ({
      id: cat.id,
      name: cat.name,
      icon: cat.icon || 'https://statici.jpworld.cn/images/default.png',
      type: 2,
      jump_type: 1,
      jump_pid: cat.id,
      link: `/pages/course/list?categoryId=${cat.id}`,
      remark: cat.description
    }));
    
    // 格式化轮播图数据
    const banner = banners.map(b => ({
      id: b.id,
      title: b.title,
      subtitle: b.subtitle,
      thumb: b.thumb || b.image,
      image: b.image,
      link: b.link,
      link_type: b.linkType,
      target_id: b.targetId,
      view_count: b.viewCount,
      click_count: b.clickCount
    }));
    
    // 默认的top_post数据
    const top_post = homeData.top_post?.length ? homeData.top_post : [
      { id: 1, title: "外教日语", thumb: "", link: "pages/course/list", type: "course" },
      { id: 2, title: "日语模考", thumb: "", link: "pages/exam/index", type: "exam" },
      { id: 3, title: "日语实习", thumb: "", link: "pages/practice/index", type: "practice" },
      { id: 4, title: "日语研修", thumb: "", link: "pages/training/index", type: "training" },
      { id: 5, title: "人民中国杯", thumb: "", link: "pages/contest/index", type: "contest" },
      { id: 6, title: "日本研学", thumb: "", link: "pages/study/index", type: "study" },
      { id: 7, title: "考研申博", thumb: "", link: "pages/exam/graduate", type: "graduate" },
      { id: 8, title: "学习资源", thumb: "", link: "pages/resource/index", type: "resource" },
      { id: 9, title: "CATTI考试", thumb: "", link: "pages/exam/catti", type: "catti" },
      { id: 10, title: "联系我们", thumb: "", link: "/pages/about-us/about-us", type: "about" }
    ];
    
    // 默认的game_foot_post数据
    const game_foot_post = homeData.game_foot_post?.length ? homeData.game_foot_post : [
      { id: 1, title: "推荐阅读", thumb: null, type: "web", remark: null, status: 1 },
      { id: 2, title: "日语提升", thumb: null, type: "web", remark: null, status: 1 }
    ];
    
    // 默认的game_show_footer数据
    const game_show_footer = homeData.game_show_footer?.length ? homeData.game_show_footer : [
      { id: 1, title: "跟外教学日语", thumb: "https://statici.jpworld.cn/images/teacher.png" },
      { id: 2, title: "日语云课", thumb: "https://statici.jpworld.cn/images/cloud.png" },
      { id: 3, title: "日语模考", thumb: "https://statici.jpworld.cn/images/exam.png" },
      { id: 4, title: "日语实习", thumb: "https://statici.jpworld.cn/images/practice.png" },
      { id: 5, title: "日本研学", thumb: "https://statici.jpworld.cn/images/study.png" },
      { id: 6, title: "日本留学", thumb: "https://statici.jpworld.cn/images/abroad.png" },
      { id: 7, title: "日语研修", thumb: "https://statici.jpworld.cn/images/training.png" },
      { id: 8, title: "翻译大赛", thumb: "https://statici.jpworld.cn/images/translate.png" },
      { id: 9, title: "才艺大赛", thumb: "https://statici.jpworld.cn/images/talent.png" },
      { id: 10, title: "听力大赛", thumb: "https://statici.jpworld.cn/images/listening.png" },
      { id: 11, title: "语法大赛", thumb: "https://statici.jpworld.cn/images/grammar.png" },
      { id: 12, title: "写作大赛", thumb: "https://statici.jpworld.cn/images/writing.png" },
      { id: 13, title: "词汇大赛", thumb: "https://statici.jpworld.cn/images/vocabulary.png" }
    ];
    
    // 返回兼容原始API的数据格式
    res.json({
      code: 0,
      status: true,
      data: {
        banner,
        course1,
        course2,
        top_post,
        game_foot_post,
        game_show_footer
      }
    });
    
  } catch (error) {
    logger.error('获取首页数据失败:', error);
    res.status(500).json({
      code: 500,
      status: false,
      message: '获取首页数据失败',
      error: error.message
    });
  }
}));

// 轮播图点击统计
router.post('/banner/:id/click', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const banner = await Banner.findByPk(id);
  if (!banner) {
    return res.status(404).json({
      code: 404,
      status: false,
      message: '轮播图不存在'
    });
  }
  
  await banner.incrementClick();
  
  res.json({
    code: 0,
    status: true,
    message: '点击统计成功'
  });
}));

// 轮播图浏览统计
router.post('/banner/:id/view', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const banner = await Banner.findByPk(id);
  if (!banner) {
    return res.status(404).json({
      code: 404,
      status: false,
      message: '轮播图不存在'
    });
  }
  
  await banner.incrementView();
  
  res.json({
    code: 0,
    status: true,
    message: '浏览统计成功'
  });
}));

// 完全兼容原始小程序的首页API
router.get('/app-index', asyncHandler(async (req, res) => {
  try {
    // 获取轮播图
    const banners = await Banner.findAll({
      where: { status: 1 },
      order: [['orderNum', 'ASC'], ['createdAt', 'DESC']],
      limit: 10
    });

    // 获取所有启用的分类
    const allCategories = await Category.findAll({
      where: { status: 'active' },
      order: [['sort', 'ASC'], ['createdAt', 'ASC']]
    });

    // 获取首页配置数据
    const homeConfigs = await HomeConfig.findAll({
      where: { status: 'active' },
      order: [['configType', 'ASC'], ['orderNum', 'ASC']]
    });

    // 按配置类型分组
    const configsByType = {};
    homeConfigs.forEach(config => {
      const type = config.configType;
      if (!configsByType[type]) {
        configsByType[type] = [];
      }
      configsByType[type].push({
        id: config.id,
        title: config.title,
        icon: config.icon,
        thumb: config.thumb,
        link: config.link,
        jump_type: config.jumpType,
        jump_pid: config.jumpPid,
        remark: config.remark,
        status: config.status
      });
    });

    // 构建完全兼容原始格式的响应数据
    const responseData = {
      code: 0,
      status: true,
      data: {
        // 轮播图
        banner: banners.map(banner => ({
          id: banner.id,
          title: banner.title,
          thumb: banner.image,
          image: banner.image,
          link: banner.link,
          jump_type: banner.linkType === 'external' ? 2 : 1,
          jump_pid: banner.targetId,
          status: banner.status
        })),

        // 第一分类（首页主要分类，前10个）
        course1: allCategories.slice(0, 10).map(cat => ({
          id: cat.id,
          name: cat.name,
          icon: cat.image || 'https://statici.jpworld.cn/images/default.png',
          type: 1,
          jump_type: 4, // 跳转到分类页面
          jump_pid: cat.id,
          show_in_home: true,
          show_in_group: false
        })),

        // 第二分类（所有分类）
        course2: allCategories.map(cat => ({
          id: cat.id,
          name: cat.name,
          icon: cat.image || 'https://statici.jpworld.cn/images/default.png',
          type: 2,
          jump_type: 4, // 跳转到分类页面
          jump_pid: cat.id,
          show_in_home: false,
          show_in_group: false
        })),

        // 顶部功能入口
        top_post: configsByType.top_post?.length ? configsByType.top_post : [
          { id: 647, title: "外教日语", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 648, title: "日语模考", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 649, title: "日语实习", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 650, title: "日语研修", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 651, title: "人民中国杯", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 652, title: "日本研学", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 653, title: "考研申博", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 654, title: "学习资源", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 655, title: "CATTI考试", thumb: "https://statici.jpworld.cn/", link: "pages/index/index", jump_type: 1, status: 1 },
          { id: 656, title: "联系我们", thumb: "https://statici.jpworld.cn/", link: "/pages/about-us/about-us", jump_type: 1, status: 1 }
        ],

        // 底部推荐
        game_foot_post: configsByType.game_foot_post?.length ? configsByType.game_foot_post : [
          { id: 1, title: "推荐阅读", thumb: null, type: "web", remark: null, status: 1 },
          { id: 2, title: "日语提升", thumb: null, type: "web", remark: null, status: 1 }
        ],

        // 功能展示区
        game_show_footer: configsByType.game_show_footer?.length ? configsByType.game_show_footer : [
          { id: 627, title: "跟外教学日语", thumb: "https://statici.jpworld.cn/images/teacher.png", link: "/pages/teacher/index", jump_type: 1, status: 1 },
          { id: 626, title: "日语云课", thumb: "https://statici.jpworld.cn/images/cloud.png", link: "/pages/cloud/index", jump_type: 1, status: 1 },
          { id: 628, title: "日语模考", thumb: "https://statici.jpworld.cn/images/exam.png", link: "/pages/exam/index", jump_type: 1, status: 1 },
          { id: 629, title: "日语实习", thumb: "https://statici.jpworld.cn/images/practice.png", link: "/pages/practice/index", jump_type: 1, status: 1 },
          { id: 630, title: "日本研学", thumb: "https://statici.jpworld.cn/images/study.png", link: "/pages/study/index", jump_type: 1, status: 1 },
          { id: 631, title: "日本留学", thumb: "https://statici.jpworld.cn/images/abroad.png", link: "/pages/abroad/index", jump_type: 1, status: 1 },
          { id: 451, title: "日语研修", thumb: "https://statici.jpworld.cn/images/training.png", link: "/pages/training/index", jump_type: 1, status: 1 },
          { id: 554, title: "翻译大赛", thumb: "https://statici.jpworld.cn/images/20230228090116.png", link: "/pages/contest/translate", jump_type: 1, status: 1 },
          { id: 673, title: "才艺大赛", thumb: "https://statici.jpworld.cn/images/talent.png", link: "/pages/contest/talent", jump_type: 1, status: 1 },
          { id: 714, title: "听力大赛", thumb: "https://statici.jpworld.cn/images/784tg.png", link: "/pages/contest/listening", jump_type: 1, status: 1 },
          { id: 514, title: "语法大赛", thumb: "https://statici.jpworld.cn/images/grammar.png", link: "/pages/contest/grammar", jump_type: 1, status: 1 },
          { id: 448, title: "写作大赛", thumb: "https://statici.jpworld.cn/images/xiezuo.png", link: "/pages/contest/writing", jump_type: 1, status: 1 },
          { id: 449, title: "词汇大赛", thumb: "https://statici.jpworld.cn/images/vocabulary.png", link: "/pages/contest/vocabulary", jump_type: 1, status: 1 }
        ]
      }
    };

    res.json(responseData);

  } catch (error) {
    logger.error('获取首页数据失败:', error);
    res.status(500).json({
      code: -1,
      status: false,
      message: '获取首页数据失败',
      error: error.message
    });
  }
}));

// 获取分类页面数据
router.get('/category/:categoryId', asyncHandler(async (req, res) => {
  const { categoryId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  try {
    // 获取分类信息
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(404).json({
        code: -1,
        status: false,
        message: '分类不存在'
      });
    }

    // 获取分类下的课程
    const offset = (page - 1) * limit;
    const { rows: courses, count } = await Course.findAndCountAll({
      where: {
        categoryId: categoryId,
        status: 'published'
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'realName']
        }
      ],
      order: [['sortOrder', 'ASC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      code: 0,
      status: true,
      data: {
        category: {
          id: category.id,
          name: category.name,
          description: category.description,
          icon: category.icon
        },
        courses: courses.map(course => ({
          id: course.id,
          title: course.title,
          description: course.description,
          cover_image: course.coverImage,
          level: course.level,
          duration: course.duration,
          view_count: course.viewCount || 0,
          like_count: course.likeCount || 0,
          author: course.creator?.realName || '未知',
          created_at: course.createdAt
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('获取分类页面数据失败:', error);
    res.status(500).json({
      code: -1,
      status: false,
      message: '获取分类页面数据失败',
      error: error.message
    });
  }
}));

module.exports = router;
