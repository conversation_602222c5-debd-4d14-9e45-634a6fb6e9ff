const express = require('express');
const router = express.Router();

// 模拟权限数据
const permissions = [
  {
    id: 1,
    name: '仪表板',
    code: 'dashboard',
    type: 'menu',
    description: '查看系统概览',
    parentId: null,
    sort: 1,
    status: 'active',
    children: []
  },
  {
    id: 2,
    name: '内容管理',
    code: 'content',
    type: 'menu',
    description: '管理系统内容',
    parentId: null,
    sort: 2,
    status: 'active',
    children: [
      {
        id: 3,
        name: '课程管理',
        code: 'course',
        type: 'menu',
        description: '管理课程信息',
        parentId: 2,
        sort: 1,
        status: 'active',
        children: [
          {
            id: 4,
            name: '创建课程',
            code: 'course:create',
            type: 'button',
            description: '创建新课程',
            parentId: 3,
            sort: 1,
            status: 'active'
          },
          {
            id: 5,
            name: '编辑课程',
            code: 'course:edit',
            type: 'button',
            description: '编辑课程信息',
            parentId: 3,
            sort: 2,
            status: 'active'
          },
          {
            id: 6,
            name: '删除课程',
            code: 'course:delete',
            type: 'button',
            description: '删除课程',
            parentId: 3,
            sort: 3,
            status: 'active'
          }
        ]
      },
      {
        id: 7,
        name: '用户管理',
        code: 'user',
        type: 'menu',
        description: '管理系统用户',
        parentId: 2,
        sort: 2,
        status: 'active',
        children: [
          {
            id: 8,
            name: '创建用户',
            code: 'user:create',
            type: 'button',
            description: '创建新用户',
            parentId: 7,
            sort: 1,
            status: 'active'
          },
          {
            id: 9,
            name: '编辑用户',
            code: 'user:edit',
            type: 'button',
            description: '编辑用户信息',
            parentId: 7,
            sort: 2,
            status: 'active'
          },
          {
            id: 10,
            name: '删除用户',
            code: 'user:delete',
            type: 'button',
            description: '删除用户',
            parentId: 7,
            sort: 3,
            status: 'active'
          }
        ]
      }
    ]
  },
  {
    id: 11,
    name: '系统管理',
    code: 'system',
    type: 'menu',
    description: '系统设置和管理',
    parentId: null,
    sort: 3,
    status: 'active',
    children: [
      {
        id: 12,
        name: '权限管理',
        code: 'permission',
        type: 'menu',
        description: '管理角色和权限',
        parentId: 11,
        sort: 1,
        status: 'active'
      },
      {
        id: 13,
        name: '菜单管理',
        code: 'menu',
        type: 'menu',
        description: '管理系统菜单',
        parentId: 11,
        sort: 2,
        status: 'active'
      },
      {
        id: 14,
        name: '系统设置',
        code: 'settings',
        type: 'menu',
        description: '系统参数配置',
        parentId: 11,
        sort: 3,
        status: 'active'
      }
    ]
  }
];

let nextId = 15;

// 获取权限树
router.get('/tree', (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        tree: permissions.filter(p => !p.parentId)
      }
    });
  } catch (error) {
    console.error('获取权限树失败:', error);
    res.status(500).json({
      success: false,
      message: '获取权限树失败'
    });
  }
});

// 获取权限列表
router.get('/', (req, res) => {
  try {
    const { page = 1, limit = 20, name, type, status } = req.query;
    
    // 扁平化权限数据
    const flattenPermissions = (perms) => {
      let result = [];
      perms.forEach(perm => {
        result.push(perm);
        if (perm.children && perm.children.length > 0) {
          result = result.concat(flattenPermissions(perm.children));
        }
      });
      return result;
    };
    
    let allPermissions = flattenPermissions(permissions);
    
    // 筛选
    if (name) {
      allPermissions = allPermissions.filter(perm => 
        perm.name.includes(name) || perm.code.includes(name)
      );
    }
    
    if (type) {
      allPermissions = allPermissions.filter(perm => perm.type === type);
    }
    
    if (status) {
      allPermissions = allPermissions.filter(perm => perm.status === status);
    }
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedPermissions = allPermissions.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        permissions: paginatedPermissions,
        total: allPermissions.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取权限列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取权限列表失败'
    });
  }
});

// 创建权限
router.post('/', (req, res) => {
  try {
    const { name, code, type, parentId, description, sort = 0 } = req.body;
    
    // 验证必填字段
    if (!name || !code || !type) {
      return res.status(400).json({
        success: false,
        message: '权限名称、标识和类型不能为空'
      });
    }
    
    // 检查权限标识是否已存在
    const flattenPermissions = (perms) => {
      let result = [];
      perms.forEach(perm => {
        result.push(perm);
        if (perm.children && perm.children.length > 0) {
          result = result.concat(flattenPermissions(perm.children));
        }
      });
      return result;
    };
    
    const allPermissions = flattenPermissions(permissions);
    const existingPermission = allPermissions.find(p => p.code === code);
    if (existingPermission) {
      return res.status(400).json({
        success: false,
        message: '权限标识已存在'
      });
    }
    
    const newPermission = {
      id: nextId++,
      name,
      code,
      type,
      description: description || '',
      parentId: parentId || null,
      sort,
      status: 'active',
      children: []
    };
    
    // 如果有父级权限，添加到父级的children中
    if (parentId) {
      const addToParent = (perms) => {
        for (let perm of perms) {
          if (perm.id === parseInt(parentId)) {
            if (!perm.children) perm.children = [];
            perm.children.push(newPermission);
            return true;
          }
          if (perm.children && perm.children.length > 0) {
            if (addToParent(perm.children)) return true;
          }
        }
        return false;
      };
      addToParent(permissions);
    } else {
      permissions.push(newPermission);
    }
    
    res.status(201).json({
      success: true,
      data: { permission: newPermission },
      message: '权限创建成功'
    });
  } catch (error) {
    console.error('创建权限失败:', error);
    res.status(500).json({
      success: false,
      message: '创建权限失败'
    });
  }
});

// 更新权限
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, type, description, sort, status } = req.body;
    
    // 查找并更新权限
    const updatePermission = (perms) => {
      for (let i = 0; i < perms.length; i++) {
        if (perms[i].id === parseInt(id)) {
          perms[i] = {
            ...perms[i],
            ...(name && { name }),
            ...(code && { code }),
            ...(type && { type }),
            ...(description !== undefined && { description }),
            ...(sort !== undefined && { sort }),
            ...(status && { status })
          };
          return perms[i];
        }
        if (perms[i].children && perms[i].children.length > 0) {
          const updated = updatePermission(perms[i].children);
          if (updated) return updated;
        }
      }
      return null;
    };
    
    const updatedPermission = updatePermission(permissions);
    
    if (!updatedPermission) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }
    
    res.json({
      success: true,
      data: { permission: updatedPermission },
      message: '权限更新成功'
    });
  } catch (error) {
    console.error('更新权限失败:', error);
    res.status(500).json({
      success: false,
      message: '更新权限失败'
    });
  }
});

// 删除权限
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找并删除权限
    const deletePermission = (perms) => {
      for (let i = 0; i < perms.length; i++) {
        if (perms[i].id === parseInt(id)) {
          perms.splice(i, 1);
          return true;
        }
        if (perms[i].children && perms[i].children.length > 0) {
          if (deletePermission(perms[i].children)) return true;
        }
      }
      return false;
    };
    
    const deleted = deletePermission(permissions);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }
    
    res.json({
      success: true,
      message: '权限删除成功'
    });
  } catch (error) {
    console.error('删除权限失败:', error);
    res.status(500).json({
      success: false,
      message: '删除权限失败'
    });
  }
});

module.exports = router;
