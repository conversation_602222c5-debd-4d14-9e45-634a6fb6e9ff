const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
const Category = sequelize.define('Category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '分类名称'
  },
  pid: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '父分类ID，0为顶级分类'
  },
  sort: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序号'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '分类描述'
  },
  image: {
    type: DataTypes.STRING(500),
    comment: '分类图片'
  },
  icon: {
    type: DataTypes.STRING(255),
    comment: '分类图标'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active',
    comment: '状态'
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '分类层级'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    field: 'updated_at'
  }
}, {
  tableName: 'categories',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  comment: '课程分类表'
});

// 定义关联关系
Category.associate = function(models) {
  // 自关联 - 父子分类关系
  Category.belongsTo(Category, {
    foreignKey: 'pid',
    as: 'parent'
  });
  Category.hasMany(Category, {
    foreignKey: 'pid',
    as: 'children'
  });
  
  // 与课程的关系
  Category.hasMany(models.Course, {
    foreignKey: 'categoryId',
    as: 'courses'
  });
  
  // 创建者关系
  Category.belongsTo(models.User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });
};

return Category;
};
