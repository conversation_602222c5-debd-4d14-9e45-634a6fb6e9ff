const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'gst_japanese_training.sqlite');

console.log('📍 数据库文件路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 连接到SQLite数据库成功');
});

// 查询用户表
db.all("SELECT * FROM users", [], (err, rows) => {
  if (err) {
    console.error('❌ 查询用户表失败:', err.message);
  } else {
    console.log(`📊 用户表中共有 ${rows.length} 条记录:`);
    rows.forEach((row) => {
      console.log(`- ID: ${row.id}, 用户名: ${row.username}, 邮箱: ${row.email}, 角色: ${row.role}, 状态: ${row.status}`);
    });
  }
  
  // 关闭数据库连接
  db.close((err) => {
    if (err) {
      console.error('❌ 关闭数据库失败:', err.message);
    } else {
      console.log('✅ 数据库连接已关闭');
    }
  });
});
