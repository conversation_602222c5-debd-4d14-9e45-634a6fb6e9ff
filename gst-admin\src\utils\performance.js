/**
 * 性能监控工具
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.init()
  }

  init() {
    // 监听页面加载性能
    this.observePageLoad()
    
    // 监听长任务
    this.observeLongTasks()
    
    // 监听内存使用
    this.observeMemory()
    
    // 监听网络请求
    this.observeNetwork()
  }

  // 监听页面加载性能
  observePageLoad() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.recordMetric('page_load', {
              dns: entry.domainLookupEnd - entry.domainLookupStart,
              tcp: entry.connectEnd - entry.connectStart,
              request: entry.responseStart - entry.requestStart,
              response: entry.responseEnd - entry.responseStart,
              dom: entry.domContentLoadedEventEnd - entry.responseEnd,
              load: entry.loadEventEnd - entry.loadEventStart,
              total: entry.loadEventEnd - entry.navigationStart
            })
          }
        }
      })
      
      observer.observe({ entryTypes: ['navigation'] })
      this.observers.push(observer)
    }
  }

  // 监听长任务
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('long_task', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            })
          }
        })
        
        observer.observe({ entryTypes: ['longtask'] })
        this.observers.push(observer)
      } catch (e) {
        console.warn('Long task observer not supported')
      }
    }
  }

  // 监听内存使用
  observeMemory() {
    if ('memory' in performance) {
      setInterval(() => {
        this.recordMetric('memory', {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        })
      }, 30000) // 每30秒记录一次
    }
  }

  // 监听网络请求
  observeNetwork() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.initiatorType === 'fetch' || entry.initiatorType === 'xmlhttprequest') {
            this.recordMetric('network', {
              url: entry.name,
              duration: entry.duration,
              size: entry.transferSize,
              type: entry.initiatorType
            })
          }
        }
      })
      
      observer.observe({ entryTypes: ['resource'] })
      this.observers.push(observer)
    }
  }

  // 记录自定义指标
  recordMetric(name, data) {
    const timestamp = Date.now()
    
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    this.metrics.get(name).push({
      ...data,
      timestamp
    })

    // 限制存储数量，避免内存泄漏
    const maxEntries = 100
    const entries = this.metrics.get(name)
    if (entries.length > maxEntries) {
      entries.splice(0, entries.length - maxEntries)
    }

    // 输出到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}:`, data)
    }
  }

  // 开始计时
  startTiming(name) {
    performance.mark(`${name}-start`)
  }

  // 结束计时
  endTiming(name) {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name, 'measure')[0]
    if (measure) {
      this.recordMetric('custom_timing', {
        name,
        duration: measure.duration
      })
    }
  }

  // 获取指标数据
  getMetrics(name) {
    return this.metrics.get(name) || []
  }

  // 获取所有指标
  getAllMetrics() {
    const result = {}
    for (const [name, data] of this.metrics) {
      result[name] = data
    }
    return result
  }

  // 清除指标
  clearMetrics(name) {
    if (name) {
      this.metrics.delete(name)
    } else {
      this.metrics.clear()
    }
  }

  // 生成性能报告
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.getAllMetrics()
    }

    // 计算统计信息
    const pageLoadMetrics = this.getMetrics('page_load')
    if (pageLoadMetrics.length > 0) {
      const latest = pageLoadMetrics[pageLoadMetrics.length - 1]
      report.summary = {
        totalLoadTime: latest.total,
        domContentLoaded: latest.dom,
        networkTime: latest.dns + latest.tcp + latest.request + latest.response
      }
    }

    const longTasks = this.getMetrics('long_task')
    if (longTasks.length > 0) {
      report.longTasksCount = longTasks.length
      report.totalBlockingTime = longTasks.reduce((sum, task) => sum + task.duration, 0)
    }

    const memoryMetrics = this.getMetrics('memory')
    if (memoryMetrics.length > 0) {
      const latest = memoryMetrics[memoryMetrics.length - 1]
      report.memoryUsage = {
        used: Math.round(latest.used / 1024 / 1024), // MB
        total: Math.round(latest.total / 1024 / 1024), // MB
        usage: Math.round((latest.used / latest.total) * 100) // %
      }
    }

    return report
  }

  // 发送性能报告
  sendReport() {
    const report = this.generateReport()
    
    // 这里可以发送到性能监控服务
    // fetch('/api/performance', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(report)
    // }).catch(console.error)

    console.log('Performance Report:', report)
    return report
  }

  // 销毁监听器
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 导出工具函数
export const startTiming = (name) => performanceMonitor.startTiming(name)
export const endTiming = (name) => performanceMonitor.endTiming(name)
export const recordMetric = (name, data) => performanceMonitor.recordMetric(name, data)
export const getMetrics = (name) => performanceMonitor.getMetrics(name)
export const generateReport = () => performanceMonitor.generateReport()
export const sendReport = () => performanceMonitor.sendReport()

// 页面卸载时发送报告
window.addEventListener('beforeunload', () => {
  performanceMonitor.sendReport()
})

// 页面隐藏时发送报告
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'hidden') {
    performanceMonitor.sendReport()
  }
})

export default performanceMonitor
