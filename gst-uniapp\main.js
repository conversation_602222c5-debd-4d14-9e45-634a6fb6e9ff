import App from './App'

// #ifndef VUE3
import Vue from 'vue'
/* 全局引入 请求拦截器，对请求做一些统一的配置 */ 
import { http } from './common/js/request.js'
Vue.prototype.$http = http;

/* 全局引入 vuex，全局状态管理 */ 
import store from './common/js/store.js'
Vue.prototype.$store = store;

Vue.config.productionTip = false

import uView from '@/uni_modules/uview-ui'
Vue.use(uView)

const updateManager = uni.getUpdateManager();

updateManager.onCheckForUpdate(function (res) {
  // 请求完新版本信息的回调
  console.log(res.hasUpdate);
});

updateManager.onUpdateReady(function (res) {
  uni.showModal({
    title: '更新提示',
    content: '新版本已经准备好，是否重启应用？',
    success(res) {
      if (res.confirm) {
        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
        updateManager.applyUpdate();
      }
    }
  });

});

updateManager.onUpdateFailed(function (res) {
  // 新的版本下载失败
});
App.mpType = 'app'
const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif