const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { CourseUnit, Course, User, UnitProgress } = require('../models');
const { auth, requireTeacher } = require('../middleware/auth');
const { asyncHandler } = require('../utils/asyncHandler');
const { uploadVideo, uploadAudio, uploadDocument } = require('../config/aliyun');
const multer = require('multer');
const logger = require('../utils/logger');

const router = express.Router();

// 获取课程单元列表 - 匹配管理后台的请求格式
router.get('/', asyncHandler(async (req, res) => {
  const { courseId, page = 1, limit = 20 } = req.query;

  if (!courseId) {
    return res.status(400).json({
      success: false,
      message: '课程ID不能为空'
    });
  }

  // 验证课程是否存在
  const course = await Course.findByPk(courseId);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  const offset = (page - 1) * limit;

  const { rows: units, count } = await CourseUnit.findAndCountAll({
    where: { courseId },
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName']
      }
    ],
    order: [['orderNum', 'ASC'], ['createdAt', 'ASC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.json({
    success: true,
    data: {
      units,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    }
  });
}));

// 文件上传配置
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /mp4|avi|mov|wmv|flv|mp3|wav|aac|pdf|doc|docx|ppt|pptx/;
    const extname = allowedTypes.test(file.originalname.toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'));
    }
  }
});

// 获取课程单元列表
router.get('/course/:courseId', asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  // 验证课程是否存在
  const course = await Course.findByPk(courseId);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  const offset = (page - 1) * limit;

  const { rows: units, count } = await CourseUnit.findAndCountAll({
    where: { courseId },
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName']
      }
    ],
    order: [['orderNum', 'ASC'], ['createdAt', 'ASC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.json({
    success: true,
    data: {
      units,
      course: {
        id: course.id,
        title: course.title,
        level: course.level,
        category: course.category
      },
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    }
  });
}));

// 获取单元详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const unit = await CourseUnit.findByPk(id, {
    include: [
      {
        model: Course,
        as: 'course',
        attributes: ['id', 'title', 'level', 'category']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'realName', 'email']
      }
    ]
  });

  if (!unit) {
    return res.status(404).json({
      success: false,
      message: '单元不存在'
    });
  }

  // 增加浏览量
  await unit.incrementView();

  res.json({
    success: true,
    data: { unit }
  });
}));

// 创建课程单元
router.post('/', auth, requireTeacher, [
  body('courseId').isInt({ min: 1 }).withMessage('课程ID必须是正整数'),
  body('title').isLength({ min: 1, max: 200 }).withMessage('单元标题长度必须在1-200个字符之间'),
  body('type').isIn(['video', 'audio', 'text', 'quiz', 'exercise']).withMessage('单元类型不正确'),
  body('duration').optional().isInt({ min: 0 }).withMessage('时长必须是非负整数'),
  body('orderNum').optional().isInt({ min: 0 }).withMessage('排序序号必须是非负整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  const {
    courseId,
    title,
    description,
    content,
    type,
    duration = 0,
    orderNum = 0,
    isRequired = true,
    isFree = false
  } = req.body;

  // 验证课程是否存在
  const course = await Course.findByPk(courseId);
  if (!course) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }

  // 权限检查
  if (course.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  // 创建单元
  const unit = await CourseUnit.create({
    courseId,
    title,
    description,
    content,
    type,
    duration,
    orderNum,
    isRequired,
    isFree,
    createdBy: req.user.id,
    status: 'draft'
  });

  logger.info(`课程单元创建成功: ${unit.title} (${unit.id}) by ${req.user.username}`);

  res.status(201).json({
    success: true,
    message: '单元创建成功',
    data: { unit }
  });
}));

// 更新课程单元
router.put('/:id', auth, requireTeacher, [
  body('title').optional().isLength({ min: 1, max: 200 }),
  body('type').optional().isIn(['video', 'audio', 'text', 'quiz', 'exercise']),
  body('duration').optional().isInt({ min: 0 }),
  body('orderNum').optional().isInt({ min: 0 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入验证失败',
      errors: errors.array()
    });
  }

  const { id } = req.params;

  const unit = await CourseUnit.findByPk(id, {
    include: [
      {
        model: Course,
        as: 'course'
      }
    ]
  });

  if (!unit) {
    return res.status(404).json({
      success: false,
      message: '单元不存在'
    });
  }

  // 权限检查
  if (unit.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  // 更新单元
  await unit.update(req.body);

  logger.info(`课程单元更新成功: ${unit.title} (${unit.id}) by ${req.user.username}`);

  res.json({
    success: true,
    message: '单元更新成功',
    data: { unit }
  });
}));

// 上传单元视频
router.post('/:id/upload-video', auth, requireTeacher, upload.single('video'), asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '没有上传视频文件'
    });
  }

  const unit = await CourseUnit.findByPk(id);
  if (!unit) {
    return res.status(404).json({
      success: false,
      message: '单元不存在'
    });
  }

  // 权限检查
  if (unit.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  try {
    // 上传到阿里云OSS
    const uploadResult = await uploadVideo(req.file);
    
    if (uploadResult.success) {
      // 更新单元视频URL
      await unit.update({
        videoUrl: uploadResult.url,
        type: 'video'
      });

      logger.info(`视频上传成功: ${unit.title} (${unit.id}) by ${req.user.username}`);

      res.json({
        success: true,
        message: '视频上传成功',
        data: {
          videoUrl: uploadResult.url,
          unit
        }
      });
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    logger.error('视频上传失败:', error);
    res.status(500).json({
      success: false,
      message: '视频上传失败: ' + error.message
    });
  }
}));

// 上传单元音频
router.post('/:id/upload-audio', auth, requireTeacher, upload.single('audio'), asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '没有上传音频文件'
    });
  }

  const unit = await CourseUnit.findByPk(id);
  if (!unit) {
    return res.status(404).json({
      success: false,
      message: '单元不存在'
    });
  }

  // 权限检查
  if (unit.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  try {
    // 上传到阿里云OSS
    const uploadResult = await uploadAudio(req.file);
    
    if (uploadResult.success) {
      // 更新单元音频URL
      await unit.update({
        audioUrl: uploadResult.url,
        type: unit.type === 'text' ? 'audio' : unit.type
      });

      logger.info(`音频上传成功: ${unit.title} (${unit.id}) by ${req.user.username}`);

      res.json({
        success: true,
        message: '音频上传成功',
        data: {
          audioUrl: uploadResult.url,
          unit
        }
      });
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    logger.error('音频上传失败:', error);
    res.status(500).json({
      success: false,
      message: '音频上传失败: ' + error.message
    });
  }
}));

// 发布单元
router.patch('/:id/publish', auth, requireTeacher, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const unit = await CourseUnit.findByPk(id);
  if (!unit) {
    return res.status(404).json({
      success: false,
      message: '单元不存在'
    });
  }

  // 权限检查
  if (unit.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  await unit.publish();

  logger.info(`课程单元发布成功: ${unit.title} (${unit.id}) by ${req.user.username}`);

  res.json({
    success: true,
    message: '单元发布成功',
    data: { unit }
  });
}));

// 删除单元
router.delete('/:id', auth, requireTeacher, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const unit = await CourseUnit.findByPk(id);
  if (!unit) {
    return res.status(404).json({
      success: false,
      message: '单元不存在'
    });
  }

  // 权限检查
  if (unit.createdBy !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }

  // 检查是否有学习记录
  const progressCount = await UnitProgress.count({
    where: { unitId: id }
  });

  if (progressCount > 0) {
    return res.status(400).json({
      success: false,
      message: '该单元已有学习记录，无法删除'
    });
  }

  await unit.destroy();

  logger.info(`课程单元删除成功: ${unit.title} (${unit.id}) by ${req.user.username}`);

  res.json({
    success: true,
    message: '单元删除成功'
  });
}));

module.exports = router;
