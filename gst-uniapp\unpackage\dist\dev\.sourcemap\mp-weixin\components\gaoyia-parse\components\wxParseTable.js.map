{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTable.vue?5695", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTable.vue?b237", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTable.vue?e16e", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTable.vue?308d", "uni-app:///components/gaoyia-parse/components/wxParseTable.vue", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTable.vue?7ca8", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseTable.vue?23b9"], "names": ["name", "props", "node", "type", "default", "inject", "data", "nodes", "mounted", "methods", "loadNode", "attrs", "class", "children", "obj", "text"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwnB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;gBCM5oB;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MAAA,2CACAR;QAAA;MAAA;QAAA;UAAA;UACA;YACA;cACAF;cACAW;gBACAC;gBACA;cACA;;cACAC;YACA;YAEAC;UACA;YACAA;cACAX;cACAY;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAAi5B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;ACAr6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/gaoyia-parse/components/wxParseTable.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxParseTable.vue?vue&type=template&id=58e5b5a3&\"\nvar renderjs\nimport script from \"./wxParseTable.vue?vue&type=script&lang=js&\"\nexport * from \"./wxParseTable.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wxParseTable.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gaoyia-parse/components/wxParseTable.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTable.vue?vue&type=template&id=58e5b5a3&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTable.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTable.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div class='tablebox'>\r\n\t\t<rich-text :nodes=\"nodes\" :class=\"node.classStr\" :style=\"'user-select:' + parseSelect\"></rich-text>\r\n\t</div>\r\n</template>\r\n<script>\r\nexport default {\r\n\tname: 'wxParseTable',\r\n\tprops: {\r\n\t\tnode: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t},\r\n\t\t},\r\n\t},\r\n\tinject: ['parseSelect'],\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tnodes:[]\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\t\tthis.nodes=this.loadNode([this.node]);\r\n\t},\r\n\tmethods: {\r\n\t\tloadNode(node) {\r\n\t\t\tlet obj = [];\r\n\t\t\tfor (let children of node) {\r\n\t\t\t\tif (children.node=='element') {\r\n\t\t\t\t\tlet t = {\r\n\t\t\t\t\t\tname:children.tag,\r\n\t\t\t\t\t\tattrs: {\r\n\t\t\t\t\t\t\tclass: children.classStr,\r\n\t\t\t\t\t\t\t// style: children.styleStr,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tchildren: children.nodes?this.loadNode(children.nodes):[]\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tobj.push(t)\r\n\t\t\t\t} else if(children.node=='text'){\r\n\t\t\t\t\tobj.push({\r\n\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\ttext: children.text\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn obj\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n<style>\r\n\t@import url(\"../parse.css\");\r\n</style>", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTable.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseTable.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041065694\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}