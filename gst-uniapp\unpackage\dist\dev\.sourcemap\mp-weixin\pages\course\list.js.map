{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/course/list.vue?ceb9", "webpack:///D:/gst/gst-uniapp/pages/course/list.vue?e5ad", "webpack:///D:/gst/gst-uniapp/pages/course/list.vue?cc4d", "webpack:///D:/gst/gst-uniapp/pages/course/list.vue?870a", "uni-app:///pages/course/list.vue", "webpack:///D:/gst/gst-uniapp/pages/course/list.vue?5543", "webpack:///D:/gst/gst-uniapp/pages/course/list.vue?bf6e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniLoadMore", "data", "cateMaskState", "headerPosition", "headerTop", "loadingType", "filterIndex", "cateId", "priceOrder", "cateList", "goodsList", "title", "onLoad", "console", "uni", "onPageScroll", "onPullDownRefresh", "onReachBottom", "methods", "loadCateList", "list", "item", "loadData", "type", "loading", "tabClick", "duration", "scrollTop", "toggleCateMask", "setTimeout", "changeCate", "navToDetailPage", "url", "stopPrevent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCqErnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAIA;IACA;IACAC;IACA;IACAC;MACAH;IACA;IACA;IACA;EAEA;EACAI;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACA;EACAC;IACA;EACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAX;kBAAA;gBAAA;gBAEAA;kBACA;oBAAA;kBAAA;kBACAY;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBACA;kBACA;oBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACAX;QACAY;QACAC;MACA;MACA;MACAb;QACAH;MACA;IACA;IACA;IACAiB;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAhB;QACAY;QACAC;MACA;MACA;MACAb;QACAH;MACA;IACA;IACA;IACAoB;MACA;MACA;MACAjB;QACAkB;MACA;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrOA;AAAA;AAAA;AAAA;AAA4oC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACAhqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/course/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/course/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=2dcba129&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/course/list.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=2dcba129&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-load-more/uni-load-more\" */ \"@/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goodsList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\r\n\t\t\t<view v-if=\" goodsList.length===0\" class=\"empty\">\r\n\t\t\t\t<image src=\"/static/emptyCart.jpg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"empty-tips\">\r\n\t\t\t\t\t空空如也\r\n\t\t\t\t\t<navigator class=\"navigator\" url=\"../index/index\" open-type=\"switchTab\">随便逛逛></navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\" v-else>\r\n\t\t\t\t<!-- <view class=\"navbar\" :style=\"{position:headerPosition,top:headerTop}\">\r\n\t\t\t<view class=\"nav-item\" :class=\"{current: filterIndex === 0}\" @click=\"tabClick(0)\">\r\n\t\t\t\t综合排序\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" :class=\"{current: filterIndex === 1}\" @click=\"tabClick(1)\">\r\n\t\t\t\t销量优先\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" :class=\"{current: filterIndex === 2}\" @click=\"tabClick(2)\">\r\n\t\t\t\t<text>价格</text>\r\n\t\t\t\t<view class=\"p-box\">\r\n\t\t\t\t\t<text :class=\"{active: priceOrder === 1 && filterIndex === 2}\" class=\"yticon icon-shang\"></text>\r\n\t\t\t\t\t<text :class=\"{active: priceOrder === 2 && filterIndex === 2}\" class=\"yticon icon-shang xia\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t <text class=\"cate-item yticon icon-fenlei1\" @click=\"toggleCateMask('show')\"></text> \r\n\t\t</view> -->\r\n\t\t\t\t<!-- 空白页 -->\r\n\r\n\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item, index) in goodsList\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"navToDetailPage(item)\">\r\n\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item.picture\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t<view class=\"title\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"des\">{{item.des}}</view>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"end\"><text style=\"text-align: right;float: right;\">￥{{item.price}}</text></view> -->\r\n\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-load-more :status=\"loadingType\"></uni-load-more>\r\n\r\n\t\t\t\t<view class=\"cate-mask\" :class=\"cateMaskState===0 ? 'none' : cateMaskState===1 ? 'show' : ''\"\r\n\t\t\t\t\t@click=\"toggleCateMask\">\r\n\t\t\t\t\t<view class=\"cate-content\" @click.stop.prevent=\"stopPrevent\" @touchmove.stop.prevent=\"stopPrevent\">\r\n\t\t\t\t\t\t<scroll-view scroll-y class=\"cate-list\">\r\n\t\t\t\t\t\t\t<view v-for=\"item in cateList\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t<view class=\"cate-item b-b two\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view v-for=\"tItem in item.child\" :key=\"tItem.id\" class=\"cate-item b-b\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{active: tItem.id==cateId}\" @click=\"changeCate(tItem)\">\r\n\t\t\t\t\t\t\t\t\t{{tItem.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\timport uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniLoadMore\t\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcateMaskState: 0, //分类面板展开状态\r\n\t\t\t\theaderPosition:\"fixed\",\r\n\t\t\t\theaderTop:\"0px\",\r\n\t\t\t\tloadingType: 'more', //加载更多状态\r\n\t\t\t\tfilterIndex: 0, \r\n\t\t\t\tcateId: 0, //已选三级分类id\r\n\t\t\t\tpriceOrder: 0, //1 价格从低到高 2价格从高到低\r\n\t\t\t\tcateList: [],\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\ttitle:''\r\n\t\t\t};\r\n\t\t},\r\n\t\t\r\n\t\tonLoad(options){\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.headerTop = document.getElementsByTagName('uni-page-head')[0].offsetHeight+'px';\r\n\t\t\t// #endif\r\n\t\t\tthis.cateId = options.id;\r\n\t\t\tthis.title = options.title;\r\n\t\t\tconsole.log(this.title);\r\n\t\t\t// 动态设置标题\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: this.title\r\n\t\t\t\t});\r\n\t\t\tthis.loadCateList(options.fid,options.sid);\r\n\t\t\tthis.loadData();\r\n\t\t\t\r\n\t\t},\r\n\t\tonPageScroll(e){\r\n\t\t\t//兼容iOS端下拉时顶部漂移\r\n\t\t\tif(e.scrollTop>=0){\r\n\t\t\t\tthis.headerPosition = \"fixed\";\r\n\t\t\t}else{\r\n\t\t\t\tthis.headerPosition = \"absolute\";\r\n\t\t\t}\r\n\t\t},\r\n\t\t//下拉刷新\r\n\t\tonPullDownRefresh(){\r\n\t\t\tthis.loadData('refresh');\r\n\t\t},\r\n\t\t//加载更多\r\n\t\tonReachBottom(){\r\n\t\t\tthis.loadData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//加载分类\r\n\t\t\tasync loadCateList(fid, sid){\r\n\t\t\t\tlet list = await this.$api.json('cateList');\r\n\t\t\t\tlet cateList = list.filter(item=>item.pid == fid);\r\n\t\t\t\t\r\n\t\t\t\tcateList.forEach(item=>{\r\n\t\t\t\t\tlet tempList = list.filter(val=>val.pid == item.id);\r\n\t\t\t\t\titem.child = tempList;\r\n\t\t\t\t})\r\n\t\t\t\tthis.cateList = cateList;\r\n\t\t\t},\r\n\t\t\t//加载商品 ，带下拉刷新和上滑加载\r\n\t\t\tasync loadData(type='add', loading) {\r\n\t\t\t\tthis.$http.get(\"v1/course_list\").then(res => {\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.goodsList = res.data.data.list.data\r\n\t\t\t\t\t} \r\n\t\t\t\t});\r\n\t\t\t\t// //没有更多直接返回\r\n\t\t\t\t// if(type === 'add'){\r\n\t\t\t\t// \tif(this.loadingType === 'nomore'){\r\n\t\t\t\t// \t\treturn;\r\n\t\t\t\t// \t}\r\n\t\t\t\t// \tthis.loadingType = 'loading';\r\n\t\t\t\t// }else{\r\n\t\t\t\t// \tthis.loadingType = 'more'\r\n\t\t\t\t// }\r\n\t\t\t\t\r\n\t\t\t\t// let goodsList = await this.$api.json('goodsList');\r\n\t\t\t\t// if(type === 'refresh'){\r\n\t\t\t\t// \tthis.goodsList = [];\r\n\t\t\t\t// }\r\n\t\t\t\t// //筛选，测试数据直接前端筛选了\r\n\t\t\t\t// if(this.filterIndex === 1){\r\n\t\t\t\t// \tgoodsList.sort((a,b)=>b.sales - a.sales)\r\n\t\t\t\t// }\r\n\t\t\t\t// if(this.filterIndex === 2){\r\n\t\t\t\t// \tgoodsList.sort((a,b)=>{\r\n\t\t\t\t// \t\tif(this.priceOrder == 1){\r\n\t\t\t\t// \t\t\treturn a.price - b.price;\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t\treturn b.price - a.price;\r\n\t\t\t\t// \t})\r\n\t\t\t\t// }\r\n\t\t\t\t\r\n\t\t\t\t// this.goodsList = this.goodsList.concat(goodsList);\r\n\t\t\t\t\r\n\t\t\t\t// //判断是否还有下一页，有是more  没有是nomore(测试数据判断大于20就没有了)\r\n\t\t\t\t// this.loadingType  = this.goodsList.length > 20 ? 'nomore' : 'more';\r\n\t\t\t\t// if(type === 'refresh'){\r\n\t\t\t\t// \tif(loading == 1){\r\n\t\t\t\t// \t\tuni.hideLoading()\r\n\t\t\t\t// \t}else{\r\n\t\t\t\t// \t\tuni.stopPullDownRefresh();\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\t//筛选点击\r\n\t\t\ttabClick(index){\r\n\t\t\t\tif(this.filterIndex === index && index !== 2){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.filterIndex = index;\r\n\t\t\t\tif(index === 2){\r\n\t\t\t\t\tthis.priceOrder = this.priceOrder === 1 ? 2: 1;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.priceOrder = 0;\r\n\t\t\t\t}\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tduration: 300,\r\n\t\t\t\t\tscrollTop: 0\r\n\t\t\t\t})\r\n\t\t\t\tthis.loadData('refresh', 1);\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//显示分类面板\r\n\t\t\ttoggleCateMask(type){\r\n\t\t\t\tlet timer = type === 'show' ? 10 : 300;\r\n\t\t\t\tlet\tstate = type === 'show' ? 1 : 0;\r\n\t\t\t\tthis.cateMaskState = 2;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.cateMaskState = state;\r\n\t\t\t\t}, timer)\r\n\t\t\t},\r\n\t\t\t//分类点击\r\n\t\t\tchangeCate(item){\r\n\t\t\t\tthis.cateId = item.id;\r\n\t\t\t\tthis.toggleCateMask();\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tduration: 300,\r\n\t\t\t\t\tscrollTop: 0\r\n\t\t\t\t})\r\n\t\t\t\tthis.loadData('refresh', 1);\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情\r\n\t\t\tnavToDetailPage(item){\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tstopPrevent(){}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage,\r\n\t.content {\r\n\t\tbackground: $page-color-base;\r\n\t}\r\n\t\r\n\t.content {\r\n\t\tpadding-top: 20upx;\r\n\t}\r\n\t\r\n\t.navbar {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: var(--window-top);\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\theight: 80upx;\r\n\t\tbackground: #fff;\r\n\t\tbox-shadow: 0 2upx 10upx rgba(0, 0, 0, .06);\r\n\t\tz-index: 10;\r\n\t\r\n\t\t.nav-item {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-size: 30upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\t\r\n\t\t\t&.current {\r\n\t\t\t\tcolor: $base-color;\r\n\t\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 120upx;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-bottom: 4upx solid $base-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.p-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\r\n\t\t\t.yticon {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 30upx;\r\n\t\t\t\theight: 14upx;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tmargin-left: 4upx;\r\n\t\t\t\tfont-size: 26upx;\r\n\t\t\t\tcolor: #888;\r\n\t\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tcolor: $base-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.xia {\r\n\t\t\t\ttransform: scaleY(-1);\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.cate-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\twidth: 80upx;\r\n\t\t\tposition: relative;\r\n\t\t\tfont-size: 44upx;\r\n\t\r\n\t\t\t&:after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\tborder-left: 1px solid #ddd;\r\n\t\t\t\twidth: 0;\r\n\t\t\t\theight: 36upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 分类 */\r\n\t.cate-mask {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: var(--window-top);\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0);\r\n\t\tz-index: 95;\r\n\t\ttransition: .3s;\r\n\t\r\n\t\t.cate-content {\r\n\t\t\twidth: 630upx;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: #fff;\r\n\t\t\tfloat: right;\r\n\t\t\ttransform: translateX(100%);\r\n\t\t\ttransition: .3s;\r\n\t\t}\r\n\t\r\n\t\t&.none {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t\r\n\t\t&.show {\r\n\t\t\tbackground: rgba(0, 0, 0, .4);\r\n\t\r\n\t\t\t.cate-content {\r\n\t\t\t\ttransform: translateX(0);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.cate-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100%;\r\n\t\r\n\t\t.cate-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 90upx;\r\n\t\t\tpadding-left: 30upx;\r\n\t\t\tfont-size: 28upx;\r\n\t\t\tcolor: #555;\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\t\r\n\t\t.two {\r\n\t\t\theight: 64upx;\r\n\t\t\tcolor: #303133;\r\n\t\t\tfont-size: 30upx;\r\n\t\t\tbackground: #f8f8f8;\r\n\t\t}\r\n\t\r\n\t\t.active {\r\n\t\t\tcolor: $base-color;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 商品列表 */\r\n\t.goods-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 30upx;\r\n\t\tbackground: #fff;\r\n\t\r\n\t\t.goods-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\twidth: 48%;\r\n\t\t\tpadding-bottom: 40upx;\r\n\t\r\n\t\t\t&:nth-child(2n+1) {\r\n\t\t\t\tmargin-right: 4%;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.image-wrapper {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 180upx;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\toverflow: hidden;\r\n\t\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.title {\r\n\t\t\tfont-size: $font-lg;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tfont-size: 28upx;\r\n\t\t\tline-height: 45upx;\r\n\t\t}\r\n\t\r\n\t\t.price-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding-right: 10upx;\r\n\t\t\tfont-size: 22upx;\r\n\t\t\tcolor: $font-color-light;\r\n\t\t}\r\n\t\r\n\t\t.price {\r\n\t\t\tfont-size: $font-lg;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\tline-height: 1;\r\n\t\r\n\t\t\t&:before {\r\n\t\t\t\tcontent: '￥';\r\n\t\t\t\tfont-size: 26upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.list-box {\r\n\t\tpadding-bottom: 20rpx;\r\n\t\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding:20rpx 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.cover :after{\r\n\t\t\t\t\t    background-color: red;\r\n\t\t\t\t\t    border-radius: 10rpx;\r\n\t\t\t\t\t    color: #fff;\r\n\t\t\t\t\t    content: \"hot\";\r\n\t\t\t\t\t    font-size: 25rpx;\r\n\t\t\t\t\t    line-height: 1;\r\n\t\t\t\t\t    padding: 2rpx 6rpx;\r\n\t\t\t\t\t    position: absolute;\r\n\t\t\t\t\t    left: 5rpx;\r\n\t\t\t\t\t    top: 5rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.button{\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.button{\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t  \r\n\t\t\t\t\r\n\t\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.des{\r\n\t\t\t\t\t\tfont-size:22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.price{\r\n\t\t\t\t\t\tfont-size:24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.end{\r\n\t\t\t\t\t\tfont-size:24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 空白页 */\r\n\t.empty {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t\tpadding-bottom: 100upx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\t\r\n\t\timage {\r\n\t\t\twidth: 240upx;\r\n\t\t\theight: 160upx;\r\n\t\t\tmargin-bottom: 30upx;\r\n\t\t}\r\n\t\r\n\t\t.empty-tips {\r\n\t\t\tdisplay: flex;\r\n\t\t\tfont-size: $font-sm+2upx;\r\n\t\t\tcolor: $font-color-disabled;\r\n\t\r\n\t\t\t.navigator {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tmargin-left: 16upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689564002\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}