
.test-tabbar.data-v-235d9b8e {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background: #ffffff;
	border-top: 1rpx solid #e5e5e5;
	z-index: 99999;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.tabs.data-v-235d9b8e {
	display: flex;
	width: 100%;
	height: 100%;
}
.tab.data-v-235d9b8e {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 8rpx 0;
	position: relative;
	transition: background-color 0.3s ease;
}
.tab.data-v-235d9b8e:active {
	background: rgba(0, 0, 0, 0.05);
}
.tab-icon.data-v-235d9b8e {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 4rpx;
}
.tab-text.data-v-235d9b8e {
	font-size: 20rpx;
	color: #C0C4CC;
	transition: color 0.3s ease;
}
.tab-text.active.data-v-235d9b8e {
	color: #2094CE;
	font-weight: 600;
}
.tab.active .tab-icon.data-v-235d9b8e {
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
}
.member-badge.data-v-235d9b8e {
	position: absolute;
	top: 2rpx;
	right: 20rpx;
	background: linear-gradient(135deg, #ff6b6b, #ffa500);
	border-radius: 20rpx;
	padding: 2rpx 8rpx;
	-webkit-transform: scale(0.8);
	        transform: scale(0.8);
}
.member-text.data-v-235d9b8e {
	font-size: 16rpx;
	color: #fff;
	font-weight: 600;
}

