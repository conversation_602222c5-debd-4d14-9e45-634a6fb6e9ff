{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page-loading.vue?4dbc", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page-loading.vue?29d8", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page-loading.vue?61fa", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page-loading.vue?a0fa", "uni-app:///GraceUI5/components/gui-page-loading.vue", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page-loading.vue?8abb", "webpack:///D:/gst/gst-uniapp/GraceUI5/components/gui-page-loading.vue?dede"], "names": ["name", "props", "data", "isLoading", "BindingXObjs", "Animate<PERSON>bjs", "animateTimer", "intervalID", "watch", "methods", "stopfun", "e", "open", "close", "setTimeout", "getRefs", "fun", "count"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACqC;;;AAGpG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6mB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0BjoB;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,QAwBA;EACAC;IAwDAC;MAAAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACA;QACAC;QACA;MACA;QACAC;QACAH;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAw5B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACA56B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "GraceUI5/components/gui-page-loading.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./gui-page-loading.vue?vue&type=template&id=227b3cb1&scoped=true&\"\nvar renderjs\nimport script from \"./gui-page-loading.vue?vue&type=script&lang=js&\"\nexport * from \"./gui-page-loading.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gui-page-loading.vue?vue&type=style&index=0&id=227b3cb1&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"227b3cb1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"GraceUI5/components/gui-page-loading.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page-loading.vue?vue&type=template&id=227b3cb1&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page-loading.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page-loading.vue?vue&type=script&lang=js&\"", "<template>\n\t<view \n\tclass=\"gui-page-loading gui-flex gui-nowrap gui-align-items-center gui-justify-content-center gui-page-loading-bg\"\n\**********=\"stopfun\" \n\************************=\"stopfun\" \n\tv-if=\"isLoading\">\n\t\t<!-- #ifndef APP-NVUE -->\n\t\t<view class=\"gui-page-loading-point gui-flex gui-rows gui-justify-content-center\">\n\t\t\t<view class=\"gui-page-loading-points animate1 gui-page-loading-color\"></view>\n\t\t\t<view class=\"gui-page-loading-points animate2 gui-page-loading-color\"></view>\n\t\t\t<view class=\"gui-page-loading-points animate3 gui-page-loading-color\"></view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t\t<!-- #ifdef APP-NVUE -->\n\t\t<view class=\"gui-page-loading-point gui-flex gui-rows gui-justify-content-center\">\n\t\t\t<view class=\"gui-page-loading-points gui-page-loading-color\" ref=\"loadingPoints1\"></view>\n\t\t\t<view class=\"gui-page-loading-points gui-page-loading-color\" ref=\"loadingPoints2\"></view>\n\t\t\t<view class=\"gui-page-loading-points gui-page-loading-color\" ref=\"loadingPoints3\"></view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t</view>\n</template>\n<script>\n// #ifdef APP-NVUE\nconst BindingX = uni.requireNativePlugin('bindingx');\n// #endif\nexport default{\n\tname  : \"gui-page-loading\",\n\tprops : {},\n\tdata() {\n\t\treturn {\n\t\t\tisLoading      : false,\n\t\t\tBindingXObjs   : [null,null,null],\n\t\t\tAnimateObjs    : [null,null,null],\n\t\t\tanimateTimer   : 800,\n\t\t\tintervalID     : null\n\t\t}\n\t},\n\twatch:{\n\t\t// #ifdef APP-NVUE\n\t\tisLoading : function (val) {\n\t\t\tif(val){\n\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\tthis.getRefs('loadingPoints1', 0, (refs)=>{\n\t\t\t\t\t\tthis.BindingXObjs = [\n\t\t\t\t\t\t\trefs.ref,\n\t\t\t\t\t\t\tthis.$refs.loadingPoints2.ref,\n\t\t\t\t\t\t\tthis.$refs.loadingPoints3.ref\n\t\t\t\t\t\t];\n\t\t\t\t\t\tthis.startAnimate();\n\t\t\t\t\t});\n\t\t\t\t}, 100);\n\t\t\t\tthis.intervalID = setInterval(()=>{\n\t\t\t\t\tif(this.isLoading){\n\t\t\t\t\t\tthis.startAnimate();\n\t\t\t\t\t}else{\n\t\t\t\t\t\tclearInterval(this.intervalID);\n\t\t\t\t\t}\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t\t// #endif\n\t},\n\tmethods:{\n\t\t// #ifdef APP-NVUE\n\t\tstartAnimate   : function(){\n\t\t\tthis.loadingAnimate(0);\n\t\t\tsetTimeout(()=>{this.loadingAnimate(1);},300);\n\t\t\tsetTimeout(()=>{this.loadingAnimate(2);},600);\n\t\t},\n\t\tloadingAnimate : function (id) {\n\t\t\tthis.AnimateObjs[id] = BindingX.bind({\n\t\t\t\teventType      : 'timing',\n\t\t\t\texitExpression : 't>'+this.animateTimer,\n\t\t\t\tprops          : [\n\t\t\t\t\t{\n\t\t\t\t\t\telement    : this.BindingXObjs[id], \n\t\t\t\t\t\tproperty   : 'transform.scale',\n\t\t\t\t\t\texpression : \"1+t/\"+this.animateTimer+\"/3\"\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\telement    : this.BindingXObjs[id], \n\t\t\t\t\t\tproperty   : 'opacity',\n\t\t\t\t\t\texpression : \"0.6+t/\"+this.animateTimer\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}, (e)=>{\n\t\t\t\tif(e.state === 'exit') {\n\t\t\t\t\tBindingX.unbind({\n\t\t\t\t\t\ttoken : this.AnimateObjs[id].token,\n\t\t\t\t\t\teventType: 'timing'\n\t\t\t\t\t});\n\t\t\t\t\tthis.AnimateObjs[id] = BindingX.bind({\n\t\t\t\t\t\teventType      : 'timing',\n\t\t\t\t\t\texitExpression : 't>'+this.animateTimer,\n\t\t\t\t\t\tprops          : [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\telement    : this.BindingXObjs[id], \n\t\t\t\t\t\t\t\tproperty   : 'transform.scale',\n\t\t\t\t\t\t\t\texpression : \"1.35-t/\"+this.animateTimer+\"/3\"\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\telement    : this.BindingXObjs[id], \n\t\t\t\t\t\t\t\tproperty   : 'opacity',\n\t\t\t\t\t\t\t\texpression : \"1.6-t/\"+this.animateTimer\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t}, (e)=>{\n\t\t\t\t\t\tif(e.state === 'exit') {\n\t\t\t\t\t\t\tBindingX.unbind({\n\t\t\t\t\t\t\t\ttoken : this.AnimateObjs[id].token,\n\t\t\t\t\t\t\t\teventType: 'timing'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// #endif\n\t\tstopfun        : function(e){e.stopPropagation(); return null;},\n\t\topen           : function(){ this.isLoading = true; },\n\t\tclose          : function(){\n\t\t\tsetTimeout(()=>{\n\t\t\t\tthis.isLoading = false; \n\t\t\t},100);\n\t\t},\n\t\tgetRefs        : function(ref, count, fun){\n\t\t\tif(count >= 30){return null;}\n\t\t\tvar refReturn = this.$refs[ref];\n\t\t\tif(refReturn){\n\t\t\t\tfun(refReturn);\n\t\t\t\treturn;\n\t\t\t}else{\n\t\t\t\tcount++;\n\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\tthis.getRefs(ref, count, fun);\n\t\t\t\t}, 50);\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n<style scoped>\n.gui-page-loading{width:750rpx; position:fixed; left:0; top:0; bottom:0; flex:1; z-index:99999;}\n.gui-page-loading-points{width:20rpx; height:20rpx; border-radius:50rpx; margin:10rpx; opacity:0.5;}\n/* #ifndef APP-NVUE */\n@keyframes pageLoading1{0% {opacity:0.5; transform:scale(1);} 40% {opacity:1; transform:scale(1.5);}  60%{opacity:0.5; transform:scale(1);}}\n@keyframes pageLoading2{20% {opacity:0.5; transform:scale(1);} 60% {opacity:1; transform:scale(1.5);}  80% {opacity:0.5; transform:scale(1);}}\n@keyframes pageLoading3{40% {opacity:0.5; transform:scale(1);} 80% {opacity:1; transform:scale(1.5);}  100% {opacity:0.5; transform:scale(1);}}\n.animate1{animation:pageLoading1 1.2s infinite linear;}\n.animate2{animation:pageLoading2 1.2s infinite linear;}\n.animate3{animation:pageLoading3 1.2s infinite linear;}\n/* #endif */\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page-loading.vue?vue&type=style&index=0&id=227b3cb1&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gui-page-loading.vue?vue&type=style&index=0&id=227b3cb1&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689561884\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}