{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/study/study.vue?e557", "webpack:///D:/gst/gst-uniapp/pages/study/study.vue?1e4f", "webpack:///D:/gst/gst-uniapp/pages/study/study.vue?05df", "webpack:///D:/gst/gst-uniapp/pages/study/study.vue?4f62", "uni-app:///pages/study/study.vue", "webpack:///D:/gst/gst-uniapp/pages/study/study.vue?8869", "webpack:///D:/gst/gst-uniapp/pages/study/study.vue?c233"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniLoadMore", "CustomTabbar", "data", "cateMaskState", "headerPosition", "headerTop", "loadingType", "filterIndex", "cateId", "priceOrder", "cateList", "goodsList", "title", "loading", "hotList", "num", "onLoad", "console", "uni", "onPageScroll", "onPullDownRefresh", "onReachBottom", "onShow", "content", "success", "url", "methods", "loadData", "type", "loadHotData", "params", "apiGetCourseList", "navToDetailPage", "navToDetailPage2"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwEtnB;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAIA;IACA;IACAC;IACA;IACAC;MACAN;IACA;IACA;;IAEA;EAGA;EACAO;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACA;EACAC;IACA;EAAA,CACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACA;IAEA;MACAJ;QACAN;QACAW;QACAC;UACA;YACAN;cACAO;YACA;UACA;YACA;AACA;AACA;UAFA;QAIA;MACA;IACA;MACA;MACA;IACA;EACA;EACAC;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAf;gBACA;kBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAgB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAD;gBAAAf;gBACA;kBACAiB,SAEA;gBACA;kBACAb;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAc;MACA;QACAD,SAEA;MACA;QACA;MACA;IACA;IACA;IACAE;MACA;MACA;MACAd;QACAO;MACA;IACA;IACAQ;MACA;MACA;MACAf;QACAO;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/study/study.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/study/study.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./study.vue?vue&type=template&id=7ca426cc&\"\nvar renderjs\nimport script from \"./study.vue?vue&type=script&lang=js&\"\nexport * from \"./study.vue?vue&type=script&lang=js&\"\nimport style0 from \"./study.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/study/study.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./study.vue?vue&type=template&id=7ca426cc&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goodsList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./study.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./study.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#ffffff;\">\r\n\t\t\t<!-- <view class=\"first\">\r\n\t\t\t\t<view>{{num}}</view>\r\n\t\t\t\t<view style=\"color: rgb(224, 198, 173);\">人正在学习</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"h2title\">我的课程</view>\r\n\t\t\t\t<view v-if=\" goodsList.length===0\" class=\"empty\">\r\n\t\t\t\t\t<image src=\"/static/null.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view class=\"empty-tips\">\r\n\t\t\t\t\t\t您还没有加入课程\r\n\t\t\t\t\t\t<!-- <navigator class=\"navigator\" url=\"../index/index\" open-type=\"switchTab\">随便逛逛></navigator> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"empty-tips\">\r\n\t\t\t\t\t\t从推荐课程中看看有没有喜欢的吧~\r\n\t\t\t\t\t\t<!-- <navigator class=\"navigator\" url=\"../index/index\" open-type=\"switchTab\">随便逛逛></navigator> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\" v-else>\r\n\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item, index) in goodsList\" :key=\"index\"\r\n\t\t\t\t\t\t\t@click=\"navToDetailPage(item)\">\r\n\t\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\" >\r\n\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item.picture\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"title\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"des\">{{item.des}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <uni-load-more :status=\"loadingType\"></uni-load-more> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin-top: 20rpx;\">\r\n\t\t\t\t<view class=\"h2title\">课程推荐</view>\r\n\t\t\t\t<view class=\"content\" >\r\n\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item, index) in hotList\" :key=\"index\"\r\n\t\t\t\t\t\t\t@click=\"navToDetailPage2(item)\">\r\n\t\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\" >\r\n\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item.picture\"></image>\r\n\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"title\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"des\">{{item.des}}</view> -->\r\n\t\t\t\t\t\t\t\t<!-- \t<view class=\"end\"><text\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"text-align: right;float: right;\">点击报名</text></view> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <uni-load-more :status=\"loadingType\"></uni-load-more> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 自定义底部导航 -->\r\n\t\t<custom-tabbar />\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\timport uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';\r\n\timport CustomTabbar from '@/components/custom-tabbar.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniLoadMore,\r\n\t\t\tCustomTabbar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcateMaskState: 0, //分类面板展开状态\r\n\t\t\t\theaderPosition: \"fixed\",\r\n\t\t\t\theaderTop: \"0px\",\r\n\t\t\t\tloadingType: 'more', //加载更多状态\r\n\t\t\t\tfilterIndex: 0,\r\n\t\t\t\tcateId: 0, //已选三级分类id\r\n\t\t\t\tpriceOrder: 0, //1 价格从低到高 2价格从高到低\r\n\t\t\t\tcateList: [],\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tloading: false,\r\n\t\t\t\thotList:[],\r\n\t\t\t\tnum:0\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.headerTop = document.getElementsByTagName('uni-page-head')[0].offsetHeight + 'px';\r\n\t\t\t// #endif\r\n\t\t\tthis.cateId = options.id;\r\n\t\t\tthis.title = options.title;\r\n\t\t\tconsole.log(this.title);\r\n\t\t\t// 动态设置标题\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.title\r\n\t\t\t});\r\n\t\t\t//this.loadCateList(options.fid, options.sid);\r\n\t\t\t\r\n\t\t\tthis.loadHotData();\r\n\t\t\t\r\n\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\t//兼容iOS端下拉时顶部漂移\r\n\t\t\tif (e.scrollTop >= 0) {\r\n\t\t\t\tthis.headerPosition = \"fixed\";\r\n\t\t\t} else {\r\n\t\t\t\tthis.headerPosition = \"absolute\";\r\n\t\t\t}\r\n\t\t},\r\n\t\t//下拉刷新\r\n\t\tonPullDownRefresh() {\r\n\t\t\t//this.loadData('refresh');\r\n\t\t},\r\n\t\t//加载更多\r\n\t\tonReachBottom() {\r\n\t\t\t//this.loadData();\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.user = this.$store.state.user;\r\n\r\n\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/login/login?type=wx\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tthis.$store.dispatch('refreshUserMember');\r\n\t\t\t\tthis.loadData();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\t//加载商品 ，带下拉刷新和上滑加载\r\n\t\t\tasync loadData(type = 'add', loading) {\r\n\t\t\t\tthis.apiGetCourseList().then(pagination => {\r\n\t\t\t\t\tthis.goodsList = pagination.data;\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//加载商品 ，带下拉刷新和上滑加载\r\n\t\t\tasync loadHotData(type = 'add', loading) {\r\n\t\t\t\tthis.$http.get(\"/v1/course/getCommend\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\tthis.hotList = res.data.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tapiGetCourseList: function() {\r\n\t\t\t\treturn this.$http.get('/v1/course/userOrder', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情页\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.good_id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavToDetailPage2(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage,\r\n\t.content {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.content {\r\n\t\t// padding-top: 20upx;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.navbar {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: var(--window-top);\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\theight: 80upx;\r\n\t\tbackground: #fff;\r\n\t\tbox-shadow: 0 2upx 10upx rgba(0, 0, 0, .06);\r\n\t\tz-index: 10;\r\n\r\n\t\t.nav-item {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-size: 30upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&.current {\r\n\t\t\t\tcolor: $base-color;\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 120upx;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-bottom: 4upx solid $base-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.p-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.yticon {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 30upx;\r\n\t\t\t\theight: 14upx;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tmargin-left: 4upx;\r\n\t\t\t\tfont-size: 26upx;\r\n\t\t\t\tcolor: #888;\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tcolor: $base-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.xia {\r\n\t\t\t\ttransform: scaleY(-1);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.cate-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100%;\r\n\t\t\twidth: 80upx;\r\n\t\t\tposition: relative;\r\n\t\t\tfont-size: 44upx;\r\n\r\n\t\t\t&:after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\tborder-left: 1px solid #ddd;\r\n\t\t\t\twidth: 0;\r\n\t\t\t\theight: 36upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* 分类 */\r\n\t.cate-mask {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: var(--window-top);\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0);\r\n\t\tz-index: 95;\r\n\t\ttransition: .3s;\r\n\r\n\t\t.cate-content {\r\n\t\t\twidth: 630upx;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: #fff;\r\n\t\t\tfloat: right;\r\n\t\t\ttransform: translateX(100%);\r\n\t\t\ttransition: .3s;\r\n\t\t}\r\n\r\n\t\t&.none {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t&.show {\r\n\t\t\tbackground: rgba(0, 0, 0, .4);\r\n\r\n\t\t\t.cate-content {\r\n\t\t\t\ttransform: translateX(0);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.cate-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100%;\r\n\r\n\t\t.cate-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 90upx;\r\n\t\t\tpadding-left: 30upx;\r\n\t\t\tfont-size: 28upx;\r\n\t\t\tcolor: #555;\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\r\n\t\t.two {\r\n\t\t\theight: 64upx;\r\n\t\t\tcolor: #303133;\r\n\t\t\tfont-size: 30upx;\r\n\t\t\tbackground: #f8f8f8;\r\n\t\t}\r\n\r\n\t\t.active {\r\n\t\t\tcolor: $base-color;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 商品列表 */\r\n\t.goods-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 0 30upx;\r\n\t\tbackground: #fff;\r\n\r\n\t\t.goods-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\twidth: 48%;\r\n\t\t\tpadding-bottom: 40upx;\r\n\r\n\t\t\t&:nth-child(2n+1) {\r\n\t\t\t\tmargin-right: 4%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.image-wrapper {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 180upx;\r\n\t\t\tborder-radius: 3px;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: $font-lg;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tfont-size: 28upx;\r\n\t\t\tline-height: 45upx;\r\n\t\t}\r\n\r\n\t\t.price-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding-right: 10upx;\r\n\t\t\tfont-size: 22upx;\r\n\t\t\tcolor: $font-color-light;\r\n\t\t}\r\n\r\n\t\t.price {\r\n\t\t\tfont-size: $font-lg;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\tline-height: 1;\r\n\r\n\t\t\t&:before {\r\n\t\t\t\tcontent: '￥';\r\n\t\t\t\tfont-size: 26upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.list-box {\r\n\t\t// padding-bottom: 20rpx;\r\n\t\r\n\t\t.item-box {\r\n\t\t\t// margin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-bottom: 1rpx solid #ebebeb;\r\n\t\r\n\t\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.cover-box-hot {\r\n\t\t\t\t\twidth: 35%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.cover :after {\r\n\t\t\t\t\t\tbackground-color: red;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tcontent: \"hot\";\r\n\t\t\t\t\t\tfont-size: 25rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 5rpx;\r\n\t\t\t\t\t\ttop: 5rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 25%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\tmin-height: 120rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tmin-height: 150rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: blue;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.cover-large-box {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t.button {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttransform: translateX(0);\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, .5) !important;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\theight: auto;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.des {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\r\n\t\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: red;\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t.end {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #0070C0;\r\n\t\t\t\t\t\t// display: flex;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\t\t\t\t\t\t// align-items: center;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\t\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t:last-child {\r\n\t\t\t// border-bottom: 1rpx solid #fff;\r\n\t\t}\r\n\t}\r\n\t/* 空白页 */\r\n\t.empty {\r\n\t\t// position: fixed;\r\n\t\t// left: 0;\r\n\t\t// top: 0;\r\n\t\twidth: 100%;\r\n\t\t// height: 100vh;\r\n\t\tpadding-bottom: 100rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\r\n\t\timage {\r\n\t\t\twidth: 300rpx;\r\n\t\t\theight: 300rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\r\n\t\t.empty-tips {\r\n\t\t\tdisplay: flex;\r\n\t\t\tfont-size: $font-sm+2upx;\r\n\t\t\tcolor: $font-color-disabled;\r\n\r\n\t\t\t.navigator {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tmargin-left: 16upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.first {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: rgb(250, 250, 243);\r\n\t\twidth: 700rpx;\r\n\t\tmargin: 20rpx auto;\r\n\t\theight: 100rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.h2title {\r\n\t\tcolor: #000;\r\n\t\tdisplay: block;\r\n\t\tfont-size: 35rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-left: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./study.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./study.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754040521933\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}