{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/groups/course-review.vue?465a", "webpack:///D:/gst/gst-uniapp/pages/groups/course-review.vue?b6dd", "webpack:///D:/gst/gst-uniapp/pages/groups/course-review.vue?e4b3", "webpack:///D:/gst/gst-uniapp/pages/groups/course-review.vue?bb2b", "uni-app:///pages/groups/course-review.vue", "webpack:///D:/gst/gst-uniapp/pages/groups/course-review.vue?714f", "webpack:///D:/gst/gst-uniapp/pages/groups/course-review.vue?5900"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "groupId", "currentFilter", "filterList", "label", "value", "courseList", "id", "title", "description", "cover", "duration", "status", "progress", "studyTime", "computed", "filteredCourses", "onLoad", "methods", "loadCourses", "setTimeout", "changeFilter", "viewCourse", "uni", "icon", "url", "getStatusText"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA0mB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2D9nB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC,aACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,aACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAC;UACAf;UACAgB;QACA;QACA;MACA;;MAEA;MACAD;QACAE;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrLA;AAAA;AAAA;AAAA;AAAq5B,CAAgB,o4BAAG,EAAC,C;;;;;;;;;;;ACAz6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/course-review.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/course-review.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./course-review.vue?vue&type=template&id=43bcbcc8&scoped=true&\"\nvar renderjs\nimport script from \"./course-review.vue?vue&type=script&lang=js&\"\nexport * from \"./course-review.vue?vue&type=script&lang=js&\"\nimport style0 from \"./course-review.vue?vue&type=style&index=0&id=43bcbcc8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43bcbcc8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/course-review.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course-review.vue?vue&type=template&id=43bcbcc8&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredCourses, function (course, index) {\n    var $orig = _vm.__get_orig(course)\n    var m0 = _vm.getStatusText(course.status)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = _vm.filteredCourses.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course-review.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course-review.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\n\t\t\t<!-- 筛选栏 -->\n\t\t\t<view class=\"filter-bar\">\n\t\t\t\t<view class=\"filter-item\" \n\t\t\t\t\tv-for=\"(filter, index) in filterList\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t:class=\"{ active: currentFilter === filter.value }\"\n\t\t\t\t\t@click=\"changeFilter(filter.value)\"\n\t\t\t\t>\n\t\t\t\t\t{{filter.label}}\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 课程列表 -->\n\t\t\t<view class=\"course-list\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"course-item\" \n\t\t\t\t\tv-for=\"(course, index) in filteredCourses\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"viewCourse(course)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"course-cover\">\n\t\t\t\t\t\t<image :src=\"course.cover\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"course-duration\">{{course.duration}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"course-info\">\n\t\t\t\t\t\t<view class=\"course-title\">{{course.title}}</view>\n\t\t\t\t\t\t<view class=\"course-desc\">{{course.description}}</view>\n\t\t\t\t\t\t<view class=\"course-meta\">\n\t\t\t\t\t\t\t<text class=\"meta-tag\" :class=\"course.status\">{{getStatusText(course.status)}}</text>\n\t\t\t\t\t\t\t<text class=\"meta-time\">{{course.studyTime}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"course-progress\">\n\t\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: course.progress + '%' }\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"progress-text\">{{course.progress}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"course-action\">\n\t\t\t\t\t\t<text class=\"iconfont icon-play\" v-if=\"course.status === 'learning'\"></text>\n\t\t\t\t\t\t<text class=\"iconfont icon-check\" v-else-if=\"course.status === 'completed'\"></text>\n\t\t\t\t\t\t<text class=\"iconfont icon-lock\" v-else></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 空状态 -->\n\t\t\t<view class=\"empty-state\" v-if=\"filteredCourses.length === 0\">\n\t\t\t\t<image src=\"/static/imgs/empty-course.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text>暂无课程内容</text>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\tgroupId: '',\n\t\t\tcurrentFilter: 'all',\n\t\t\tfilterList: [\n\t\t\t\t{ label: '全部', value: 'all' },\n\t\t\t\t{ label: '学习中', value: 'learning' },\n\t\t\t\t{ label: '已完成', value: 'completed' },\n\t\t\t\t{ label: '未开始', value: 'pending' }\n\t\t\t],\n\t\t\tcourseList: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '第1课：五十音图（平假名）',\n\t\t\t\t\tdescription: '学习日语基础的平假名发音和书写',\n\t\t\t\t\tcover: '/static/imgs/course1.jpg',\n\t\t\t\t\tduration: '25:30',\n\t\t\t\t\tstatus: 'completed',\n\t\t\t\t\tprogress: 100,\n\t\t\t\t\tstudyTime: '2024-01-10'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '第2课：五十音图（片假名）',\n\t\t\t\t\tdescription: '学习日语基础的片假名发音和书写',\n\t\t\t\t\tcover: '/static/imgs/course2.jpg',\n\t\t\t\t\tduration: '28:15',\n\t\t\t\t\tstatus: 'completed',\n\t\t\t\t\tprogress: 100,\n\t\t\t\t\tstudyTime: '2024-01-12'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '第3课：基础问候语',\n\t\t\t\t\tdescription: '学习日常生活中的基本问候用语',\n\t\t\t\t\tcover: '/static/imgs/course3.jpg',\n\t\t\t\t\tduration: '22:45',\n\t\t\t\t\tstatus: 'learning',\n\t\t\t\t\tprogress: 75,\n\t\t\t\t\tstudyTime: '2024-01-15'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 4,\n\t\t\t\t\ttitle: '第4课：数字和时间表达',\n\t\t\t\t\tdescription: '学习数字的读法和时间的表达方式',\n\t\t\t\t\tcover: '/static/imgs/course4.jpg',\n\t\t\t\t\tduration: '30:20',\n\t\t\t\t\tstatus: 'learning',\n\t\t\t\t\tprogress: 45,\n\t\t\t\t\tstudyTime: '2024-01-16'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 5,\n\t\t\t\t\ttitle: '第5课：家族称呼',\n\t\t\t\t\tdescription: '学习家庭成员的称呼方式',\n\t\t\t\t\tcover: '/static/imgs/course5.jpg',\n\t\t\t\t\tduration: '26:10',\n\t\t\t\t\tstatus: 'pending',\n\t\t\t\t\tprogress: 0,\n\t\t\t\t\tstudyTime: ''\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\tcomputed: {\n\t\tfilteredCourses() {\n\t\t\tif (this.currentFilter === 'all') {\n\t\t\t\treturn this.courseList;\n\t\t\t}\n\t\t\treturn this.courseList.filter(course => course.status === this.currentFilter);\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.groupId) {\n\t\t\tthis.groupId = options.groupId;\n\t\t\tthis.loadCourses();\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载课程列表\n\t\tloadCourses() {\n\t\t\t// 这里应该根据groupId调用API获取课程列表\n\t\t\t// 暂时使用模拟数据\n\t\t\tthis.pageLoading = true;\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.pageLoading = false;\n\t\t\t}, 1000);\n\t\t},\n\n\t\t// 切换筛选条件\n\t\tchangeFilter(value) {\n\t\t\tthis.currentFilter = value;\n\t\t},\n\n\t\t// 查看课程详情\n\t\tviewCourse(course) {\n\t\t\tif (course.status === 'pending') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '课程尚未开放',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 跳转到课程播放页面\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/projects/course?courseId=${course.id}&groupId=${this.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'completed': '已完成',\n\t\t\t\t'learning': '学习中',\n\t\t\t\t'pending': '未开始'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知';\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.filter-bar {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tgap: 20rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.filter-item {\n\tpadding: 16rpx 32rpx;\n\tbackground: #f8f8f8;\n\tborder-radius: 30rpx;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\ttransition: all 0.3s;\n}\n\n.filter-item.active {\n\tbackground: #2094CE;\n\tcolor: #fff;\n}\n\n.course-list {\n\tpadding: 0 30rpx;\n}\n\n.course-item {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.course-cover {\n\twidth: 160rpx;\n\theight: 120rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tposition: relative;\n\tmargin-right: 20rpx;\n}\n\n.course-cover image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.course-duration {\n\tposition: absolute;\n\tbottom: 8rpx;\n\tright: 8rpx;\n\tbackground: rgba(0,0,0,0.7);\n\tcolor: #fff;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 20rpx;\n}\n\n.course-info {\n\tflex: 1;\n}\n\n.course-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.course-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 12rpx;\n\tline-height: 1.4;\n}\n\n.course-meta {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.meta-tag {\n\tfont-size: 22rpx;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 12rpx;\n}\n\n.meta-tag.completed {\n\tbackground: #e8f5e8;\n\tcolor: #52c41a;\n}\n\n.meta-tag.learning {\n\tbackground: #e6f7ff;\n\tcolor: #1890ff;\n}\n\n.meta-tag.pending {\n\tbackground: #f0f0f0;\n\tcolor: #999;\n}\n\n.meta-time {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.course-progress {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n}\n\n.progress-bar {\n\tflex: 1;\n\theight: 8rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 4rpx;\n\toverflow: hidden;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: #2094CE;\n\ttransition: width 0.3s;\n}\n\n.progress-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tmin-width: 60rpx;\n}\n\n.course-action {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-left: 20rpx;\n}\n\n.course-action .iconfont {\n\tfont-size: 36rpx;\n}\n\n.icon-play {\n\tcolor: #2094CE;\n}\n\n.icon-check {\n\tcolor: #52c41a;\n}\n\n.icon-lock {\n\tcolor: #ccc;\n}\n\n.empty-state {\n\ttext-align: center;\n\tpadding: 100rpx 30rpx;\n\tcolor: #999;\n}\n\n.empty-state image {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tmargin-bottom: 30rpx;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course-review.vue?vue&type=style&index=0&id=43bcbcc8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course-review.vue?vue&type=style&index=0&id=43bcbcc8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753689556116\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}