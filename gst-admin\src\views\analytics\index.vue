<template>
  <div class="page-container">
    <div class="page-header">
      <h1>分析报告</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportReport" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="loadAnalytics"
          />
        </el-form-item>
        <el-form-item label="报告类型">
          <el-select v-model="filters.reportType" @change="loadAnalytics">
            <el-option label="用户分析" value="user" />
            <el-option label="课程分析" value="course" />
            <el-option label="学习分析" value="learning" />
            <el-option label="收入分析" value="revenue" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 概览统计 -->
    <el-row :gutter="20" class="overview-row">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overview.totalUsers }}</div>
              <div class="overview-label">总用户数</div>
              <div class="overview-trend" :class="{ positive: overview.userGrowth > 0 }">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.userGrowth > 0 ? '+' : '' }}{{ overview.userGrowth }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon course-icon">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overview.totalCourses }}</div>
              <div class="overview-label">总课程数</div>
              <div class="overview-trend" :class="{ positive: overview.courseGrowth > 0 }">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.courseGrowth > 0 ? '+' : '' }}{{ overview.courseGrowth }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon learning-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ overview.totalLearningTime }}</div>
              <div class="overview-label">总学习时长(小时)</div>
              <div class="overview-trend" :class="{ positive: overview.learningGrowth > 0 }">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.learningGrowth > 0 ? '+' : '' }}{{ overview.learningGrowth }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon revenue-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">¥{{ overview.totalRevenue }}</div>
              <div class="overview-label">总收入</div>
              <div class="overview-trend" :class="{ positive: overview.revenueGrowth > 0 }">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.revenueGrowth > 0 ? '+' : '' }}{{ overview.revenueGrowth }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
            </div>
          </template>
          <div id="userGrowthChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>课程完成率</span>
            </div>
          </template>
          <div id="courseCompletionChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户分布</span>
            </div>
          </template>
          <div id="userDistributionChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>热门课程</span>
            </div>
          </template>
          <div id="popularCoursesChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>学习时段分布</span>
            </div>
          </template>
          <div id="learningTimeChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>详细数据</span>
          <el-button-group>
            <el-button 
              v-for="tab in dataTabs" 
              :key="tab.key"
              :type="activeDataTab === tab.key ? 'primary' : ''"
              @click="switchDataTab(tab.key)"
            >
              {{ tab.label }}
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 用户数据表格 -->
      <el-table v-if="activeDataTab === 'users'" :data="userData" v-loading="loading">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="newUsers" label="新增用户" width="100" />
        <el-table-column prop="activeUsers" label="活跃用户" width="100" />
        <el-table-column prop="retentionRate" label="留存率" width="100">
          <template #default="{ row }">
            {{ row.retentionRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="avgSessionTime" label="平均会话时长" width="120" />
      </el-table>

      <!-- 课程数据表格 -->
      <el-table v-if="activeDataTab === 'courses'" :data="courseData" v-loading="loading">
        <el-table-column prop="courseName" label="课程名称" min-width="200" />
        <el-table-column prop="enrollments" label="报名人数" width="100" />
        <el-table-column prop="completions" label="完成人数" width="100" />
        <el-table-column prop="completionRate" label="完成率" width="100">
          <template #default="{ row }">
            {{ row.completionRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="avgRating" label="平均评分" width="100" />
        <el-table-column prop="revenue" label="收入" width="120">
          <template #default="{ row }">
            ¥{{ row.revenue }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 学习数据表格 -->
      <el-table v-if="activeDataTab === 'learning'" :data="learningData" v-loading="loading">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="totalLearningTime" label="总学习时长" width="120" />
        <el-table-column prop="avgLearningTime" label="平均学习时长" width="120" />
        <el-table-column prop="completedLessons" label="完成课时" width="100" />
        <el-table-column prop="activeStudents" label="活跃学员" width="100" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { get } from '@/utils/request'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  Download, 
  User, 
  Reading, 
  Timer, 
  Money,
  TrendCharts
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const activeDataTab = ref('users')

// 筛选器
const filters = reactive({
  dateRange: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()],
  reportType: 'user'
})

// 概览数据
const overview = reactive({
  totalUsers: 0,
  userGrowth: 0,
  totalCourses: 0,
  courseGrowth: 0,
  totalLearningTime: 0,
  learningGrowth: 0,
  totalRevenue: 0,
  revenueGrowth: 0
})

// 表格数据
const userData = ref([])
const courseData = ref([])
const learningData = ref([])

// 数据标签页
const dataTabs = [
  { key: 'users', label: '用户数据' },
  { key: 'courses', label: '课程数据' },
  { key: 'learning', label: '学习数据' }
]

// 加载分析数据
const loadAnalytics = async () => {
  loading.value = true
  try {
    const params = {
      startDate: filters.dateRange?.[0],
      endDate: filters.dateRange?.[1],
      type: filters.reportType
    }
    
    const response = await get('/api/analytics', params)
    if (response.success) {
      Object.assign(overview, response.data.overview || {})
      userData.value = response.data.userData || []
      courseData.value = response.data.courseData || []
      learningData.value = response.data.learningData || []
      
      // 渲染图表
      await nextTick()
      renderCharts()
    }
  } catch (error) {
    console.error('加载分析数据失败:', error)
    ElMessage.error('加载分析数据失败')
    
    // 使用模拟数据
    generateMockData()
    await nextTick()
    renderCharts()
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  Object.assign(overview, {
    totalUsers: 12580,
    userGrowth: 15.6,
    totalCourses: 156,
    courseGrowth: 8.3,
    totalLearningTime: 45680,
    learningGrowth: 22.1,
    totalRevenue: 256800,
    revenueGrowth: 18.9
  })

  userData.value = Array.from({ length: 30 }, (_, i) => ({
    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
    newUsers: Math.floor(Math.random() * 50) + 10,
    activeUsers: Math.floor(Math.random() * 200) + 100,
    retentionRate: Math.floor(Math.random() * 30) + 60,
    avgSessionTime: `${Math.floor(Math.random() * 30) + 15}分钟`
  }))

  courseData.value = [
    { courseName: 'N5基础日语', enrollments: 1250, completions: 980, completionRate: 78.4, avgRating: 4.6, revenue: 125000 },
    { courseName: 'N4进阶日语', enrollments: 890, completions: 720, completionRate: 80.9, avgRating: 4.7, revenue: 89000 },
    { courseName: '商务日语', enrollments: 560, completions: 420, completionRate: 75.0, avgRating: 4.5, revenue: 84000 }
  ]

  learningData.value = Array.from({ length: 30 }, (_, i) => ({
    date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
    totalLearningTime: `${Math.floor(Math.random() * 500) + 800}小时`,
    avgLearningTime: `${Math.floor(Math.random() * 30) + 45}分钟`,
    completedLessons: Math.floor(Math.random() * 100) + 200,
    activeStudents: Math.floor(Math.random() * 50) + 150
  }))
}

// 渲染图表
const renderCharts = () => {
  // 这里可以集成图表库如ECharts来渲染图表
  console.log('渲染图表...')
}

// 切换数据标签页
const switchDataTab = (tab) => {
  activeDataTab.value = tab
}

// 导出报告
const exportReport = async () => {
  exporting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  } finally {
    exporting.value = false
  }
}

const refreshData = () => {
  loadAnalytics()
}

// 组件挂载时加载数据
onMounted(() => {
  loadAnalytics()
})
</script>

<style lang="scss" scoped>
.overview-row, .charts-row {
  margin-bottom: 20px;
}

.overview-card {
  .overview-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .overview-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;

    &.user-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    &.course-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    &.learning-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &.revenue-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }

  .overview-info {
    flex: 1;

    .overview-value {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }

    .overview-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
    }

    .overview-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--el-color-danger);

      &.positive {
        color: var(--el-color-success);
      }
    }
  }
}

.chart-card {
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
    background: var(--el-fill-color-lighter);
    border-radius: 6px;
  }
}

.data-table-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
