<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序API测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #4A90E2;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 10px;
        }
        .api-test {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .api-url {
            font-family: monospace;
            background: #f8f8f8;
            padding: 8px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #357ABD;
        }
        .result {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #4CAF50;
            background-color: #f1f8e9;
        }
        .error {
            border-color: #f44336;
            background-color: #ffebee;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.success {
            color: #4CAF50;
        }
        .status.error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>🎓 GST日语培训班小程序API测试</h1>
    
    <div class="container">
        <h2>📱 小程序首页API</h2>
        <div class="api-test">
            <h3>首页数据 (v1/course/index)</h3>
            <div class="api-url">GET /api/v1/course/index</div>
            <button onclick="testHomeAPI()">测试首页API</button>
            <div id="home-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📚 课程相关API</h2>
        <div class="api-test">
            <h3>课程详情 (index/courses/index)</h3>
            <div class="api-url">POST /api/v1/index/courses/index</div>
            <button onclick="testCourseDetailAPI()">测试课程详情API</button>
            <div id="course-detail-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="api-test">
            <h3>课程列表 (course/list)</h3>
            <div class="api-url">GET /api/v1/course/list</div>
            <button onclick="testCourseListAPI()">测试课程列表API</button>
            <div id="course-list-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>👥 小组相关API</h2>
        <div class="api-test">
            <h3>小组数据 (miniprogram/groups)</h3>
            <div class="api-url">GET /api/miniprogram/groups</div>
            <button onclick="testGroupsAPI()">测试小组API</button>
            <div id="groups-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>👤 用户相关API</h2>
        <div class="api-test">
            <h3>会员信息 (v1/member)</h3>
            <div class="api-url">GET /api/v1/member</div>
            <button onclick="testMemberAPI()">测试会员信息API</button>
            <div id="member-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-test">
            <h3>我的课程 (index/user/my_course)</h3>
            <div class="api-url">POST /api/v1/index/user/my_course</div>
            <button onclick="testMyCourseAPI()">测试我的课程API</button>
            <div id="my-course-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-test">
            <h3>我的收藏 (index/user/my_dingyue)</h3>
            <div class="api-url">POST /api/v1/index/user/my_dingyue</div>
            <button onclick="testMyFavoriteAPI()">测试我的收藏API</button>
            <div id="my-favorite-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🏷️ 分类相关API</h2>
        <div class="api-test">
            <h3>课程分类 (course/getCate)</h3>
            <div class="api-url">GET /api/v1/course/getCate</div>
            <button onclick="testCategoriesAPI()">测试课程分类API</button>
            <div id="categories-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-test">
            <h3>子分类 (course/getSubClass)</h3>
            <div class="api-url">GET /api/v1/course/getSubClass?id=2</div>
            <button onclick="testSubCategoriesAPI()">测试子分类API</button>
            <div id="sub-categories-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔐 认证相关API</h2>
        <div class="api-test">
            <h3>微信登录 (auth/wxlogin)</h3>
            <div class="api-url">POST /api/auth/wxlogin</div>
            <button onclick="testWxLoginAPI()">测试微信登录API</button>
            <div id="wx-login-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8005/api';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'result success';
                element.innerHTML = `
                    <div class="status success">✅ 请求成功 (${result.status})</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'result error';
                element.innerHTML = `
                    <div class="status error">❌ 请求失败</div>
                    <pre>${result.error || JSON.stringify(result.data, null, 2)}</pre>
                `;
            }
        }

        async function testHomeAPI() {
            const result = await makeRequest('/v1/course/index');
            displayResult('home-result', result);
        }

        async function testCourseDetailAPI() {
            const result = await makeRequest('/v1/index/courses/index', {
                method: 'POST',
                body: JSON.stringify({ id: 1 })
            });
            displayResult('course-detail-result', result);
        }

        async function testCourseListAPI() {
            const result = await makeRequest('/v1/course/list');
            displayResult('course-list-result', result);
        }

        async function testGroupsAPI() {
            const result = await makeRequest('/miniprogram/groups');
            displayResult('groups-result', result);
        }

        async function testMyCourseAPI() {
            const result = await makeRequest('/v1/index/user/my_course', {
                method: 'POST',
                body: JSON.stringify({ uid: 123, page: 1, limit: 10 })
            });
            displayResult('my-course-result', result);
        }

        async function testMyFavoriteAPI() {
            const result = await makeRequest('/v1/index/user/my_dingyue', {
                method: 'POST',
                body: JSON.stringify({ uid: 123, page: 1, limit: 10 })
            });
            displayResult('my-favorite-result', result);
        }

        async function testMemberAPI() {
            const result = await makeRequest('/v1/member');
            displayResult('member-result', result);
        }

        async function testCategoriesAPI() {
            const result = await makeRequest('/v1/course/getCate');
            displayResult('categories-result', result);
        }

        async function testSubCategoriesAPI() {
            const result = await makeRequest('/v1/course/getSubClass?id=2');
            displayResult('sub-categories-result', result);
        }

        async function testWxLoginAPI() {
            const result = await makeRequest('/auth/wxlogin', {
                method: 'POST',
                body: JSON.stringify({
                    code: 'test_code_123',
                    type: 'class',
                    iv: 'test_iv',
                    encryptedData: 'test_encrypted_data'
                })
            });
            displayResult('wx-login-result', result);
        }

        // 页面加载时自动测试首页API
        window.onload = function() {
            console.log('🚀 小程序API测试页面已加载');
            console.log('📡 API基础地址:', API_BASE);
        };
    </script>
</body>
</html>
