{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/gst/gst-uniapp/pages/groups/index_clean.vue?bedf", "webpack:///D:/gst/gst-uniapp/pages/groups/index_clean.vue?0d9e", "webpack:///D:/gst/gst-uniapp/pages/groups/index_clean.vue?2fdc", "uni-app:///pages/groups/index_clean.vue", "webpack:///D:/gst/gst-uniapp/pages/groups/index_clean.vue?6935", "webpack:///D:/gst/gst-uniapp/pages/groups/index_clean.vue?291e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "hasGroupPermission", "selectedGroupId", "selectedGroup", "selectedGroupIndex", "conceptTutorial", "id", "title", "description", "totalLessons", "completedLessons", "categories", "name", "icon", "lessons", "groupList", "level", "status", "members", "courses", "progress", "completedCourses", "totalCourses", "computed", "totalMembers", "onLoad", "onShow", "methods", "checkPermission", "console", "userToken", "userInfo", "<PERSON><PERSON><PERSON><PERSON>", "checkGroupAccess", "isLoggedIn", "uni", "duration", "initializeData", "goToLogin", "url", "contactAdmin", "content", "showCancel", "confirmText", "selectGroupForDetail", "selectConceptTutorial", "getGroupColor", "enterGroup", "viewGroupProgress", "enterConceptCategory", "startConceptLearning", "continueConceptLearning", "viewConceptProgress"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmP5nB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,aACA;UACAL;UACAM;UACAJ;UACAK;UACAC;QACA,GACA;UACAR;UACAM;UACAJ;UACAK;UACAC;QACA,GACA;UACAR;UACAM;UACAJ;UACAK;UACAC;QACA,GACA;UACAR;UACAM;UACAJ;UACAK;UACAC;QACA;MAEA;MACA;MACAC,YACA;QACAT;QACAM;QACAJ;QACAQ;QACAC;QACAJ;QACAK;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAhB;QACAM;QACAJ;QACAQ;QACAC;QACAJ;QACAK;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAhB;QACAM;QACAJ;QACAQ;QACAC;QACAJ;QACAK;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;;MAEA;MACA;MACA;MACA;MAEAA;QACAC;QACAC;QACAC;MACA;MAEA;QACAH;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;MACA;MAEAJ;MACAA;MACAA;MACAA;QAAAG;QAAAE;MAAA;;MAEA;MACA;QACA;QACAL;QACA;MACA;;MAEA;MACA;QACA;QACAA;MACA;QACA;QACAA;MACA;MAEAA;;MAEA;MACA;MAEA;QACAA;QACA;QACAM;UACA5B;UACAM;UACAuB;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACAH;QACAI;MACA;IACA;IAEA;IACAC;MACAL;QACA5B;QACAkC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA,cACA,6CACA,6CACA,6CACA,6CACA,4CACA;MACA;IACA;IAEA;IACAC;MACAZ;QACA5B;QACAM;MACA;IACA;IAEA;IACAmC;MACAb;QACA5B;QACAM;MACA;IACA;IAEA;IACAoC;MACAd;QACA5B;QACAM;MACA;IACA;IAEAqC;MACAf;QACA5B;QACAM;MACA;IACA;IAEAsC;MACAhB;QACA5B;QACAM;MACA;IACA;IAEAuC;MACAjB;QACA5B;QACAM;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/fA;AAAA;AAAA;AAAA;AAAm5B,CAAgB,k4BAAG,EAAC,C;;;;;;;;;;;ACAv6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/index_clean.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/index_clean.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index_clean.vue?vue&type=template&id=59cfa49a&scoped=true&\"\nvar renderjs\nimport script from \"./index_clean.vue?vue&type=script&lang=js&\"\nexport * from \"./index_clean.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index_clean.vue?vue&type=style&index=0&id=59cfa49a&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59cfa49a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/index_clean.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index_clean.vue?vue&type=template&id=59cfa49a&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hasGroupPermission ? _vm.groupList.length : null\n  var g1 = _vm.hasGroupPermission ? _vm.groupList.length : null\n  var l0 = _vm.hasGroupPermission\n    ? _vm.__map(_vm.groupList, function (group, index) {\n        var $orig = _vm.__get_orig(group)\n        var m0 = _vm.getGroupColor(index)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 =\n    _vm.hasGroupPermission &&\n    !(_vm.selectedGroupId === \"concept\") &&\n    _vm.selectedGroup\n      ? _vm.getGroupColor(_vm.selectedGroupIndex)\n      : null\n  var m2 =\n    _vm.hasGroupPermission &&\n    !(_vm.selectedGroupId === \"concept\") &&\n    _vm.selectedGroup\n      ? _vm.getGroupColor(_vm.selectedGroupIndex)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index_clean.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index_clean.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"groups-page\">\n\t\t\t<!-- 有权限时显示内容 -->\n\t\t\t<view v-if=\"hasGroupPermission\" class=\"page-content\">\n\t\t\t\t<!-- 页面头部 -->\n\t\t\t\t<view class=\"page-header\">\n\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t<view class=\"title-section\">\n\t\t\t\t\t\t\t<text class=\"page-title\">🎓 GST派遣日语培训班</text>\n\t\t\t\t\t\t\t<text class=\"page-subtitle\">与同伴一起进步，共同成长</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stats-section\">\n\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">{{groupList.length}}</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">个小组</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">{{totalMembers}}</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">名成员</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">48</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">门课程</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 左右联动布局 -->\n\t\t\t\t<view class=\"split-layout\">\n\t\t\t\t\t<!-- 左侧小组列表 -->\n\t\t\t\t\t<view class=\"left-panel\">\n\t\t\t\t\t\t<view class=\"panel-header\">\n\t\t\t\t\t\t\t<text class=\"panel-title\">学习内容</text>\n\t\t\t\t\t\t\t<text class=\"panel-subtitle\">{{groupList.length + 1}}项</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<scroll-view class=\"group-list\" scroll-y=\"true\">\n\t\t\t\t\t\t\t<!-- 公共新概念教程 -->\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"concept-tutorial-item\"\n\t\t\t\t\t\t\t\t:class=\"{ 'active': selectedGroupId === 'concept' }\"\n\t\t\t\t\t\t\t\t@click=\"selectConceptTutorial()\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<view class=\"concept-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"concept-icon\">📖</view>\n\t\t\t\t\t\t\t\t\t<text class=\"concept-title\">新概念教程</text>\n\t\t\t\t\t\t\t\t\t<view class=\"concept-badge\">公共</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"group-item\"\n\t\t\t\t\t\t\t\tv-for=\"(group, index) in groupList\"\n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t:class=\"{ 'active': selectedGroupId === group.id }\"\n\t\t\t\t\t\t\t\t@click=\"selectGroupForDetail(group, index)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<view class=\"simple-group-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"group-level-badge\" :style=\"{ background: getGroupColor(index) }\">\n\t\t\t\t\t\t\t\t\t\t{{group.level}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text class=\"simple-group-name\">{{group.name}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"simple-status-dot\" :class=\"group.status\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 右侧详情面板 -->\n\t\t\t\t\t<view class=\"right-panel\">\n\t\t\t\t\t\t<!-- 新概念教程详情 -->\n\t\t\t\t\t\t<view v-if=\"selectedGroupId === 'concept'\" class=\"concept-detail-content\">\n\t\t\t\t\t\t\t<!-- 教程头部 -->\n\t\t\t\t\t\t\t<view class=\"concept-detail-header\">\n\t\t\t\t\t\t\t\t<view class=\"concept-bg\">\n\t\t\t\t\t\t\t\t\t<view class=\"concept-overlay\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-detail-icon\">📖</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-detail-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-detail-title\">{{conceptTutorial.title}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-detail-subtitle\">{{conceptTutorial.description}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"concept-progress-info\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"progress-info-text\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{conceptTutorial.completedLessons}} / {{conceptTutorial.totalLessons}} 课时\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 学习分类 -->\n\t\t\t\t\t\t\t<view class=\"concept-categories\">\n\t\t\t\t\t\t\t\t<view class=\"categories-title\">学习分类</view>\n\t\t\t\t\t\t\t\t<view class=\"categories-grid\">\n\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\tclass=\"category-card\"\n\t\t\t\t\t\t\t\t\t\tv-for=\"(category, index) in conceptTutorial.categories\"\n\t\t\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t\t\t@click=\"enterConceptCategory(category)\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<view class=\"category-icon\">{{category.icon}}</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"category-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"category-name\">{{category.name}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"category-desc\">{{category.description}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"category-lessons\">{{category.lessons}}课时</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"category-arrow\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 快速操作 -->\n\t\t\t\t\t\t\t<view class=\"concept-actions\">\n\t\t\t\t\t\t\t\t<view class=\"concept-action-title\">快速开始</view>\n\t\t\t\t\t\t\t\t<view class=\"concept-action-buttons\">\n\t\t\t\t\t\t\t\t\t<view class=\"concept-btn primary\" @click=\"startConceptLearning()\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-icon\">🚀</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-title\">开始学习</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-desc\">从第一课开始</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"concept-btn secondary\" @click=\"continueConceptLearning()\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-icon\">📖</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-title\">继续学习</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-desc\">从上次位置继续</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"concept-btn tertiary\" @click=\"viewConceptProgress()\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-icon\">📊</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-title\">学习进度</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-desc\">查看详细进度</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 小组详情 -->\n\t\t\t\t\t\t<view v-else-if=\"selectedGroup\" class=\"detail-content\">\n\t\t\t\t\t\t\t<!-- 详情头部 -->\n\t\t\t\t\t\t\t<view class=\"detail-header\">\n\t\t\t\t\t\t\t\t<view class=\"detail-bg\" :style=\"{ background: getGroupColor(selectedGroupIndex) }\">\n\t\t\t\t\t\t\t\t\t<view class=\"detail-overlay\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-avatar\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"selectedGroup.icon\" mode=\"aspectFit\" />\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-title\">{{selectedGroup.name}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-subtitle\">{{selectedGroup.description}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"detail-level\">{{selectedGroup.level}}等级</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 统计卡片 -->\n\t\t\t\t\t\t\t<view class=\"detail-stats\">\n\t\t\t\t\t\t\t\t<view class=\"stat-card-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"stat-icon-detail\">👥</view>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-number-detail\">{{selectedGroup.members}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-label-detail\">成员</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"stat-card-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"stat-icon-detail\">📚</view>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-number-detail\">{{selectedGroup.courses}}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-label-detail\">课程</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"stat-card-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"stat-icon-detail\">⭐</view>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-number-detail\">{{selectedGroup.progress}}%</text>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-label-detail\">进度</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 进度详情 -->\n\t\t\t\t\t\t\t<view class=\"progress-detail\">\n\t\t\t\t\t\t\t\t<view class=\"progress-header\">\n\t\t\t\t\t\t\t\t\t<text class=\"progress-title\">学习进度</text>\n\t\t\t\t\t\t\t\t\t<text class=\"progress-value\">{{selectedGroup.progress}}%</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"progress-bar-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"progress-fill-detail\" :style=\"{ width: selectedGroup.progress + '%', background: getGroupColor(selectedGroupIndex) }\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"progress-desc\">已完成 {{selectedGroup.completedCourses}} / {{selectedGroup.totalCourses}} 门课程</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 功能按钮 -->\n\t\t\t\t\t\t\t<view class=\"detail-actions\">\n\t\t\t\t\t\t\t\t<view class=\"action-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn-large primary\" @click=\"enterGroup(selectedGroup)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-icon-large\">🚀</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-title\">进入小组</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-desc\">开始学习</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-arrow-large\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn-large secondary\" @click=\"viewGroupProgress(selectedGroup)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-icon-large\">📊</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-title\">学习进度</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-desc\">查看详细进度</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-arrow-large\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 未选择状态 -->\n\t\t\t\t\t\t<view v-else class=\"empty-detail\">\n\t\t\t\t\t\t\t<view class=\"empty-icon\">👈</view>\n\t\t\t\t\t\t\t<text class=\"empty-title\">选择一个小组</text>\n\t\t\t\t\t\t\t<text class=\"empty-desc\">点击左侧小组查看详细信息</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 无权限提示 -->\n\t\t\t<view v-else class=\"no-permission-page\">\n\t\t\t\t<view class=\"permission-container\">\n\t\t\t\t\t<view class=\"permission-icon\">🔒</view>\n\t\t\t\t\t<text class=\"permission-title\">访问受限</text>\n\t\t\t\t\t<text class=\"permission-desc\">您暂时没有访问学习小组的权限</text>\n\t\t\t\t\t<text class=\"permission-hint\">请联系管理员开通权限或使用授权账号登录</text>\n\t\t\t\t\t<view class=\"permission-actions\">\n\t\t\t\t\t\t<button class=\"btn-login\" @click=\"goToLogin\">重新登录</button>\n\t\t\t\t\t\t<button class=\"btn-contact\" @click=\"contactAdmin\">联系管理员</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\thasGroupPermission: false,\n\t\t\tselectedGroupId: null,\n\t\t\tselectedGroup: null,\n\t\t\tselectedGroupIndex: 0,\n\t\t\t// 新概念教程数据\n\t\t\tconceptTutorial: {\n\t\t\t\tid: 'concept',\n\t\t\t\ttitle: '新概念英语教程',\n\t\t\t\tdescription: '从基础到高级的系统化英语学习',\n\t\t\t\ttotalLessons: 144,\n\t\t\t\tcompletedLessons: 32,\n\t\t\t\tcategories: [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'basic',\n\t\t\t\t\t\tname: '基础语法',\n\t\t\t\t\t\tdescription: '掌握基本语法结构',\n\t\t\t\t\t\ticon: '📝',\n\t\t\t\t\t\tlessons: 36\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'vocabulary',\n\t\t\t\t\t\tname: '词汇积累',\n\t\t\t\t\t\tdescription: '扩展词汇量',\n\t\t\t\t\t\ticon: '📚',\n\t\t\t\t\t\tlessons: 48\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'listening',\n\t\t\t\t\t\tname: '听力训练',\n\t\t\t\t\t\tdescription: '提升听力理解能力',\n\t\t\t\t\t\ticon: '🎧',\n\t\t\t\t\t\tlessons: 36\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'speaking',\n\t\t\t\t\t\tname: '口语练习',\n\t\t\t\t\t\tdescription: '提高口语表达能力',\n\t\t\t\t\t\ticon: '🗣️',\n\t\t\t\t\t\tlessons: 24\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t},\n\t\t\t// 小组数据\n\t\t\tgroupList: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\tname: 'N5基础班',\n\t\t\t\t\tdescription: '日语入门基础学习',\n\t\t\t\t\tlevel: 'N5',\n\t\t\t\t\tstatus: 'active',\n\t\t\t\t\ticon: '/static/imgs/group1.png',\n\t\t\t\t\tmembers: 25,\n\t\t\t\t\tcourses: 12,\n\t\t\t\t\tprogress: 75,\n\t\t\t\t\tcompletedCourses: 9,\n\t\t\t\t\ttotalCourses: 12\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\tname: 'N4进阶班',\n\t\t\t\t\tdescription: '日语进阶学习',\n\t\t\t\t\tlevel: 'N4',\n\t\t\t\t\tstatus: 'completed',\n\t\t\t\t\ticon: '/static/imgs/group2.png',\n\t\t\t\t\tmembers: 18,\n\t\t\t\t\tcourses: 15,\n\t\t\t\t\tprogress: 100,\n\t\t\t\t\tcompletedCourses: 15,\n\t\t\t\t\ttotalCourses: 15\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\tname: 'N3中级班',\n\t\t\t\t\tdescription: '日语中级学习',\n\t\t\t\t\tlevel: 'N3',\n\t\t\t\t\tstatus: 'pending',\n\t\t\t\t\ticon: '/static/imgs/group3.png',\n\t\t\t\t\tmembers: 12,\n\t\t\t\t\tcourses: 18,\n\t\t\t\t\tprogress: 45,\n\t\t\t\t\tcompletedCourses: 8,\n\t\t\t\t\ttotalCourses: 18\n\t\t\t\t}\n\t\t\t]\n\t\t};\n\t},\n\tcomputed: {\n\t\ttotalMembers() {\n\t\t\treturn this.groupList.reduce((total, group) => total + group.members, 0);\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.checkPermission();\n\t\tthis.initializeData();\n\t},\n\tonShow() {\n\t\t// 每次显示页面时都检查权限，确保权限状态是最新的\n\t\tthis.checkPermission();\n\t},\n\tmethods: {\n\t\t// 检查用户权限\n\t\tcheckPermission() {\n\t\t\tconsole.log('=== 开始检查小组权限 ===');\n\t\t\t\n\t\t\t// 检查用户是否登录\n\t\t\tconst userToken = this.$store.state.user.token;\n\t\t\tconst userInfo = this.$store.state.user.userInfo;\n\t\t\tconst hasLogin = this.$store.getters.hasLogin;\n\t\t\t\n\t\t\tconsole.log('权限检查数据:', {\n\t\t\t\tuserToken: userToken ? '存在' : '不存在',\n\t\t\t\tuserInfo: userInfo,\n\t\t\t\thasLogin: hasLogin\n\t\t\t});\n\t\t\t\n\t\t\tif (!userToken && !hasLogin) {\n\t\t\t\tconsole.log('用户未登录，拒绝访问');\n\t\t\t\tthis.hasGroupPermission = false;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查用户是否有小组权限\n\t\t\tthis.checkGroupAccess();\n\t\t},\n\t\t\n\t\t// 检查小组访问权限\n\t\tcheckGroupAccess() {\n\t\t\tconst userInfo = this.$store.state.user.userInfo;\n\t\t\tconst userMember = this.$store.state.user.member;\n\t\t\tconst hasLogin = this.$store.getters.hasLogin;\n\t\t\tconst isLoggedIn = this.$store.getters.isLoggedIn;\n\n\t\t\tconsole.log('=== 小组权限详细检查 ===');\n\t\t\tconsole.log('用户信息:', userInfo);\n\t\t\tconsole.log('会员信息:', userMember);\n\t\t\tconsole.log('登录状态:', { hasLogin, isLoggedIn });\n\n\t\t\t// 为彭伟用户(ID: 576)特别开放权限\n\t\t\tif (userInfo && (userInfo.id === 576 || userInfo.name === '彭伟')) {\n\t\t\t\tthis.hasGroupPermission = true;\n\t\t\t\tconsole.log('✅ 为用户彭伟开放小组权限');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 一般权限检查逻辑 - 只要登录就可以访问\n\t\t\tif (hasLogin || isLoggedIn || userInfo) {\n\t\t\t\tthis.hasGroupPermission = true;\n\t\t\t\tconsole.log('✅ 登录用户可以访问小组功能');\n\t\t\t} else {\n\t\t\t\tthis.hasGroupPermission = false;\n\t\t\t\tconsole.log('❌ 用户未登录，无法访问小组功能');\n\t\t\t}\n\n\t\t\tconsole.log('=== 最终权限结果:', this.hasGroupPermission, '===');\n\t\t\t\n\t\t\t// 强制触发视图更新\n\t\t\tthis.$forceUpdate();\n\n\t\t\tif (!this.hasGroupPermission) {\n\t\t\t\tconsole.log('权限被拒绝，用户信息:', userInfo);\n\t\t\t\t// 显示提示信息\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 初始化数据\n\t\tinitializeData() {\n\t\t\t// 默认选择第一个小组\n\t\t\tif (this.groupList.length > 0) {\n\t\t\t\tthis.selectGroupForDetail(this.groupList[0], 0);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到登录页面\n\t\tgoToLogin() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login/login'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 联系管理员\n\t\tcontactAdmin() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '联系管理员',\n\t\t\t\tcontent: '请通过以下方式联系管理员开通权限：\\n\\n微信：admin123\\n电话：400-123-4567\\n邮箱：<EMAIL>',\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '我知道了'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 选择小组查看详情\n\t\tselectGroupForDetail(group, index) {\n\t\t\tthis.selectedGroupId = group.id;\n\t\t\tthis.selectedGroup = group;\n\t\t\tthis.selectedGroupIndex = index;\n\t\t},\n\t\t\n\t\t// 选择新概念教程\n\t\tselectConceptTutorial() {\n\t\t\tthis.selectedGroupId = 'concept';\n\t\t\tthis.selectedGroup = null;\n\t\t},\n\t\t\n\t\t// 获取小组颜色\n\t\tgetGroupColor(index) {\n\t\t\tconst colors = [\n\t\t\t\t'linear-gradient(135deg, #667eea, #764ba2)',\n\t\t\t\t'linear-gradient(135deg, #f093fb, #f5576c)',\n\t\t\t\t'linear-gradient(135deg, #4facfe, #00f2fe)',\n\t\t\t\t'linear-gradient(135deg, #43e97b, #38f9d7)',\n\t\t\t\t'linear-gradient(135deg, #fa709a, #fee140)'\n\t\t\t];\n\t\t\treturn colors[index % colors.length];\n\t\t},\n\t\t\n\t\t// 进入小组\n\t\tenterGroup(group) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: `进入${group.name}`,\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 查看小组进度\n\t\tviewGroupProgress(group) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: `查看${group.name}进度`,\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 新概念教程相关方法\n\t\tenterConceptCategory(category) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: `进入${category.name}`,\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\tstartConceptLearning() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '开始新概念学习',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\tcontinueConceptLearning() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '继续新概念学习',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t},\n\t\t\n\t\tviewConceptProgress() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '查看新概念进度',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n/* ===== 基础页面样式 ===== */\n.groups-page {\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n\tmin-height: 100vh;\n}\n\n.page-content {\n\tposition: relative;\n\tz-index: 1;\n}\n\n/* ===== 页面头部样式 ===== */\n.page-header {\n\tbackground: white;\n\tborder-radius: 0 0 30rpx 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.header-content {\n\tpadding: 40rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.title-section {\n\tflex: 1;\n}\n\n.page-title {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.page-subtitle {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n.stats-section {\n\tdisplay: flex;\n\tgap: 25rpx;\n}\n\n.stat-item {\n\ttext-align: center;\n}\n\n.stat-number {\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tcolor: #667eea;\n\tdisplay: block;\n\tline-height: 1;\n}\n\n.stat-label {\n\tfont-size: 18rpx;\n\tcolor: #999;\n\tmargin-top: 5rpx;\n}\n\n/* ===== 左右联动布局 ===== */\n.split-layout {\n\tdisplay: flex;\n\theight: calc(100vh - 200rpx);\n\tbackground: white;\n\tborder-radius: 20rpx 20rpx 0 0;\n\toverflow: hidden;\n\tmargin: 0 20rpx;\n\tbox-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);\n}\n\n/* ===== 左侧面板 ===== */\n.left-panel {\n\twidth: 200rpx;\n\tbackground: #f8f9fa;\n\tborder-right: 1rpx solid #e9ecef;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.panel-header {\n\tpadding: 25rpx 15rpx 20rpx;\n\tbackground: white;\n\tborder-bottom: 1rpx solid #e9ecef;\n\ttext-align: center;\n}\n\n.panel-title {\n\tfont-size: 22rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.panel-subtitle {\n\tfont-size: 18rpx;\n\tcolor: #999;\n}\n\n.group-list {\n\tflex: 1;\n\tpadding: 10rpx 0;\n}\n\n/* ===== 小组列表项 ===== */\n.group-item {\n\tmargin: 0 10rpx 15rpx;\n\tbackground: white;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx 15rpx;\n\ttransition: all 0.3s ease;\n\tborder: 2rpx solid transparent;\n\ttext-align: center;\n\tposition: relative;\n}\n\n.group-item.active {\n\tborder-color: #667eea;\n\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));\n\ttransform: scale(1.05);\n\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n}\n\n.group-item:active {\n\ttransform: scale(0.95);\n}\n\n.simple-group-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.group-level-badge {\n\tcolor: white;\n\tpadding: 8rpx 12rpx;\n\tborder-radius: 12rpx;\n\tfont-size: 18rpx;\n\tfont-weight: 700;\n\tmin-width: 40rpx;\n\ttext-align: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);\n}\n\n.simple-group-name {\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\ttext-align: center;\n\tline-height: 1.3;\n\tword-break: break-all;\n}\n\n.simple-status-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n}\n\n.simple-status-dot.active {\n\tbackground: #4CAF50;\n\tbox-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);\n}\n\n.simple-status-dot.completed {\n\tbackground: #2196F3;\n\tbox-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);\n}\n\n.simple-status-dot.pending {\n\tbackground: #FF9800;\n\tbox-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);\n}\n\n/* ===== 新概念教程样式 ===== */\n.concept-tutorial-item {\n\tmargin: 0 10rpx 20rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tborder-radius: 15rpx;\n\tpadding: 25rpx 15rpx;\n\ttransition: all 0.3s ease;\n\tborder: 2rpx solid transparent;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.concept-tutorial-item.active {\n\ttransform: scale(1.05);\n\tbox-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);\n\tborder-color: rgba(255,255,255,0.3);\n}\n\n.concept-tutorial-item:active {\n\ttransform: scale(0.95);\n}\n\n.concept-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 12rpx;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.concept-icon {\n\tfont-size: 32rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.concept-title {\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tcolor: white;\n\ttext-align: center;\n\tline-height: 1.3;\n}\n\n.concept-badge {\n\tbackground: rgba(255,255,255,0.2);\n\tcolor: white;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 16rpx;\n\tfont-weight: 500;\n}\n\n/* ===== 右侧面板 ===== */\n.right-panel {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* ===== 新概念教程详情 ===== */\n.concept-detail-content {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.concept-detail-header {\n\tposition: relative;\n\theight: 180rpx;\n\toverflow: hidden;\n}\n\n.concept-bg {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n}\n\n.concept-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0,0,0,0.2);\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n}\n\n.concept-detail-icon {\n\tfont-size: 50rpx;\n\tmargin-right: 20rpx;\n}\n\n.concept-detail-info {\n\tflex: 1;\n\tcolor: white;\n}\n\n.concept-detail-title {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.concept-detail-subtitle {\n\tfont-size: 22rpx;\n\topacity: 0.9;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.concept-progress-info {\n\tbackground: rgba(255,255,255,0.2);\n\tpadding: 8rpx 15rpx;\n\tborder-radius: 15rpx;\n\tdisplay: inline-block;\n}\n\n.progress-info-text {\n\tfont-size: 18rpx;\n\tfont-weight: 600;\n}\n\n/* ===== 学习分类 ===== */\n.concept-categories {\n\tpadding: 30rpx;\n\tflex: 1;\n}\n\n.categories-title {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.categories-grid {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n}\n\n.category-card {\n\tbackground: #f8f9fa;\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\ttransition: all 0.3s ease;\n}\n\n.category-card:active {\n\tbackground: #e9ecef;\n\ttransform: scale(0.98);\n}\n\n.category-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 15rpx;\n}\n\n.category-info {\n\tflex: 1;\n}\n\n.category-name {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.category-desc {\n\tfont-size: 20rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.category-lessons {\n\tfont-size: 18rpx;\n\tcolor: #999;\n}\n\n.category-arrow {\n\tfont-size: 18rpx;\n\tcolor: #999;\n}\n\n/* ===== 快速操作 ===== */\n.concept-actions {\n\tpadding: 0 30rpx 30rpx;\n}\n\n.concept-action-title {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.concept-action-buttons {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n}\n\n.concept-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tborder-radius: 15rpx;\n\ttransition: all 0.3s ease;\n}\n\n.concept-btn.primary {\n\tbackground: linear-gradient(135deg, #4CAF50, #45A049);\n\tcolor: white;\n}\n\n.concept-btn.secondary {\n\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\n\tcolor: white;\n}\n\n.concept-btn.tertiary {\n\tbackground: linear-gradient(135deg, #FF9800, #F57C00);\n\tcolor: white;\n}\n\n.concept-btn:active {\n\ttransform: scale(0.98);\n}\n\n.concept-btn-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 15rpx;\n}\n\n.concept-btn-content {\n\tflex: 1;\n}\n\n.concept-btn-title {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.concept-btn-desc {\n\tfont-size: 20rpx;\n\topacity: 0.9;\n}\n\n/* ===== 小组详情 ===== */\n.detail-content {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.detail-header {\n\tposition: relative;\n\theight: 180rpx;\n\toverflow: hidden;\n}\n\n.detail-bg {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n}\n\n.detail-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0,0,0,0.3);\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n}\n\n.detail-avatar {\n\twidth: 70rpx;\n\theight: 70rpx;\n\tmargin-right: 20rpx;\n}\n\n.detail-avatar image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 15rpx;\n}\n\n.detail-info {\n\tflex: 1;\n\tcolor: white;\n}\n\n.detail-title {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.detail-subtitle {\n\tfont-size: 22rpx;\n\topacity: 0.9;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.detail-level {\n\tbackground: rgba(255,255,255,0.2);\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 15rpx;\n\tfont-size: 18rpx;\n\tfont-weight: 600;\n\tdisplay: inline-block;\n}\n\n/* ===== 统计卡片 ===== */\n.detail-stats {\n\tdisplay: flex;\n\tpadding: 30rpx;\n\tgap: 20rpx;\n}\n\n.stat-card-detail {\n\tflex: 1;\n\tbackground: #f8f9fa;\n\tborder-radius: 15rpx;\n\tpadding: 25rpx 20rpx;\n\ttext-align: center;\n\ttransition: all 0.3s ease;\n}\n\n.stat-card-detail:active {\n\tbackground: #e9ecef;\n\ttransform: scale(0.95);\n}\n\n.stat-icon-detail {\n\tfont-size: 32rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.stat-number-detail {\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.stat-label-detail {\n\tfont-size: 20rpx;\n\tcolor: #666;\n}\n\n/* ===== 进度详情 ===== */\n.progress-detail {\n\tpadding: 0 30rpx 30rpx;\n}\n\n.progress-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.progress-title {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.progress-value {\n\tfont-size: 26rpx;\n\tfont-weight: 700;\n\tcolor: #667eea;\n}\n\n.progress-bar-detail {\n\theight: 12rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 6rpx;\n\toverflow: hidden;\n\tmargin-bottom: 15rpx;\n}\n\n.progress-fill-detail {\n\theight: 100%;\n\tborder-radius: 6rpx;\n\ttransition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.progress-desc {\n\tfont-size: 20rpx;\n\tcolor: #666;\n\ttext-align: center;\n}\n\n/* ===== 功能按钮 ===== */\n.detail-actions {\n\tflex: 1;\n\tpadding: 0 30rpx 30rpx;\n}\n\n.action-row {\n\tmargin-bottom: 15rpx;\n}\n\n.action-btn-large {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 25rpx;\n\tborder-radius: 20rpx;\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.action-btn-large.primary {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n}\n\n.action-btn-large.secondary {\n\tbackground: #f8f9fa;\n\tcolor: #333;\n\tborder: 2rpx solid #e9ecef;\n}\n\n.action-btn-large:active {\n\ttransform: scale(0.98);\n}\n\n.btn-icon-large {\n\tfont-size: 32rpx;\n\tmargin-right: 20rpx;\n}\n\n.btn-content {\n\tflex: 1;\n}\n\n.btn-title {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.btn-desc {\n\tfont-size: 20rpx;\n\topacity: 0.9;\n}\n\n.btn-arrow-large {\n\tfont-size: 22rpx;\n\topacity: 0.8;\n}\n\n/* ===== 空状态 ===== */\n.empty-detail {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx;\n\ttext-align: center;\n}\n\n.empty-icon {\n\tfont-size: 60rpx;\n\tmargin-bottom: 30rpx;\n\topacity: 0.5;\n}\n\n.empty-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 15rpx;\n}\n\n.empty-desc {\n\tfont-size: 22rpx;\n\tcolor: #999;\n\tline-height: 1.5;\n}\n\n/* ===== 无权限页面样式 ===== */\n.no-permission-page {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 1000;\n}\n\n.permission-container {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 30rpx;\n\tpadding: 80rpx 60rpx;\n\ttext-align: center;\n\tmax-width: 600rpx;\n\tmargin: 0 40rpx;\n\tbackdrop-filter: blur(20rpx);\n\tbox-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);\n}\n\n.permission-icon {\n\tfont-size: 80rpx;\n\tmargin-bottom: 40rpx;\n\topacity: 0.8;\n}\n\n.permission-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.permission-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n\tline-height: 1.5;\n}\n\n.permission-hint {\n\tfont-size: 22rpx;\n\tcolor: #999;\n\tmargin-bottom: 60rpx;\n\tdisplay: block;\n\tline-height: 1.5;\n}\n\n.permission-actions {\n\tdisplay: flex;\n\tgap: 30rpx;\n\tjustify-content: center;\n}\n\n.btn-login, .btn-contact {\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 40rpx;\n\tfont-size: 26rpx;\n\tborder: none;\n\tmin-width: 160rpx;\n\ttransition: all 0.3s ease;\n}\n\n.btn-login {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n}\n\n.btn-contact {\n\tbackground: #f0f0f0;\n\tcolor: #666;\n}\n\n.btn-login:active {\n\ttransform: scale(0.95);\n}\n\n.btn-contact:active {\n\ttransform: scale(0.95);\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index_clean.vue?vue&type=style&index=0&id=59cfa49a&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index_clean.vue?vue&type=style&index=0&id=59cfa49a&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753750518518\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}