{"version": 3, "names": ["_utils", "require", "defineType", "defineAliasedType", "visitor", "aliases", "fields", "name", "validate", "assertNodeType", "value", "optional", "builder", "Object", "assign", "openingElement", "closingElement", "children", "validateArrayOfType", "selfClosing", "assertValueType", "expression", "object", "property", "namespace", "default", "attributes", "typeArguments", "typeParameters", "argument", "openingFragment", "closingFragment"], "sources": ["../../src/definitions/jsx.ts"], "sourcesContent": ["import {\n  defineAliasedType,\n  assertNodeType,\n  assertValueType,\n  validateArrayOfType,\n} from \"./utils.ts\";\n\nconst defineType = defineAliasedType(\"JSX\");\n\ndefineType(\"JSXAttribute\", {\n  visitor: [\"name\", \"value\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\"JSXIdentifier\", \"JSXNamespacedName\"),\n    },\n    value: {\n      optional: true,\n      validate: assertNodeType(\n        \"JSXElement\",\n        \"JSXFragment\",\n        \"StringLiteral\",\n        \"JSXExpressionContainer\",\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXClosingElement\", {\n  visitor: [\"name\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\n        \"JSXIdentifier\",\n        \"JSXMemberExpression\",\n        \"JSXNamespacedName\",\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXElement\", {\n  builder: process.env.BABEL_8_BREAKING\n    ? [\"openingElement\", \"closingElement\", \"children\"]\n    : [\"openingElement\", \"closingElement\", \"children\", \"selfClosing\"],\n  visitor: [\"openingElement\", \"children\", \"closingElement\"],\n  aliases: [\"Immutable\", \"Expression\"],\n  fields: {\n    openingElement: {\n      validate: assertNodeType(\"JSXOpeningElement\"),\n    },\n    closingElement: {\n      optional: true,\n      validate: assertNodeType(\"JSXClosingElement\"),\n    },\n    children: validateArrayOfType(\n      \"JSXText\",\n      \"JSXExpressionContainer\",\n      \"JSXSpreadChild\",\n      \"JSXElement\",\n      \"JSXFragment\",\n    ),\n    ...(process.env.BABEL_8_BREAKING\n      ? {}\n      : {\n          selfClosing: {\n            validate: assertValueType(\"boolean\"),\n            optional: true,\n          },\n        }),\n  },\n});\n\ndefineType(\"JSXEmptyExpression\", {});\n\ndefineType(\"JSXExpressionContainer\", {\n  visitor: [\"expression\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\", \"JSXEmptyExpression\"),\n    },\n  },\n});\n\ndefineType(\"JSXSpreadChild\", {\n  visitor: [\"expression\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"JSXIdentifier\", {\n  builder: [\"name\"],\n  fields: {\n    name: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"JSXMemberExpression\", {\n  visitor: [\"object\", \"property\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"JSXMemberExpression\", \"JSXIdentifier\"),\n    },\n    property: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n  },\n});\n\ndefineType(\"JSXNamespacedName\", {\n  visitor: [\"namespace\", \"name\"],\n  fields: {\n    namespace: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n    name: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n  },\n});\n\ndefineType(\"JSXOpeningElement\", {\n  builder: [\"name\", \"attributes\", \"selfClosing\"],\n  visitor: process.env.BABEL_8_BREAKING\n    ? [\"name\", \"typeArguments\", \"attributes\"]\n    : [\"name\", \"typeParameters\", \"typeArguments\", \"attributes\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\n        \"JSXIdentifier\",\n        \"JSXMemberExpression\",\n        \"JSXNamespacedName\",\n      ),\n    },\n    selfClosing: {\n      default: false,\n    },\n    attributes: validateArrayOfType(\"JSXAttribute\", \"JSXSpreadAttribute\"),\n    typeArguments: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterInstantiation\",\n            \"TSTypeParameterInstantiation\",\n          )\n        : assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    ...(process.env.BABEL_8_BREAKING\n      ? {}\n      : {\n          typeParameters: {\n            validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n            optional: true,\n          },\n        }),\n  },\n});\n\ndefineType(\"JSXSpreadAttribute\", {\n  visitor: [\"argument\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"JSXText\", {\n  aliases: [\"Immutable\"],\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"JSXFragment\", {\n  builder: [\"openingFragment\", \"closingFragment\", \"children\"],\n  visitor: [\"openingFragment\", \"children\", \"closingFragment\"],\n  aliases: [\"Immutable\", \"Expression\"],\n  fields: {\n    openingFragment: {\n      validate: assertNodeType(\"JSXOpeningFragment\"),\n    },\n    closingFragment: {\n      validate: assertNodeType(\"JSXClosingFragment\"),\n    },\n    children: validateArrayOfType(\n      \"JSXText\",\n      \"JSXExpressionContainer\",\n      \"JSXSpreadChild\",\n      \"JSXElement\",\n      \"JSXFragment\",\n    ),\n  },\n});\n\ndefineType(\"JSXOpeningFragment\", {\n  aliases: [\"Immutable\"],\n});\n\ndefineType(\"JSXClosingFragment\", {\n  aliases: [\"Immutable\"],\n});\n"], "mappings": ";;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAOA,MAAMC,UAAU,GAAG,IAAAC,wBAAiB,EAAC,KAAK,CAAC;AAE3CD,UAAU,CAAC,cAAc,EAAE;EACzBE,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe,EAAE,mBAAmB;IAC/D,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdH,QAAQ,EAAE,IAAAC,qBAAc,EACtB,YAAY,EACZ,aAAa,EACb,eAAe,EACf,wBACF;IACF;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9BE,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EACtB,eAAe,EACf,qBAAqB,EACrB,mBACF;IACF;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,YAAY,EAAE;EACvBU,OAAO,EAEH,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,CAAC;EACnER,OAAO,EAAE,CAAC,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,CAAC;EACzDC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACpCC,MAAM,EAAAO,MAAA,CAAAC,MAAA;IACJC,cAAc,EAAE;MACdP,QAAQ,EAAE,IAAAC,qBAAc,EAAC,mBAAmB;IAC9C,CAAC;IACDO,cAAc,EAAE;MACdL,QAAQ,EAAE,IAAI;MACdH,QAAQ,EAAE,IAAAC,qBAAc,EAAC,mBAAmB;IAC9C,CAAC;IACDQ,QAAQ,EAAE,IAAAC,0BAAmB,EAC3B,SAAS,EACT,wBAAwB,EACxB,gBAAgB,EAChB,YAAY,EACZ,aACF;EAAC,GAGG;IACEC,WAAW,EAAE;MACXX,QAAQ,EAAE,IAAAY,sBAAe,EAAC,SAAS,CAAC;MACpCT,QAAQ,EAAE;IACZ;EACF,CAAC;AAET,CAAC,CAAC;AAEFT,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AAEpCA,UAAU,CAAC,wBAAwB,EAAE;EACnCE,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNe,UAAU,EAAE;MACVb,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY,EAAE,oBAAoB;IAC7D;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,gBAAgB,EAAE;EAC3BE,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNe,UAAU,EAAE;MACVb,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,eAAe,EAAE;EAC1BU,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBN,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAY,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFlB,UAAU,CAAC,qBAAqB,EAAE;EAChCE,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC/BE,MAAM,EAAE;IACNgB,MAAM,EAAE;MACNd,QAAQ,EAAE,IAAAC,qBAAc,EAAC,qBAAqB,EAAE,eAAe;IACjE,CAAC;IACDc,QAAQ,EAAE;MACRf,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe;IAC1C;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9BE,OAAO,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;EAC9BE,MAAM,EAAE;IACNkB,SAAS,EAAE;MACThB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe;IAC1C,CAAC;IACDF,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe;IAC1C;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9BU,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC;EAC9CR,OAAO,EAEH,CAAC,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,YAAY,CAAC;EAC7DC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAAO,MAAA,CAAAC,MAAA;IACJP,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EACtB,eAAe,EACf,qBAAqB,EACrB,mBACF;IACF,CAAC;IACDU,WAAW,EAAE;MACXM,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE,IAAAR,0BAAmB,EAAC,cAAc,EAAE,oBAAoB,CAAC;IACrES,aAAa,EAAE;MACbnB,QAAQ,EAKJ,IAAAC,qBAAc,EAAC,4BAA4B,CAAC;MAChDE,QAAQ,EAAE;IACZ;EAAC,GAGG;IACEiB,cAAc,EAAE;MACdpB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,8BAA8B,CAAC;MACxDE,QAAQ,EAAE;IACZ;EACF,CAAC;AAET,CAAC,CAAC;AAEFT,UAAU,CAAC,oBAAoB,EAAE;EAC/BE,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBE,MAAM,EAAE;IACNuB,QAAQ,EAAE;MACRrB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,SAAS,EAAE;EACpBG,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBO,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBN,MAAM,EAAE;IACNI,KAAK,EAAE;MACLF,QAAQ,EAAE,IAAAY,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFlB,UAAU,CAAC,aAAa,EAAE;EACxBU,OAAO,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,UAAU,CAAC;EAC3DR,OAAO,EAAE,CAAC,iBAAiB,EAAE,UAAU,EAAE,iBAAiB,CAAC;EAC3DC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACpCC,MAAM,EAAE;IACNwB,eAAe,EAAE;MACftB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,oBAAoB;IAC/C,CAAC;IACDsB,eAAe,EAAE;MACfvB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,oBAAoB;IAC/C,CAAC;IACDQ,QAAQ,EAAE,IAAAC,0BAAmB,EAC3B,SAAS,EACT,wBAAwB,EACxB,gBAAgB,EAChB,YAAY,EACZ,aACF;EACF;AACF,CAAC,CAAC;AAEFhB,UAAU,CAAC,oBAAoB,EAAE;EAC/BG,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC;AAEFH,UAAU,CAAC,oBAAoB,EAAE;EAC/BG,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC", "ignoreList": []}