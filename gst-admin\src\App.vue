<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { healthCheck } from '@/utils/request'

const authStore = useAuthStore()

// 应用初始化
onMounted(async () => {
  // 初始化认证状态
  authStore.initAuth()

  // 健康检查
  await healthCheck()

  console.log('🎉 GST日语培训班管理后台启动成功')
  console.log(`📡 前端地址: ${window.location.origin}`)
  console.log('🌐 现代化Vue 3 + Element Plus架构')
})
</script>

<style>
/* 全局样式已移至 styles/index.scss */
#app {
  height: 100%;
}
</style>
��
 
 