{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/gst/gst-uniapp/pages/course/course.vue?0d4f", "webpack:///D:/gst/gst-uniapp/pages/course/course.vue?8979", "webpack:///D:/gst/gst-uniapp/pages/course/course.vue?f470", "webpack:///D:/gst/gst-uniapp/pages/course/course.vue?f529", "uni-app:///pages/course/course.vue", "webpack:///D:/gst/gst-uniapp/pages/course/course.vue?6621", "webpack:///D:/gst/gst-uniapp/pages/course/course.vue?ebea"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uParse", "data", "kechengList", "btnnum", "currentTime", "<PERSON><PERSON>us", "menuinfo", "teacher", "courses", "comment", "HOST_URL", "videostate", "durationTime", "coursehour", "audio_paused", "audio_progress", "gaosi", "t1", "t2", "t3", "videosrc", "free_time", "video_controls", "videofullscreen", "menuid", "sonid", "videoStudyTime", "audioStudyTime", "is_free", "son_is_pay", "uid", "is_dingyue", "videoContext", "creditinfo", "action", "seckillin<PERSON>", "pintuaninfo", "project", "videoId", "id", "option", "is_show", "onShareAppMessage", "title", "path", "imageUrl", "onShareTimeline", "onReady", "onLoad", "withShareTicket", "menus", "onShow", "audio", "clearInterval", "onUnload", "methods", "getdetail", "uni", "console", "jsdetail", "url", "get_creditinfo", "goodstype", "method", "success", "fail", "get_seckillinfo", "get_pintuaninfo", "saveStudytime", "media", "studytime", "saveAtime", "saveVtime", "countVtime", "countAtime", "dingyue", "pay", "pay1", "content", "_this", "provider", "icon", "curPage", "collect", "params", "exchange", "seckill", "pin<PERSON>an", "fullscreen", "video_onplay", "video_onpause", "video_onend", "video_timeUpdate", "gaosi_ani", "progress_text", "progress_change", "change_slide", "play", "muluchange", "videoErrorCallback", "openvideo", "format", "String", "Math", "apiGetCourseList", "sign", "setTimeout", "pid", "cid", "apiPay", "money", "type"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC6KvnB;AAGA;AAEA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAKA;AAAA,eAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEA;MACAC;MACAC;MACAC;IACA;EAEA;EACA;EACAC;IACA;MACAH;MACAC;MACAC;IACA;EACA;EACAE;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IAEA;IACA;IACA;IACAtD;MACAuD;MACAC;IACA;IACA;EACA;EACAC;IAAA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;;IACAA;MACA;IACA;IACAA;MACA;MACA;MACA;MACA;MACA;IACA;IACAA;MACA;MACAC;MACAA;MACA;MACA;IACA;IACAD;MACA;MACAC;MACAA;MACA;IACA;IACAD;MACAC;MACAA;MACA;MACA;MACAD;MACA;IACA;EACA;EACAE;IACAD;IACAA;IACAA;IACA;IACA;IACA;IACA;EACA;EACAE;IACAC;MAAA;MACAC;MACA;QACAC;QACAD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;UACA;UACA;QACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UAAA,CACA;YACA;YACA;UACA;QACA;MAGA;IACA;IACAE;MACAF;QACAG;MACA;IACA;IACAC;MAAA;MACA;MACAJ;QACAG;QACA3D;UACAsC;UACAuB;QACA;QACAC;QACAC;UACAN;UACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IACAQ;MAAA;MACA;MACAT;QACAG;QACA3D;UACAsC;UACAuB;QACA;QACAC;QACAC;UACAN;UACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IACAS;MAAA;MACA;MACAV;QACAG;QACA3D;UACAsC;UACAuB;QACA;QACAC;QACAC;UACAN;UACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IACAU;MACA;MACAX;QACAG;QACAG;QACA9D;UACA6B;UACAuC;UACAC;QACA;QACAN;UACAN;QACA;QACAO;UACAP;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;QACAtE;QACAA;QACAwD;QACA;MACA;QACAxD;QACAA;QACAwD;QACA;MACA;IACA;IACAe;MACA;MACA;MACA;QACAvE;QACAA;QACAwD;QACA;MACA;QACAxD;QACAA;QACAwD;QACA;MACA;IACA;IACAgB;MAAA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAlB;QACAG;QACAG;QACA9D;UACA6B;UACAuC;UACA7C;UACAsC;QACA;QACAE;UACAN;UACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IACAkB;MACA;QACAnB;UACAG,4EACA;QACA;MACA;QACAH;UACAG,wFACA;QACA;MACA;IACA;IACAiB;MACA;MACA;QACApB;UACAd;UACAmC;UACAd;YACA;cACAe;cACAtB;gBACAG;cACA;YACA;cACAH;YACA;UACA;QACA;MACA;QACA;UACAC;UACA;UACAD;YACAuB;UAAA,GACA/E;YACA+D;cACAP;cACAA;gBACAwB;gBACAtC;cACA;cACA;cACA;cACA;cACAuC;YACA;YACAjB;cACAR;cACAA;gBACAwB;gBACAtC;cACA;cACAe;YACA;UAAA,GACA;QACA;MACA;IACA;IACAyB;MACA;MACA;QACA1B;UACAd;UACAmC;UACAd;YACA;cACAe;cACAtB;gBACAG;cACA;YACA;cACAH;YACA;UACA;QACA;MACA;QACA;UACA2B;YACA7C;UACA;QACA;UACAmB;UACA;YACA;YACAD;cACAd;cACAsC;YACA;YACA;YACA;YACAC;UACA;YACAzB;cACAd;cACAsC;YACA;UACA;QACA;MACA;IACA;IACAI;MACA5B;QACAG,4EACA;MACA;IACA;IACA0B;MACA7B;QACAG,4EACA;MACA;IACA;IACA2B;MACA9B;QACAG,4EACA;MACA;IACA;IACA4B;MACA;IACA;IACAC;MACA;MACA/B;MACA;QACA;QACA;QACAN;MACA;QACA;QACA;QACA;QACA;MAAA;IAGA;IACAsC;MACArC;MACA;IACA;IACAsC;MACAtC;MACA;IACA;IACAuC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA3C;IACA;IACA4C;MACA;MACA;QACA5C;MACA;QACAA;MACA;IACA;IACA6C;MACA;MACA;QACA7C;MAEA;QACAA;MAEA;IACA;IACA8C;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IAAA,CACA;IACAC;MAAA;MACA1C;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;UACAD;YACAd;YACAmC;YACAd;cACA;cACA;gBACA;cACA;gBACA;gBACA;gBACA;cAAA;YAEA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAKA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAKA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAqC;MACA,yGACAC,OACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACApB;UACA7C;QAEA;MACA;QACA;MACA;IACA;IACAkE;MAAA;MACAC;QACA;UACAnE;UACAoE;UACAC;QACA,wBAEA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACAtE;QACAuE;QACAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACz5BA;AAAA;AAAA;AAAA;AAAsoC,CAAgB,wlCAAG,EAAC,C;;;;;;;;;;;ACA1pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/course/course.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/course/course.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./course.vue?vue&type=template&id=b1db4d34&scoped=true&\"\nvar renderjs\nimport script from \"./course.vue?vue&type=script&lang=js&\"\nexport * from \"./course.vue?vue&type=script&lang=js&\"\nimport style0 from \"./course.vue?vue&type=style&index=0&id=b1db4d34&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b1db4d34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/course/course.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=template&id=b1db4d34&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\r\n\t\t<!-- 媒体部分 -->\r\n\t\t<view>\r\n\t\t\t<!-- 视频 -->\r\n\t\t\t<video  v-if=\"menuinfo.Cost==0 || menuinfo.is_buy==1\"style=\"width: 100%;\" :model=\"menuinfo\" :src=\"videosrc\" @error=\"videoErrorCallback\"\r\n\t\t\t\t@timeupdate=\"video_timeUpdate\" @fullscreenchange=\"fullscreen\" @play='video_onplay' autoplay=\"true\"\r\n\t\t\t\t@pause='video_onpause' @ended='video_onend' :controls=\"video_controls\"  show-center-play-btn\r\n\t\t\t\tenable-play-gesture :poster=\"menuinfo.picture\">\r\n\t\t\t</video>\r\n\t\t\t<view class=\"cover-box lp-flex-center\" v-if=\"menuinfo.Cost>0 && menuinfo.is_buy==0\">\r\n\t\t\t\t<image class=\"cover\" :src=\"menuinfo.picture\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"button\"  style=\"background-color:#333;\">{{menuinfo.count}}课时</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"shipinpart-info\" style=\"background-color: #ffffff;\">\r\n\t\t\t<text class=\"info-span4\" >￥{{menuinfo.Cost?menuinfo.Cost:0}}</text>\r\n\t\t\t<text class=\"info-span1\" style=\"margin-top: 10rpx;\">{{menuinfo.title}}</text>\r\n\t\t\t<!-- <view class=\"info-span2\">\r\n\t\t\t\t<text>{{menuinfo.jianjie}}</text>\r\n\t\t\t\t<text>更新中</text>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"info-span5\">\r\n\t\t\t\t<text class=\"info-span3\">\r\n\t\t\t\t\t<!-- <text>{{menuinfo.count}}课时</text> -->\r\n\t\t\t\t\t<!-- <text>|</text> -->\r\n\t\t\t\t\t<!-- <text>{{menuinfo.viewnum ? menuinfo.viewnum : '0'}}人学过</text> -->\r\n\t\t\t\t</text>\r\n\t\t\t\t<!-- \t<text class=\"info-span4\" >￥{{menuinfo.price}}</text>\r\n\t\t\t\t<text class=\"info-span4\" v-if=\"action=='credit' \">{{creditinfo.credit}}积分</text>\r\n\t\t\t\t<text class=\"info-span4\" v-if=\"action=='seckill' \">秒杀价:￥{{seckillinfo.price}}</text>\r\n\t\t\t\t<text class=\"info-span4\" v-if=\"action=='pintuan' \">拼团价:￥{{pintuaninfo.price}}</text> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"shipinpart-info\" style=\"background-color: #ffffff;margin-top: 20rpx;padding: 20rpx;\">\r\n\t\t\t<text class=\"info-span1\" style=\"margin-top: 10rpx;\">课程介绍</text>\r\n\t\t\t<view class=\"kcjs\" :class=\"{dis:btnnum == 0}\" :model=\"teacher\" style=\"margin-top: 20rpx;\">\r\n\t\t\t<view class=\"kcjs-brief\">\r\n\t\t\t\t<view class=\"kcjs-brief-center\">\r\n\t\t\t\t\t<rich-text :nodes=\"menuinfo.content\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"shipinpart-info\" style=\"background-color: #ffffff;margin-top: 20rpx;\">\r\n\t\t\t<text class=\"info-span1\" style=\"margin-top: 10rpx;\">课程目录</text>\r\n\t\t\t<view class=\"kcml\" style=\"margin-bottom: 122upx;margin-top: 20rpx;\" :class=\"{dis:btnnum == 1}\">\r\n\t\t\t<view class=\"kcml-list\" v-for=\"(item,index) in menuinfo.li\" :key=\"item.id\" @click=\"openvideo(item)\">\r\n\t\t\t\t<view >\r\n\t\t\t\t\t<text  v-if=\"item.id == videoId\" class=\"fontcolor\" >{{(index+1)+' . '+item.title}}</text>\r\n\t\t\t\t\t<text  v-else>{{(index+1)+' . '+item.title}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<!-- 课程介绍、课程目录、附件、用户评价 -->\r\n\t<!-- \t<view class=\"kechengpart\">\r\n\t\t\t<view class=\"kechengpart-title\">\r\n\t\t\t\t<view class=\"kechengpart-title-item\" v-for=\"(item, index) in kechengList\" :key=\"index\">\r\n\t\t\t\t\t<view @click=\"muluchange(index)\" :class=\"{btna:btnnum == index}\">{{item}}</view>\r\n\t\t\t\t\t<text :class=\"{_underline: btnnum == index}\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"kechengpart-content\"> -->\r\n\r\n\r\n\t\t\t\t<!-- 课程介绍 -->\r\n\t\t\t\t<!-- <view class=\"kcjs\" :class=\"{dis:btnnum == 0}\" :model=\"teacher\"> -->\r\n\t\t\t\t\t<!-- <view class=\"kcjs-lecturer\">\r\n\t\t\t\t\t\t<view class=\"kcjs-lecturer-top\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/kechengjiangshi.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<text>课程讲师</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"jiangshi-right\" @click=\"jsdetail\">\r\n\t\t\t\t\t\t\t<text>讲师主页</text>\r\n\t\t\t\t\t\t\t<image src=\"../../static/jt.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"kcjs-lecturer-bottom\" >\r\n\t\t\t\t\t\t\t<image :src=\"teacher.img?(HOST_URL + teacher.img):'' \" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class=\"kcjs-brief\"> -->\r\n\t\t\t\t\t\t<!-- <view class=\"kcjs-brief-top\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/kechengjianjie.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<text>课程简介</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<!-- <view class=\"kcjs-brief-center\"> -->\r\n\t\t\t\t\t\t\t<!-- <u-parse :content=\"menuinfo.content\"></u-parse> -->\r\n\t\t\t\t<!-- \t\t\t<rich-text :nodes=\"menuinfo.content\"></rich-text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\r\n\r\n\t\t\t\t<!-- 课程目录 -->\r\n\t\t\t<!-- \t<view class=\"kcml\" style=\"margin-bottom: 122upx;\" :class=\"{dis:btnnum == 1}\">\r\n\t\t\t\t\t<view class=\"kcml-list\" v-for=\"(item,index) in menuinfo.li\" :key=\"item.id\" @click=\"openvideo(item)\">\r\n\t\t\t\t\t\t<view > -->\r\n\t\t\t\t\t\t\t<!-- \t<text class=\"yinpin\" v-if=\"item.media === 'audio'\">音频</text>\r\n\t\t\t\t\t\t\t<text class=\"shipin\" v-else-if=\"item.media === 'video'\">视频</text>\r\n\t\t\t\t\t\t\t<text class=\"tuwen\" v-else>图文</text> -->\r\n\t\t\t\t\t\t\t<!-- <text  v-if=\"item.id == videoId\" class=\"fontcolor\" >{{(index+1)+' . '+item.title}}</text>\r\n\t\t\t\t\t\t\t<text  v-else>{{(index+1)+' . '+item.title}}</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<!-- <view class=\"kcml-list-right\" v-if=\"!is_free && item.is_pay==0\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.is_sk == 1\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.media == 'video'\">试看</text>\r\n\t\t\t\t\t\t\t\t<text v-else-if=\"item.media == 'audio'\">试听</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.media == 'tuwen'\">试读</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<image v-else-if=\"item.is_sk == 0\" src=\"../../static/lock.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t<!-- \t</view>\r\n\t\t\t\t</view>\r\n -->\r\n\r\n\r\n\t\t\t\t<!-- 用户评价 -->\r\n\t\t\t\t<!-- <view class=\"yhpj\" :class=\"{dis:btnnum == 2}\">\r\n\t\t\t\t\t<view class=\"yhpj-list\" v-if=\"comment.length > 0\">\r\n\t\t\t\t\t\t<view class=\"item\" v-for=\"(item, index) in comment\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image class=\"item-left\" :src=\"item.avatar\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t\t\t\t<view class=\"item-right-top\">\r\n\t\t\t\t\t\t\t\t\t<text>{{item.nickname}}</text>\r\n\t\t\t\t\t\t\t\t\t<text>{{item.addtime}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"item-right-bottom\">\r\n\t\t\t\t\t\t\t\t\t<text>{{item.comment}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image v-else class=\"nopl\" src=\"../../static/nopl.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view> -->\r\n\t\t\t<!-- </view> -->\r\n\t<!-- \t</view> -->\r\n\r\n\r\n\t\t<!-- 立即购买 -->\r\n\t\t\t<view class=\"buy\" v-if=\"btnnum <= 2\">\r\n\t\t\t<view  class=\"p-b-btn\" style=\"margin-left: 10rpx;\" @click=\"collect\" :style=\"{color:menuinfo.is_collect==1?'#f00':''}\">\r\n\t\t\t\t<text class=\"yticon icon-shoucang2\"></text>\r\n\t\t\t\t<text>收藏</text>\r\n\t\t\t</view>\t\r\n\t\t\t<view class=\"p-b-btn\">\r\n\t\t\t\t<!-- <text class=\"yticon icon-dianhua-copy\"></text> -->\r\n\t\t\t\t<image src=\"/static/kf.png\" style=\"width: 50rpx;height: 50rpx;\"></image>\r\n\t\t\t\t<text>客服</text>\r\n\t\t\t\t<button class='contact-btn' open-type='contact'>a</button> \r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"buy-right\" @click=\"pay1\" v-if=\"menuinfo.Cost>0 && menuinfo.is_buy==0 && is_show===true\">立即购买</view>\r\n\t\t\t<view class=\"buy-right\" v-if=\"menuinfo.Cost>0 && menuinfo.is_buy==1\">已购买</view>\r\n\t\t\t<view class=\"buy-right\" @click=\"exchange\" v-if=\"!is_free && action=='credit' \">立即兑换</view>\r\n\t\t\t<view class=\"buy-right\" @click=\"seckill\" v-if=\"!is_free && action=='seckill' \">立即秒杀</view>\r\n\t\t\t<view class=\"buy-right\" @click=\"pintuan\" v-if=\"!is_free && action=='pintuan' \">开始拼团</view>\r\n\t\t\t<view class=\"buy-right\" v-if=\"menuinfo.Cost==0\">免费</view>\r\n\t\t</view>\r\n\r\n\t\t<zaudio></zaudio>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport uParse from '@/components/gaoyia-parse/parse.vue'\r\n\timport {\r\n\t\tgetCourseDetails\r\n\t} from '@/request/courses.js'\r\n\timport {\r\n\t\tcheckUserinfo\r\n\t} from '@/request/checkUserinfo'\r\n\t// #ifndef MP-WEIXIN\r\n\tconst innerAudioContext = uni.createInnerAudioContext()\r\n\t// #endif\r\n\t// #ifdef MP-WEIXIN\r\n\tconst innerAudioContext = uni.getBackgroundAudioManager();\r\n\t// #endif\r\n\texport default {\r\n\t\t// computed: {\r\n\t\t//     contentMobile() {\r\n\t\t//       // 小程序中rich-text 样式设置需要通过正则给富文本添加样式\r\n\t\t//       if (this.menuinfo.content)\r\n\t\t//         return this.menuinfo.content.replace(\r\n\t\t//           /font-size:[^;]+;/gi, \r\n\t\t// \t\t  'font-size:10px;'\r\n\t\t//         );\r\n\t\t//       return \"\";\r\n\t\t//     },\r\n\t\t// },\r\n\t\tcomponents: {\r\n\t\t\tuParse\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tkechengList: ['课程介绍', '目录'],\r\n\t\t\t\tbtnnum: 0,\r\n\t\t\t\tcurrentTime: '',\r\n\t\t\t\tcoursemenus: [],\r\n\t\t\t\tmenuinfo: [],\r\n\t\t\t\tteacher: [],\r\n\t\t\t\tcourses: [],\r\n\t\t\t\tcomment: [],\r\n\t\t\t\tHOST_URL: uni.HOST_URL,\r\n\t\t\t\tvideostate: 'video',\r\n\t\t\t\tdurationTime: \"\", //音频的总时长\r\n\t\t\t\tcoursehour: 0, // 课时\r\n\t\t\t\taudio_paused: true,\r\n\t\t\t\taudio_progress: 0,\r\n\t\t\t\tgaosi: false,\r\n\t\t\t\tt1: '',\r\n\t\t\t\tt2: '', //video计时器id\r\n\t\t\t\tt3: '', //audio计时器id\r\n\t\t\t\tvideosrc: '',\r\n\t\t\t\tfree_time: '',\r\n\t\t\t\tvideo_controls: true,\r\n\t\t\t\tvideofullscreen: false,\r\n\t\t\t\tmenuid: '',\r\n\t\t\t\tsonid: '',\r\n\t\t\t\tvideoStudyTime: 0, //秒\r\n\t\t\t\taudioStudyTime: 0, //秒\r\n\t\t\t\tis_free: false,\r\n\t\t\t\tson_is_pay: false,\r\n\t\t\t\tuid: '',\r\n\t\t\t\tis_dingyue: false,\r\n\t\t\t\tvideoContext: {},\r\n\t\t\t\tcreditinfo: {},\r\n\t\t\t\taction: '',\r\n\t\t\t\tseckillinfo: {},\r\n\t\t\t\tpintuaninfo: {},\r\n\t\t\t\tproject: {},\r\n\t\t\t\tvideoId:1,\r\n\t\t\t\tid:1,\r\n\t\t\t\toption:0,\r\n\t\t\t\tis_show:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShareAppMessage() {\r\n\t\t\r\n\t\t\treturn {\r\n\t\t\t\t\ttitle: this.menuinfo.title,\r\n\t\t\t\t\tpath: `/pages/course/course?id=${this.menuinfo.id}`,\r\n\t\t\t\t\timageUrl: ''\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\t// 分享到朋友圈\r\n\t\tonShareTimeline() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.menuinfo.title,\r\n\t\t\t\tpath: `/pages/course/course?id=${this.menuinfo.id}`,\r\n\t\t\t\timageUrl: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tonReady: function(res) {\r\n\t\t\tthis.videoContext = uni.createVideoContext('myVideo')\r\n\t\t\tlet platform = this.$store.state.systemInfo.platform\r\n\t\t\tif(platform=='ios'){\r\n\t\t\t\tthis.is_show = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\t\r\n\t\t\t//保存当前页面的option对象\r\n\t\t\tthis.option = option;\r\n\t\t\tthis.id = option.id\r\n\t\t\twx.showShareMenu({\r\n\t\t\t\t\twithShareTicket:true,\r\n\t\t\t\t\tmenus:[\"shareAppMessage\",\"shareTimeline\"]\r\n\t\t\t\t})\r\n\t\t\tthis.getdetail()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t\t//checkUserinfo()\r\n\t\t\tlet audio = innerAudioContext;\r\n\t\t\taudio.onTimeUpdate((e) => {\r\n\t\t\t\tthis.durationTime = this.format(audio.duration)\r\n\t\t\t\tthis.currentTime = this.format(audio.currentTime)\r\n\t\t\t\tlet progress = (audio.currentTime / audio.duration).toFixed(2)\r\n\t\t\t\tthis.audio_progress = progress * 100\r\n\t\t\t\t// if(!this.is_free){\r\n\t\t\t\t// \tif(!this.son_is_pay){\r\n\t\t\t\t// \t\tif(audio.currentTime>=this.free_time){\r\n\t\t\t\t// \t\t\taudio.pause()\r\n\t\t\t\t// \t\t\tthis.currentTime='00:00'\r\n\t\t\t\t// \t\t\taudio.seek(0)\r\n\t\t\t\t// \t\t\tthis.audio_progress=0\r\n\t\t\t\t// \t\t\tuni.showModal({\r\n\t\t\t\t// \t\t\t\ttitle: '试听结束',\r\n\t\t\t\t// \t\t\t\tcontent: '需要解锁该课程吗?',\r\n\t\t\t\t// \t\t\t\tsuccess: (res) =>{\r\n\t\t\t\t// \t\t\t\t\tvar that=this\r\n\t\t\t\t// \t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t// \t\t\t\t\t\tthis.pay()\r\n\t\t\t\t// \t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t// \t\t\t\t\t\t// that.currentTime='00:00'\r\n\t\t\t\t// \t\t\t\t\t\t// audio.seek(0)\r\n\t\t\t\t// \t\t\t\t\t\t// that.audio_progress=0\r\n\t\t\t\t// \t\t\t\t\t}\r\n\t\t\t\t// \t\t\t\t}\r\n\t\t\t\t// \t\t\t});\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\t// console.log(this.currentTime)\r\n\t\t\t})\r\n\t\t\taudio.onSeeked((e) => {\r\n\t\t\t\tthis.currentTime = this.format(audio.currentTime)\r\n\t\t\t})\r\n\t\t\taudio.onPlay((e) => {\r\n\t\t\t\tthis.videoContext.pause()\r\n\t\t\t\tthis.audio_paused = false\r\n\t\t\t\tthis.gaosi_ani()\r\n\t\t\t\tthis.gaosi = true\r\n\t\t\t\tthis.countAtime()\r\n\t\t\t})\r\n\t\t\taudio.onPause((e) => {\r\n\t\t\t\tthis.audio_paused = true\r\n\t\t\t\tclearInterval(this.t1)\r\n\t\t\t\tclearInterval(this.t3)\r\n\t\t\t\tthis.gaosi = false\r\n\t\t\t\tthis.saveAtime()\r\n\t\t\t})\r\n\t\t\taudio.onStop((e) => {\r\n\t\t\t\tthis.audio_paused = true\r\n\t\t\t\tclearInterval(this.t1)\r\n\t\t\t\tclearInterval(this.t3)\r\n\t\t\t\tthis.saveAtime()\r\n\t\t\t})\r\n\t\t\taudio.onEnded((e) => {\r\n\t\t\t\tclearInterval(this.t1)\r\n\t\t\t\tclearInterval(this.t3)\r\n\t\t\t\tthis.audio_paused = true\r\n\t\t\t\tthis.currentTime = '00:00'\r\n\t\t\t\taudio.seek(0)\r\n\t\t\t\tthis.saveAtime()\r\n\t\t\t})\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\tclearInterval(this.t1)\r\n\t\t\tclearInterval(this.t2)\r\n\t\t\tclearInterval(this.t3)\r\n\t\t\tthis.menuid = ''\r\n\t\t\tthis.sonid = ''\r\n\t\t\tthis.saveAtime()\r\n\t\t\tthis.saveVtime()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetdetail(){\r\n\t\t\t\tuni.showLoading();\r\n\t\t\t\tthis.apiGetCourseList(this.id).then(pagination => {\r\n\t\t\t\t\tconsole.log('pagination' + pagination)\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.menuinfo = pagination;\r\n\t\t\t\t\t// var strings = this.menuinfo.content;    \r\n\t\t\t\t\t// //请求到的富文本数据文件\r\n\t\t\t\t\t// const regex = new RegExp(/font-size:^;/,'gi');    \r\n\t\t\t\t\t// //创建一个正则匹配规则\t\t\t\r\n\t\t\t\t\t// var richtext = strings.replace(regex, `font-size:10px;`);\r\n\t\t\t\t\t// // 将想要的样式属性携带在标签上，替换原来的标签即可。\r\n\t\t\t\t\t// console.log('richtext' + richtext)\r\n\t\t\t\t\t// this.menuinfo.content = richtext;\r\n\t\t\t\t\tvar richtext1 = this.menuinfo.content.replace(/<p/g, \"<p style='font-size:14px;color:#333;background-color:#fff;'\")\r\n\t\t\t\t\tvar richtext2 = richtext1.replace(/font-size.*?;/g, 'font-size:14px;')\r\n\t\t\t\t\tvar richtext3 = richtext2.replace(/font-color.*?;/g, 'font-color:#333;')\r\n\t\t\t\t\tthis.menuinfo.content = richtext3;\r\n\t\t\t\t    // 最后将修改后的富文本传到data中，渲染到视图层即可。\r\n\r\n\t\t\t\t\tif(this.menuinfo.Cost==0){\r\n\t\t\t\t\t\tthis.videosrc = pagination.li[0].url;\r\n\t\t\t\t\t\tthis.videoId = pagination.li[0].id;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif(this.menuinfo.is_buy==0){\r\n\t\t\t\t\t\t\t// uni.showModal({\r\n\t\t\t\t\t\t\t// \t\ttitle: '提示',\r\n\t\t\t\t\t\t\t// \t    content: '购买课程后可观看?',\r\n\t\t\t\t\t\t\t// \t\t\t\tsuccess: (res) =>{\r\n\t\t\t\t\t\t\t// \t\t\t\t\tvar that=this\r\n\t\t\t\t\t\t\t// \t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// \t\t\t\t\t\tthis.pay1()\r\n\t\t\t\t\t\t\t// \t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t// \t\t\t\t\t\t// that.currentTime='00:00'\r\n\t\t\t\t\t\t\t// \t\t\t\t\t\t// audio.seek(0)\r\n\t\t\t\t\t\t\t// \t\t\t\t\t\t// that.audio_progress=0\r\n\t\t\t\t\t\t\t// \t\t\t\t\t}\r\n\t\t\t\t\t\t\t// \t\t\t\t}\r\n\t\t\t\t\t\t\t// });\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.videosrc = pagination.li[0].url;\r\n\t\t\t\t\t\t\tthis.videoId = pagination.li[0].id;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tjsdetail() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '../tutor-introduced/tutor-introduced?tid=' + this.teacher.id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tget_creditinfo(id, goodstype) {\r\n\t\t\t\tconst BASE_URL = uni.BASE_URL\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: BASE_URL + 'index/credit/creditinfo',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tid: id,\r\n\t\t\t\t\t\tgoodstype: goodstype\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data)\r\n\t\t\t\t\t\tthis.creditinfo = res.data.data\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tget_seckillinfo(id, goodstype) {\r\n\t\t\t\tconst BASE_URL = uni.BASE_URL\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: BASE_URL + 'index/seckill/seckillinfo',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tid: id,\r\n\t\t\t\t\t\tgoodstype: goodstype\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data)\r\n\t\t\t\t\t\tthis.seckillinfo = res.data.data\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tget_pintuaninfo(id, goodstype) {\r\n\t\t\t\tconst BASE_URL = uni.BASE_URL\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: BASE_URL + 'index/pintuan/pintuaninfo',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tid: id,\r\n\t\t\t\t\t\tgoodstype: goodstype\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data)\r\n\t\t\t\t\t\tthis.pintuaninfo = res.data.data\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsaveStudytime(studytime, media) {\r\n\t\t\t\tconst BASE_URL = uni.BASE_URL\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: BASE_URL + 'index/user/save_studytime',\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tuid: this.uid,\r\n\t\t\t\t\t\tmedia: media,\r\n\t\t\t\t\t\tstudytime: studytime\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsaveAtime() {\r\n\t\t\t\tvar atime = uni.getStorageSync('atime')\r\n\t\t\t\tvar data = {}\r\n\t\t\t\tif (atime) {\r\n\t\t\t\t\tdata.atime = atime.atime + this.audioStudyTime\r\n\t\t\t\t\tdata.nowtime = (new Date()).getTime()\r\n\t\t\t\t\tuni.setStorageSync('atime', data)\r\n\t\t\t\t\tthis.saveStudytime(data.atime, 'audio')\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdata.atime = this.audioStudyTime\r\n\t\t\t\t\tdata.nowtime = (new Date()).getTime()\r\n\t\t\t\t\tuni.setStorageSync('atime', data)\r\n\t\t\t\t\tthis.saveStudytime(data.atime, 'audio')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsaveVtime() {\r\n\t\t\t\tvar vtime = uni.getStorageSync('vtime')\r\n\t\t\t\tvar data = {}\r\n\t\t\t\tif (vtime) {\r\n\t\t\t\t\tdata.vtime = vtime.vtime + this.videoStudyTime\r\n\t\t\t\t\tdata.nowtime = (new Date()).getTime()\r\n\t\t\t\t\tuni.setStorageSync('vtime', data)\r\n\t\t\t\t\tthis.saveStudytime(data.vtime, 'video')\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdata.vtime = this.videoStudyTime\r\n\t\t\t\t\tdata.nowtime = (new Date()).getTime()\r\n\t\t\t\t\tuni.setStorageSync('vtime', data)\r\n\t\t\t\t\tthis.saveStudytime(data.vtime, 'video')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcountVtime() {\r\n\t\t\t\tthis.t2 = setInterval(() => {\r\n\t\t\t\t\tthis.videoStudyTime++\r\n\t\t\t\t\t//console.log(this.videoStudyTime)\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\tcountAtime() {\r\n\t\t\t\tthis.t3 = setInterval(() => {\r\n\t\t\t\t\tthis.audioStudyTime++\r\n\t\t\t\t\t//console.log(this.audioStudyTime)\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\tdingyue() {\r\n\t\t\t\tconst BASE_URL = uni.BASE_URL\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: BASE_URL + 'index/courses/dingyue',\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tuid: this.uid,\r\n\t\t\t\t\t\tmedia: this.menuinfo.media,\r\n\t\t\t\t\t\tmenuid: this.menuid,\r\n\t\t\t\t\t\tgoodstype: 'course'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t\tthis.is_dingyue = res.data.data == 1 ? true : false\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tconsole.log(res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tpay() {\r\n\t\t\t\tif (this.sonid == '') {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +\r\n\t\t\t\t\t\t\t'&goodstype=course'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid + '&sonid=' +\r\n\t\t\t\t\t\t\tthis.sonid + '&goodstype=course'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpay1() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t_this.reload = true;\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login?type=wx\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\tthis.apiPay(this.menuinfo.id, this.menuinfo.Cost,'class').then(data => {\r\n\t\t\t\t\tconsole.log(data);\r\n\t\t\t\t\t// 仅作为示例，非真实参数信息。\r\n\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\t\t...data.pay,\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// 此处的代码为刷新当前页面\r\n\t\t\t\t\t\t\tvar pages = getCurrentPages();\r\n\t\t\t\t\t\t\tvar curPage = pages[pages.length - 1];\r\n\t\t\t\t\t\t\tcurPage.onLoad(_this.option);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\ttitle: '支付失败',\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tconsole.log('fail:' + JSON.stringify(err));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcollect() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t_this.reload = true;\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login?type=wx\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\tthis.$http.get(\"v1/course/docollect\", {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid:_this.menuinfo.id,\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tif (res.data.code == 0) {\r\n\t\t\t\t\t\t// 此处的代码为刷新当前页面\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.data.data,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tvar pages = getCurrentPages();\r\n\t\t\t\t\t\tvar curPage = pages[pages.length - 1];\r\n\t\t\t\t\t\tcurPage.onLoad(_this.option);\r\n\t\t\t\t\t} else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '操作失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\texchange() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +\r\n\t\t\t\t\t\t'&goodstype=course&action=credit'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tseckill() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +\r\n\t\t\t\t\t\t'&goodstype=course&action=seckill'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tpintuan() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/confirm-order-form/confirm-order-form?menuid=' + this.menuid +\r\n\t\t\t\t\t\t'&goodstype=course&action=pintuan'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tfullscreen(e) {\r\n\t\t\t\tthis.videofullscreen = e.detail.fullScreen\r\n\t\t\t},\r\n\t\t\tvideo_onplay() {\r\n\t\t\t\t// this.sign()\r\n\t\t\t\tconsole.log('play')\r\n\t\t\t\tif(this.menuinfo.is_buy==1){\r\n\t\t\t\t\tthis.countVtime()\r\n\t\t\t\t\tlet audio = innerAudioContext;\r\n\t\t\t\t\taudio.pause()\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t// \ttitle: '请购买',\r\n\t\t\t\t\t// \ticon: 'none'\r\n\t\t\t\t\t// });\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tvideo_onpause() {\r\n\t\t\t\tclearInterval(this.t2)\r\n\t\t\t\tthis.saveVtime()\r\n\t\t\t},\r\n\t\t\tvideo_onend() {\r\n\t\t\t\tclearInterval(this.t2)\r\n\t\t\t\tthis.saveVtime()\r\n\t\t\t},\r\n\t\t\tvideo_timeUpdate(e) {\r\n\t\t\t\tlet video_currentTime = e.detail.currentTime\r\n\t\t\t\tlet video_duration = e.detail.duration\r\n\t\t\t\t// if(!this.is_free || !this.son_is_pay){\r\n\t\t\t\t// \tif(!this.son_is_pay){\r\n\t\t\t\t// \t\tif(video_currentTime>=this.free_time){\r\n\t\t\t\t// \t\t\tif(this.videofullscreen){\r\n\t\t\t\t// \t\t\t\tthis.videoContext.exitFullScreen()\r\n\t\t\t\t// \t\t\t}\r\n\t\t\t\t// \t\t\tthis.videoContext.pause()\r\n\t\t\t\t// \t\t\tuni.showModal({\r\n\t\t\t\t// \t\t\t\ttitle: '试看结束',\r\n\t\t\t\t// \t\t\t\tcontent: '需要解锁该课程吗?',\r\n\t\t\t\t// \t\t\t\tsuccess: function (res) {\r\n\t\t\t\t// \t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t// \t\t\t\t\t\tthis.pay()\r\n\t\t\t\t// \t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t// \t\t\t\t\t\tthis.videoContext.seek(0)\r\n\t\t\t\t// \t\t\t\t\t}\r\n\t\t\t\t// \t\t\t\t}\r\n\t\t\t\t// \t\t\t});\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\tgaosi_ani() {\r\n\t\t\t\tif (this.gaosi == true) {\r\n\t\t\t\t\tthis.t1 = setInterval(function() {\r\n\t\t\t\t\t\tthis.gaosi = false\r\n\t\t\t\t\t\tif (this.gaosi == false) {\r\n\t\t\t\t\t\t\tthis.gaosi = true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 4000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tprogress_text(e) {\r\n\t\t\t\tlet audio = innerAudioContext\r\n\t\t\t\tlet progress = e.detail.value\r\n\t\t\t\tthis.currentTime = this.format((progress / 100) * audio.duration)\r\n\t\t\t},\r\n\t\t\tprogress_change(e) {\r\n\t\t\t\tlet audio = innerAudioContext\r\n\t\t\t\tlet progress = e.detail.value\r\n\t\t\t\tthis.currentTime = this.format((progress / 100) * audio.duration)\r\n\t\t\t\taudio.seek((progress / 100) * audio.duration)\r\n\t\t\t},\r\n\t\t\tchange_slide(e) {\r\n\t\t\t\tlet audio = innerAudioContext;\r\n\t\t\t\tif (e == 1) {\r\n\t\t\t\t\taudio.seek(audio.currentTime + 15)\r\n\t\t\t\t} else {\r\n\t\t\t\t\taudio.seek(audio.currentTime - 15)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tplay() {\r\n\t\t\t\tlet audio = innerAudioContext;\r\n\t\t\t\tif (audio.paused) {\r\n\t\t\t\t\taudio.play()\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\taudio.pause()\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmuluchange(e) {\r\n\t\t\t\tthis.btnnum = e\r\n\t\t\t},\r\n\t\t\tvideoErrorCallback(e) {\r\n\t\t\t\t// uni.showModal({\r\n\t\t\t\t// \tcontent: e.target.errMsg,\r\n\t\t\t\t// \tshowCancel: false\r\n\t\t\t\t// })\r\n\t\t\t},\r\n\t\t\topenvideo(item) {\r\n\t\t\t\tconsole.log(item)\r\n\t\t\t\tif(this.menuinfo.Cost==0){\r\n\t\t\t\t\tthis.sonid = item.id\r\n\t\t\t\t\tthis.son_is_pay = true\r\n\t\t\t\t\tthis.videostate = 'video'\r\n\t\t\t\t\tthis.videosrc = item.url\r\n\t\t\t\t\tthis.videoId = item.id\r\n\t\t\t\t\tthis.videoContext.pause();\r\n\t\t\t\t\tthis.videoContext.play();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(this.menuinfo.is_buy==0){\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t    content: '购买课程后可观看?',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) =>{\r\n\t\t\t\t\t\t\t\t\t\t\tvar that=this\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.pay1()\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// that.currentTime='00:00'\r\n\t\t\t\t\t\t\t\t\t\t\t\t// audio.seek(0)\r\n\t\t\t\t\t\t\t\t\t\t\t\t// that.audio_progress=0\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.sonid = item.id\r\n\t\t\t\t\t\tthis.son_is_pay = true\r\n\t\t\t\t\t\tthis.videostate = 'video'\r\n\t\t\t\t\t\tthis.videosrc = item.url\r\n\t\t\t\t\t\tthis.videoId = item.id\r\n\t\t\t\t\t\tthis.videoContext.pause();\r\n\t\t\t\t\t\tthis.videoContext.play();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t//this.video_onplay()\r\n\t\t\t\t//this.sign()\r\n\t\t\t\t// var menu_free=this.is_free || item.is_pay==1?1:0\r\n\t\t\t\t// if(this.is_free || item.is_pay==1){\r\n\t\t\t\t// \tthis.son_is_pay=true\r\n\t\t\t\t// \tif(item.media === 'video') {\r\n\t\t\t\t// \t\tthis.videostate = 'video'\r\n\t\t\t\t// \t\tthis.videosrc = item.src\r\n\t\t\t\t// \t} else if (item.media === 'audio') {\r\n\t\t\t\t// \t\tthis.videostate = 'audio'\r\n\t\t\t\t// \t\tif(item.src){\r\n\t\t\t\t// \t\t\tthis.gaosi=true\r\n\t\t\t\t// \t\t\tlet audio=innerAudioContext;\r\n\t\t\t\t// \t\t\t//#ifndef MP-WEIXIN\r\n\t\t\t\t// \t\t\taudio.src = item.src\r\n\t\t\t\t// \t\t\taudio.autoplay = true\r\n\r\n\t\t\t\t// \t\t\t//#endif\r\n\t\t\t\t// \t\t\t//#ifdef MP-WEIXIN\r\n\t\t\t\t// \t\t\taudio.title = item.coursename;\r\n\t\t\t\t// \t\t\taudio.singer = this.teacher.imgname;\r\n\t\t\t\t// \t\t\taudio.coverImgUrl = this.HOST_URL+this.menuinfo.thumb;\r\n\t\t\t\t// \t\t\taudio.src = item.src;\r\n\t\t\t\t// \t\t\t//#endif\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t} else {\r\n\t\t\t\t// \t\tuni.navigateTo({\r\n\t\t\t\t// \t\t\turl: '/pages/tuwen/tuwen?twid='+item.id+'&is_free='+menu_free,\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }else{\r\n\t\t\t\t// \tthis.son_is_pay=false\r\n\t\t\t\t// \tif(item.is_sk==1){\r\n\t\t\t\t// \t\tthis.free_time=item.freesecond\r\n\t\t\t\t// \t\tif(item.media === 'video') {\r\n\t\t\t\t// \t\t\tthis.videostate = 'video'\r\n\t\t\t\t// \t\t\tthis.videosrc = item.src\r\n\t\t\t\t// \t\t} else if (item.media === 'audio') {\r\n\t\t\t\t// \t\t\tthis.videostate = 'audio'\r\n\t\t\t\t// \t\t\tif(item.src){\r\n\t\t\t\t// \t\t\t\tthis.gaosi=true\r\n\t\t\t\t// \t\t\t\tlet audio=innerAudioContext;\r\n\t\t\t\t// \t\t\t\t//#ifndef MP-WEIXIN\r\n\t\t\t\t// \t\t\t\taudio.src = item.src\r\n\t\t\t\t// \t\t\t\taudio.autoplay = true\r\n\t\t\t\t// \t\t\t\t//audio.loop = true\r\n\t\t\t\t// \t\t\t\t//#endif\r\n\t\t\t\t// \t\t\t\t//#ifdef MP-WEIXIN\r\n\t\t\t\t// \t\t\t\taudio.title = item.coursename;\r\n\t\t\t\t// \t\t\t\taudio.singer = this.teacher.imgname;\r\n\t\t\t\t// \t\t\t\taudio.coverImgUrl = this.HOST_URL+this.menuinfo.thumb;\r\n\t\t\t\t// \t\t\t\taudio.src = item.src;\r\n\t\t\t\t// \t\t\t\t//#endif\r\n\t\t\t\t// \t\t\t}\r\n\t\t\t\t// \t\t} else {\r\n\t\t\t\t// \t\t\tuni.navigateTo({\r\n\t\t\t\t// \t\t\t\turl: '/pages/tuwen/tuwen?twid='+item.id+'&is_free='+menu_free,\r\n\t\t\t\t// \t\t\t})\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}else{\r\n\t\t\t\t// \t\tuni.showModal({\r\n\t\t\t\t// \t\t\ttitle: '提示',\r\n\t\t\t\t// \t\t\tcontent: '要解锁单个课程吗?',\r\n\t\t\t\t// \t\t\tsuccess: function (res) {\r\n\t\t\t\t// \t\t\t\tif (res.confirm) {\r\n\t\t\t\t// \t\t\t\t\tthis.pay()\r\n\t\t\t\t// \t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t// \t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t// \t\t\t\t}\r\n\t\t\t\t// \t\t\t}\r\n\t\t\t\t// \t\t});\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\t//格式化时长\r\n\t\t\tformat(num) {\r\n\t\t\t\treturn '0'.repeat(2 - String(Math.floor(num / 60)).length) + Math.floor(num / 60) + ':' + '0'.repeat(2 -\r\n\t\t\t\t\tString(\r\n\t\t\t\t\t\tMath.floor(num % 60)).length) + Math.floor(num % 60)\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tapiGetCourseList: function(id) {\r\n\t\t\t\treturn this.$http.get('/v1/course/course_detail', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tid\r\n\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsign(){\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.$http.post(\"v1/member/doSign\", {\r\n\t\t\t\t\t\tid: this.project.pid,\r\n\t\t\t\t\t\tpid: this.project.id,\r\n\t\t\t\t\t\tcid:this.videoId\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 5000);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 创建订单\r\n\t\t\t * @param {Object} project_id\r\n\t\t\t * @param {Object} money\r\n\t\t\t */\r\n\t\t\tapiPay: function(id, money,type) {\r\n\t\t\t\treturn this.$http.post('/v1/course_sign_up', {\r\n\t\t\t\t\tid,\r\n\t\t\t\t\tmoney,\r\n\t\t\t\t\ttype\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n\tpage {\r\n\t\tbackground-color: #f3f3f3;\r\n\t\tfont-family: SimHei;\r\n\t}\r\n\r\n\t.container {\r\n\t\tbackground-color: #f3f3f3;\r\n\t}\r\n\r\n\t.video {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t// 视频部分\r\n\t.shipinpart {\r\n\t\theight: 400rpx;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t&-media {\r\n\t\t\theight: 400rpx;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.audio_bg {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: -19;\r\n\t\t\t\ttransition: all .4s cubic-bezier(0.42, 0, 0.58, 1) 0s;\r\n\t\t\t}\r\n\r\n\t\t\t.donghua(@DHname) {\r\n\t\t\t\t@keyframes @DHname {\r\n\t\t\t\t\t0% {\r\n\t\t\t\t\t\tfilter: blur(0upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t5% {\r\n\t\t\t\t\t\tfilter: blur(1upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t10% {\r\n\t\t\t\t\t\tfilter: blur(2upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t15% {\r\n\t\t\t\t\t\tfilter: blur(3upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t20% {\r\n\t\t\t\t\t\tfilter: blur(4upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t25% {\r\n\t\t\t\t\t\tfilter: blur(5upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t30% {\r\n\t\t\t\t\t\tfilter: blur(6upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t35% {\r\n\t\t\t\t\t\tfilter: blur(7upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t40% {\r\n\t\t\t\t\t\tfilter: blur(8upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t45% {\r\n\t\t\t\t\t\tfilter: blur(9upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t50% {\r\n\t\t\t\t\t\tfilter: blur(10upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t55% {\r\n\t\t\t\t\t\tfilter: blur(9upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t60% {\r\n\t\t\t\t\t\tfilter: blur(8upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t65% {\r\n\t\t\t\t\t\tfilter: blur(7upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t70% {\r\n\t\t\t\t\t\tfilter: blur(6upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t75% {\r\n\t\t\t\t\t\tfilter: blur(5upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t80% {\r\n\t\t\t\t\t\tfilter: blur(4upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t85% {\r\n\t\t\t\t\t\tfilter: blur(3upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t90% {\r\n\t\t\t\t\t\tfilter: blur(2upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t95% {\r\n\t\t\t\t\t\tfilter: blur(1upx);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t100% {\r\n\t\t\t\t\t\tfilter: blur(0upx);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t;\r\n\t\t\t.donghua(myDongHua);\r\n\r\n\t\t\t.animation(@animation-name, @animation-duration, @animation-iteration-count) {\r\n\t\t\t\tanimation: @arguments;\r\n\t\t\t}\r\n\r\n\t\t\t.gaosi_filter {\r\n\t\t\t\t.animation(myDongHua, 4s, infinite);\r\n\t\t\t\ttransition: all 4s cubic-bezier(0.42, 0, 0.58, 1) 0s;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 400rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.audio {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 400rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tz-index: 19;\r\n\t\t\t\tpadding-bottom: 20upx;\r\n\r\n\t\t\t\t.bofang {\r\n\t\t\t\t\twidth: 80upx;\r\n\t\t\t\t\theight: 80upx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 80upx;\r\n\t\t\t\t\t\theight: 80upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&-wrapper {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tbackground-image: linear-gradient(to bottom, rgba(255, 255, 255, .25), #fff);\r\n\r\n\t\t\t\t\t.prev,\r\n\t\t\t\t\t.next {\r\n\t\t\t\t\t\twidth: 48upx;\r\n\t\t\t\t\t\theight: 44upx;\r\n\t\t\t\t\t\tmargin: 0 20upx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.audio-number {\r\n\t\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.audio-slider {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&-info {\r\n\t\t\tz-index: 19;\r\n\t\t\tpadding: 0 20upx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.info-span1 {\r\n\t\t\t\tmargin-top: 35upx;\r\n\t\t\t\tfont-size: 32upx;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #070707;\r\n\t\t\t\tletter-spacing: 5upx;\r\n\t\t\t}\r\n\r\n\t\t\t.info-span2 {\r\n\t\t\t\tletter-spacing: 5upx;\r\n\t\t\t\tfont-size: 26upx;\r\n\t\t\t\tcolor: #373737;\r\n\t\t\t\tmargin-top: 16upx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\ttext:nth-child(2) {\r\n\t\t\t\t\tletter-spacing: 1upx;\r\n\t\t\t\t\twidth: 110upx;\r\n\t\t\t\t\theight: 43upx;\r\n\t\t\t\t\tborder-radius: 10upx;\r\n\t\t\t\t\tbackground-color: #e4edff;\r\n\t\t\t\t\tcolor: #4b89ff;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 43upx;\r\n\t\t\t\t\tmargin-left: 50upx;\r\n\t\t\t\t\tmargin-right: 30upx;\r\n\t\t\t\t\tbox-shadow: 0upx 2upx 3upx 1upx #8dbeff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.info-span5 {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.info-span3 {\r\n\t\t\t\tmargin-top: 18upx;\r\n\t\t\t\tfont-size: 24upx;\r\n\t\t\t\tcolor: #b3b3b3;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\ttext:nth-child(2) {\r\n\t\t\t\t\tmargin: 0 15upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.info-span4 {\r\n\t\t\t\tmargin-top: 22upx;\r\n\t\t\t\tcolor: #E98438;\r\n\t\t\t\tfont-size: 50upx;\r\n\t\t\t\tmargin-right: 40upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t// 课程部分\r\n\t.kechengpart {\r\n\t\tmargin-top: 10upx;\r\n\t\tbackground-color: #fff;\r\n\t\tmin-height: 1000rpx;\r\n\r\n\t\t&-title {\r\n\t\t\theight: 125upx;\r\n\t\t\t// background-color: red;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding-top: 45upx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\r\n\t\t\t&-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\tview {\r\n\t\t\t\t\tfont-size: 30upx;\r\n\t\t\t\t\tcolor: #575757;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btna {\r\n\t\t\t\t\t// 需要追加到view上的class\r\n\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\tcolor: #131313;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t._underline {\r\n\t\t\t\t\t// 需要追加到view下方的下划线class\r\n\t\t\t\t\twidth: 70upx;\r\n\t\t\t\t\theight: 7upx;\r\n\t\t\t\t\tbackground-color: #2f77ff;\r\n\t\t\t\t\tborder-radius: 5upx;\r\n\t\t\t\t\tmargin-top: 15upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t&-content {\r\n\r\n\t\t\t// 课程简介\r\n\t\t\t.kcjs {\r\n\t\t\t\tpadding: 0 20upx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tdisplay: none;\r\n\r\n\t\t\t\t&-lecturer {\r\n\t\t\t\t\t&-top {\r\n\t\t\t\t\t\theight: 60upx;\r\n\t\t\t\t\t\tdisplay: inline-flex;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 47upx;\r\n\t\t\t\t\t\t\theight: 33upx;\r\n\t\t\t\t\t\t\tmargin-top: 4upx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 26upx;\r\n\t\t\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\t\t\tcolor: #020202;\r\n\t\t\t\t\t\t\tmargin-left: 15upx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.jiangshi-right {\r\n\t\t\t\t\t\tdisplay: inline-flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tfloat: right;\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 22upx;\r\n\t\t\t\t\t\t\tcolor: #6d6d6d;\r\n\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 20upx;\r\n\t\t\t\t\t\t\theight: 22upx;\r\n\t\t\t\t\t\t\tmargin-left: 5upx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&-bottom {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 400upx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.jiangshi-left {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 80upx;\r\n\t\t\t\t\t\t\t\theight: 80upx;\r\n\t\t\t\t\t\t\t\tborder-radius: 40upx;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tfont-size: 26upx;\r\n\t\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.jiangshi-right {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tfont-size: 26upx;\r\n\t\t\t\t\t\t\t\tcolor: #C0C0C0;\r\n\t\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 20upx;\r\n\t\t\t\t\t\t\t\theight: 26upx;\r\n\t\t\t\t\t\t\t\tmargin-left: 5upx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&-brief {\r\n\t\t\t\t\tmargin-top: 40upx;\r\n\t\t\t\t\tmargin-bottom: 150upx;\r\n\r\n\t\t\t\t\t&-top {\r\n\t\t\t\t\t\theight: 75upx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 53upx;\r\n\t\t\t\t\t\t\theight: 49upx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 26upx;\r\n\t\t\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\t\t\tcolor: #020202;\r\n\t\t\t\t\t\t\tmargin-left: 15upx;\r\n\t\t\t\t\t\t\tmargin-top: 4upx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&-center {\r\n\t\t\t\t\t\tpadding-bottom: 30upx;\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 26upx;\r\n\t\t\t\t\t\t\tcolor: #313131;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 课程目录\r\n\t\t\t.kcml {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\tpadding: 0 20upx;\r\n\t\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t\t&-list {\r\n\t\t\t\t\tpadding: 20upx;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tborder-bottom: 1rpx solid #eee;\r\n\r\n\t\t\t\t\t&-left {\r\n\t\t\t\t\t\twidth: 436upx;\r\n\t\t\t\t\t\theight: 60upx;\r\n\t\t\t\t\t\tline-height: 60upx;\r\n\r\n\t\t\t\t\t\t.yinpin {\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\twidth: 64upx;\r\n\t\t\t\t\t\t\theight: 32upx;\r\n\t\t\t\t\t\t\tfont-size: 20upx;\r\n\t\t\t\t\t\t\tcolor: #ff6969;\r\n\t\t\t\t\t\t\tborder: 2upx solid #ff6969;\r\n\t\t\t\t\t\t\tborder-radius: 5upx;\r\n\t\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 28upx;\r\n\t\t\t\t\t\t\tmargin-right: 15upx;\r\n\t\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.shipin {\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\twidth: 64upx;\r\n\t\t\t\t\t\t\theight: 32upx;\r\n\t\t\t\t\t\t\tfont-size: 20upx;\r\n\t\t\t\t\t\t\tcolor: #398cff;\r\n\t\t\t\t\t\t\tborder: 2upx solid #398cff;\r\n\t\t\t\t\t\t\tborder-radius: 5upx;\r\n\t\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 24upx;\r\n\t\t\t\t\t\t\tmargin-right: 15upx;\r\n\t\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.tuwen {\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\twidth: 64upx;\r\n\t\t\t\t\t\t\theight: 32upx;\r\n\t\t\t\t\t\t\tfont-size: 20upx;\r\n\t\t\t\t\t\t\tcolor: #8bc34a;\r\n\t\t\t\t\t\t\tborder: 2upx solid #8bc34a;\r\n\t\t\t\t\t\t\tborder-radius: 5upx;\r\n\t\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 24upx;\r\n\t\t\t\t\t\t\tmargin-right: 15upx;\r\n\t\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttext:nth-child(2) {\r\n\t\t\t\t\t\t\tfont-size: 25upx;\r\n\t\t\t\t\t\t\tcolor: #070707;\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\twidth: 80%;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&-right {\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t\t\t\tcolor: #007AFF;\r\n\t\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 30upx;\r\n\t\t\t\t\t\t\theight: 30upx;\r\n\t\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 用户评价\r\n\t\t\t.yhpj {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-bottom: 140upx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\r\n\t\t\t\t&-list {\r\n\t\t\t\t\t.item {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tpadding: 0 20upx;\r\n\t\t\t\t\t\tmargin-top: 40upx;\r\n\r\n\t\t\t\t\t\t&-left {\r\n\t\t\t\t\t\t\twidth: 100upx;\r\n\t\t\t\t\t\t\theight: 100upx;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tmargin-right: 20upx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&-right {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t\t\t\t&-top {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t\t\ttext:nth-child(1) {\r\n\t\t\t\t\t\t\t\t\tfont-size: 32upx;\r\n\t\t\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\ttext:nth-child(2) {\r\n\t\t\t\t\t\t\t\t\tfont-size: 26upx;\r\n\t\t\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&-bottom {\r\n\t\t\t\t\t\t\t\tmax-width: 500upx;\r\n\t\t\t\t\t\t\t\t// height: 60upx;\r\n\t\t\t\t\t\t\t\tmargin-top: 10upx;\r\n\t\t\t\t\t\t\t\tpadding: 20upx;\r\n\t\t\t\t\t\t\t\tborder-radius: 10upx;\r\n\t\t\t\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\t\t\t\ttext-align: left;\r\n\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tfont-size: 30upx;\r\n\t\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nopl {\r\n\t\t\t\twidth: 334upx;\r\n\t\t\t\theight: 243upx;\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t}\r\n\r\n\t\t\t.dis {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.buy {\r\n\t\twidth: 100%;\r\n\t\theight: 122upx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-evenly;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 40upx 40upx 0 0;\r\n\t\tborder-top: 1rpx solid #eee;\r\n\r\n\t\t&-left {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: space-evenly;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 37upx;\r\n\t\t\t\theight: 37upx;\r\n\t\t\t}\r\n\r\n\t\t\ttext {\r\n\t\t\t\t// margin-top: 15upx;\r\n\t\t\t\tfont-size: 24upx;\r\n\t\t\t\tcolor: #ff6229;\r\n\t\t\t}\r\n\t\t\t.info-span4 {\r\n\t\t\t\tcolor: #ff6229;\r\n\t\t\t\tfont-size: 50upx;\r\n\t\t\t\tmargin-right: 40upx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.sharebtn {\r\n\t\t\tmargin: 0;\r\n\t\t\tpadding: 0;\r\n\t\t\toutline: none;\r\n\t\t\tborder-radius: 0;\r\n\t\t\tbackground-color: transparent;\r\n\t\t\tline-height: inherit;\r\n\t\t\twidth: max-content;\r\n\t\t}\r\n\r\n\t\t.sharebtn:after {\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t&-right {\r\n\t\t\twidth: 450upx;\r\n\t\t\theight: 80upx;\r\n\t\t\tbackground-image: linear-gradient(to right, #4498ff, #1763ff);\r\n\t\t\tborder-radius: 80upx;\r\n\t\t\tfont-size: 34upx;\r\n\t\t\tfont-weight: 700;\r\n\t\t\tcolor: #fff;\r\n\t\t\t// border: 3upx solid #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 80upx;\r\n\t\t\tbox-shadow: 0rpx 2rpx 2rpx 1rpx #8dbeff;\r\n\t\t\tletter-spacing: 7rpx;\r\n\t\t}\r\n\t}\r\n\t.fontcolor{\r\n\t\tcolor:#FF0066;\r\n\t}\r\n\t.cover-box {\r\n\t\twidth: 100%;\r\n\t\t// height: auto;\r\n\t\tmin-height: 200rpx;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t.cover {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\t// border-radius: 10rpx;\r\n\t\t}\r\n\t\t.button{\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 5%;\r\n\t\t\tright: 5%;\r\n\t\t\ttransform: translateX(0);\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tbackground-color: blue;\r\n\t\t\tcolor: white;\r\n\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\tfont-size: 40rpx;\r\n\t\t}\r\n\t}\r\n\t.p-b-btn{\r\n\t\tdisplay:flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\t// font-size: $font-sm;\r\n\t\t// color: $font-color-base;\r\n\t\twidth: 96upx;\r\n\t\t.yticon{\r\n\t\t\tfont-size: 40upx;\r\n\t\t\tline-height: 48upx;\r\n\t\t\t// color: $font-color-light;\r\n\t\t}\r\n\t\t&.active, &.active .yticon{\r\n\t\t\t// color: $uni-color-primary;\r\n\t\t}\r\n\t\t.icon-fenxiang2{\r\n\t\t\tfont-size: 42upx;\r\n\t\t\ttransform: translateY(-2upx);\r\n\t\t}\r\n\t\t.icon-shoucang{\r\n\t\t\tfont-size: 46upx;\r\n\t\t}\r\n\t}\r\n\t.contact-btn {\r\n\t  display: inline-block;\r\n\t  position: absolute;\r\n\t  width: 20%;\r\n\t  background: salmon;\r\n\t    opacity: 0;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=style&index=0&id=b1db4d34&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./course.vue?vue&type=style&index=0&id=b1db4d34&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754041064024\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}