.search.data-v-3e425c46 {
  height: 90rpx;
  background-color: #4b89ff;
  padding: 0 20rpx;
  padding-top: 30rpx;
}
.search-input.data-v-3e425c46 {
  position: relative;
  display: flex;
}
.search-input text.data-v-3e425c46 {
  font-size: 31rpx;
  color: #fff;
  margin-top: 10rpx;
}
.search-input image.data-v-3e425c46 {
  width: 25rpx;
  height: 14rpx;
  margin: 25rpx 20rpx 0 10rpx;
}
.search-input input.data-v-3e425c46 {
  width: 710rpx;
  height: 63rpx;
  border: 0;
  background-color: #fff;
  border-radius: 63rpx;
  font-size: 24rpx;
  padding-left: 20rpx;
  box-sizing: border-box;
}
.search-input .goods-search.data-v-3e425c46 {
  width: 28rpx;
  height: 28rpx;
  position: absolute;
  right: 10rpx;
  top: -5rpx;
  z-index: 99;
}
.scroll.data-v-3e425c46 {
  height: calc(100vh - 5rpx);
  display: flex;
}
.scroll-left.data-v-3e425c46 {
  flex: 2;
}
.scroll-left view.data-v-3e425c46 {
  height: 120rpx;
  background-color: #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  letter-spacing: 2rpx;
}
.scroll-left .active.data-v-3e425c46 {
  background-color: #fff;
}
.scroll-right.data-v-3e425c46 {
  flex: 5;
  background-color: #fff;
  padding: 0 10rpx;
  box-sizing: border-box;
}
.scroll-right .item.data-v-3e425c46 {
  display: inline-block;
  width: 220rpx;
  height: 60rpx;
  background-color: #eee;
  text-align: center;
  line-height: 60rpx;
  border-radius: 60rpx;
  margin: 20rpx 12rpx 0;
}
.scroll-right .item text.data-v-3e425c46 {
  font-size: 30rpx;
  font-weight: 700;
  letter-spacing: 4rpx;
  color: #333;
}
.list-box .item-box.data-v-3e425c46 {
  padding: 10rpx 10rpx;
  background-color: #fff;
  border-radius: 10rpx;
  color: #666;
  font-size: 28rpx;
  border-bottom: 1rpx solid #ebebeb;
}
.list-box .item-box .top-box.data-v-3e425c46 {
  position: relative;
  padding: 20rpx;
}
.list-box .item-box .top-box .cover-box-hot.data-v-3e425c46 {
  width: 35%;
  height: auto;
  min-height: 120rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot .cover.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box-hot .cover.data-v-3e425c46 :after {
  background-color: red;
  border-radius: 10rpx;
  color: #fff;
  content: "hot";
  font-size: 25rpx;
  line-height: 1;
  padding: 2rpx 6rpx;
  position: absolute;
  left: 5rpx;
  top: 5rpx;
}
.list-box .item-box .top-box .cover-box-hot .button.data-v-3e425c46 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-box.data-v-3e425c46 {
  width: 150rpx;
  height: auto;
  min-height: 150rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box .cover.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box .button.data-v-3e425c46 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-large-box.data-v-3e425c46 {
  width: 100%;
  height: auto;
  height: 200rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-large-box .cover.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-large-box .button.data-v-3e425c46 {
  position: absolute;
  bottom: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: white;
  padding: 15rpx 20rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .info-box.data-v-3e425c46 {
  flex: 1;
  margin-left: 15rpx;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.list-box .item-box .top-box .info-box .publish-date.data-v-3e425c46 {
  font-size: 32rpx;
  font-weight: bold;
}
.list-box .item-box .top-box .info-box .lang-box.data-v-3e425c46 {
  font-size: 24rpx;
}
.list-box .item-box .top-box .info-box .title.data-v-3e425c46 {
  font-weight: bold;
  font-size: 24rpx;
  color: #666666;
}
.list-box .item-box .top-box .info-box .end-date.data-v-3e425c46 {
  font-size: 20rpx;
  color: #999999;
}
.list-box .item-box .top-box .info-box .total.data-v-3e425c46 {
  font-size: 20rpx;
  color: #39b54a;
}
.list-box .item-box .top-box .info-box .des.data-v-3e425c46 {
  font-size: 22rpx;
  color: #8f8f94;
}
.list-box .item-box .top-box .info-box .price.data-v-3e425c46 {
  font-size: 24rpx;
  color: red;
  float: right;
}
.list-box .item-box .top-box .info-box .end.data-v-3e425c46 {
  font-size: 24rpx;
  color: blue;
  width: 100%;
}
.list-box .item-box .margin-tb-sm.data-v-3e425c46 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.list-box .item-box .margin-tb-sm .text-sm.data-v-3e425c46 {
  font-size: 24rpx;
}
.list-box .item-box .margin-tb-sm .title.data-v-3e425c46 {
  overflow: hidden;
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
.list-box .item-box .margin-tb-sm .uni-row.data-v-3e425c46 {
  flex-direction: row;
}
.list-box .item-box .margin-tb-sm .align-center.data-v-3e425c46 {
  align-items: center;
}
.list-box .item-box .margin-tb-sm .margin-left-sm.data-v-3e425c46 {
  margin-left: 20rpx;
}

