<template>
  <div class="page-container">
    <div class="page-header">
      <h1>用户管理</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加用户
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item label="角色">
          <el-select v-model="filters.role" placeholder="选择角色" clearable @change="loadUsers">
            <el-option label="管理员" value="admin" />
            <el-option label="教师" value="teacher" />
            <el-option label="学生" value="student" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="loadUsers">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="disabled" />
            <el-option label="待审核" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="搜索用户名或姓名"
            clearable
            @input="handleSearch"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 用户表格 -->
    <div class="table-section">
      <!-- 加载骨架屏 -->
      <Skeleton v-if="loading" type="table" :rows="10" :columns="8" />

      <!-- 用户表格 -->
      <el-table
        v-else
        :data="users"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="#" width="60" />

        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              {{ row.realName?.charAt(0) || row.username?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column prop="username" label="用户名" min-width="120" sortable="custom" />

        <el-table-column prop="realName" label="真实姓名" min-width="120" />

        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />

        <el-table-column prop="phone" label="手机号" width="130" />

        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)" size="small">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastLoginAt" label="最后登录" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginAt) || '从未登录' }}
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button link size="small" @click="viewUser(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button link size="small" @click="editUser(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                link
                size="small"
                @click="toggleUserStatus(row)"
                :class="row.status === 'disabled' ? 'success-button' : 'warning-button'"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === 'disabled' ? '启用' : '禁用' }}
              </el-button>
              <el-button
                link
                size="small"
                @click="deleteUser(row)"
                class="danger-button"
                v-if="row.id !== authStore.user?.id"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedUsers.length > 0">
      <div class="batch-info">
        已选择 {{ selectedUsers.length }} 个用户
      </div>
      <div class="batch-buttons">
        <el-button @click="batchEnable" :disabled="!canBatchEnable">
          <el-icon><Check /></el-icon>
          批量启用
        </el-button>
        <el-button @click="batchDisable" :disabled="!canBatchDisable">
          <el-icon><Close /></el-icon>
          批量禁用
        </el-button>
        <el-button @click="batchDelete" type="danger" :disabled="!canBatchDelete">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadUsers"
        @current-change="loadUsers"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <UserForm
      v-model="showCreateDialog"
      :user="selectedUser"
      @success="handleFormSuccess"
    />

    <!-- 详情对话框 -->
    <UserDetail
      v-model="showDetailDialog"
      :user="selectedUser"
      @edit="editUser"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { get, put, del } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserForm from './components/UserForm.vue'
import UserDetail from './components/UserDetail.vue'
import Skeleton from '@/components/Skeleton/index.vue'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const users = ref([])
const total = ref(0)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedUser = ref(null)
const selectedUsers = ref([])

// 筛选器
const filters = reactive({
  role: '',
  status: '',
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 排序
const sort = reactive({
  prop: '',
  order: ''
})

// 搜索防抖
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    pagination.page = 1
    loadUsers()
  }, 500)
}

// 加载用户数据
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...filters,
      ...sort
    }

    const response = await get('/api/users', params, { showLoading: false })
    if (response.success) {
      users.value = response.data.users || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
    // 使用模拟数据
    users.value = generateMockUsers()
    total.value = users.value.length
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockUsers = () => {
  return [
    {
      id: 1,
      username: 'admin',
      realName: '系统管理员',
      email: '<EMAIL>',
      phone: '13800138000',
      role: 'admin',
      status: 'active',
      avatar: '',
      lastLoginAt: new Date('2024-07-28T10:30:00'),
      createdAt: new Date('2024-01-01')
    },
    {
      id: 2,
      username: 'teacher1',
      realName: '张老师',
      email: '<EMAIL>',
      phone: '13800138001',
      role: 'teacher',
      status: 'active',
      avatar: '',
      lastLoginAt: new Date('2024-07-28T09:15:00'),
      createdAt: new Date('2024-01-15')
    },
    {
      id: 3,
      username: 'pengwei',
      realName: '彭伟',
      email: '<EMAIL>',
      phone: '13800138002',
      role: 'student',
      status: 'active',
      avatar: '',
      lastLoginAt: new Date('2024-07-27T20:45:00'),
      createdAt: new Date('2024-02-01')
    },
    {
      id: 4,
      username: 'student2',
      realName: '李同学',
      email: '<EMAIL>',
      phone: '13800138003',
      role: 'student',
      status: 'pending',
      avatar: '',
      lastLoginAt: null,
      createdAt: new Date('2024-07-25')
    }
  ]
}

// 刷新数据
const refreshData = () => {
  loadUsers()
}

// 查看用户
const viewUser = (user) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

// 编辑用户
const editUser = (user) => {
  selectedUser.value = user
  showCreateDialog.value = true
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  const newStatus = user.status === 'disabled' ? 'active' : 'disabled'
  const action = newStatus === 'active' ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(`确定要${action}用户"${user.realName || user.username}"吗？`, `确认${action}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用API
    user.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch {
    // 用户取消
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户"${user.realName || user.username}"吗？此操作不可恢复！`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用API
    ElMessage.success('删除成功')
    loadUsers()
  } catch {
    // 用户取消
  }
}

// 处理排序
const handleSortChange = ({ prop, order }) => {
  sort.prop = prop
  sort.order = order
  loadUsers()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 批量操作权限检查
const canBatchEnable = computed(() => {
  return selectedUsers.value.some(user => user.status === 'disabled')
})

const canBatchDisable = computed(() => {
  return selectedUsers.value.some(user => user.status === 'active' && user.id !== authStore.user?.id)
})

const canBatchDelete = computed(() => {
  return selectedUsers.value.some(user => user.id !== authStore.user?.id)
})

// 批量启用
const batchEnable = async () => {
  const disabledUsers = selectedUsers.value.filter(user => user.status === 'disabled')
  if (disabledUsers.length === 0) return

  try {
    await ElMessageBox.confirm(`确定要启用选中的 ${disabledUsers.length} 个用户吗？`, '批量启用', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    // 这里应该调用API
    disabledUsers.forEach(user => {
      user.status = 'active'
    })
    ElMessage.success(`成功启用 ${disabledUsers.length} 个用户`)
    selectedUsers.value = []
  } catch {
    // 用户取消
  }
}

// 批量禁用
const batchDisable = async () => {
  const activeUsers = selectedUsers.value.filter(user => user.status === 'active' && user.id !== authStore.user?.id)
  if (activeUsers.length === 0) return

  try {
    await ElMessageBox.confirm(`确定要禁用选中的 ${activeUsers.length} 个用户吗？`, '批量禁用', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用API
    activeUsers.forEach(user => {
      user.status = 'disabled'
    })
    ElMessage.success(`成功禁用 ${activeUsers.length} 个用户`)
    selectedUsers.value = []
  } catch {
    // 用户取消
  }
}

// 批量删除
const batchDelete = async () => {
  const deletableUsers = selectedUsers.value.filter(user => user.id !== authStore.user?.id)
  if (deletableUsers.length === 0) return

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${deletableUsers.length} 个用户吗？此操作不可恢复！`, '批量删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用API
    ElMessage.success(`成功删除 ${deletableUsers.length} 个用户`)
    selectedUsers.value = []
    loadUsers()
  } catch {
    // 用户取消
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedUser.value = null
  loadUsers()
}

// 获取角色类型
const getRoleType = (role) => {
  const types = {
    admin: 'danger',
    teacher: 'warning',
    student: 'primary'
  }
  return types[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    teacher: '教师',
    student: '学生'
  }
  return texts[role] || '未知'
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    active: 'success',
    disabled: 'danger',
    pending: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    active: '正常',
    disabled: '禁用',
    pending: '待审核'
  }
  return texts[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsers()
})
</script>

<style lang="scss" scoped>
.filter-section {
  background: var(--bg-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  margin-bottom: var(--spacing-lg);
}

.table-section {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  overflow: hidden;
}

.table-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;

  .success-button {
    color: var(--success-color);

    &:hover {
      background: var(--success-color);
      color: white;
    }
  }

  .warning-button {
    color: var(--warning-color);

    &:hover {
      background: var(--warning-color);
      color: white;
    }
  }

  .danger-button {
    color: var(--danger-color);

    &:hover {
      background: var(--danger-color);
      color: white;
    }
  }
}

.batch-actions {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-top: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;

  .batch-info {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    font-weight: 500;
  }

  .batch-buttons {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

@media (max-width: 768px) {
  .filter-section {
    .el-form {
      flex-direction: column;

      .el-form-item {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
      }
    }
  }

  .table-actions {
    flex-direction: column;

    .el-button {
      justify-content: flex-start;
    }
  }

  .batch-actions {
    flex-direction: column;
    gap: var(--spacing-md);

    .batch-buttons {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
