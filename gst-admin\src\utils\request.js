import { ElMessage, ElLoading } from 'element-plus'
import router from '@/router'

// 获取API基础URL
export function getApiBaseURL() {
  // 如果是localhost访问，使用localhost
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:8005'
  }
  // 如果是IP访问，使用相同的IP
  return `http://${window.location.hostname}:8005`
}

// 请求拦截器配置
const defaultOptions = {
  timeout: 30000, // 30秒超时
  showLoading: false,
  showError: true,
  showSuccess: false
}

// API请求封装
export async function apiRequest(url, options = {}) {
  const config = { ...defaultOptions, ...options }
  const token = localStorage.getItem('gst_token')
  
  // 显示loading
  let loadingInstance = null
  if (config.showLoading) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }

  try {
    const baseURL = getApiBaseURL()
    const requestOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      ...options
    }

    // 处理请求体
    if (requestOptions.body && typeof requestOptions.body === 'object') {
      requestOptions.body = JSON.stringify(requestOptions.body)
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), config.timeout)

    const response = await fetch(`${baseURL}${url}`, {
      ...requestOptions,
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    // 处理HTTP错误状态
    if (!response.ok) {
      if (response.status === 401) {
        // Token过期或无效
        localStorage.removeItem('gst_token')
        localStorage.removeItem('gst_user')
        ElMessage.error('登录已过期，请重新登录')
        router.push('/login')
        throw new Error('登录已过期')
      }
      
      if (response.status === 403) {
        ElMessage.error('没有权限访问此资源')
        throw new Error('权限不足')
      }
      
      if (response.status === 404) {
        ElMessage.error('请求的资源不存在')
        throw new Error('资源不存在')
      }
      
      if (response.status >= 500) {
        ElMessage.error('服务器错误，请稍后重试')
        throw new Error('服务器错误')
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // 处理业务错误
    if (!data.success && config.showError) {
      ElMessage.error(data.message || '请求失败')
    }

    // 显示成功消息
    if (data.success && config.showSuccess && data.message) {
      ElMessage.success(data.message)
    }

    return data

  } catch (error) {
    console.error('API请求错误:', error)
    
    if (error.name === 'AbortError') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (config.showError && !error.message.includes('登录已过期')) {
      ElMessage.error(error.message || '网络错误，请稍后重试')
    }
    
    throw error
  } finally {
    // 关闭loading
    if (loadingInstance) {
      loadingInstance.close()
    }
  }
}

// GET请求
export function get(url, params = {}, options = {}) {
  const queryString = new URLSearchParams(params).toString()
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return apiRequest(fullUrl, {
    method: 'GET',
    ...options
  })
}

// POST请求
export function post(url, data = {}, options = {}) {
  return apiRequest(url, {
    method: 'POST',
    body: data,
    ...options
  })
}

// PUT请求
export function put(url, data = {}, options = {}) {
  return apiRequest(url, {
    method: 'PUT',
    body: data,
    ...options
  })
}

// DELETE请求
export function del(url, options = {}) {
  return apiRequest(url, {
    method: 'DELETE',
    ...options
  })
}

// 文件上传
export async function uploadFile(url, file, options = {}) {
  const token = localStorage.getItem('gst_token')
  const baseURL = getApiBaseURL()

  const formData = new FormData()
  formData.append('file', file)

  // 添加额外的表单数据
  if (options.data) {
    Object.keys(options.data).forEach(key => {
      formData.append(key, options.data[key])
    })
  }

  try {
    const response = await fetch(`${baseURL}${url}`, {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success && options.showError !== false) {
      ElMessage.error(data.message || '上传失败')
    }

    if (data.success && options.showSuccess) {
      ElMessage.success(data.message || '上传成功')
    }

    return data
  } catch (error) {
    console.error('文件上传错误:', error)
    if (options.showError !== false) {
      ElMessage.error(error.message || '上传失败')
    }
    throw error
  }
}

// 批量文件上传
export async function uploadFiles(url, files, options = {}) {
  const results = []
  const errors = []

  for (let i = 0; i < files.length; i++) {
    try {
      const result = await uploadFile(url, files[i], {
        ...options,
        showError: false,
        showSuccess: false
      })
      results.push(result)
    } catch (error) {
      errors.push({ file: files[i], error })
    }
  }

  if (errors.length > 0 && options.showError !== false) {
    ElMessage.error(`${errors.length} 个文件上传失败`)
  }

  if (results.length > 0 && options.showSuccess) {
    ElMessage.success(`成功上传 ${results.length} 个文件`)
  }

  return { results, errors }
}

// 图片压缩上传
export async function uploadImage(url, file, options = {}) {
  const { maxWidth = 1920, maxHeight = 1080, quality = 0.8 } = options

  // 如果是图片文件，进行压缩
  if (file.type.startsWith('image/')) {
    try {
      const compressedFile = await compressImage(file, { maxWidth, maxHeight, quality })
      return uploadFile(url, compressedFile, options)
    } catch (error) {
      console.warn('图片压缩失败，使用原文件上传:', error)
      return uploadFile(url, file, options)
    }
  } else {
    return uploadFile(url, file, options)
  }
}

// 图片压缩工具函数
function compressImage(file, options = {}) {
  return new Promise((resolve, reject) => {
    const { maxWidth = 1920, maxHeight = 1080, quality = 0.8 } = options

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }

      canvas.width = width
      canvas.height = height

      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height)

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            // 创建新的File对象
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            reject(new Error('图片压缩失败'))
          }
        },
        file.type,
        quality
      )
    }

    img.onerror = () => reject(new Error('图片加载失败'))
    img.src = URL.createObjectURL(file)
  })
}

// 健康检查
export async function healthCheck() {
  try {
    const baseURL = getApiBaseURL()
    const response = await fetch(`${baseURL}/health`, {
      method: 'GET',
      timeout: 5000
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 后端连接正常:', data)
      return true
    } else {
      console.error('❌ 后端健康检查失败:', response.status)
      return false
    }
  } catch (error) {
    console.error('❌ 后端连接失败:', error)
    return false
  }
}
