{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-parse/u-parse.vue?04cd", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-parse/u-parse.vue?afc2", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-parse/u-parse.vue?7ebd", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-parse/u-parse.vue?4ea2", "uni-app:///uni_modules/uview-ui/components/u-parse/u-parse.vue", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-parse/u-parse.vue?9795", "webpack:///D:/gst/gst-uniapp/uni_modules/uview-ui/components/u-parse/u-parse.vue?3536"], "names": ["name", "data", "nodes", "mixins", "components", "node", "watch", "content", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "in", "page", "selector", "scrollTop", "navigateTo", "offset", "deep", "select", "uni", "duration", "resolve", "getText", "text", "traversal", "getRect", "<PERSON><PERSON><PERSON><PERSON>", "height", "_hook"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACatpB;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAOA;EACAA;EACAC;IACA;MACAC;IAIA;EACA;EACAC;EAEAC;IACAC;EACA;EAEAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IAAA;EACA;EACAC;IACA,wCACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAEA,mCACA;QACAC;QACAC;QACAC;MACA;IAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA,sBACA;QACAC;QAiBA;QAEAC;QAEA,yCAEAN,uCAEAO;QACA,eACAL,mDACAK;QAAA,KAEAL;QACAA;UACA,aACA;UACA;UACA;YACA;YACA;YAEA;YACAM;cACAL;cACAM;YACA;UACAC;QACA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;UACA,yBACAC,8CACA,uBACAA,kBACA;YACA;YACA;YACA,sDACAA;YACA;YACA,mBACAC;YACA,8CACAD,kBACA,4CACAA;UACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAE;MAAA;MACA;QACAN,0BAEAR,WAEAO;UAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAQ;MAAA;MACA,8BACA;MACA;MAKA;MAGA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACAjB;MACA;QACA;UACA;UACA;YACA;YACAA;UACA;UACAkB;QACA;MACA;IAEA;IAEA;AACA;AACA;IACAC;MACA;QACA,2BACA;MAAA;IACA;EAkGA;AACA;AAAA,2B;;;;;;;;;;;;;AC7VA;AAAA;AAAA;AAAA;AAAi6B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;ACAr7B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-parse/u-parse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-parse.vue?vue&type=template&id=1674d3be&\"\nvar renderjs\nimport script from \"./u-parse.vue?vue&type=script&lang=js&\"\nexport * from \"./u-parse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-parse.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-parse/u-parse.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=template&id=1674d3be&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=script&lang=js&\"", "<template>\n  <view id=\"_root\" :class=\"(selectable?'_select ':'')+'_root'\">\n    <slot v-if=\"!nodes[0]\" />\n    <!-- #ifndef APP-PLUS-NVUE -->\n    <node v-else :childs=\"nodes\" :opts=\"[lazyLoad,loadingImg,errorImg,showImgMenu]\" />\n    <!-- #endif -->\n    <!-- #ifdef APP-PLUS-NVUE -->\n    <web-view ref=\"web\" src=\"/static/app-plus/mp-html/local.html\" :style=\"'margin-top:-2px;height:' + height + 'px'\" @onPostMessage=\"_onMessage\" />\n    <!-- #endif -->\n  </view>\n</template>\n\n<script>\n\timport props from './props.js';\n/**\n * mp-html v2.0.4\n * @description 富文本组件\n * @tutorial https://github.com/jin-yufeng/mp-html\n * @property {String}\t\t\tbgColor\t\t背景颜色，只适用与APP-PLUS-NVUE\n * @property {String}\t\t\tcontent\t\t用于渲染的富文本字符串（默认 true ）\n * @property {Boolean}\t\t\tcopyLink\t是否允许外部链接被点击时自动复制\n * @property {String}\t\t\tdomain\t\t主域名，用于拼接链接\n * @property {String}\t\t\terrorImg\t图片出错时的占位图链接\n * @property {Boolean}\t\t\tlazyLoad\t是否开启图片懒加载（默认 true ）\n * @property {string}\t\t\tloadingImg\t图片加载过程中的占位图链接\n * @property {Boolean}\t\t\tpauseVideo\t是否在播放一个视频时自动暂停其它视频（默认 true ）\n * @property {Boolean}\t\t\tpreviewImg\t是否允许图片被点击时自动预览（默认 true ）\n * @property {Boolean}\t\t\tscrollTable\t是否给每个表格添加一个滚动层使其能单独横向滚动\n * @property {Boolean}\t\t\tselectable\t是否开启长按复制\n * @property {Boolean}\t\t\tsetTitle\t是否将 title 标签的内容设置到页面标题（默认 true ）\n * @property {Boolean}\t\t\tshowImgMenu\t是否允许图片被长按时显示菜单（默认 true ）\n * @property {Object}\t\t\ttagStyle\t标签的默认样式\n * @property {Boolean | Number}\tuseAnchor\t是否使用锚点链接\n * \n * @event {Function}\tload\tdom 结构加载完毕时触发\n * @event {Function}\tready\t所有图片加载完毕时触发\n * @event {Function}\timgTap\t图片被点击时触发\n * @event {Function}\tlinkTap\t链接被点击时触发\n * @event {Function}\terror\t媒体加载出错时触发\n */\nconst plugins=[]\nconst parser = require('./parser')\n// #ifndef APP-PLUS-NVUE\nimport node from './node/node'\n// #endif\n// #ifdef APP-PLUS-NVUE\nconst dom = weex.requireModule('dom')\n// #endif\nexport default {\n  name: 'mp-html',\n  data() {\n    return {\n      nodes: [],\n      // #ifdef APP-PLUS-NVUE\n      height: 0\n      // #endif\n    }\n  },\n  mixins:[props],\n  // #ifndef APP-PLUS-NVUE\n  components: {\n    node\n  },\n  // #endif\n  watch: {\n    content(content) {\n      this.setContent(content)\n    }\n  },\n  created() {\n    this.plugins = []\n    for (let i = plugins.length; i--;)\n      this.plugins.push(new plugins[i](this))\n  },\n  mounted() {\n    if (this.content && !this.nodes.length)\n      this.setContent(this.content)\n  },\n  beforeDestroy() {\n    this._hook('onDetached')\n    clearInterval(this._timer)\n  },\n  methods: {\n    /**\n     * @description 将锚点跳转的范围限定在一个 scroll-view 内\n     * @param {Object} page scroll-view 所在页面的示例\n     * @param {String} selector scroll-view 的选择器\n     * @param {String} scrollTop scroll-view scroll-top 属性绑定的变量名\n     */\n    in(page, selector, scrollTop) {\n      // #ifndef APP-PLUS-NVUE\n      if (page && selector && scrollTop)\n        this._in = {\n          page,\n          selector,\n          scrollTop\n        }\n      // #endif\n    },\n\n    /**\n     * @description 锚点跳转\n     * @param {String} id 要跳转的锚点 id\n     * @param {Number} offset 跳转位置的偏移量\n     * @returns {Promise}\n     */\n    navigateTo(id, offset) {\n      return new Promise((resolve, reject) => {\n        if (!this.useAnchor)\n          return reject('Anchor is disabled')\n        offset = offset || parseInt(this.useAnchor) || 0\n        // #ifdef APP-PLUS-NVUE\n        if (!id) {\n          dom.scrollToElement(this.$refs.web, {\n            offset\n          })\n          resolve()\n        } else {\n          this._navigateTo = {\n            resolve,\n            reject,\n            offset\n          }\n          this.$refs.web.evalJs('uni.postMessage({data:{action:\"getOffset\",offset:(document.getElementById(' + id + ')||{}).offsetTop}})')\n        }\n        // #endif\n        // #ifndef APP-PLUS-NVUE\n        let deep = ' '\n        // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\n        deep = '>>>'\n        // #endif\n        const selector = uni.createSelectorQuery()\n          // #ifndef MP-ALIPAY\n          .in(this._in ? this._in.page : this)\n          // #endif\n          .select((this._in ? this._in.selector : '._root') + (id ? `${deep}#${id}` : '')).boundingClientRect()\n        if (this._in)\n          selector.select(this._in.selector).scrollOffset()\n            .select(this._in.selector).boundingClientRect() // 获取 scroll-view 的位置和滚动距离\n        else\n          selector.selectViewport().scrollOffset() // 获取窗口的滚动距离\n        selector.exec(res => {\n          if (!res[0])\n            return reject('Label not found')\n          const scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + offset\n          if (this._in)\n            // scroll-view 跳转\n            this._in.page[this._in.scrollTop] = scrollTop\n          else\n            // 页面跳转\n            uni.pageScrollTo({\n              scrollTop,\n              duration: 300\n            })\n          resolve()\n        })\n        // #endif\n      })\n    },\n\n    /**\n     * @description 获取文本内容\n     * @return {String}\n     */\n    getText() {\n      let text = '';\n      (function traversal(nodes) {\n        for (let i = 0; i < nodes.length; i++) {\n          const node = nodes[i]\n          if (node.type == 'text')\n            text += node.text.replace(/&amp;/g, '&')\n          else if (node.name == 'br')\n            text += '\\n'\n          else {\n            // 块级标签前后加换行\n            const isBlock = node.name == 'p' || node.name == 'div' || node.name == 'tr' || node.name == 'li' || (node.name[0] == 'h' && node.name[1] > '0' && node.name[1] < '7')\n            if (isBlock && text && text[text.length - 1] != '\\n')\n              text += '\\n'\n            // 递归获取子节点的文本\n            if (node.children)\n              traversal(node.children)\n            if (isBlock && text[text.length - 1] != '\\n')\n              text += '\\n'\n            else if (node.name == 'td' || node.name == 'th')\n              text += '\\t'\n          }\n        }\n      })(this.nodes)\n      return text\n    },\n\n    /**\n     * @description 获取内容大小和位置\n     * @return {Promise}\n     */\n    getRect() {\n      return new Promise((resolve, reject) => {\n        uni.createSelectorQuery()\n          // #ifndef MP-ALIPAY\n          .in(this)\n          // #endif\n          .select('#_root').boundingClientRect().exec(res => res[0] ? resolve(res[0]) : reject('Root label not found'))\n      })\n    },\n\n    /**\n     * @description 设置内容\n     * @param {String} content html 内容\n     * @param {Boolean} append 是否在尾部追加\n     */\n    setContent(content, append) {\n      if (!append || !this.imgList)\n        this.imgList = []\n      const nodes = new parser(this).parse(content)\n      // #ifdef APP-PLUS-NVUE\n      if (this._ready)\n        this._set(nodes, append)\n      // #endif\n      this.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes)\n\n      // #ifndef APP-PLUS-NVUE\n      this._videos = []\n      this.$nextTick(() => {\n        this._hook('onLoad')\n        this.$emit('load')\n      })\n\n      // 等待图片加载完毕\n      let height\n      clearInterval(this._timer)\n      this._timer = setInterval(() => {\n        this.getRect().then(rect => {\n          // 350ms 总高度无变化就触发 ready 事件\n          if (rect.height == height) {\n            this.$emit('ready', rect)\n            clearInterval(this._timer)\n          }\n          height = rect.height\n        }).catch(() => { })\n      }, 350)\n      // #endif\n    },\n\n    /**\n     * @description 调用插件钩子函数\n     */\n    _hook(name) {\n      for (let i = plugins.length; i--;)\n        if (this.plugins[i][name])\n          this.plugins[i][name]()\n    },\n\n    // #ifdef APP-PLUS-NVUE\n    /**\n     * @description 设置内容\n     */\n    _set(nodes, append) {\n      this.$refs.web.evalJs('setContent(' + JSON.stringify(nodes) + ',' + JSON.stringify([this.bgColor, this.errorImg, this.loadingImg, this.pauseVideo, this.scrollTable, this.selectable]) + ',' + append + ')')\n    },\n\n    /**\n     * @description 接收到 web-view 消息\n     */\n    _onMessage(e) {\n      const message = e.detail.data[0]\n      switch (message.action) {\n        // web-view 初始化完毕\n        case 'onJSBridgeReady':\n          this._ready = true\n          if (this.nodes)\n            this._set(this.nodes)\n          break\n        // 内容 dom 加载完毕\n        case 'onLoad':\n          this.height = message.height\n          this._hook('onLoad')\n          this.$emit('load')\n          break\n        // 所有图片加载完毕\n        case 'onReady':\n          this.getRect().then(res => {\n            this.$emit('ready', res)\n          }).catch(() => { })\n          break\n        // 总高度发生变化\n        case 'onHeightChange':\n          this.height = message.height\n          break\n        // 图片点击\n        case 'onImgTap':\n          this.$emit('imgTap', message.attrs)\n          if (this.previewImg)\n            uni.previewImage({\n              current: parseInt(message.attrs.i),\n              urls: this.imgList\n            })\n          break\n        // 链接点击\n        case 'onLinkTap':\n          const href = message.attrs.href\n          this.$emit('linkTap', message.attrs)\n          if (href) {\n            // 锚点跳转\n            if (href[0] == '#') {\n              if (this.useAnchor)\n                dom.scrollToElement(this.$refs.web, {\n                  offset: message.offset\n                })\n            }\n            // 打开外链\n            else if (href.includes('://')) {\n              if (this.copyLink)\n                plus.runtime.openWeb(href)\n            }\n            else\n              uni.navigateTo({\n                url: href,\n                fail() {\n                  wx.switchTab({\n                    url: href\n                  })\n                }\n              })\n          }\n          break\n        // 获取到锚点的偏移量\n        case 'getOffset':\n          if (typeof message.offset == 'number') {\n            dom.scrollToElement(this.$refs.web, {\n              offset: message.offset + this._navigateTo.offset\n            })\n            this._navigateTo.resolve()\n          } else\n            this._navigateTo.reject('Label not found')\n          break\n        // 点击\n        case 'onClick':\n          this.$emit('tap')\n          break\n        // 出错\n        case 'onError':\n          this.$emit('error', {\n            source: message.source,\n            attrs: message.attrs\n          })\n      }\n    }\n    // #endif\n  }\n}\n</script>\n\n<style>\n/* #ifndef APP-PLUS-NVUE */\n/* 根节点样式 */\n._root {\n  overflow: auto;\n  -webkit-overflow-scrolling: touch;\n}\n\n/* 长按复制 */\n._select {\n  user-select: text;\n}\n/* #endif */\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-parse.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754039746402\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}