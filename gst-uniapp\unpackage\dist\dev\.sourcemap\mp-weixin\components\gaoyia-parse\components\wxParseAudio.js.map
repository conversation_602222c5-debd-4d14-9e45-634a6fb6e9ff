{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseAudio.vue?70ae", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseAudio.vue?f13c", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseAudio.vue?1821", "webpack:///D:/gst/gst-uniapp/components/gaoyia-parse/components/wxParseAudio.vue?29d5", "uni-app:///components/gaoyia-parse/components/wxParseAudio.vue"], "names": ["name", "props", "node", "type", "default"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;;;AAG3D;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwnB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCgB5oB;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "components/gaoyia-parse/components/wxParseAudio.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wxParseAudio.vue?vue&type=template&id=4ab85caa&\"\nvar renderjs\nimport script from \"./wxParseAudio.vue?vue&type=script&lang=js&\"\nexport * from \"./wxParseAudio.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/gaoyia-parse/components/wxParseAudio.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseAudio.vue?vue&type=template&id=4ab85caa&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseAudio.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wxParseAudio.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- '<audio/>' 组件不再维护，建议使用能力更强的 'uni.createInnerAudioContext' 接口 有时间再改-->\r\n  <!--增加audio标签支持-->\r\n  <audio\r\n    :id=\"node.attr.id\"\r\n    :class=\"node.classStr\"\r\n    :style=\"node.styleStr\"\r\n    :src=\"node.attr.src\"\r\n    :loop=\"node.attr.loop\"\r\n    :poster=\"node.attr.poster\"\r\n    :name=\"node.attr.name\"\r\n    :author=\"node.attr.author\"\r\n    controls></audio>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'wxParseAudio',\r\n  props: {\r\n    node: {\r\n      type: Object,\r\n      default() {\r\n        return {};\r\n      },\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "sourceRoot": ""}