const { sequelize } = require('../src/models');

async function checkDatabase() {
  try {
    console.log('🔍 检查数据库结构...');

    // 1. 检查所有表
    const [tables] = await sequelize.query(`
      SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
    `);
    
    console.log('📋 数据库中的所有表:');
    tables.forEach(table => {
      console.log(`  - ${table.name}`);
    });

    // 2. 检查courses表结构
    const [courseFields] = await sequelize.query('PRAGMA table_info(courses)');
    console.log('\n📋 courses表字段:');
    courseFields.forEach(field => {
      console.log(`  - ${field.name} (${field.type})`);
    });

    // 3. 检查categories表结构
    const [categoryFields] = await sequelize.query('PRAGMA table_info(categories)');
    console.log('\n📋 categories表字段:');
    categoryFields.forEach(field => {
      console.log(`  - ${field.name} (${field.type})`);
    });

    // 4. 检查course_units表结构
    const [unitFields] = await sequelize.query('PRAGMA table_info(course_units)');
    console.log('\n📋 course_units表字段:');
    unitFields.forEach(field => {
      console.log(`  - ${field.name} (${field.type})`);
    });

    // 5. 检查数据统计
    const [courseCount] = await sequelize.query('SELECT COUNT(*) as count FROM courses');
    const [categoryCount] = await sequelize.query('SELECT COUNT(*) as count FROM categories');
    const [unitCount] = await sequelize.query('SELECT COUNT(*) as count FROM course_units');

    console.log('\n📊 数据统计:');
    console.log(`  - 课程数量: ${courseCount[0].count}`);
    console.log(`  - 分类数量: ${categoryCount[0].count}`);
    console.log(`  - 课程单元数量: ${unitCount[0].count}`);

    // 6. 检查课程分类关联
    const [coursesWithCategory] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses WHERE category_id IS NOT NULL
    `);
    
    const [coursesWithoutCategory] = await sequelize.query(`
      SELECT COUNT(*) as count FROM courses WHERE category_id IS NULL
    `);

    console.log('\n📊 课程分类关联:');
    console.log(`  - 有分类的课程: ${coursesWithCategory[0].count}`);
    console.log(`  - 无分类的课程: ${coursesWithoutCategory[0].count}`);

    // 7. 显示分类示例
    const [categories] = await sequelize.query(`
      SELECT id, name, pid FROM categories ORDER BY pid, id LIMIT 10
    `);

    console.log('\n📋 分类示例:');
    categories.forEach(cat => {
      console.log(`  - ID: ${cat.id}, 名称: ${cat.name}, 父ID: ${cat.pid}`);
    });

    // 8. 显示课程示例
    const [courses] = await sequelize.query(`
      SELECT c.id, c.title, c.category_id, cat.name as category_name
      FROM courses c
      LEFT JOIN categories cat ON c.category_id = cat.id
      LIMIT 10
    `);

    console.log('\n📋 课程示例:');
    courses.forEach(course => {
      console.log(`  - ID: ${course.id}, 标题: ${course.title}, 分类: ${course.category_name || '无分类'} (${course.category_id})`);
    });

    console.log('\n✅ 数据库检查完成');

  } catch (error) {
    console.error('❌ 数据库检查失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkDatabase().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = checkDatabase;
