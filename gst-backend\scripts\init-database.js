const { sequelize } = require('../src/config/database');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 导入所有模型
require('../src/models');

async function initDatabase() {
  try {
    console.log('🔄 开始初始化SQLite数据库...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ SQLite数据库连接成功');

    // 强制同步数据库（会删除现有表并重新创建）
    await sequelize.sync({ force: true });
    console.log('✅ 数据库表结构创建完成');
    
    // 获取模型
    const { User, StudyGroup, Course, CourseCategory, SystemConfig, Banner, HomeConfig, Menu } = sequelize.models;
    
    // 创建管理员用户
    const hashedPassword = await bcrypt.hash('123456', 12);
    
    console.log('🔄 开始创建用户...');
    const users = await User.bulkCreate([
      {
        username: 'admin',
        password: hashedPassword,
        email: '<EMAIL>',
        realName: '系统管理员',
        role: 'admin',
        status: 'active'
      },
      {
        username: 'teacher1',
        password: hashedPassword,
        email: '<EMAIL>',
        realName: '田中老师',
        role: 'teacher',
        status: 'active'
      },
      {
        username: 'pengwei',
        password: hashedPassword,
        email: '<EMAIL>',
        realName: '彭伟',
        role: 'student',
        status: 'active'
      }
    ]);
    console.log(`✅ 默认用户创建完成，共创建 ${users.length} 个用户`);

    // 验证用户是否真的被创建
    const userCount = await User.count();
    console.log(`📊 数据库中现在有 ${userCount} 个用户`);
    
    // 创建学习小组
    const groups = await StudyGroup.bulkCreate([
      {
        name: 'N5基础班',
        description: '日语入门基础学习小组，适合零基础学员',
        level: 'N5',
        status: 'active',
        maxMembers: 30,
        currentMembers: 1,
        teacherId: users[1].id,
        startDate: '2024-01-01',
        endDate: '2024-06-30',
        createdBy: users[0].id
      },
      {
        name: 'N4进阶班',
        description: '日语进阶学习小组，适合有一定基础的学员',
        level: 'N4',
        status: 'active',
        maxMembers: 25,
        currentMembers: 1,
        teacherId: users[1].id,
        startDate: '2024-02-01',
        endDate: '2024-07-31',
        createdBy: users[0].id
      },
      {
        name: 'N3中级班',
        description: '日语中级学习小组，适合中级水平学员',
        level: 'N3',
        status: 'pending',
        maxMembers: 20,
        currentMembers: 0,
        teacherId: users[1].id,
        startDate: '2024-03-01',
        endDate: '2024-08-31',
        createdBy: users[0].id
      }
    ]);
    console.log('✅ 学习小组创建完成');

    // 创建课程分类
    const categories = await CourseCategory.bulkCreate([
      // 一级分类
      {
        id: 1,
        name: '基础日语',
        code: 'basic_japanese',
        description: '适合初学者的基础日语课程',
        icon: '📚',
        color: '#007bff',
        level: 1,
        path: '/1',
        orderNum: 1,
        showInHome: true,
        showInSearch: true,
        showInGroup: false,
        isActive: true,
        createdBy: users[0].id
      },
      {
        id: 2,
        name: '进阶日语',
        code: 'advanced_japanese',
        description: '适合有一定基础的学习者',
        icon: '🎯',
        color: '#28a745',
        level: 1,
        path: '/2',
        orderNum: 2,
        showInHome: true,
        showInSearch: true,
        showInGroup: true,
        isActive: true,
        createdBy: users[0].id
      },
      // 二级分类
      {
        id: 11,
        name: '语法',
        code: 'grammar',
        description: '日语语法学习',
        icon: '📝',
        color: '#007bff',
        parentId: 1,
        level: 2,
        path: '/1/11',
        orderNum: 1,
        showInHome: true,
        showInSearch: true,
        showInGroup: false,
        isActive: true,
        createdBy: users[0].id
      },
      {
        id: 12,
        name: '词汇',
        code: 'vocabulary',
        description: '日语词汇学习',
        icon: '📖',
        color: '#007bff',
        parentId: 1,
        level: 2,
        path: '/1/12',
        orderNum: 2,
        showInHome: true,
        showInSearch: true,
        showInGroup: false,
        isActive: true,
        createdBy: users[0].id
      },
      {
        id: 21,
        name: '听力',
        code: 'listening',
        description: '日语听力训练',
        icon: '👂',
        color: '#28a745',
        parentId: 2,
        level: 2,
        path: '/2/21',
        orderNum: 1,
        showInHome: false,
        showInSearch: true,
        showInGroup: true,
        isActive: true,
        createdBy: users[0].id
      },
      {
        id: 22,
        name: '口语',
        code: 'speaking',
        description: '日语口语练习',
        icon: '💬',
        color: '#28a745',
        parentId: 2,
        level: 2,
        path: '/2/22',
        orderNum: 2,
        showInHome: false,
        showInSearch: true,
        showInGroup: true,
        isActive: true,
        createdBy: users[0].id
      }
    ]);
    console.log('✅ 课程分类创建完成');

    // 创建轮播图
    const banners = await Banner.bulkCreate([
      {
        id: 1,
        title: '2025"强国杯"来了｜日专生值得拥有的"中字头"英语证书！',
        subtitle: '全国大学生英语挑战赛',
        image: 'https://statici.jpworld.cn/images/banner1.jpg',
        link: '/pages/contest/english',
        linkType: 'internal',
        orderNum: 1,
        status: 'active',
        createdBy: users[0].id
      },
      {
        id: 2,
        title: '第八届"人民中国杯"日语国际翻译大赛正式开赛！',
        subtitle: '展示翻译才华，赢取丰厚奖品',
        image: 'https://statici.jpworld.cn/images/banner2.jpg',
        link: '/pages/contest/translation',
        linkType: 'internal',
        orderNum: 2,
        status: 'active',
        createdBy: users[0].id
      },
      {
        id: 3,
        title: '"音"你精彩 | 第六届"人民中国杯"全国日语才艺大赛报名正式启动！',
        subtitle: '展现日语才艺，绽放青春风采',
        image: 'https://statici.jpworld.cn/images/banner3.jpg',
        link: '/pages/contest/talent',
        linkType: 'internal',
        orderNum: 3,
        status: 'active',
        createdBy: users[0].id
      }
    ]);
    console.log('✅ 轮播图创建完成');

    // 创建首页配置
    const homeConfigs = await HomeConfig.bulkCreate([
      // top_post配置
      {
        configKey: 'top_post_1',
        configName: '外教日语',
        configType: 'top_post',
        title: '外教日语',
        link: 'pages/course/list',
        linkType: 'course',
        orderNum: 1,
        status: 'active',
        createdBy: users[0].id
      },
      {
        configKey: 'top_post_2',
        configName: '日语模考',
        configType: 'top_post',
        title: '日语模考',
        link: 'pages/exam/index',
        linkType: 'internal',
        orderNum: 2,
        status: 'active',
        createdBy: users[0].id
      },
      // game_show_footer配置
      {
        configKey: 'footer_1',
        configName: '跟外教学日语',
        configType: 'game_show_footer',
        title: '跟外教学日语',
        thumb: 'https://statici.jpworld.cn/images/teacher.png',
        link: 'pages/teacher/index',
        linkType: 'internal',
        orderNum: 1,
        status: 'active',
        createdBy: users[0].id
      },
      {
        configKey: 'footer_2',
        configName: '日语云课',
        configType: 'game_show_footer',
        title: '日语云课',
        thumb: 'https://statici.jpworld.cn/images/cloud.png',
        link: 'pages/course/index',
        linkType: 'internal',
        orderNum: 2,
        status: 'active',
        createdBy: users[0].id
      }
    ]);
    console.log('✅ 首页配置创建完成');

    // 创建课程
    const courses = await Course.bulkCreate([
      {
        title: '五十音图基础',
        description: '学习日语五十音图的发音和书写，掌握日语的基础发音规则',
        level: 'N5',
        category: 'grammar',
        duration: 60,
        difficulty: 1,
        orderNum: 1,
        status: 'published',
        createdBy: users[1].id
      },
      {
        title: '基础词汇100',
        description: '掌握日语基础词汇100个，包括日常生活常用词汇',
        level: 'N5',
        category: 'vocabulary',
        duration: 90,
        difficulty: 1,
        orderNum: 2,
        status: 'published',
        createdBy: users[1].id
      },
      {
        title: '简单对话练习',
        description: '日常简单对话练习，学会基本的问候和自我介绍',
        level: 'N5',
        category: 'speaking',
        duration: 45,
        difficulty: 2,
        orderNum: 3,
        status: 'published',
        createdBy: users[1].id
      },
      {
        title: 'N4语法要点',
        description: 'N4级别重要语法点讲解，包括动词变位和敬语基础',
        level: 'N4',
        category: 'grammar',
        duration: 120,
        difficulty: 3,
        orderNum: 1,
        status: 'published',
        createdBy: users[1].id
      },
      {
        title: '听力训练基础',
        description: '基础听力理解训练，提高日语听力水平',
        level: 'N4',
        category: 'listening',
        duration: 75,
        difficulty: 3,
        orderNum: 2,
        status: 'published',
        createdBy: users[1].id
      }
    ]);
    console.log('✅ 课程数据创建完成');
    
    // 创建系统配置
    await SystemConfig.bulkCreate([
      {
        configKey: 'site_name',
        configValue: 'GST日语培训班管理系统',
        description: '网站名称',
        type: 'string',
        isPublic: true
      },
      {
        configKey: 'max_upload_size',
        configValue: '10485760',
        description: '最大上传文件大小(字节)',
        type: 'number',
        isPublic: false
      },
      {
        configKey: 'allow_registration',
        configValue: 'true',
        description: '是否允许用户注册',
        type: 'boolean',
        isPublic: true
      },
      {
        configKey: 'default_group_size',
        configValue: '30',
        description: '默认小组最大人数',
        type: 'number',
        isPublic: false
      },
      {
        configKey: 'system_email',
        configValue: '<EMAIL>',
        description: '系统邮箱地址',
        type: 'string',
        isPublic: false
      }
    ]);
    console.log('✅ 系统配置创建完成');
    
    console.log('\n🎉 数据库初始化完成！');
    console.log('\n📋 默认账号信息：');
    console.log('管理员: admin / 123456');
    console.log('教师: teacher1 / 123456');
    console.log('学生: pengwei / 123456');
    console.log('\n🚀 现在可以启动服务器了！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
