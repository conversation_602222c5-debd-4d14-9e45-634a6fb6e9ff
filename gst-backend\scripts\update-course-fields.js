const { sequelize } = require('../src/models');

async function updateCourseFields() {
  try {
    console.log('🔄 开始更新courses表字段...');

    const queryInterface = sequelize.getQueryInterface();
    const coursesTableInfo = await queryInterface.describeTable('courses');
    
    console.log('📋 当前courses表字段:', Object.keys(coursesTableInfo));

    // 需要添加的字段列表
    const fieldsToAdd = [
      {
        name: 'Cost',
        definition: {
          type: sequelize.Sequelize.DECIMAL(10, 2),
          defaultValue: 0.00,
          comment: '课程价格'
        }
      },
      {
        name: 'picture',
        definition: {
          type: sequelize.Sequelize.STRING(255),
          comment: '课程封面图片'
        }
      },
      {
        name: 'sort',
        definition: {
          type: sequelize.Sequelize.INTEGER,
          defaultValue: 0,
          comment: '排序权重'
        }
      },
      {
        name: 'rating',
        definition: {
          type: sequelize.Sequelize.DECIMAL(3, 2),
          defaultValue: 0.00,
          comment: '课程评分'
        }
      },
      {
        name: 'student_count',
        definition: {
          type: sequelize.Sequelize.INTEGER,
          defaultValue: 0,
          comment: '学生数量'
        }
      }
    ];

    // 逐个添加缺失的字段
    for (const field of fieldsToAdd) {
      if (!coursesTableInfo[field.name]) {
        console.log(`📝 添加 courses.${field.name} 字段...`);
        try {
          await queryInterface.addColumn('courses', field.name, field.definition);
          console.log(`✅ courses.${field.name} 字段添加成功`);
        } catch (error) {
          console.error(`❌ 添加 courses.${field.name} 字段失败:`, error.message);
        }
      } else {
        console.log(`✅ courses.${field.name} 字段已存在`);
      }
    }

    console.log('🎉 courses表字段更新完成！');

  } catch (error) {
    console.error('❌ courses表字段更新失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  updateCourseFields().then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = updateCourseFields;
