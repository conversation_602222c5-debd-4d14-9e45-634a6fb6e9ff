{"version": 3, "sources": ["webpack:///D:/gst/gst-uniapp/pages/article/detail/analyse.vue?eff2", "webpack:///D:/gst/gst-uniapp/pages/article/detail/analyse.vue?06af", "webpack:///D:/gst/gst-uniapp/pages/article/detail/analyse.vue?c95d", "webpack:///D:/gst/gst-uniapp/pages/article/detail/analyse.vue?b52c", "uni-app:///pages/article/detail/analyse.vue", "webpack:///D:/gst/gst-uniapp/pages/article/detail/analyse.vue?0b01", "webpack:///D:/gst/gst-uniapp/pages/article/detail/analyse.vue?0c46"], "names": ["components", "lpAudioPlayer", "props", "comment", "type", "default", "methods", "onSetClipboardDataHandler", "console", "uni", "data", "success", "icon", "title"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAmnB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+BvoB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAA;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MACAC;MACAA;MACAC;QACAC;QACAC;UACAF;YACAG;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAksC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAttC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/article/detail/analyse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./analyse.vue?vue&type=template&id=414f38b2&scoped=true&\"\nvar renderjs\nimport script from \"./analyse.vue?vue&type=script&lang=js&\"\nexport * from \"./analyse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./analyse.vue?vue&type=style&index=0&id=414f38b2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"414f38b2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article/detail/analyse.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./analyse.vue?vue&type=template&id=414f38b2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 =\n    _vm.comment.file != \"\"\n      ? {\n          src: _vm.comment.file,\n        }\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./analyse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./analyse.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"root-box\">\n\t\t<view class=\"content-box\">\n\t\t\t<rich-text class=\"content\" :nodes=\"comment.content\" @longpress=\"onSetClipboardDataHandler(comment.content)\"></rich-text>\n\t\t</view>\n\n\t\t<view class=\"file-box\" v-if=\"comment.file != ''\">\n\t\t\t<view class=\"left-box\">\n\t\t\t\t<image :src=\"comment.author.avatar\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"center-box\">\n\t\t\t\t<view class=\"name-box\">\n\t\t\t\t\t<text>老师 ● </text>\n\t\t\t\t\t<text class=\"name\">{{comment.author.name}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"job\">\n\t\t\t\t\t<text>{{comment.author.job}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"intro\">\n\t\t\t\t\t<text>{{comment.author.intro}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"right-box\">\n\t\t\t\t<lp-audio-player :mini=\"true\" :audio=\"{src:comment.file}\"></lp-audio-player>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport lpAudioPlayer from '@/components/audio-player/audio-player.vue';\n\timport util from '@/common/js/util.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tlpAudioPlayer\n\t\t},\n\t\tprops: {\n\t\t\tcomment: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: null\n\t\t\t}\n\t\t},\n\t\tmethods:{\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tonSetClipboardDataHandler: function(text) {\r\n\t\t\t\tconsole.log('text'+text);\r\n\t\t\t\tconsole.log('text2'+util.html.getPureContent(text))\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t    data: util.html.getPureContent(text),\n\t\t\t\t    success: function () {\n\t\t\t\t        uni.showToast({\n\t\t\t\t        \ticon:'none',\n\t\t\t\t\t\t\ttitle:'已复制'\n\t\t\t\t        })\n\t\t\t\t    }\n\t\t\t\t});\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.root-box {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tfont-size: 28rpx;\n\t\tborder: solid 1px #eee;\n\t\tpadding: 30rpx;\n\t\tbackground: #f5f5f5;\n\t\tborder-radius: 10rpx;\n\n\t\t.content-box {\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tmin-height: 100rpx;\n\n\t\t\t.content {\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\n\t\t.file-box {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 30rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\tbackground: #fff;\n\n\n\t\t\t.left-box {\n\t\t\t\twidth: 96rpx;\n\t\t\t\theight: 96rpx;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.center-box {\n\t\t\t\tflex: 1;\n\t\t\t\tcolor: #aaa;\n\t\t\t\tmargin: 0 30rpx;\n\n\t\t\t\t.name {\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.right-box {}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./analyse.vue?vue&type=style&index=0&id=414f38b2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./analyse.vue?vue&type=style&index=0&id=414f38b2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699115971\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}